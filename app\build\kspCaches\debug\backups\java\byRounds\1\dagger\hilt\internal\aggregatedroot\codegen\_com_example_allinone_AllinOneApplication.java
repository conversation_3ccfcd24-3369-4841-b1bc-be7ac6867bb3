package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.example.allinone.AllinOneApplication",
    rootPackage = "com.example.allinone",
    originatingRoot = "com.example.allinone.AllinOneApplication",
    originatingRootPackage = "com.example.allinone",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "AllinOneApplication",
    originatingRootSimpleNames = "AllinOneApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_example_allinone_AllinOneApplication {
}
