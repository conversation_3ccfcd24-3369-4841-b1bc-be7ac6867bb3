package com.example.allinone.ui

import android.os.Bundle
import android.view.View
import android.widget.ImageButton
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.example.allinone.R

/**
 * Base Fragment class - drawer functionality now handled by Compose MainActivity
 */
abstract class BaseFragment : Fragment() {
    
    // Note: Drawer functionality is now handled by the Compose MainActivity
    // Individual fragments that need drawer access should be migrated to Compose
} 