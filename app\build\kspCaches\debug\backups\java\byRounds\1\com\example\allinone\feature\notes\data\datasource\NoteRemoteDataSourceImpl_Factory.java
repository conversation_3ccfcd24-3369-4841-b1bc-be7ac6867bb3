package com.example.allinone.feature.notes.data.datasource;

import com.example.allinone.firebase.FirebaseManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NoteRemoteDataSourceImpl_Factory implements Factory<NoteRemoteDataSourceImpl> {
  private final Provider<FirebaseManager> firebaseManagerProvider;

  public NoteRemoteDataSourceImpl_Factory(Provider<FirebaseManager> firebaseManagerProvider) {
    this.firebaseManagerProvider = firebaseManagerProvider;
  }

  @Override
  public NoteRemoteDataSourceImpl get() {
    return newInstance(firebaseManagerProvider.get());
  }

  public static NoteRemoteDataSourceImpl_Factory create(
      Provider<FirebaseManager> firebaseManagerProvider) {
    return new NoteRemoteDataSourceImpl_Factory(firebaseManagerProvider);
  }

  public static NoteRemoteDataSourceImpl newInstance(FirebaseManager firebaseManager) {
    return new NoteRemoteDataSourceImpl(firebaseManager);
  }
}
