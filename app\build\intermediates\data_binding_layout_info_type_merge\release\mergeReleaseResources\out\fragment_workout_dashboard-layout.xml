<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_workout_dashboard" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\fragment_workout_dashboard.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_workout_dashboard_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="144" endOffset="51"/></Target><Target id="@+id/swipe_refresh_layout" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="8" startOffset="4" endLine="142" endOffset="59"/></Target><Target id="@+id/weekly_workout_count" view="TextView"><Expressions/><location startLine="46" startOffset="24" endLine="52" endOffset="80"/></Target><Target id="@+id/weekly_workout_duration" view="TextView"><Expressions/><location startLine="54" startOffset="24" endLine="60" endOffset="80"/></Target><Target id="@+id/recent_workout_name" view="TextView"><Expressions/><location startLine="84" startOffset="24" endLine="90" endOffset="80"/></Target><Target id="@+id/recent_workout_date" view="TextView"><Expressions/><location startLine="92" startOffset="24" endLine="98" endOffset="80"/></Target><Target id="@+id/total_workout_count" view="TextView"><Expressions/><location startLine="121" startOffset="24" endLine="127" endOffset="80"/></Target><Target id="@+id/total_workout_duration" view="TextView"><Expressions/><location startLine="129" startOffset="24" endLine="135" endOffset="80"/></Target></Targets></Layout>