<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_instagram_post" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_instagram_post.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_instagram_post_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="108" endOffset="51"/></Target><Target id="@+id/postType" view="TextView"><Expressions/><location startLine="20" startOffset="8" endLine="34" endOffset="32"/></Target><Target id="@+id/postThumbnail" view="ImageView"><Expressions/><location startLine="36" startOffset="8" endLine="46" endOffset="65"/></Target><Target id="@+id/postCaption" view="TextView"><Expressions/><location startLine="48" startOffset="8" endLine="60" endOffset="73"/></Target><Target id="@+id/postDate" view="TextView"><Expressions/><location startLine="62" startOffset="8" endLine="71" endOffset="39"/></Target><Target id="@+id/divider" view="View"><Expressions/><location startLine="73" startOffset="8" endLine="80" endOffset="65"/></Target><Target id="@+id/insightsLabel" view="TextView"><Expressions/><location startLine="82" startOffset="8" endLine="92" endOffset="64"/></Target><Target id="@+id/postInsights" view="TextView"><Expressions/><location startLine="94" startOffset="8" endLine="105" endOffset="84"/></Target></Targets></Layout>