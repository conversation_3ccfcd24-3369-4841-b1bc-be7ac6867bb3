<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:hint="Event Title"
        app:boxBackgroundMode="filled"
        app:boxBackgroundColor="@color/white">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/eventTitleInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="1"
            android:background="@color/white" />

    </com.google.android.material.textfield.TextInputLayout>
    
    <!-- Time Selection -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="8dp">
        
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="4dp"
            android:hint="Start Time (HH:MM)"
            app:boxBackgroundMode="filled"
            app:boxBackgroundColor="@color/white">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/eventTimeInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="time"
                android:text="12:00"
                android:background="@color/white" />

        </com.google.android.material.textfield.TextInputLayout>
        
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="4dp"
            android:hint="End Time (HH:MM)"
            app:boxBackgroundMode="filled"
            app:boxBackgroundColor="@color/white">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/eventEndTimeInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="time"
                android:text="13:00"
                android:background="@color/white" />

        </com.google.android.material.textfield.TextInputLayout>
    </LinearLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Description (optional)"
        app:boxBackgroundMode="filled"
        app:boxBackgroundColor="@color/white">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/eventDescriptionInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textMultiLine"
            android:maxLines="3"
            android:background="@color/white" />

    </com.google.android.material.textfield.TextInputLayout>

</LinearLayout> 