// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemProgramExerciseBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView exerciseName;

  @NonNull
  public final TextView exerciseNotes;

  @NonNull
  public final TextView exerciseReps;

  @NonNull
  public final TextView exerciseSets;

  @NonNull
  public final TextView exerciseWeight;

  private ItemProgramExerciseBinding(@NonNull MaterialCardView rootView,
      @NonNull TextView exerciseName, @NonNull TextView exerciseNotes,
      @NonNull TextView exerciseReps, @NonNull TextView exerciseSets,
      @NonNull TextView exerciseWeight) {
    this.rootView = rootView;
    this.exerciseName = exerciseName;
    this.exerciseNotes = exerciseNotes;
    this.exerciseReps = exerciseReps;
    this.exerciseSets = exerciseSets;
    this.exerciseWeight = exerciseWeight;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemProgramExerciseBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemProgramExerciseBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_program_exercise, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemProgramExerciseBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.exercise_name;
      TextView exerciseName = ViewBindings.findChildViewById(rootView, id);
      if (exerciseName == null) {
        break missingId;
      }

      id = R.id.exercise_notes;
      TextView exerciseNotes = ViewBindings.findChildViewById(rootView, id);
      if (exerciseNotes == null) {
        break missingId;
      }

      id = R.id.exercise_reps;
      TextView exerciseReps = ViewBindings.findChildViewById(rootView, id);
      if (exerciseReps == null) {
        break missingId;
      }

      id = R.id.exercise_sets;
      TextView exerciseSets = ViewBindings.findChildViewById(rootView, id);
      if (exerciseSets == null) {
        break missingId;
      }

      id = R.id.exercise_weight;
      TextView exerciseWeight = ViewBindings.findChildViewById(rootView, id);
      if (exerciseWeight == null) {
        break missingId;
      }

      return new ItemProgramExerciseBinding((MaterialCardView) rootView, exerciseName,
          exerciseNotes, exerciseReps, exerciseSets, exerciseWeight);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
