<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_backup" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_backup.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_backup_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="70" endOffset="35"/></Target><Target id="@+id/backup_name" view="TextView"><Expressions/><location startLine="14" startOffset="8" endLine="22" endOffset="55"/></Target><Target id="@+id/backup_size" view="TextView"><Expressions/><location startLine="24" startOffset="8" endLine="32" endOffset="67"/></Target><Target id="@+id/restore_button" view="Button"><Expressions/><location startLine="34" startOffset="8" endLine="45" endOffset="67"/></Target><Target id="@+id/share_button" view="Button"><Expressions/><location startLine="47" startOffset="8" endLine="56" endOffset="67"/></Target><Target id="@+id/delete_button" view="Button"><Expressions/><location startLine="58" startOffset="8" endLine="67" endOffset="67"/></Target></Targets></Layout>