<?xml version="1.0" encoding="utf-8"?>
<androidx.swiperefreshlayout.widget.SwipeRefreshLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/futuresSwipeRefreshLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <!-- Summary Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/summaryCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp">

                <TextView
                    android:id="@+id/balanceTitleText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Margin Balance"
                    android:textAppearance="?attr/textAppearanceSubtitle1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/balanceValueText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="$0.00"
                    android:textAppearance="?attr/textAppearanceHeadline5"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/balanceTitleText" />

                <TextView
                    android:id="@+id/pnlTitleText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Unrealized PNL"
                    android:textAppearance="?attr/textAppearanceSubtitle1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/pnlValueText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="$0.00"
                    android:textAppearance="?attr/textAppearanceHeadline5"
                    android:textColor="@android:color/holo_green_dark"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/pnlTitleText" />

                <View
                    android:id="@+id/divider"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="16dp"
                    android:background="?android:attr/listDivider"
                    app:layout_constraintTop_toBottomOf="@id/balanceValueText" />



                <TextView
                    android:id="@+id/marginBalanceTitleText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="Wallet Balance"
                    android:textAppearance="?attr/textAppearanceBody2"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider" />

                <TextView
                    android:id="@+id/marginBalanceValueText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="$0.00"
                    android:textAppearance="?attr/textAppearanceBody1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/marginBalanceTitleText" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.cardview.widget.CardView>

        <!-- Positions Title -->
        <TextView
            android:id="@+id/positionsTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Open Positions"
            android:textAppearance="?attr/textAppearanceHeadline6"
            android:layout_marginTop="8dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/summaryCard" />

        <!-- Positions RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/positionsRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="8dp"
            app:layout_constraintTop_toBottomOf="@id/positionsTitle"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Empty State -->
        <TextView
            android:id="@+id/emptyStateText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="No open positions"
            android:textAppearance="?attr/textAppearanceBody1"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/positionsTitle" />

        <!-- Loading Progress -->
        <ProgressBar
            android:id="@+id/loadingProgress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.swiperefreshlayout.widget.SwipeRefreshLayout>