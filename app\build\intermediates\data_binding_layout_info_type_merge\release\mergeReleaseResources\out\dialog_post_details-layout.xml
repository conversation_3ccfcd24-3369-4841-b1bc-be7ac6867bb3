<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_post_details" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\dialog_post_details.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView"><Targets><Target tag="layout/dialog_post_details_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="164" endOffset="39"/></Target><Target id="@+id/postDetailThumbnail" view="ImageView"><Expressions/><location startLine="13" startOffset="8" endLine="22" endOffset="55"/></Target><Target id="@+id/postCaptionLabel" view="TextView"><Expressions/><location startLine="24" startOffset="8" endLine="33" endOffset="45"/></Target><Target id="@+id/postCaption" view="TextView"><Expressions/><location startLine="35" startOffset="8" endLine="46" endOffset="53"/></Target><Target id="@+id/postDateLabel" view="TextView"><Expressions/><location startLine="48" startOffset="8" endLine="57" endOffset="68"/></Target><Target id="@+id/postDate" view="TextView"><Expressions/><location startLine="59" startOffset="8" endLine="68" endOffset="70"/></Target><Target id="@+id/postTypeLabel" view="TextView"><Expressions/><location startLine="70" startOffset="8" endLine="79" endOffset="65"/></Target><Target id="@+id/postType" view="TextView"><Expressions/><location startLine="81" startOffset="8" endLine="90" endOffset="70"/></Target><Target id="@+id/postLinkLabel" view="TextView"><Expressions/><location startLine="92" startOffset="8" endLine="101" endOffset="65"/></Target><Target id="@+id/postLink" view="TextView"><Expressions/><location startLine="103" startOffset="8" endLine="115" endOffset="70"/></Target><Target id="@+id/divider" view="View"><Expressions/><location startLine="117" startOffset="8" endLine="123" endOffset="65"/></Target><Target id="@+id/insightsLabel" view="TextView"><Expressions/><location startLine="125" startOffset="8" endLine="135" endOffset="64"/></Target><Target id="@+id/postInsights" view="TextView"><Expressions/><location startLine="137" startOffset="8" endLine="150" endOffset="129"/></Target><Target id="@+id/closeButton" view="Button"><Expressions/><location startLine="152" startOffset="8" endLine="161" endOffset="69"/></Target></Targets></Layout>