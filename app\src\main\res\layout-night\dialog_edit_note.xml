<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/navy_surface">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="Title"
            android:textColorHint="@color/white"
            app:hintTextColor="@color/white"
            style="@style/Widget.App.TextInputLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editNoteTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:maxLines="1"
                android:textColor="@color/white" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- Rich Text Editor Toolbar -->
        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <ImageButton
                    android:id="@+id/boldButton"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="Bold"
                    android:src="@drawable/ic_format_bold"
                    app:tint="@color/white" />

                <ImageButton
                    android:id="@+id/italicButton"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="Italic"
                    android:src="@drawable/ic_format_italic"
                    app:tint="@color/white" />

                <ImageButton
                    android:id="@+id/underlineButton"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="Underline"
                    android:src="@drawable/ic_format_underline"
                    app:tint="@color/white" />

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:layout_marginHorizontal="8dp"
                    android:background="?android:attr/listDivider" />

                <ImageButton
                    android:id="@+id/bulletListButton"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="Bullet List"
                    android:src="@drawable/ic_format_list_bulleted"
                    app:tint="@color/white" />

                <ImageButton
                    android:id="@+id/checkboxListButton"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="Checkbox List"
                    android:src="@drawable/ic_checkbox"
                    app:tint="@color/white" />

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:layout_marginHorizontal="8dp"
                    android:background="?android:attr/listDivider" />

                <ImageButton
                    android:id="@+id/addImageButton"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="Add Image"
                    android:src="@drawable/ic_image"
                    app:tint="@color/white" />
            </LinearLayout>
        </HorizontalScrollView>

        <!-- Rich Text Editor -->
        <jp.wasabeef.richeditor.RichEditor
            android:id="@+id/editNoteContent"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:layout_marginTop="8dp"
            android:background="@android:color/transparent"
            android:padding="8dp" />

        <!-- Image Attachments -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="Attached Images"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:textAppearance="?attr/textAppearanceSubtitle1"
            android:visibility="visible" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/imagesRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        <Button
            android:id="@+id/addAttachmentButton"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Add Image"
            android:textColor="@color/white"
            android:drawableStart="@drawable/ic_add_photo"
            android:drawablePadding="8dp"
            app:strokeColor="@color/white"
            app:iconTint="@color/white" />

        <Button
            android:id="@+id/shareNoteButton"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Share Note"
            android:textColor="@color/white"
            android:drawableStart="@drawable/ic_share"
            android:drawablePadding="8dp"
            app:strokeColor="@color/white"
            app:iconTint="@color/white" />

    </LinearLayout>
</androidx.core.widget.NestedScrollView> 