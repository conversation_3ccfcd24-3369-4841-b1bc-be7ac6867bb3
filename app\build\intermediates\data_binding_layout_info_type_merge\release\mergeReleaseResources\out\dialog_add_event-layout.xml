<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_add_event" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\dialog_add_event.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_add_event_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="89" endOffset="14"/></Target><Target id="@+id/eventTitleInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="16" startOffset="8" endLine="22" endOffset="47"/></Target><Target id="@+id/eventTimeInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="42" startOffset="12" endLine="48" endOffset="51"/></Target><Target id="@+id/eventEndTimeInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="61" startOffset="12" endLine="67" endOffset="51"/></Target><Target id="@+id/eventDescriptionInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="79" startOffset="8" endLine="85" endOffset="47"/></Target></Targets></Layout>