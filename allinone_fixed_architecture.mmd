classDiagram
    %% Main Application Components
    class AllinOneApplication {
        +onCreate()
        +getFirebaseRepository() FirebaseRepository
        +getCacheManager() CacheManager
        +getNetworkUtils() NetworkUtils
        +getFirebaseIdManager() FirebaseIdManager
        +getFirebaseStorageUtil() FirebaseStorageUtil
        +getDataChangeNotifier() DataChangeNotifier
    }

    class MainActivity {
        +onCreate()
        +setupNavigation()
        +handleFragmentNavigation()
        +setupBottomNavigation()
        +handleBackPress()
        +showNetworkStatus()
        +setupWorkers()
    }

    %% Core Data Models
    class Transaction {
        +Long id
        +Double amount
        +String type
        +String description
        +Boolean isIncome
        +Date date
        +String category
        +Long? relatedRegistrationId
    }

    class Investment {
        +Long id
        +String name
        +Double amount
        +String type
        +String? description
        +String? imageUri
        +Date date
        +Boolean isPast
        +Double profitLoss
        +Double currentValue
    }

    class Note {
        +Long id
        +String title
        +String content
        +Date date
        +String? imageUris
        +String? videoUris
        +String? voiceNoteUris
        +Date lastEdited
        +Boolean isRichText
    }

    class WTStudent {
        +Long id
        +String name
        +String phoneNumber
        +String? email
        +String? instagram
        +Boolean isActive
        +String? profileImageUri
        +Date? startDate
        +Date? endDate
        +Double amount
        +Boolean isPaid
        +Date? paymentDate
        +String? attachmentUri
        +String? deviceId
        +String? notes
        +String? photoUri
    }

    class WTRegistration {
        +Long id
        +Long studentId
        +Double amount
        +String? attachmentUri
        +Date? startDate
        +Date? endDate
        +Date paymentDate
        +String? notes
        +Boolean isPaid
    }

    class WTLesson {
        +Long id
        +String title
        +String description
        +Date date
        +String type
        +String? location
        +String? instructor
    }

    class WTSeminar {
        +Long id
        +String title
        +String description
        +Date date
        +String location
        +String instructor
        +Double price
        +Int maxParticipants
        +List~String~ participants
        +Boolean isActive
    }

    class Task {
        +Long id
        +String name
        +String? description
        +Boolean completed
        +Date date
        +Date? dueDate
        +Long? groupId
    }

    class TaskGroup {
        +Long id
        +String title
        +String? description
        +String color
        +Date createdAt
        +Boolean isCompleted
    }

    class Event {
        +Long id
        +String title
        +String? description
        +Date date
        +Date? endDate
        +String type
    }

    class HistoryItem {
        +Long id
        +String title
        +String description
        +Date date
        +Double amount
        +String type
        +String? imageUri
        +ItemType itemType
    }

    class Program {
        +Long id
        +String name
        +String? description
        +List~ProgramExercise~ exercises
        +Date createdDate
        +Date lastModifiedDate
    }

    class Workout {
        +Long id
        +Long? programId
        +String? programName
        +Date startTime
        +Date? endTime
        +Long duration
        +List~WorkoutExercise~ exercises
        +String? notes
    }

    class Exercise {
        +Long id
        +String name
        +String? muscleGroup
        +String? description
        +String? instructions
    }

    class VoiceNote {
        +Long id
        +String filePath
        +Long duration
        +Date createdAt
        +String? noteId
    }

    %% Firebase Layer
    class FirebaseRepository {
        +StateFlow~List~Transaction~~ transactions
        +StateFlow~List~Investment~~ investments
        +StateFlow~List~Note~~ notes
        +StateFlow~List~Task~~ tasks
        +StateFlow~List~TaskGroup~~ taskGroups
        +StateFlow~List~WTStudent~~ students
        +StateFlow~List~Event~~ events
        +StateFlow~List~WTLesson~~ wtLessons
        +StateFlow~List~WTRegistration~~ registrations
        +StateFlow~List~Program~~ programs
        +StateFlow~List~Workout~~ workouts
        +LiveData~Boolean~ isNetworkAvailable
        +LiveData~String?~ errorMessage
        +LiveData~Boolean~ isLoading
        +suspend refreshAllData()
        +suspend insertTransaction(Transaction)
        +suspend updateTransaction(Transaction)
        +suspend deleteTransaction(Transaction)
        +suspend insertInvestment(Investment)
        +suspend updateInvestment(Investment)
        +suspend deleteInvestment(Investment)
        +suspend insertNote(Note)
        +suspend updateNote(Note)
        +suspend deleteNote(Note)
        +suspend insertTask(Task)
        +suspend updateTask(Task)
        +suspend deleteTask(Task)
        +suspend insertTaskGroup(TaskGroup)
        +suspend updateTaskGroup(TaskGroup)
        +suspend deleteTaskGroup(TaskGroup)
        +suspend insertStudent(WTStudent)
        +suspend updateStudent(WTStudent) Boolean
        +suspend deleteStudent(WTStudent)
        +suspend insertRegistration(WTRegistration)
        +suspend updateRegistration(WTRegistration)
        +suspend deleteRegistration(WTRegistration)
        +suspend saveProgram(Program) Long
        +suspend saveWorkout(Workout) Long
        +suspend uploadFile(Uri, String, String?) String?
        +suspend deleteFile(String) Boolean
        +suspend getNextId(String) Long
    }

    class FirebaseManager {
        +FirebaseFirestore firestore
        +FirebaseStorage storage
        +FirebaseIdManager idManager
        +String deviceId
        +suspend saveTransaction(Transaction) Boolean
        +suspend getTransactions() List~Transaction~
        +suspend deleteTransaction(Transaction)
        +suspend saveInvestment(Investment) Boolean
        +suspend getInvestments() List~Investment~
        +suspend deleteInvestment(Investment)
        +suspend saveNote(Note)
        +suspend getNotes() List~Note~
        +suspend deleteNote(Note)
        +suspend saveTask(Task)
        +suspend getTasks() List~Task~
        +suspend deleteTask(Task)
        +suspend saveTaskGroup(TaskGroup)
        +suspend getTaskGroups() List~TaskGroup~
        +suspend deleteTaskGroup(TaskGroup)
        +suspend saveStudent(WTStudent) Boolean
        +suspend getStudents() List~WTStudent~
        +suspend deleteStudent(WTStudent)
        +suspend saveRegistration(WTRegistration) Task~Void~
        +suspend getRegistrations() List~WTRegistration~
        +suspend deleteRegistration(Long) Task~Void~
        +suspend saveProgram(Program) Long
        +suspend getPrograms() List~Program~
        +suspend deleteProgram(Long)
        +suspend saveWorkout(Workout) Long
        +suspend getWorkouts() List~Workout~
        +suspend deleteWorkout(Long)
        +suspend uploadImage(Uri) String?
        +suspend uploadAttachment(Uri) String?
        +suspend deleteAllData()
    }

    class FirebaseIdManager {
        +Map~String, Long~ counters
        +suspend getNextId(String) Long
        +suspend initializeCounters()
        +suspend updateCounter(String, Long)
    }

    class FirebaseStorageUtil {
        +FirebaseStorage storage
        +suspend uploadFile(Uri, String, String?) String?
        +suspend deleteFile(String) Boolean
        +suspend deleteFolder(String, String) Boolean
        +suspend getFileUrl(String) String?
    }

    class DataChangeNotifier {
        +MutableLiveData~Boolean~ transactionsChanged
        +MutableLiveData~Boolean~ investmentsChanged
        +MutableLiveData~Boolean~ notesChanged
        +MutableLiveData~Boolean~ tasksChanged
        +MutableLiveData~Boolean~ taskGroupsChanged
        +MutableLiveData~Boolean~ studentsChanged
        +MutableLiveData~Boolean~ eventsChanged
        +MutableLiveData~Boolean~ lessonsChanged
        +MutableLiveData~Boolean~ registrationsChanged
        +MutableLiveData~Boolean~ programsChanged
        +MutableLiveData~Boolean~ workoutsChanged
        +static notifyTransactionsChanged()
        +static notifyInvestmentsChanged()
        +static notifyNotesChanged()
        +static notifyTasksChanged()
        +static notifyTaskGroupsChanged()
        +static notifyStudentsChanged()
        +static notifyEventsChanged()
        +static notifyLessonsChanged()
        +static notifyRegistrationsChanged()
        +static notifyProgramsChanged()
        +static notifyWorkoutsChanged()
    }

    class OfflineQueue {
        +enum DataType
        +enum Operation
        +data class QueueItem
        +suspend enqueue(DataType, Operation, String)
        +suspend processQueue()
        +suspend getQueue() List~QueueItem~
        +suspend clearQueue()
    }

    %% Cache Layer
    class CacheManager {
        +SharedPreferences preferences
        +suspend cacheTransactions(List~Transaction~)
        +suspend getCachedTransactions() List~Transaction~
        +suspend cacheInvestments(List~Investment~)
        +suspend getCachedInvestments() List~Investment~
        +suspend cacheNotes(List~Note~)
        +suspend getCachedNotes() List~Note~
        +suspend cacheTasks(List~Task~)
        +suspend getCachedTasks() List~Task~
        +suspend cacheTaskGroups(List~TaskGroup~)
        +suspend getCachedTaskGroups() List~TaskGroup~
        +suspend cacheStudents(List~WTStudent~)
        +suspend getCachedStudents() List~WTStudent~
        +suspend cacheEvents(List~Event~)
        +suspend getCachedEvents() List~Event~
        +suspend cacheLessons(List~WTLesson~)
        +suspend getCachedLessons() List~WTLesson~
        +suspend cacheRegistrations(List~WTRegistration~)
        +suspend getCachedRegistrations() List~WTRegistration~
        +suspend cachePrograms(List~Program~)
        +suspend getCachedPrograms() List~Program~
        +suspend cacheWorkouts(List~Workout~)
        +suspend getCachedWorkouts() List~Workout~
        +suspend clearAllCache()
        +suspend clearCache(String)
    }

    %% Utility Classes
    class NetworkUtils {
        +ConnectivityManager connectivityManager
        +LiveData~Boolean~ isNetworkAvailable
        +LiveData~Boolean~ isOnline
        +Boolean isActiveNetworkConnected()
        +suspend checkNetworkConnectivity()
        +suspend waitForNetworkConnection()
    }

    class BackupHelper {
        +suspend exportData(Context, Uri) Boolean
        +suspend importData(Context, Uri) Boolean
        +suspend validateBackupFile(Uri) String?
        +suspend createBackupFile() File?
        +suspend restoreFromBackup(List~Transaction~, List~Investment~, List~Note~, List~WTStudent~)
    }

    class LogcatHelper {
        +suspend captureLogcat() String
        +suspend saveLogcatToFile(String) Boolean
        +suspend getLogcatFile() File?
        +suspend clearLogcatFile()
        +suspend getLogcatContent() String
    }

    class ApiKeyManager {
        +suspend saveApiKey(String, String)
        +suspend getApiKey(String) String?
        +suspend deleteApiKey(String)
        +suspend getAllApiKeys() Map~String, String~
        +Boolean isApiKeyValid(String)
    }

    class TradingUtils {
        +suspend calculateProfitLoss(Investment) Double
        +suspend calculateTotalPortfolioValue() Double
        +suspend getInvestmentSummary() Map~String, Double~
        +suspend validateTradeParameters(Double, String) Boolean
        +suspend formatCurrency(Double) String
    }

    %% ViewModels
    class HomeViewModel {
        +LiveData~List~Transaction~~ recentTransactions
        +LiveData~Double~ totalIncome
        +LiveData~Double~ totalExpense
        +LiveData~Double~ balance
        +LiveData~List~CategorySummary~~ categorySpending
        +suspend refreshData()
        +suspend getTransactionSummary()
        +suspend getCategorySpending()
    }

    class InvestmentsViewModel {
        +LiveData~List~Investment~~ investments
        +LiveData~Double~ totalValue
        +LiveData~Double~ totalProfitLoss
        +LiveData~List~Investment~~ pastInvestments
        +LiveData~List~Investment~~ activeInvestments
        +LiveData~Boolean~ isLoading
        +LiveData~String~ errorMessage
        +suspend refreshInvestments()
        +suspend addInvestment(Investment)
        +suspend updateInvestment(Investment)
        +suspend deleteInvestment(Investment)
        +suspend calculatePortfolioMetrics()
        +suspend getInvestmentById(Long) Investment?
    }

    class NotesViewModel {
        +LiveData~List~Note~~ notes
        +LiveData~Boolean~ isLoading
        +LiveData~String~ errorMessage
        +suspend refreshNotes()
        +suspend addNote(Note)
        +suspend updateNote(Note)
        +suspend deleteNote(Note)
        +suspend searchNotes(String) List~Note~
    }

    class TasksViewModel {
        +LiveData~List~Task~~ tasks
        +LiveData~List~TaskGroup~~ taskGroups
        +LiveData~List~Task~~ completedTasks
        +LiveData~List~Task~~ pendingTasks
        +LiveData~Boolean~ isLoading
        +LiveData~String~ errorMessage
        +suspend refreshTasks()
        +suspend addTask(Task)
        +suspend updateTask(Task)
        +suspend deleteTask(Task)
        +suspend addTaskGroup(TaskGroup)
        +suspend updateTaskGroup(TaskGroup)
        +suspend deleteTaskGroup(TaskGroup)
        +suspend getTasksByGroup(Long) List~Task~
    }

    class WTRegisterViewModel {
        +LiveData~List~WTStudent~~ allStudents
        +LiveData~List~WTStudent~~ activeStudents
        +LiveData~List~WTStudent~~ unpaidStudents
        +LiveData~List~WTStudent~~ paidStudents
        +LiveData~List~WTStudent~~ registeredStudents
        +LiveData~List~WTLesson~~ lessonSchedule
        +LiveData~Boolean~ isNetworkAvailable
        +LiveData~String?~ errorMessage
        +suspend refreshData()
        +suspend addStudent(WTStudent)
        +suspend updateStudent(WTStudent)
        +suspend deleteStudent(WTStudent)
        +suspend registerStudentForCourse(WTStudent, Date, Date, Double, Boolean)
        +suspend markAsPaid(WTStudent)
        +suspend markAsUnpaid(WTStudent)
        +suspend calculateEndDateBasedOnLessons(Date) Date?
    }

    class WTLessonsViewModel {
        +LiveData~List~WTLesson~~ lessons
        +LiveData~Boolean~ isLoading
        +LiveData~String~ errorMessage
        +suspend refreshLessons()
        +suspend addLesson(WTLesson)
        +suspend updateLesson(WTLesson)
        +suspend deleteLesson(WTLesson)
        +suspend getLessonsByDate(Date) List~WTLesson~
        +suspend getLessonsByType(String) List~WTLesson~
    }

    class WTSeminarsViewModel {
        +LiveData~List~WTSeminar~~ seminars
        +LiveData~List~WTSeminar~~ activeSeminars
        +LiveData~List~WTSeminar~~ pastSeminars
        +LiveData~Boolean~ isLoading
        +LiveData~String~ errorMessage
        +suspend refreshSeminars()
        +suspend addSeminar(WTSeminar)
        +suspend updateSeminar(WTSeminar)
        +suspend deleteSeminar(WTSeminar)
        +suspend registerParticipant(Long, String)
        +suspend unregisterParticipant(Long, String)
    }

    class CalendarViewModel {
        +LiveData~List~Event~~ events
        +LiveData~List~WTLesson~~ lessons
        +LiveData~List~WTSeminar~~ seminars
        +LiveData~Date~ selectedDate
        +LiveData~Boolean~ isLoading
        +LiveData~String~ errorMessage
        +suspend refreshEvents()
        +suspend addEvent(Event)
        +suspend updateEvent(Event)
        +suspend deleteEvent(Event)
        +suspend getEventsByDate(Date) List~Event~
        +suspend getLessonsByDate(Date) List~WTLesson~
        +suspend getSeminarsByDate(Date) List~WTSeminar~
    }

    class HistoryViewModel {
        +LiveData~List~HistoryItem~~ historyItems
        +LiveData~List~HistoryItem~~ filteredItems
        +LiveData~Boolean~ isLoading
        +LiveData~String~ errorMessage
        +suspend refreshHistory()
        +suspend filterByType(String)
        +suspend filterByDateRange(Date, Date)
        +suspend filterByAmountRange(Double, Double)
        +suspend clearFilters()
        +suspend getHistorySummary() Map~String, Double~
    }

    class WorkoutViewModel {
        +LiveData~List~Program~~ programs
        +LiveData~List~Workout~~ workouts
        +LiveData~List~Exercise~~ exercises
        +LiveData~Program?~ currentProgram
        +LiveData~Workout?~ activeWorkout
        +LiveData~Boolean~ isLoading
        +LiveData~String~ errorMessage
        +suspend refreshPrograms()
        +suspend refreshWorkouts()
        +suspend addProgram(Program)
        +suspend updateProgram(Program)
        +suspend deleteProgram(Program)
        +suspend startWorkout(Program)
        +suspend endWorkout(Workout)
        +suspend saveWorkout(Workout)
        +suspend getWorkoutStats() Map~String, Any~
    }

    class LogErrorViewModel {
        +LiveData~List~String~~ logEntries
        +LiveData~Boolean~ isLoading
        +LiveData~String~ errorMessage
        +suspend refreshLogs()
        +suspend captureLogcat()
        +suspend saveLogsToFile()
        +suspend clearLogs()
        +suspend getLogFile() File?
    }

    %% UI Fragments
    class HomeFragment {
        +onCreateView()
        +onViewCreated()
        +setupRecyclerView()
        +observeTransactions()
        +observeBalance()
        +showAddTransactionDialog()
    }

    class TransactionsOverviewFragment {
        +onCreateView()
        +onViewCreated()
        +setupRecyclerView()
        +observeTransactions()
        +setupFiltering()
        +showTransactionDetails(Transaction)
        +showAddTransactionDialog()
    }

    class TransactionReportFragment {
        +onCreateView()
        +onViewCreated()
        +setupCharts()
        +generateReport()
        +exportReport()
        +filterByDateRange()
        +filterByCategory()
    }

    class InvestmentsFragment {
        +onCreateView()
        +onViewCreated()
        +setupRecyclerView()
        +setupTabLayout()
        +observeInvestments()
        +showAddInvestmentDialog()
        +showInvestmentDetails(Investment)
        +setupImageHandling()
    }

    class InvestmentsTabFragment {
        +onCreateView()
        +onViewCreated()
        +setupRecyclerView()
        +observeInvestments()
        +setupFiltering()
        +showInvestmentDetails(Investment)
    }

    class FuturesFragment {
        +onCreateView()
        +onViewCreated()
        +setupViewPager()
        +setupTabLayout()
        +observeFuturesData()
    }

    class UsdMFuturesFragment {
        +onCreateView()
        +onViewCreated()
        +setupRecyclerView()
        +setupWebSocket()
        +observePositions()
        +showPositionDetails(BinancePosition)
        +setupTradingInterface()
    }

    class CoinMFuturesFragment {
        +onCreateView()
        +onViewCreated()
        +setupRecyclerView()
        +setupWebSocket()
        +observePositions()
        +showPositionDetails(BinancePosition)
        +setupTradingInterface()
    }

    class ExternalFuturesFragment {
        +onCreateView()
        +onViewCreated()
        +setupRecyclerView()
        +setupApiClient()
        +observeExternalData()
        +refreshData()
        +setupErrorHandling()
    }

    class NotesFragment {
        +onCreateView()
        +onViewCreated()
        +setupRecyclerView()
        +setupSearchView()
        +observeNotes()
        +showAddNoteDialog()
        +showNoteDetails(Note)
        +setupImageHandling()
        +setupVoiceNotes()
    }

    class TasksFragment {
        +onCreateView()
        +onViewCreated()
        +setupRecyclerView()
        +setupGroupedAdapter()
        +observeTasks()
        +observeTaskGroups()
        +showAddTaskDialog()
        +showAddGroupDialog()
        +setupDragAndDrop()
    }

    class HistoryFragment {
        +onCreateView()
        +onViewCreated()
        +setupRecyclerView()
        +setupFiltering()
        +observeHistory()
        +showHistoryDetails(HistoryItem)
        +exportHistory()
        +setupSearch()
    }

    class CalendarFragment {
        +onCreateView()
        +onViewCreated()
        +setupCalendarView()
        +setupEventList()
        +observeEvents()
        +observeLessons()
        +observeSeminars()
        +showAddEventDialog()
        +showEventDetails(Event)
        +setupDateSelection()
    }

    class DatabaseManagementFragment {
        +onCreateView()
        +onViewCreated()
        +setupDataOverview()
        +observeDataCounts()
        +showBackupDialog()
        +showRestoreDialog()
        +showClearDataDialog()
        +exportData()
        +importData()
    }

    class LogErrorsFragment {
        +onCreateView()
        +onViewCreated()
        +setupRecyclerView()
        +observeLogs()
        +captureLogcat()
        +saveLogs()
        +clearLogs()
        +shareLogs()
    }

    %% WT (Wing Tzun) UI Components
    class WTRegistryFragment {
        +onCreateView()
        +onViewCreated()
        +setupBottomNavigation()
        +setupNetworkStatus()
        +switchFragment(Fragment)
        +updateTitle(Int)
        +setupNavigation()
    }

    class WTStudentsFragment {
        +onCreateView()
        +onViewCreated()
        +setupRecyclerView()
        +setupFab()
        +observeStudents()
        +showAddStudentDialog()
        +showEditStudentDialog(WTStudent)
        +saveStudent(DialogAddStudentBinding)
        +setupImageHandling()
        +setupCameraPermission()
    }

    class WTRegisterFragment {
        +onCreateView()
        +onViewCreated()
        +setupRecyclerView()
        +setupFab()
        +observeStudents()
        +observeActiveStudents()
        +showAddDialog()
        +showEditDialog(WTStudent)
        +setupStudentDropdown()
        +validateForm()
        +saveRegistration()
    }

    class WTRegisterContentFragment {
        +onCreateView()
        +onViewCreated()
        +setupRecyclerView()
        +setupFab()
        +observeRegistrations()
        +showAddRegistrationDialog()
        +showEditRegistrationDialog(WTRegistration)
        +setupStudentSelection()
        +setupFileAttachment()
        +saveRegistration()
        +deleteRegistration()
    }

    class WTLessonsFragment {
        +onCreateView()
        +onViewCreated()
        +setupRecyclerView()
        +setupFab()
        +observeLessons()
        +showAddLessonDialog()
        +showEditLessonDialog(WTLesson)
        +setupDatePicker()
        +saveLesson()
        +deleteLesson()
    }

    class WTSeminarsFragment {
        +onCreateView()
        +onViewCreated()
        +setupRecyclerView()
        +setupFab()
        +observeSeminars()
        +showAddSeminarDialog()
        +showEditSeminarDialog(WTSeminar)
        +setupParticipantManagement()
        +saveSeminar()
        +deleteSeminar()
    }

    %% Workout UI Components
    class WorkoutFragment {
        +onCreateView()
        +onViewCreated()
        +setupViewPager()
        +setupTabLayout()
        +observePrograms()
        +observeWorkouts()
        +setupNavigation()
    }

    class WorkoutDashboardFragment {
        +onCreateView()
        +onViewCreated()
        +setupStatsCards()
        +setupRecentWorkouts()
        +observeWorkoutStats()
        +showWorkoutDetails(Workout)
        +setupQuickActions()
    }

    class WorkoutProgramFragment {
        +onCreateView()
        +onViewCreated()
        +setupRecyclerView()
        +setupFab()
        +observePrograms()
        +showAddProgramDialog()
        +showEditProgramDialog(Program)
        +setupExerciseSelection()
        +saveProgram()
        +deleteProgram()
        +startWorkout(Program)
    }

    class WorkoutExerciseFragment {
        +onCreateView()
        +onViewCreated()
        +setupRecyclerView()
        +setupFab()
        +observeExercises()
        +showAddExerciseDialog()
        +showEditExerciseDialog(Exercise)
        +setupMuscleGroupFilter()
        +saveExercise()
        +deleteExercise()
    }

    class ActiveWorkoutFragment {
        +onCreateView()
        +onViewCreated()
        +setupWorkoutInterface()
        +observeActiveWorkout()
        +setupExerciseNavigation()
        +updateSetCompletion()
        +saveWorkout()
        +endWorkout()
        +pauseWorkout()
        +resumeWorkout()
    }

    class WorkoutStatsFragment {
        +onCreateView()
        +onViewCreated()
        +setupCharts()
        +observeWorkoutStats()
        +generateStatsReport()
        +exportStats()
    }

    %% Instagram UI Components
    class InstagramBusinessFragment {
        +onCreateView()
        +onViewCreated()
        +setupViewPager()
        +setupTabLayout()
        +setupNavigation()
    }

    class InstagramPostsFragment {
        +onCreateView()
        +onViewCreated()
        +setupRecyclerView()
        +observePosts()
        +showAddPostDialog()
        +setupPostScheduling()
        +setupAnalytics()
    }

    class InstagramInsightsFragment {
        +onCreateView()
        +onViewCreated()
        +setupCharts()
        +observeInsights()
        +setupDateRangeFilter()
        +generateInsightsReport()
        +exportInsights()
    }

    class InstagramAskAIFragment {
        +onCreateView()
        +onViewCreated()
        +setupChatInterface()
        +setupAIConnection()
        +sendMessage(String)
        +receiveResponse(String)
        +setupVoiceInput()
        +setupImageUpload()
        +saveConversation()
    }

    %% Drawing UI Components
    class DrawingActivity {
        +onCreate()
        +onCreateView()
        +setupCanvas()
        +setupTools()
        +setupColorPicker()
        +setupBrushSize()
        +saveDrawing()
        +loadDrawing()
        +shareDrawing()
        +undoAction()
        +redoAction()
    }

    %% Base Components
    class BaseFragment {
        +onCreateView()
        +onViewCreated()
        +setupCommonUI()
        +handleError(String)
        +showLoading()
        +hideLoading()
        +showNetworkStatus()
    }

    class EditNoteActivity {
        +onCreate()
        +onCreateView()
        +setupRichTextEditor()
        +setupImageHandling()
        +setupVoiceNotes()
        +setupVideoHandling()
        +saveNote()
        +loadNote()
        +shareNote()
        +setupAutoSave()
    }

    %% Adapters
    class TransactionAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~Transaction~)
        +setupClickListeners()
    }

    class InvestmentAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~Investment~)
        +setupImageLoading()
        +setupClickListeners()
    }

    class NotesAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~Note~)
        +setupImageLoading()
        +setupVoiceNotePlayback()
        +setupClickListeners()
    }

    class TasksAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~Task~)
        +setupCheckboxHandling()
        +setupDragAndDrop()
        +setupClickListeners()
    }

    class GroupedTasksAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~TaskGroup~)
        +setupGroupExpansion()
        +setupTaskReordering()
        +setupClickListeners()
    }

    class WTStudentAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~WTStudent~)
        +setupStatusIndicator()
        +setupProfileImage()
        +setupClickListeners()
    }

    class WTRegistrationAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~WTStudent~)
        +setupPaymentStatus()
        +setupProfileImage()
        +setupClickListeners()
    }

    class HistoryAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~HistoryItem~)
        +setupItemTypeDisplay()
        +setupImageLoading()
        +setupClickListeners()
    }

    class EventAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~Event~)
        +setupDateDisplay()
        +setupTypeIndicator()
        +setupClickListeners()
    }

    class BinanceFuturesAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~BinancePosition~)
        +setupPnLDisplay()
        +setupPositionSize()
        +setupClickListeners()
    }

    class BinancePositionAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~BinancePosition~)
        +setupLeverageDisplay()
        +setupMarginInfo()
        +setupClickListeners()
    }

    class InvestmentImageAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~String~)
        +setupImageLoading()
        +setupFullscreenView()
        +setupClickListeners()
    }

    class NoteImageAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~String~)
        +setupImageLoading()
        +setupFullscreenView()
        +setupClickListeners()
    }

    class NoteVideoAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~String~)
        +setupVideoPlayer()
        +setupThumbnailLoading()
        +setupClickListeners()
    }

    class VoiceNoteAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~VoiceNote~)
        +setupAudioPlayer()
        +setupDurationDisplay()
        +setupClickListeners()
    }

    class SeminarAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~WTSeminar~)
        +setupParticipantCount()
        +setupPriceDisplay()
        +setupClickListeners()
    }

    class CategorySummaryAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~CategorySummary~)
        +setupPercentageDisplay()
        +setupColorCoding()
        +setupClickListeners()
    }

    class CategorySpendingAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~CategorySummary~)
        +setupSpendingDisplay()
        +setupTrendIndicator()
        +setupClickListeners()
    }

    class TransactionReportAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~Transaction~)
        +setupCategoryDisplay()
        +setupAmountFormatting()
        +setupClickListeners()
    }

    class InvestmentSelectionAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~Investment~)
        +setupSelectionState()
        +setupInvestmentInfo()
        +setupClickListeners()
    }

    class InvestmentDropdownAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~Investment~)
        +setupDropdownDisplay()
        +setupFiltering()
        +setupClickListeners()
    }

    class CategoryDropdownAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~String~)
        +setupCategoryDisplay()
        +setupColorCoding()
        +setupClickListeners()
    }

    class InvestmentPagerAdapter {
        +getCount()
        +getItem(Int)
        +instantiateItem(ViewGroup, Int)
        +destroyItem(ViewGroup, Int, Object)
        +isViewFromObject(View, Object)
    }

    class FullscreenImageAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~String~)
        +setupZooming()
        +setupSwipeGestures()
        +setupClickListeners()
    }

    class LogEntryAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~String~)
        +setupLogLevelDisplay()
        +setupTimestampFormatting()
        +setupClickListeners()
    }

    class WTEventAdapter {
        +onCreateViewHolder()
        +onBindViewHolder()
        +getItemCount()
        +updateData(List~Event~)
        +setupEventTypeDisplay()
        +setupDateFormatting()
        +setupClickListeners()
    }

    %% API Components
    class ExternalBinanceRepository {
        +ExternalBinanceApiClient apiClient
        +BinanceWebSocketClient webSocketClient
        +suspend getAccountInfo() BinanceAccountInfo
        +suspend getPositions() List~BinancePosition~
        +suspend getBalances() List~BinanceBalance~
        +suspend getFutures() List~BinanceFuture~
        +suspend placeOrder(BinanceOrder) BinanceOrderResponse
        +suspend cancelOrder(String) Boolean
        +suspend getOrderHistory() List~BinanceOrder~
        +suspend connectWebSocket()
        +suspend disconnectWebSocket()
        +suspend subscribeToPositions()
        +suspend subscribeToBalances()
    }

    class ExternalBinanceApiClient {
        +Retrofit retrofit
        +BinanceExternalService service
        +suspend getAccountInfo() Response~BinanceAccountInfo~
        +suspend getPositions() Response~List~BinancePosition~~
        +suspend getBalances() Response~List~BinanceBalance~~
        +suspend getFutures() Response~List~BinanceFuture~~
        +suspend placeOrder(BinanceOrder) Response~BinanceOrderResponse~
        +suspend cancelOrder(String) Response~Boolean~
        +suspend getOrderHistory() Response~List~BinanceOrder~~
    }

    class BinanceWebSocketClient {
        +WebSocket webSocket
        +suspend connect(String)
        +suspend disconnect()
        +suspend subscribe(String)
        +suspend unsubscribe(String)
        +suspend sendMessage(String)
        +setupMessageHandler()
        +setupErrorHandler()
        +setupConnectionHandler()
    }

    class BinanceExternalService {
        +@GET getAccountInfo()
        +@GET getPositions()
        +@GET getBalances()
        +@GET getFutures()
        +@POST placeOrder()
        +@DELETE cancelOrder()
        +@GET getOrderHistory()
    }

    %% Workers
    class BackupWorker {
        +doWork() Result
        +performBackup() Boolean
        +uploadToCloud() Boolean
        +notifyCompletion(Boolean)
        +handleError(Exception)
    }

    class ExpirationNotificationWorker {
        +doWork() Result
        +checkExpiringItems() List~ExpiringItem~
        +sendNotification(ExpiringItem)
        +scheduleNextCheck()
        +handleError(Exception)
    }

    class LogcatCaptureWorker {
        +doWork() Result
        +captureLogcat() String
        +saveToFile(String) Boolean
        +cleanupOldLogs()
        +handleError(Exception)
    }

    %% Relationships
    AllinOneApplication --> FirebaseRepository
    AllinOneApplication --> CacheManager
    AllinOneApplication --> NetworkUtils
    AllinOneApplication --> FirebaseIdManager
    AllinOneApplication --> FirebaseStorageUtil
    AllinOneApplication --> DataChangeNotifier

    MainActivity --> HomeFragment
    MainActivity --> InvestmentsFragment
    MainActivity --> NotesFragment
    MainActivity --> TasksFragment
    MainActivity --> HistoryFragment
    MainActivity --> CalendarFragment
    MainActivity --> WTRegistryFragment
    MainActivity --> WorkoutFragment
    MainActivity --> InstagramBusinessFragment
    MainActivity --> DrawingActivity
    MainActivity --> EditNoteActivity
    MainActivity --> DatabaseManagementFragment
    MainActivity --> LogErrorsFragment
    MainActivity --> BackupWorker
    MainActivity --> ExpirationNotificationWorker
    MainActivity --> LogcatCaptureWorker

    %% Fragment Relationships
    HomeFragment --> TransactionsOverviewFragment
    HomeFragment --> HomeViewModel
    HomeViewModel --> FirebaseRepository

    InvestmentsFragment --> InvestmentsTabFragment
    InvestmentsFragment --> FuturesFragment
    InvestmentsFragment --> ExternalFuturesFragment
    InvestmentsTabFragment --> InvestmentsViewModel
    FuturesFragment --> UsdMFuturesFragment
    FuturesFragment --> CoinMFuturesFragment
    UsdMFuturesFragment --> InvestmentsViewModel
    UsdMFuturesFragment --> ExternalBinanceRepository
    UsdMFuturesFragment --> BinanceWebSocketClient
    CoinMFuturesFragment --> InvestmentsViewModel
    CoinMFuturesFragment --> ExternalBinanceRepository
    CoinMFuturesFragment --> BinanceWebSocketClient
    ExternalFuturesFragment --> ExternalBinanceRepository
    InvestmentsViewModel --> FirebaseRepository

    NotesFragment --> NotesViewModel
    NotesViewModel --> FirebaseRepository
    EditNoteActivity --> FirebaseRepository

    TasksFragment --> TasksViewModel
    TasksViewModel --> FirebaseRepository

    HistoryFragment --> HistoryViewModel
    HistoryViewModel --> FirebaseRepository

    CalendarFragment --> CalendarViewModel
    CalendarViewModel --> FirebaseRepository

    %% WT Relationships
    WTRegistryFragment --> WTStudentsFragment
    WTRegistryFragment --> WTRegisterFragment
    WTRegistryFragment --> WTRegisterContentFragment
    WTRegistryFragment --> WTLessonsFragment
    WTRegistryFragment --> WTSeminarsFragment
    WTStudentsFragment --> WTRegisterViewModel
    WTRegisterFragment --> WTRegisterViewModel
    WTRegisterContentFragment --> WTRegisterViewModel
    WTLessonsFragment --> WTLessonsViewModel
    WTSeminarsFragment --> WTSeminarsViewModel
    WTRegisterViewModel --> FirebaseRepository
    WTRegisterViewModel --> FirebaseIdManager
    WTRegisterViewModel --> FirebaseStorageUtil
    WTLessonsViewModel --> FirebaseRepository
    WTSeminarsViewModel --> FirebaseRepository

    %% Workout Relationships
    WorkoutFragment --> WorkoutDashboardFragment
    WorkoutFragment --> WorkoutProgramFragment
    WorkoutFragment --> WorkoutExerciseFragment
    WorkoutFragment --> WorkoutStatsFragment
    WorkoutFragment --> WorkoutViewModel
    WorkoutViewModel --> FirebaseRepository
    WorkoutDashboardFragment --> WorkoutViewModel
    WorkoutProgramFragment --> WorkoutViewModel
    WorkoutExerciseFragment --> WorkoutViewModel
    ActiveWorkoutFragment --> WorkoutViewModel
    ActiveWorkoutFragment --> WorkoutFragment
    ActiveWorkoutFragment --> FirebaseRepository

    %% Instagram Relationships
    InstagramBusinessFragment --> InstagramPostsFragment
    InstagramBusinessFragment --> InstagramInsightsFragment
    InstagramBusinessFragment --> InstagramAskAIFragment
    InstagramBusinessFragment --> BaseFragment
    InstagramPostsFragment --> InstagramViewModel
    InstagramInsightsFragment --> InstagramViewModel
    InstagramAskAIFragment --> InstagramViewModel

    %% Firebase Layer Relationships
    FirebaseRepository --> FirebaseManager
    FirebaseRepository --> OfflineQueue
    FirebaseRepository --> CacheManager
    FirebaseRepository --> NetworkUtils
    FirebaseRepository --> FirebaseIdManager
    FirebaseRepository --> DataChangeNotifier
    FirebaseRepository --> FirebaseStorageUtil

    FirebaseManager --> FirebaseIdManager
    FirebaseManager --> FirebaseStorageUtil

    %% API Relationships
    ExternalBinanceRepository --> ExternalBinanceApiClient
    ExternalBinanceRepository --> BinanceWebSocketClient
    ExternalBinanceRepository --> BackupWorker
    ExternalBinanceRepository --> FirebaseRepository
    ExternalBinanceApiClient --> BinanceExternalService
    ExternalBinanceApiClient --> BinanceWebSocketClient

    %% Utility Relationships
    OfflineQueue --> CacheManager
    CacheManager --> NetworkUtils
    LogcatHelper --> BackupHelper
    LogErrorsFragment --> LogErrorViewModel
    LogErrorViewModel --> LogcatHelper
    MainActivity --> BackupWorker
    MainActivity --> ExpirationNotificationWorker
    MainActivity --> LogcatCaptureWorker
    BackupWorker --> FirebaseRepository
    ExpirationNotificationWorker --> FirebaseRepository
    ExpirationNotificationWorker --> NotificationManager
    LogcatCaptureWorker --> LogcatHelper

    %% Data Flow Relationships
    FirebaseRepository ..> Transaction
    FirebaseRepository ..> Investment
    FirebaseRepository ..> Note
    FirebaseRepository ..> Task
    FirebaseRepository ..> TaskGroup
    FirebaseRepository ..> WTStudent
    FirebaseRepository ..> WTRegistration
    FirebaseRepository ..> Event
    FirebaseRepository ..> WTLesson
    FirebaseRepository ..> Program
    FirebaseRepository ..> Workout

    %% Adapter Relationships
    HomeFragment --> TransactionAdapter
    TransactionsOverviewFragment --> TransactionAdapter
    TransactionReportFragment --> TransactionReportAdapter
    InvestmentsFragment --> InvestmentAdapter
    InvestmentsTabFragment --> InvestmentAdapter
    UsdMFuturesFragment --> BinanceFuturesAdapter
    CoinMFuturesFragment --> BinancePositionAdapter
    NotesFragment --> NotesAdapter
    TasksFragment --> GroupedTasksAdapter
    HistoryFragment --> HistoryAdapter
    CalendarFragment --> EventAdapter
    WTStudentsFragment --> WTStudentAdapter
    WTRegisterFragment --> WTRegistrationAdapter
    WTLessonsFragment --> WTEventAdapter
    WTSeminarsFragment --> SeminarAdapter
    WorkoutProgramFragment --> InvestmentSelectionAdapter

    %% Data Model Relationships
    WTRegistration --> WTStudent
    Workout --> Program
    Program --> Exercise
    Note --> VoiceNote
    Task --> TaskGroup
    HistoryItem --> Transaction
    HistoryItem --> Investment
    HistoryItem --> Note
    HistoryItem --> WTStudent
