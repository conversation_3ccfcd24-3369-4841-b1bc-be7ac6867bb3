// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogTaskGroupBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton btnCancel;

  @NonNull
  public final MaterialButton btnDelete;

  @NonNull
  public final MaterialButton btnSave;

  @NonNull
  public final View colorBlue;

  @NonNull
  public final View colorGreen;

  @NonNull
  public final View colorOrange;

  @NonNull
  public final View colorPurple;

  @NonNull
  public final View colorRed;

  @NonNull
  public final TextView dialogTitle;

  @NonNull
  public final TextInputEditText groupDescriptionEdit;

  @NonNull
  public final TextInputLayout groupDescriptionLayout;

  @NonNull
  public final TextInputEditText groupTitleEdit;

  @NonNull
  public final TextInputLayout groupTitleLayout;

  private DialogTaskGroupBinding(@NonNull LinearLayout rootView, @NonNull MaterialButton btnCancel,
      @NonNull MaterialButton btnDelete, @NonNull MaterialButton btnSave, @NonNull View colorBlue,
      @NonNull View colorGreen, @NonNull View colorOrange, @NonNull View colorPurple,
      @NonNull View colorRed, @NonNull TextView dialogTitle,
      @NonNull TextInputEditText groupDescriptionEdit,
      @NonNull TextInputLayout groupDescriptionLayout, @NonNull TextInputEditText groupTitleEdit,
      @NonNull TextInputLayout groupTitleLayout) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnDelete = btnDelete;
    this.btnSave = btnSave;
    this.colorBlue = colorBlue;
    this.colorGreen = colorGreen;
    this.colorOrange = colorOrange;
    this.colorPurple = colorPurple;
    this.colorRed = colorRed;
    this.dialogTitle = dialogTitle;
    this.groupDescriptionEdit = groupDescriptionEdit;
    this.groupDescriptionLayout = groupDescriptionLayout;
    this.groupTitleEdit = groupTitleEdit;
    this.groupTitleLayout = groupTitleLayout;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogTaskGroupBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogTaskGroupBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_task_group, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogTaskGroupBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_cancel;
      MaterialButton btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btn_delete;
      MaterialButton btnDelete = ViewBindings.findChildViewById(rootView, id);
      if (btnDelete == null) {
        break missingId;
      }

      id = R.id.btn_save;
      MaterialButton btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.color_blue;
      View colorBlue = ViewBindings.findChildViewById(rootView, id);
      if (colorBlue == null) {
        break missingId;
      }

      id = R.id.color_green;
      View colorGreen = ViewBindings.findChildViewById(rootView, id);
      if (colorGreen == null) {
        break missingId;
      }

      id = R.id.color_orange;
      View colorOrange = ViewBindings.findChildViewById(rootView, id);
      if (colorOrange == null) {
        break missingId;
      }

      id = R.id.color_purple;
      View colorPurple = ViewBindings.findChildViewById(rootView, id);
      if (colorPurple == null) {
        break missingId;
      }

      id = R.id.color_red;
      View colorRed = ViewBindings.findChildViewById(rootView, id);
      if (colorRed == null) {
        break missingId;
      }

      id = R.id.dialog_title;
      TextView dialogTitle = ViewBindings.findChildViewById(rootView, id);
      if (dialogTitle == null) {
        break missingId;
      }

      id = R.id.group_description_edit;
      TextInputEditText groupDescriptionEdit = ViewBindings.findChildViewById(rootView, id);
      if (groupDescriptionEdit == null) {
        break missingId;
      }

      id = R.id.group_description_layout;
      TextInputLayout groupDescriptionLayout = ViewBindings.findChildViewById(rootView, id);
      if (groupDescriptionLayout == null) {
        break missingId;
      }

      id = R.id.group_title_edit;
      TextInputEditText groupTitleEdit = ViewBindings.findChildViewById(rootView, id);
      if (groupTitleEdit == null) {
        break missingId;
      }

      id = R.id.group_title_layout;
      TextInputLayout groupTitleLayout = ViewBindings.findChildViewById(rootView, id);
      if (groupTitleLayout == null) {
        break missingId;
      }

      return new DialogTaskGroupBinding((LinearLayout) rootView, btnCancel, btnDelete, btnSave,
          colorBlue, colorGreen, colorOrange, colorPurple, colorRed, dialogTitle,
          groupDescriptionEdit, groupDescriptionLayout, groupTitleEdit, groupTitleLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
