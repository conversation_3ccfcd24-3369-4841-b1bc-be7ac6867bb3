<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_edit_note" modulePackage="com.example.allinone" filePath="app\src\main\res\layout-night\activity_edit_note.xml" directory="layout-night" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout-night/activity_edit_note_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="218" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="11" startOffset="8" endLine="18" endOffset="65"/></Target><Target id="@+id/editNoteTitle" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="44" startOffset="16" endLine="51" endOffset="63"/></Target><Target id="@+id/boldButton" view="ImageButton"><Expressions/><location startLine="65" startOffset="20" endLine="72" endOffset="49"/></Target><Target id="@+id/italicButton" view="ImageButton"><Expressions/><location startLine="74" startOffset="20" endLine="81" endOffset="49"/></Target><Target id="@+id/underlineButton" view="ImageButton"><Expressions/><location startLine="83" startOffset="20" endLine="90" endOffset="49"/></Target><Target id="@+id/bulletListButton" view="ImageButton"><Expressions/><location startLine="98" startOffset="20" endLine="105" endOffset="49"/></Target><Target id="@+id/checkboxListButton" view="ImageButton"><Expressions/><location startLine="107" startOffset="20" endLine="114" endOffset="49"/></Target><Target id="@+id/addImageButton" view="ImageButton"><Expressions/><location startLine="122" startOffset="20" endLine="129" endOffset="49"/></Target><Target id="@+id/editNoteContent" view="jp.wasabeef.richeditor.RichEditor"><Expressions/><location startLine="134" startOffset="12" endLine="140" endOffset="39"/></Target><Target id="@+id/imagesRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="164" startOffset="20" endLine="170" endOffset="94"/></Target><Target id="@+id/addAttachmentButton" view="Button"><Expressions/><location startLine="180" startOffset="12" endLine="191" endOffset="45"/></Target><Target id="@+id/shareNoteButton" view="Button"><Expressions/><location startLine="193" startOffset="12" endLine="204" endOffset="45"/></Target><Target id="@+id/saveFab" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="208" startOffset="4" endLine="217" endOffset="57"/></Target></Targets></Layout>