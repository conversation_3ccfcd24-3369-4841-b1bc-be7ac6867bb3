<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/program_selection_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Select Program"
                    android:textAppearance="?attr/textAppearanceHeadline6" />

                <Spinner
                    android:id="@+id/program_spinner"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:minHeight="48dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/create_workout_button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="Start Workout" />

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/workout_history_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/program_selection_card">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/workout_history"
                    android:textAppearance="?attr/textAppearanceHeadline6" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:orientation="vertical">

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:hint="Muscle Group">

                        <AutoCompleteTextView
                            android:id="@+id/filter_muscle_group"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none"
                            android:minHeight="48dp"
                            android:maxHeight="48dp" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:hint="Sort By">

                        <AutoCompleteTextView
                            android:id="@+id/sort_option"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none"
                            android:minHeight="48dp"
                            android:maxHeight="48dp" />
                    </com.google.android.material.textfield.TextInputLayout>
                </LinearLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/apply_filters_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_gravity="center"
                    android:text="Apply" />

                <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                    android:id="@+id/swipe_refresh_layout"
                    android:layout_width="match_parent"
                    android:layout_height="300dp"
                    android:layout_marginTop="8dp">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/workout_log_recycler_view"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        tools:listitem="@layout/item_workout_log" />

                </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

                <TextView
                    android:id="@+id/empty_log_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="No workout history yet. Complete a workout to see it here."
                    android:textAppearance="?attr/textAppearanceBody1"
                    android:visibility="gone"
                    android:gravity="center"
                    android:padding="16dp" />

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>
