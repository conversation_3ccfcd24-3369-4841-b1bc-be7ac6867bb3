<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_investments_tab" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\fragment_investments_tab.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.swiperefreshlayout.widget.SwipeRefreshLayout" rootNodeViewId="@+id/swipeRefreshLayout"><Targets><Target id="@+id/swipeRefreshLayout" tag="layout/fragment_investments_tab_0" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="1" startOffset="0" endLine="78" endOffset="55"/></Target><Target id="@+id/summaryCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="15" startOffset="8" endLine="53" endOffset="59"/></Target><Target id="@+id/totalInvestmentsText" view="TextView"><Expressions/><location startLine="37" startOffset="16" endLine="43" endOffset="61"/></Target><Target id="@+id/investmentCountText" view="TextView"><Expressions/><location startLine="45" startOffset="16" endLine="51" endOffset="61"/></Target><Target id="@+id/investmentsRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="55" startOffset="8" endLine="63" endOffset="55"/></Target><Target id="@+id/emptyStateText" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="75" endOffset="55"/></Target></Targets></Layout>