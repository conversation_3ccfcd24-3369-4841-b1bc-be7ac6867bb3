// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemEventBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final TextView eventDate;

  @NonNull
  public final TextView eventTime;

  @NonNull
  public final TextView eventTitle;

  @NonNull
  public final TextView eventTypeTag;

  private ItemEventBinding(@NonNull CardView rootView, @NonNull TextView eventDate,
      @NonNull TextView eventTime, @NonNull TextView eventTitle, @NonNull TextView eventTypeTag) {
    this.rootView = rootView;
    this.eventDate = eventDate;
    this.eventTime = eventTime;
    this.eventTitle = eventTitle;
    this.eventTypeTag = eventTypeTag;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemEventBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemEventBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_event, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemEventBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.eventDate;
      TextView eventDate = ViewBindings.findChildViewById(rootView, id);
      if (eventDate == null) {
        break missingId;
      }

      id = R.id.eventTime;
      TextView eventTime = ViewBindings.findChildViewById(rootView, id);
      if (eventTime == null) {
        break missingId;
      }

      id = R.id.eventTitle;
      TextView eventTitle = ViewBindings.findChildViewById(rootView, id);
      if (eventTitle == null) {
        break missingId;
      }

      id = R.id.eventTypeTag;
      TextView eventTypeTag = ViewBindings.findChildViewById(rootView, id);
      if (eventTypeTag == null) {
        break missingId;
      }

      return new ItemEventBinding((CardView) rootView, eventDate, eventTime, eventTitle,
          eventTypeTag);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
