<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="offline_status_view" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\offline_status_view.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView" rootNodeViewId="@+id/offline_status_card"><Targets><Target id="@+id/offline_status_card" tag="layout/offline_status_view_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="63" endOffset="35"/></Target><Target id="@+id/offline_icon" view="ImageView"><Expressions/><location startLine="18" startOffset="8" endLine="25" endOffset="32"/></Target><Target id="@+id/offline_status_title" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="41" endOffset="42"/></Target><Target id="@+id/offline_status_message" view="TextView"><Expressions/><location startLine="43" startOffset="12" endLine="50" endOffset="41"/></Target><Target id="@+id/pending_operations_count" view="TextView"><Expressions/><location startLine="52" startOffset="12" endLine="60" endOffset="44"/></Target></Targets></Layout>