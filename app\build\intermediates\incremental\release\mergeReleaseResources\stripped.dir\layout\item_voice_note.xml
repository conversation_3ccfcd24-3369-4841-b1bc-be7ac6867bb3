<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="12dp">

        <!-- Recording details -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/voiceNoteTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textStyle="bold"
                android:text="Voice Recording"
                android:maxLines="1"
                android:ellipsize="end" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="2dp">

                <TextView
                    android:id="@+id/voiceNoteCurrentTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="12sp"
                    android:text="00:00"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/timeSlash"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="12sp"
                    android:text=" / "
                    android:visibility="gone"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/voiceNoteDuration"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="12sp"
                    android:text="00:45" />
            </LinearLayout>
        </LinearLayout>

        <!-- Playback controls -->
        <ImageButton
            android:id="@+id/playPauseButton"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_media_play"
            android:tint="?attr/colorOnSurface"
            app:tint="?attr/colorOnSurface"
            android:contentDescription="Play/Pause" />

        <ImageButton
            android:id="@+id/deleteVoiceNoteButton"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_marginStart="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_menu_delete"
            android:tint="#FF0000"
            app:tint="#FF0000"
            android:contentDescription="Delete Voice Note" />
    </LinearLayout>
</com.google.android.material.card.MaterialCardView> 