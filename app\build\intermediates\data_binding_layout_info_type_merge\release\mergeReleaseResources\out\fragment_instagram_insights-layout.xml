<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_instagram_insights" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\fragment_instagram_insights.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_instagram_insights_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="483" endOffset="12"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="14" startOffset="8" endLine="19" endOffset="39"/></Target><Target id="@+id/textInsightsTitle" view="TextView"><Expressions/><location startLine="22" startOffset="8" endLine="30" endOffset="48"/></Target><Target id="@+id/textTotalPosts" view="TextView"><Expressions/><location startLine="65" startOffset="20" endLine="72" endOffset="68"/></Target><Target id="@+id/textTotalEngagement" view="TextView"><Expressions/><location startLine="99" startOffset="20" endLine="106" endOffset="63"/></Target><Target id="@+id/textAvgEngagementRate" view="TextView"><Expressions/><location startLine="133" startOffset="20" endLine="140" endOffset="64"/></Target><Target id="@+id/textTotalReach" view="TextView"><Expressions/><location startLine="167" startOffset="20" endLine="174" endOffset="65"/></Target><Target id="@+id/textEngagementTrend" view="TextView"><Expressions/><location startLine="223" startOffset="20" endLine="229" endOffset="46"/></Target><Target id="@+id/textReachTrend" view="TextView"><Expressions/><location startLine="246" startOffset="20" endLine="252" endOffset="45"/></Target><Target id="@+id/textConsistencyScore" view="TextView"><Expressions/><location startLine="269" startOffset="20" endLine="275" endOffset="44"/></Target><Target id="@+id/textGrowthPotential" view="TextView"><Expressions/><location startLine="291" startOffset="20" endLine="297" endOffset="44"/></Target><Target id="@+id/cardTopPost" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="312" startOffset="8" endLine="358" endOffset="59"/></Target><Target id="@+id/textTopPostCaption" view="TextView"><Expressions/><location startLine="326" startOffset="16" endLine="333" endOffset="45"/></Target><Target id="@+id/textTopPostEngagement" view="TextView"><Expressions/><location startLine="341" startOffset="20" endLine="347" endOffset="49"/></Target><Target id="@+id/textTopPostInteractions" view="TextView"><Expressions/><location startLine="349" startOffset="20" endLine="355" endOffset="49"/></Target><Target id="@+id/textVideoCount" view="TextView"><Expressions/><location startLine="405" startOffset="20" endLine="410" endOffset="49"/></Target><Target id="@+id/textVideoEngagement" view="TextView"><Expressions/><location startLine="412" startOffset="20" endLine="419" endOffset="45"/></Target><Target id="@+id/textImageCount" view="TextView"><Expressions/><location startLine="435" startOffset="20" endLine="440" endOffset="49"/></Target><Target id="@+id/textImageEngagement" view="TextView"><Expressions/><location startLine="442" startOffset="20" endLine="449" endOffset="45"/></Target><Target id="@+id/textPostingFrequency" view="TextView"><Expressions/><location startLine="462" startOffset="16" endLine="467" endOffset="45"/></Target><Target id="@+id/textInsightsMessage" view="TextView"><Expressions/><location startLine="472" startOffset="8" endLine="480" endOffset="39"/></Target></Targets></Layout>