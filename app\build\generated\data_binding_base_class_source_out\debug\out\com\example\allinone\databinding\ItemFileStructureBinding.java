// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemFileStructureBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView fileIcon;

  @NonNull
  public final TextView fileName;

  private ItemFileStructureBinding(@NonNull LinearLayout rootView, @NonNull ImageView fileIcon,
      @NonNull TextView fileName) {
    this.rootView = rootView;
    this.fileIcon = fileIcon;
    this.fileName = fileName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemFileStructureBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemFileStructureBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_file_structure, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemFileStructureBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.fileIcon;
      ImageView fileIcon = ViewBindings.findChildViewById(rootView, id);
      if (fileIcon == null) {
        break missingId;
      }

      id = R.id.fileName;
      TextView fileName = ViewBindings.findChildViewById(rootView, id);
      if (fileName == null) {
        break missingId;
      }

      return new ItemFileStructureBinding((LinearLayout) rootView, fileIcon, fileName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
