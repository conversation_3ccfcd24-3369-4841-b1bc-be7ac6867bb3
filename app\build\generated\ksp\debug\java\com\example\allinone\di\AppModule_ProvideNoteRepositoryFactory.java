package com.example.allinone.di;

import com.example.allinone.feature.notes.data.datasource.NoteLocalDataSource;
import com.example.allinone.feature.notes.data.datasource.NoteRemoteDataSource;
import com.example.allinone.feature.notes.domain.repository.NoteRepository;
import com.example.allinone.firebase.OfflineQueue;
import com.example.allinone.utils.NetworkUtils;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class AppModule_ProvideNoteRepositoryFactory implements Factory<NoteRepository> {
  private final Provider<NoteLocalDataSource> localDataSourceProvider;

  private final Provider<NoteRemoteDataSource> remoteDataSourceProvider;

  private final Provider<NetworkUtils> networkUtilsProvider;

  private final Provider<OfflineQueue> offlineQueueProvider;

  public AppModule_ProvideNoteRepositoryFactory(
      Provider<NoteLocalDataSource> localDataSourceProvider,
      Provider<NoteRemoteDataSource> remoteDataSourceProvider,
      Provider<NetworkUtils> networkUtilsProvider, Provider<OfflineQueue> offlineQueueProvider) {
    this.localDataSourceProvider = localDataSourceProvider;
    this.remoteDataSourceProvider = remoteDataSourceProvider;
    this.networkUtilsProvider = networkUtilsProvider;
    this.offlineQueueProvider = offlineQueueProvider;
  }

  @Override
  public NoteRepository get() {
    return provideNoteRepository(localDataSourceProvider.get(), remoteDataSourceProvider.get(), networkUtilsProvider.get(), offlineQueueProvider.get());
  }

  public static AppModule_ProvideNoteRepositoryFactory create(
      Provider<NoteLocalDataSource> localDataSourceProvider,
      Provider<NoteRemoteDataSource> remoteDataSourceProvider,
      Provider<NetworkUtils> networkUtilsProvider, Provider<OfflineQueue> offlineQueueProvider) {
    return new AppModule_ProvideNoteRepositoryFactory(localDataSourceProvider, remoteDataSourceProvider, networkUtilsProvider, offlineQueueProvider);
  }

  public static NoteRepository provideNoteRepository(NoteLocalDataSource localDataSource,
      NoteRemoteDataSource remoteDataSource, NetworkUtils networkUtils, OfflineQueue offlineQueue) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideNoteRepository(localDataSource, remoteDataSource, networkUtils, offlineQueue));
  }
}
