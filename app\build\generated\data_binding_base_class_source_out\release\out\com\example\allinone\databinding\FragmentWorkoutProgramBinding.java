// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentWorkoutProgramBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final FloatingActionButton addProgramFab;

  @NonNull
  public final TextView emptyProgramsText;

  @NonNull
  public final RecyclerView programsRecyclerView;

  @NonNull
  public final SwipeRefreshLayout swipeRefreshLayout;

  private FragmentWorkoutProgramBinding(@NonNull ConstraintLayout rootView,
      @NonNull FloatingActionButton addProgramFab, @NonNull TextView emptyProgramsText,
      @NonNull RecyclerView programsRecyclerView, @NonNull SwipeRefreshLayout swipeRefreshLayout) {
    this.rootView = rootView;
    this.addProgramFab = addProgramFab;
    this.emptyProgramsText = emptyProgramsText;
    this.programsRecyclerView = programsRecyclerView;
    this.swipeRefreshLayout = swipeRefreshLayout;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentWorkoutProgramBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentWorkoutProgramBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_workout_program, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentWorkoutProgramBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.add_program_fab;
      FloatingActionButton addProgramFab = ViewBindings.findChildViewById(rootView, id);
      if (addProgramFab == null) {
        break missingId;
      }

      id = R.id.empty_programs_text;
      TextView emptyProgramsText = ViewBindings.findChildViewById(rootView, id);
      if (emptyProgramsText == null) {
        break missingId;
      }

      id = R.id.programs_recycler_view;
      RecyclerView programsRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (programsRecyclerView == null) {
        break missingId;
      }

      id = R.id.swipe_refresh_layout;
      SwipeRefreshLayout swipeRefreshLayout = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefreshLayout == null) {
        break missingId;
      }

      return new FragmentWorkoutProgramBinding((ConstraintLayout) rootView, addProgramFab,
          emptyProgramsText, programsRecyclerView, swipeRefreshLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
