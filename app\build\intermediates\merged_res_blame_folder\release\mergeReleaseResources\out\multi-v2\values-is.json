{"logs": [{"outputFile": "com.example.allinone.app-mergeReleaseResources-108:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\75ac7f2a23ffbf48dd6409062de1d131\\transformed\\material-1.12.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,344,416,495,577,657,754,869,951,1009,1074,1162,1226,1287,1377,1441,1504,1566,1634,1698,1754,1877,1942,2004,2060,2131,2258,2342,2416,2513,2594,2678,2814,2891,2968,3084,3171,3250,3307,3362,3428,3504,3584,3655,3731,3798,3872,3942,4008,4110,4196,4266,4357,4447,4521,4594,4683,4734,4815,4887,4968,5054,5116,5180,5243,5312,5426,5532,5640,5742,5803,5862,5942,6026,6105", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,73,71,78,81,79,96,114,81,57,64,87,63,60,89,63,62,61,67,63,55,122,64,61,55,70,126,83,73,96,80,83,135,76,76,115,86,78,56,54,65,75,79,70,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,105,107,101,60,58,79,83,78,74", "endOffsets": "265,339,411,490,572,652,749,864,946,1004,1069,1157,1221,1282,1372,1436,1499,1561,1629,1693,1749,1872,1937,1999,2055,2126,2253,2337,2411,2508,2589,2673,2809,2886,2963,3079,3166,3245,3302,3357,3423,3499,3579,3650,3726,3793,3867,3937,4003,4105,4191,4261,4352,4442,4516,4589,4678,4729,4810,4882,4963,5049,5111,5175,5238,5307,5421,5527,5635,5737,5798,5857,5937,6021,6100,6175"}, "to": {"startLines": "19,52,53,54,55,56,64,65,66,91,92,144,148,151,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,270,274,275,277", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "784,3862,3936,4008,4087,4169,4966,5063,5178,7967,8025,11823,12219,12458,18594,18684,18748,18811,18873,18941,19005,19061,19184,19249,19311,19367,19438,19565,19649,19723,19820,19901,19985,20121,20198,20275,20391,20478,20557,20614,20669,20735,20811,20891,20962,21038,21105,21179,21249,21315,21417,21503,21573,21664,21754,21828,21901,21990,22041,22122,22194,22275,22361,22423,22487,22550,22619,22733,22839,22947,23049,23110,23557,23879,23963,24112", "endLines": "22,52,53,54,55,56,64,65,66,91,92,144,148,151,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,270,274,275,277", "endColumns": "12,73,71,78,81,79,96,114,81,57,64,87,63,60,89,63,62,61,67,63,55,122,64,61,55,70,126,83,73,96,80,83,135,76,76,115,86,78,56,54,65,75,79,70,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,105,107,101,60,58,79,83,78,74", "endOffsets": "949,3931,4003,4082,4164,4244,5058,5173,5255,8020,8085,11906,12278,12514,18679,18743,18806,18868,18936,19000,19056,19179,19244,19306,19362,19433,19560,19644,19718,19815,19896,19980,20116,20193,20270,20386,20473,20552,20609,20664,20730,20806,20886,20957,21033,21100,21174,21244,21310,21412,21498,21568,21659,21749,21823,21896,21985,22036,22117,22189,22270,22356,22418,22482,22545,22614,22728,22834,22942,23044,23105,23164,23632,23958,24037,24182"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8e199df52e4d067d471a8ec3433b6506\\transformed\\core-1.13.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "57,58,59,60,61,62,63,281", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4249,4344,4451,4548,4648,4751,4855,24411", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "4339,4446,4543,4643,4746,4850,4961,24507"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37fc1bdcb99e9ca53adfd9dc62ba1cb8\\transformed\\foundation-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,86", "endOffsets": "137,224"}, "to": {"startLines": "285,286", "startColumns": "4,4", "startOffsets": "24777,24864", "endColumns": "86,86", "endOffsets": "24859,24946"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cc3c3a73d52d53a742bae480359958fe\\transformed\\credentials-1.2.0-rc01\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,119", "endOffsets": "164,284"}, "to": {"startLines": "50,51", "startColumns": "4,4", "startOffsets": "3628,3742", "endColumns": "113,119", "endOffsets": "3737,3857"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1375e70be1fe8041f38907ceec5f1093\\transformed\\appcompat-1.7.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,2855"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,273", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "954,1054,1151,1263,1348,1449,1563,1644,1723,1814,1907,2000,2094,2200,2293,2388,2483,2574,2668,2749,2859,2966,3063,3172,3272,3375,3530,23798", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "1049,1146,1258,1343,1444,1558,1639,1718,1809,1902,1995,2089,2195,2288,2383,2478,2569,2663,2744,2854,2961,3058,3167,3267,3370,3525,3623,23874"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98a7034e786415bc9e009aca9ccbea7a\\transformed\\play-services-basement-18.4.0\\res\\values-is\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "77", "startColumns": "4", "startOffsets": "6406", "endColumns": "128", "endOffsets": "6530"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cd483c27f09d2683183fc5f548eeb5d9\\transformed\\play-services-base-18.5.0\\res\\values-is\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,445,566,671,808,929,1034,1135,1285,1387,1540,1662,1800,1950,2010,2069", "endColumns": "101,149,120,104,136,120,104,100,149,101,152,121,137,149,59,58,74", "endOffsets": "294,444,565,670,807,928,1033,1134,1284,1386,1539,1661,1799,1949,2009,2068,2143"}, "to": {"startLines": "69,70,71,72,73,74,75,76,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5432,5538,5692,5817,5926,6067,6192,6301,6535,6689,6795,6952,7078,7220,7374,7438,7501", "endColumns": "105,153,124,108,140,124,108,104,153,105,156,125,141,153,63,62,78", "endOffsets": "5533,5687,5812,5921,6062,6187,6296,6401,6684,6790,6947,7073,7215,7369,7433,7496,7575"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ff95595b5660c73a0c7669f7cd696712\\transformed\\material3-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,388,500,597,696,812,953,1080,1215,1305,1406,1503,1603,1718,1844,1950,2075,2199,2341,2512,2635,2751,2870,2992,3090,3188,3297,3419,3525,3633,3736,3866,4001,4109,4214,4290,4384,4477,4562,4647,4756,4836,4927,5028,5129,5224,5332,5420,5525,5626,5732,5852,5932,6034", "endColumns": "113,111,106,111,96,98,115,140,126,134,89,100,96,99,114,125,105,124,123,141,170,122,115,118,121,97,97,108,121,105,107,102,129,134,107,104,75,93,92,84,84,108,79,90,100,100,94,107,87,104,100,105,119,79,101,95", "endOffsets": "164,276,383,495,592,691,807,948,1075,1210,1300,1401,1498,1598,1713,1839,1945,2070,2194,2336,2507,2630,2746,2865,2987,3085,3183,3292,3414,3520,3628,3731,3861,3996,4104,4209,4285,4379,4472,4557,4642,4751,4831,4922,5023,5124,5219,5327,5415,5520,5621,5727,5847,5927,6029,6125"}, "to": {"startLines": "152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12519,12633,12745,12852,12964,13061,13160,13276,13417,13544,13679,13769,13870,13967,14067,14182,14308,14414,14539,14663,14805,14976,15099,15215,15334,15456,15554,15652,15761,15883,15989,16097,16200,16330,16465,16573,16678,16754,16848,16941,17026,17111,17220,17300,17391,17492,17593,17688,17796,17884,17989,18090,18196,18316,18396,18498", "endColumns": "113,111,106,111,96,98,115,140,126,134,89,100,96,99,114,125,105,124,123,141,170,122,115,118,121,97,97,108,121,105,107,102,129,134,107,104,75,93,92,84,84,108,79,90,100,100,94,107,87,104,100,105,119,79,101,95", "endOffsets": "12628,12740,12847,12959,13056,13155,13271,13412,13539,13674,13764,13865,13962,14062,14177,14303,14409,14534,14658,14800,14971,15094,15210,15329,15451,15549,15647,15756,15878,15984,16092,16195,16325,16460,16568,16673,16749,16843,16936,17021,17106,17215,17295,17386,17487,17588,17683,17791,17879,17984,18085,18191,18311,18391,18493,18589"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e82eb3f2167345ace1b8e91ea59beec2\\transformed\\navigation-ui-2.8.2\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,158", "endColumns": "102,113", "endOffsets": "153,267"}, "to": {"startLines": "266,267", "startColumns": "4,4", "startOffsets": "23169,23272", "endColumns": "102,113", "endOffsets": "23267,23381"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f9ce903e2ff26f2c616d9362ffafb64\\transformed\\exoplayer-ui-2.19.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,488,689,774,858,935,1024,1121,1190,1254,1345,1436,1499,1563,1625,1693,1817,1943,2067,2142,2223,2296,2365,2448,2530,2595,2675,2728,2789,2839,2900,2959,3029,3092,3154,3218,3278,3344,3409,3479,3531,3591,3665,3739", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,84,83,76,88,96,68,63,90,90,62,63,61,67,123,125,123,74,80,72,68,82,81,64,79,52,60,49,60,58,69,62,61,63,59,65,64,69,51,59,73,73,52", "endOffsets": "283,483,684,769,853,930,1019,1116,1185,1249,1340,1431,1494,1558,1620,1688,1812,1938,2062,2137,2218,2291,2360,2443,2525,2590,2670,2723,2784,2834,2895,2954,3024,3087,3149,3213,3273,3339,3404,3474,3526,3586,3660,3734,3787"}, "to": {"startLines": "2,11,15,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,383,583,8090,8175,8259,8336,8425,8522,8591,8655,8746,8837,8900,8964,9026,9094,9218,9344,9468,9543,9624,9697,9766,9849,9931,9996,10706,10759,10820,10870,10931,10990,11060,11123,11185,11249,11309,11375,11440,11510,11562,11622,11696,11770", "endLines": "10,14,18,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143", "endColumns": "17,12,12,84,83,76,88,96,68,63,90,90,62,63,61,67,123,125,123,74,80,72,68,82,81,64,79,52,60,49,60,58,69,62,61,63,59,65,64,69,51,59,73,73,52", "endOffsets": "378,578,779,8170,8254,8331,8420,8517,8586,8650,8741,8832,8895,8959,9021,9089,9213,9339,9463,9538,9619,9692,9761,9844,9926,9991,10071,10754,10815,10865,10926,10985,11055,11118,11180,11244,11304,11370,11435,11505,11557,11617,11691,11765,11818"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d7bf19bacce7fd4c141ebe47b25076b8\\transformed\\ui-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,277,376,475,560,640,735,824,906,984,1067,1137,1212,1287,1361,1438,1506", "endColumns": "90,80,98,98,84,79,94,88,81,77,82,69,74,74,73,76,67,119", "endOffsets": "191,272,371,470,555,635,730,819,901,979,1062,1132,1207,1282,1356,1433,1501,1621"}, "to": {"startLines": "67,68,88,89,90,149,150,268,269,271,272,276,278,279,280,282,283,284", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5260,5351,7684,7783,7882,12283,12363,23386,23475,23637,23715,24042,24187,24262,24337,24512,24589,24657", "endColumns": "90,80,98,98,84,79,94,88,81,77,82,69,74,74,73,76,67,119", "endOffsets": "5346,5427,7778,7877,7962,12358,12453,23470,23552,23710,23793,24107,24257,24332,24406,24584,24652,24772"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\eec5b5722953297dab7b2da4ce527235\\transformed\\browser-1.4.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,366", "endColumns": "103,100,105,100", "endOffsets": "154,255,361,462"}, "to": {"startLines": "87,145,146,147", "startColumns": "4,4,4,4", "startOffsets": "7580,11911,12012,12118", "endColumns": "103,100,105,100", "endOffsets": "7679,12007,12113,12214"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e87b0a766c58fccb5c3caec65c96631b\\transformed\\exoplayer-core-2.19.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,181,240,306,382,445,534,616", "endColumns": "66,58,58,65,75,62,88,81,68", "endOffsets": "117,176,235,301,377,440,529,611,680"}, "to": {"startLines": "117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10076,10143,10202,10261,10327,10403,10466,10555,10637", "endColumns": "66,58,58,65,75,62,88,81,68", "endOffsets": "10138,10197,10256,10322,10398,10461,10550,10632,10701"}}]}]}