<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_instagram_posts" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\fragment_instagram_posts.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_instagram_posts_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="131" endOffset="51"/></Target><Target id="@+id/textInstagramBusiness" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="18" endOffset="51"/></Target><Target id="@+id/scrollView" view="ScrollView"><Expressions/><location startLine="20" startOffset="4" endLine="37" endOffset="16"/></Target><Target id="@+id/textInstagramStats" view="TextView"><Expressions/><location startLine="31" startOffset="8" endLine="36" endOffset="37"/></Target><Target id="@+id/textLastSync" view="TextView"><Expressions/><location startLine="39" startOffset="4" endLine="49" endOffset="51"/></Target><Target id="@+id/swipeRefresh" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="51" startOffset="4" endLine="69" endOffset="59"/></Target><Target id="@+id/recyclerInstagramPosts" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="61" startOffset="8" endLine="67" endOffset="58"/></Target><Target id="@+id/emptyStateView" view="TextView"><Expressions/><location startLine="71" startOffset="4" endLine="83" endOffset="51"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="85" startOffset="4" endLine="94" endOffset="51"/></Target><Target id="@+id/buttonContainer" view="LinearLayout"><Expressions/><location startLine="96" startOffset="4" endLine="130" endOffset="18"/></Target><Target id="@+id/btnInstagramLogin" view="Button"><Expressions/><location startLine="105" startOffset="8" endLine="112" endOffset="39"/></Target><Target id="@+id/btnFetchProfileData" view="Button"><Expressions/><location startLine="114" startOffset="8" endLine="121" endOffset="39"/></Target><Target id="@+id/btnFetchInsights" view="Button"><Expressions/><location startLine="123" startOffset="8" endLine="129" endOffset="39"/></Target></Targets></Layout>