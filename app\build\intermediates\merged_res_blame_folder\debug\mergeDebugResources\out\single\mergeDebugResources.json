[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_format_list_numbered.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_format_list_numbered.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\color_drawer_item_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\color\\drawer_item_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_exercise.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_exercise.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_add_exercise.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_add_exercise.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_fragment_futures.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\fragment_futures.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_fragment_transactions_overview.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\fragment_transactions_overview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_notification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_notification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_note.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_note.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_category_transport.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_category_transport.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_dialog_task_group.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_task_group.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_dialog_rounded_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\dialog_rounded_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\menu_instagram_bottom_nav_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\menu\\instagram_bottom_nav_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_dialog_add_event.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_add_event.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_wt.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_wt.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_code.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_code.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\menu_wt_bottom_nav_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\menu\\wt_bottom_nav_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_attach_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_attach_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_circle_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\circle_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable-v24_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable-v24\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_fullscreen_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_fullscreen_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_format_text.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_format_text.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\menu_menu_group_options.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\menu\\menu_group_options.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_dialog_edit_investment.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_edit_investment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_fragment_futures_tab.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\fragment_futures_tab.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_delete.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_delete.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable-night_dialog_rounded_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable-night\\dialog_rounded_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_back.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_back.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\xml_file_paths.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\xml\\file_paths.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_dialog_program_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_program_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_selected_circle_shape.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\selected_circle_shape.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_backup.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_backup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_lesson.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_lesson.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_category_summary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_category_summary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout-night_item_history.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout-night\\item_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_search_white.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_search_white.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_note_video.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_note_video.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_calendar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_calendar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_format_bold.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_format_bold.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_layout_page_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\layout_page_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_draw.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_draw.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_save.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_save.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_cleardata.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_cleardata.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\menu_bottom_nav_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\menu\\bottom_nav_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_category_spending.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_category_spending.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_edit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_edit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_category_wing_tzun.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_category_wing_tzun.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_investment_dropdown.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_investment_dropdown.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_format_italic.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_format_italic.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\font_opensans_regular.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\font\\opensans_regular.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_fragment_transaction_report.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\fragment_transaction_report.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_investment.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_investment.xml"}, {"merged": "com.example.allinone.app-debug-114:/layout_item_voice_note.xml.flat", "source": "com.example.allinone.app-main-116:/layout/item_voice_note.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_play_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_play_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout-night_activity_backup.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout-night\\activity_backup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_call.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_call.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\menu_menu_task_options.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\menu\\menu_task_options.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_expense.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_expense.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_no_students.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_no_students.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_bg_category_chip.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\bg_category_chip.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_activity_backup.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\activity_backup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_chevron_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_chevron_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_category_shopping.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_category_shopping.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_income.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_income.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_bg_tag_red.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\bg_tag_red.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_uncompleted_exercise_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\uncompleted_exercise_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_circle_shape.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\circle_shape.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\color_wt_bottom_nav_item_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\color\\wt_bottom_nav_item_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_transaction_report.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_transaction_report.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_whatsapp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_whatsapp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_bg_tag_blue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\bg_tag_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\color_chip_background_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\color\\chip_background_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_wt_registration.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_wt_registration.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_video_placeholder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_video_placeholder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_bg_day_with_events.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\bg_day_with_events.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\menu_drawer_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\menu\\drawer_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_border_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\border_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\xml_network_security_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\xml\\network_security_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_circle_background_red.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\circle_background_red.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_tasks.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_tasks.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_category_general.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_category_general.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_expand_less.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_expand_less.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_remove.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_remove.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable-night_bg_category_chip.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable-night\\bg_category_chip.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_fragment_investments_tab.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\fragment_investments_tab.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\color-night_text_input_box_stroke.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\color-night\\text_input_box_stroke.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_bg_day_with_registration.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\bg_day_with_registration.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_fragment_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\fragment_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\color_text_input_text_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\color\\text_input_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_close.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_close.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_placeholder_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\placeholder_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_binance_position.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_binance_position.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_note.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_note.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_dialog_student_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_student_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_splash_layout_drawable.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\splash_layout_drawable.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_transaction.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_transaction.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_instagram_posts.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_instagram_posts.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_history.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_bg_selected_day.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\bg_selected_day.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_transactions.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_transactions.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_play.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_play.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_category_food.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_category_food.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_dialog_edit_lesson.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_edit_lesson.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_stats.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_stats.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\color_text_input_box_stroke.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\color\\text_input_box_stroke.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_notes.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_notes.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_chat_user.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_chat_user.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_format_list_bulleted.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_format_list_bulleted.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_category_salary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_category_salary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_category_bills.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_category_bills.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout-night_activity_edit_note.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout-night\\activity_edit_note.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\menu_workout_bottom_nav_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\menu\\workout_bottom_nav_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\menu_wt_student_context_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\menu\\wt_student_context_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_nav_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\nav_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_chat_source.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_chat_source.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_stop.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_stop.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_dialog_expense_investment.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_expense_investment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_wt_student.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_wt_student.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\menu_search_history.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\menu\\search_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout-night_dialog_student_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout-night\\dialog_student_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_program.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_program.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\color_dialog_delete_button_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\color\\dialog_delete_button_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_file_structure.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_file_structure.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_note_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_note_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_investment_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_investment_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_dialog_progress.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_progress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_rounded_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\rounded_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\font_opensans.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\font\\opensans.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_pie_chart_tooltip.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\pie_chart_tooltip.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout-night_item_backup.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout-night\\item_backup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_dialog_edit_seminar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_edit_seminar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\menu_menu_tasks.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\menu\\menu_tasks.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_investment_selection.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_investment_selection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_format_underline.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_format_underline.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_video_error.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_video_error.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_graduation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_graduation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\menu_menu_edit_note.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\menu\\menu_edit_note.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_dialog_fullscreen_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_fullscreen_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_instagram.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_instagram.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_dialog_add_student.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_add_student.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_students.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_students.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_bg_day_with_event.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\bg_day_with_event.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_dialog_edit_program.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_edit_program.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_expand_more.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_expand_more.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_category_dropdown.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_category_dropdown.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_reports.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_reports.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_transparent.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\transparent.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_chat_ai.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_chat_ai.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_file.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_file.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_dialog_post_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_post_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_share.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_share.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\menu_search_students.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\menu\\search_students.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_rounded_corner_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\rounded_corner_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_dialog_add_task.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_add_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_completed_exercise_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\completed_exercise_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_dialog_edit_wt_student.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_edit_wt_student.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\menu_search_register.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\menu\\search_register.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\color_dialog_action_button_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\color\\dialog_action_button_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_add_photo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_add_photo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_folder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_folder.xml"}, {"merged": "com.example.allinone.app-debug-114:/layout_item_lesson.xml.flat", "source": "com.example.allinone.app-main-116:/layout/item_lesson.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_wt_event.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_wt_event.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_error.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_error.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_dropdown_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dropdown_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_dialog_add_program.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_add_program.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_bg_day_with_lesson.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\bg_day_with_lesson.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_bg_tag_green.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\bg_tag_green.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_pause.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_pause.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\menu_wt_registration_context_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\menu\\wt_registration_context_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\menu_search_notes.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\menu\\search_notes.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\color-night_dialog_delete_button_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\color-night\\dialog_delete_button_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_dialog_income_investment.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_income_investment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_log.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_log.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_dialog_pin_input.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_pin_input.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_futures_position.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_futures_position.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\color-night_text_input_text_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\color-night\\text_input_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_empty_state.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_empty_state.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_error_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\error_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_investments.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_investments.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_category_sports.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_category_sports.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_backup.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_backup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_lessons.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_lessons.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_dialog_profit_loss.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_profit_loss.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_dialog_loading.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_loading.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\mipmap-anydpi-v33_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\mipmap-anydpi-v33\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_dialog_workout_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_workout_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_wt_registers.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_wt_registers.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_simple_text_splash.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\simple_text_splash.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\font_opensans_bold.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\font\\opensans_bold.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_checkbox.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_checkbox.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\color-night_dialog_action_button_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\color-night\\dialog_action_button_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_seminar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_seminar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_theme_switch_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\theme_switch_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\color_bottom_nav_item_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\color\\bottom_nav_item_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_add.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_add.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_fitness.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_fitness.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_view_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_view_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_clear_data.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_clear_data.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_category_investment.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_category_investment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_voice_note.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_voice_note.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_offline_status_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\offline_status_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_dialog_futures_tp_sl.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_futures_tp_sl.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_fragment_investments.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\fragment_investments.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_student.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_student.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_category_game.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_category_game.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_chevron_left.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_chevron_left.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_fully_uncompleted_exercise_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\fully_uncompleted_exercise_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_dashboard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_database.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_database.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_default_profile.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\default_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout-night_fragment_history.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout-night\\fragment_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_registration.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_registration.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_no_registrations.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_no_registrations.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\color_bottom_nav_item_color_light.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\color\\bottom_nav_item_color_light.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_bg_current_day.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\bg_current_day.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_splash_text_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\splash_text_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\layout_item_investment.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_investment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-debug-114:\\drawable_ic_category_all.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\drawable\\ic_category_all.xml"}]