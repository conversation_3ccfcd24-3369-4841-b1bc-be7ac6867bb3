-- Merging decision tree log ---
provider#androidx.startup.InitializationProvider
INJECTED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:32:9-36:35
REJECTED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5071997fcfc97d02bafc8941f239eee\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aea5573deca53022a416382f3264193f\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aea5573deca53022a416382f3264193f\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed63fd1d6f0c32c1e430798002671246\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed63fd1d6f0c32c1e430798002671246\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:36:13-32
	android:authorities
		INJECTED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:34:13-68
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:35:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:33:13-67
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:58:9-66:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:62:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:60:13-60
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:61:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:59:13-62
manifest
ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:2:1-92:12
INJECTED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:2:1-92:12
INJECTED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:2:1-92:12
INJECTED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:2:1-92:12
MERGED from [androidx.databinding:databinding-adapters:8.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e1bad23bdb5e6cc441d58fefb38d94e\transformed\databinding-adapters-8.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-ktx:8.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa4ef47006e4b8b8308327162806eebe\transformed\databinding-ktx-8.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-runtime:8.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ab49c54b76bd63e9b51dedbdd7ffaa2\transformed\databinding-runtime-8.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69523a2d8e64b595678f05bbea41d824\transformed\viewbinding-8.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f06757f7768abe1e62751d0ace0f97ff\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\305bd98951c960dec40e72aa6b9162c1\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\536a5d77f69c07dc1aedc4b2fccb6e40\transformed\navigation-common-2.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25819f6a6edef9ba0843eed17bb2da45\transformed\navigation-runtime-2.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\38dc6b0e8abdbadfbe760b3216d7d12d\transformed\navigation-common-ktx-2.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e82eb3f2167345ace1b8e91ea59beec2\transformed\navigation-ui-2.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa24054c4015fd46b5a3fc653cb09666\transformed\navigation-fragment-2.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72e9453ef828cff9a49b70f5928fd438\transformed\navigation-runtime-ktx-2.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28ec13728b3851c8f2cd1f3f23f56d9f\transformed\navigation-ui-ktx-2.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60461ea28113d9f3dde31fd2e6c2a0de\transformed\navigation-fragment-ktx-2.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\505bfc2505d423b2b5359ed947920737\transformed\navigation-compose-2.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75ac7f2a23ffbf48dd6409062de1d131\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc472afffedb0f448961e1c05cc4dbbf\transformed\constraintlayout-2.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.github.chrisbanes:PhotoView:2.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe15545a327dd3e628a07d26bf9a38c8\transformed\PhotoView-2.3.0\AndroidManifest.xml:2:1-11:12
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a44c52eb3e2d2d4c9df76b5d9814581\transformed\coil-compose-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a54cd9ff86e142881ef03f497a78d3f0\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46e0541dd417aaeb2f970b774c9b8599\transformed\coil-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a385e492662b576a6d8d8112efe855\transformed\coil-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de5534398a635c9be4065ca9ebc0cd2b\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1375e70be1fe8041f38907ceec5f1093\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:17:1-75:12
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc3c3a73d52d53a742bae480359958fe\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63063255e3aad00729fed87c165307c6\transformed\exoplayer-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f9ce903e2ff26f2c616d9362ffafb64\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.paging:paging-common-android:3.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05628feb40637c25ba1c41c2d30ca1bc\transformed\paging-common-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.paging:paging-runtime-ktx:3.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d25a4a00a25ea047bc8f6558ed670e6\transformed\paging-runtime-ktx-3.3.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.paging:paging-runtime:3.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76ef5c50f8c23f7c27c8571c06f9514a\transformed\paging-runtime-3.3.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db3070082bb6446577445679923bae22\transformed\recyclerview-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03a3627e1824ee70cf489e592292748f\transformed\viewpager2-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f697fc0f692dda17f643d9f51015b66\transformed\hilt-android-2.52\AndroidManifest.xml:16:1-19:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\660371d89149d49eb6de9d074ae462ea\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6659db04af5d97d80d3064c8475b99cd\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14c2bcefa55c73856487bee5353b1cb1\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f1617ad11298e9459bdbc3d95c5abe\transformed\firebase-analytics-22.1.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f55701a2806ad5fbdfaf217a3797a4c8\transformed\play-services-measurement-sdk-22.1.2\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\431ca28934aa0e759e1ba596c21cfdf5\transformed\recaptcha-18.5.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d05001fc8405447d73c08ee91a97e6b\transformed\integrity-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ce1a7dd250c9120ea5bf8555ae67b1a\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:15:1-34:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d29eeb71e4b590606a79f795e337f4\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f35fa3093855a6c92d644585e0d68472\transformed\firebase-installations-interop-17.2.0\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867cfca9bc377d60b7e8af899bf51df1\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\506c2d7d88d4acfac211914fc9a152c8\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46fa31863f3fabfb17c38d00da312695\transformed\play-services-fido-20.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [me.saket.swipe:swipe:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d46e4b7ad062911a593f57ff2088e553\transformed\swipe-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff95595b5660c73a0c7669f7cd696712\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37fc1bdcb99e9ca53adfd9dc62ba1cb8\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62a1b30182b029e62d22b2dd2c857809\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ac8b48e17b53bc98e07f9162f8a892b\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09d506d5e9fe13c46304e6da3dfa04ec\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44ba11e360ef7ca5413456b5c51a2c4f\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05b18cf898fc46bb81d6dc6a059bbb55\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebbec17bf6c104ade9cbd4874189386e\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1525187ec8133cbbd491972c29238be8\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1364c55b39eaf8182e00cae12bebcc59\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7378be35798f99346c38131207a4d92\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5d37fd89a97cf267377a06b09c52112\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e326cf46a3e0f3591896fb1f0061a989\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\572ce6cba238291b12bb33f3d23310a2\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba9ea08de9541b65dd4ab1b547e9b60d\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f998e930f7cae181b32542910621c7\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\759b6acd1f3bc3a615e39717df962334\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1635c0b3dfe1dbc32ae4074e1749255b\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc269eb2a18ceb548d2a5ea95adc8c96\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.ui:ui-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7bf19bacce7fd4c141ebe47b25076b8\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99bc7333b09f49c249631ba3577589ba\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5071997fcfc97d02bafc8941f239eee\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e448ed96b6af2b004b3c7ca7af320d3d\transformed\activity-1.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f4b32b2d4266b520bc3135d7b2885dc\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63bcf4d35504fd2547407dc074988349\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e33bfbc537352276299afc3330dd456\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\748db3a4e46b24efea46d5e564085cf2\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fcfe4ac222496a2880ccbd7dce119031\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\549d3916b6809f2845fb5e42208cb6be\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6fb973f260208f7e6314e3dee7fad99\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\473ea2509b1f540a006e124452427516\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c656b25fa2dc95e881345daed065935\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff2de5d72fce1ffa11fecc0c6983bab7\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb516a4a0a9943a281ce6f19605ad596\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1469d513bd6443e66703587865ac5ade\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0b251d390f4afb1f9ca91dbc800110d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d1983e8f816433307bdd0bbd836e24a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed65d3d7a605f20c30bf5c652dd965e5\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f03f196a6ab2e86824c49d07db5ba34\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6896777e83f77c6a440bcb66c31cbaf\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e272b663b90dafb232bdf1f1361c364\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d89ef1c21515073ae40a0ac1b12cafa4\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e87b0a766c58fccb5c3caec65c96631b\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.work:work-runtime-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1783a6756ba8fb878c7385cec384cd49\transformed\work-runtime-ktx-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eec5b5722953297dab7b2da4ce527235\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a591881580f7036f2574d26b337679f8\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\feb00527f72d755a5627314bd3f96d19\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd1523a13beb55e8e76f6d0d33c82918\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5670563a54c53bc97f5592427a4620d3\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44ff130bb3a3d63f1a426dff612a1f40\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd483c27f09d2683183fc5f548eeb5d9\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f8c3e6828abcbffa3c9f5535dfc64a3\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6638fe5cf8f78eefbcd16cdca2d9e5c\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f316befd3963bb027428722e4bcae7f\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\572be8aa37747fccc1b25ccc0ad34731\transformed\lifecycle-viewmodel-2.8.6\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a59630a39ebfd67a70a167ceaa9ba65d\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb84a08ced79840842eaf49383a58010\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5fb10182892807adb5a50c0d073a398\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-livedata:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a0de01b529fb2bb0e648b53a3676679\transformed\runtime-livedata-1.7.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c8d87b123cdc25e1157775c2004abe2\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a317f68135dc86eb1a707ff08cbd84a8\transformed\lifecycle-livedata-core-2.8.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\770bfe3740ffe9976cba2d0c51b81483\transformed\lifecycle-viewmodel-savedstate-2.8.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30b5cfcb00be2cdd572d6c04b1241c09\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ccabb738105bd78fcd4c70adbea2096\transformed\lifecycle-service-2.8.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aea5573deca53022a416382f3264193f\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca32da0058e2cfd9c70ee1e7c1adb262\transformed\lifecycle-livedata-core-ktx-2.8.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6b877db508dcfb3e7ad410982fcb18a\transformed\lifecycle-livedata-2.8.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d29603c3210a6d0855335cb17a945164\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0b737481420e6e821cc1bf6564e28e6\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2a03bf9a2c91a7cce1ae06827c08ffc\transformed\lifecycle-livedata-ktx-2.8.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4a1b7671c535604a38c60aa72e81002c\transformed\lifecycle-viewmodel-ktx-2.8.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3bc0680d46b752b3dba03a568ba1a3\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d87d94b5c13a001fa84294ac4c30cec6\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\414e0683e18e9aaca9df3e519609757c\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fefa0f32ba14f41623918fd0f72addf5\transformed\datastore-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9223f0e1271e59953c421b866dd50ca\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e0672aec5af16978afd7bbd8e460675\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd946245e66d6cf7b58e6eb126b81219\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1cab4525135b934e909b93124109edf\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd31ab42f6df184811fef9a3c088b0f0\transformed\play-services-measurement-base-22.1.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98a7034e786415bc9e009aca9ccbea7a\transformed\play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.8.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d472de1097adacf621614efcbac1e21\transformed\fragment-1.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.8.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c44a06b68f01c286a9a7efde56763d5b\transformed\fragment-ktx-1.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8dccd7121ce23f325500264b1927919\transformed\activity-ktx-1.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f07e0e8f60f626ea9f27bd033ae12b\transformed\activity-compose-1.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d3c9c4ac15ccaa3d4f8bb593bf66c\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e74249777095f16bf90043ea48d86009\transformed\timber-5.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c2ac540a0dbb432b7e96e8026104ab5\transformed\firebase-config-interop-16.0.1\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e2967fd5b276966c87bd4e88ea401c8\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a5ff77ed6e8796335744dd9c5829aa6\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:15:1-22:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\373d4135f1586ad81ac2cd37e6ac32ee\transformed\googleid-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b7572c291721d887a8a69c42c05f8fb\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89a4d9fea819de7bc2d872220c0a8f0f\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\81e1762d7a12f7128da367a6a2a55145\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5798195514dcf9f54facb6622514625d\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:2:1-20:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5438095388770c65ceef79c1ed6cdce\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d96accd3e95a849b411f6d40626c55c1\transformed\documentfile-1.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee551ec3fd119864ca11d7dccd96f4e7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\376a5cf04f9e52a43728211b447c8cba\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f43bef631a3a38a9d08e025479944c3e\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a23e484f9ab2c8f2fe1c821821b8dd08\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d06e38b9254253d89d40561949adceca\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952b95c172269eee0bea8252aa77f918\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f84f39bce60e40271bbffac62df7748d\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26aba51db4309e78491b8b75b9ce4e8c\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c58cd8b4696f8fa5bb0d8d3c36af5768\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7201e597345aa82cd022c99533bbf50\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d20d3d5f1f5a05dec5f94ea4a31a1428\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea3d85634f8413586751d19babfb5cfb\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed63fd1d6f0c32c1e430798002671246\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\383f67f12f6be5aabe17a1a6b60ff97c\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63d14b553fca4381dcc9cb52bec5050b\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b54da9fb788aca02c18c87741f8a5cf\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44ad0f2412c7c8a71f80cba0dcd24c9e\transformed\transport-api-3.2.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9891cc273c69fca165fe759e0b5d848a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc83f3f0f89460506f12f9fa8d48059e\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d441db952f6ad88c2cd980962bd4c407\transformed\grpc-android-1.62.2\AndroidManifest.xml:2:1-9:12
MERGED from [jp.wasabeef:richeditor-android:2.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5b4b351cbe3a8b1d05e0f2d04d73525\transformed\richeditor-android-2.0.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32aace43ec0d198bec8cbb1cbaf9874c\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:16:1-19:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\673ec8ce3066443b41d99eb75dbc1193\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed98f692eef56b9ec9255f707f2cecc8\transformed\core-common-2.0.3\AndroidManifest.xml:2:1-21:12
	package
		INJECTED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:5:5-107
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:5:78-104
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:5:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:6:5-76
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:6:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:7:5-75
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:7:22-72
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:8:5-108
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:8:79-105
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:8:22-78
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:9:5-65
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:9:22-62
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:10:5-77
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:10:22-74
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:11:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\431ca28934aa0e759e1ba596c21cfdf5\transformed\recaptcha-18.5.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\431ca28934aa0e759e1ba596c21cfdf5\transformed\recaptcha-18.5.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\506c2d7d88d4acfac211914fc9a152c8\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\506c2d7d88d4acfac211914fc9a152c8\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1cab4525135b934e909b93124109edf\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1cab4525135b934e909b93124109edf\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:11:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:12:5-79
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:10:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\431ca28934aa0e759e1ba596c21cfdf5\transformed\recaptcha-18.5.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\431ca28934aa0e759e1ba596c21cfdf5\transformed\recaptcha-18.5.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\506c2d7d88d4acfac211914fc9a152c8\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\506c2d7d88d4acfac211914fc9a152c8\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e87b0a766c58fccb5c3caec65c96631b\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e87b0a766c58fccb5c3caec65c96631b\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1cab4525135b934e909b93124109edf\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1cab4525135b934e909b93124109edf\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c58cd8b4696f8fa5bb0d8d3c36af5768\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c58cd8b4696f8fa5bb0d8d3c36af5768\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d441db952f6ad88c2cd980962bd4c407\transformed\grpc-android-1.62.2\AndroidManifest.xml:7:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d441db952f6ad88c2cd980962bd4c407\transformed\grpc-android-1.62.2\AndroidManifest.xml:7:5-79
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:12:22-76
uses-permission#android.permission.GET_ACCOUNTS
ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:13:5-71
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:13:22-68
uses-permission#android.permission.USE_CREDENTIALS
ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:14:5-74
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:14:22-71
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:15:5-71
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:15:22-68
application
ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:17:5-90:19
INJECTED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:17:5-90:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75ac7f2a23ffbf48dd6409062de1d131\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75ac7f2a23ffbf48dd6409062de1d131\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc472afffedb0f448961e1c05cc4dbbf\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc472afffedb0f448961e1c05cc4dbbf\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:28:5-73:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f1617ad11298e9459bdbc3d95c5abe\transformed\firebase-analytics-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f1617ad11298e9459bdbc3d95c5abe\transformed\firebase-analytics-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f55701a2806ad5fbdfaf217a3797a4c8\transformed\play-services-measurement-sdk-22.1.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f55701a2806ad5fbdfaf217a3797a4c8\transformed\play-services-measurement-sdk-22.1.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d05001fc8405447d73c08ee91a97e6b\transformed\integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d05001fc8405447d73c08ee91a97e6b\transformed\integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ce1a7dd250c9120ea5bf8555ae67b1a\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ce1a7dd250c9120ea5bf8555ae67b1a\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d29eeb71e4b590606a79f795e337f4\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d29eeb71e4b590606a79f795e337f4\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867cfca9bc377d60b7e8af899bf51df1\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867cfca9bc377d60b7e8af899bf51df1\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\506c2d7d88d4acfac211914fc9a152c8\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\506c2d7d88d4acfac211914fc9a152c8\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46fa31863f3fabfb17c38d00da312695\transformed\play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46fa31863f3fabfb17c38d00da312695\transformed\play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5d37fd89a97cf267377a06b09c52112\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5d37fd89a97cf267377a06b09c52112\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e326cf46a3e0f3591896fb1f0061a989\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e326cf46a3e0f3591896fb1f0061a989\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5071997fcfc97d02bafc8941f239eee\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5071997fcfc97d02bafc8941f239eee\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\748db3a4e46b24efea46d5e564085cf2\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\748db3a4e46b24efea46d5e564085cf2\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c656b25fa2dc95e881345daed065935\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c656b25fa2dc95e881345daed065935\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd483c27f09d2683183fc5f548eeb5d9\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd483c27f09d2683183fc5f548eeb5d9\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aea5573deca53022a416382f3264193f\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aea5573deca53022a416382f3264193f\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3bc0680d46b752b3dba03a568ba1a3\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3bc0680d46b752b3dba03a568ba1a3\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9223f0e1271e59953c421b866dd50ca\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9223f0e1271e59953c421b866dd50ca\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e0672aec5af16978afd7bbd8e460675\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e0672aec5af16978afd7bbd8e460675\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd946245e66d6cf7b58e6eb126b81219\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd946245e66d6cf7b58e6eb126b81219\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd31ab42f6df184811fef9a3c088b0f0\transformed\play-services-measurement-base-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd31ab42f6df184811fef9a3c088b0f0\transformed\play-services-measurement-base-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98a7034e786415bc9e009aca9ccbea7a\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98a7034e786415bc9e009aca9ccbea7a\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e74249777095f16bf90043ea48d86009\transformed\timber-5.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e74249777095f16bf90043ea48d86009\transformed\timber-5.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e2967fd5b276966c87bd4e88ea401c8\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e2967fd5b276966c87bd4e88ea401c8\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed63fd1d6f0c32c1e430798002671246\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed63fd1d6f0c32c1e430798002671246\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9891cc273c69fca165fe759e0b5d848a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9891cc273c69fca165fe759e0b5d848a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed98f692eef56b9ec9255f707f2cecc8\transformed\core-common-2.0.3\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed98f692eef56b9ec9255f707f2cecc8\transformed\core-common-2.0.3\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml
	android:roundIcon
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:24:9-54
	android:icon
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:22:9-43
	android:networkSecurityConfig
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:27:9-69
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:25:9-35
	android:label
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:23:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:21:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:29:9-29
	android:allowBackup
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:19:9-35
	android:theme
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:26:9-46
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:20:9-65
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:28:9-44
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:18:9-44
activity#com.example.allinone.MainActivity
ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:38:9-47:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:42:13-49
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:40:13-36
	android:theme
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:41:13-59
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:39:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:43:13-46:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:44:17-69
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:44:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:45:17-77
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:45:27-74
activity#com.example.allinone.backup.BackupActivity
ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:49:9-55:58
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:53:13-49
	android:parentActivityName
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:55:13-55
	android:label
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:54:13-47
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:51:13-37
	android:theme
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:52:13-50
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:50:13-50
meta-data#com.google.android.gms.version
ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:68:9-70:69
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98a7034e786415bc9e009aca9ccbea7a\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98a7034e786415bc9e009aca9ccbea7a\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:70:13-66
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:69:13-58
meta-data#com.google.android.gms.games.APP_ID
ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:72:9-74:46
	android:value
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:74:13-43
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:73:13-63
meta-data#com.google.android.gms.wallet.api.enabled
ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:76:9-78:36
	android:value
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:78:13-33
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:77:13-69
activity#com.example.allinone.ui.drawing.DrawingActivity
ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:85:9-89:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:89:13-49
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:87:13-37
	android:theme
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:88:13-62
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:86:13-55
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:63:13-65:54
	android:resource
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:65:17-51
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:64:17-67
uses-sdk
INJECTED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:databinding-adapters:8.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e1bad23bdb5e6cc441d58fefb38d94e\transformed\databinding-adapters-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:8.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e1bad23bdb5e6cc441d58fefb38d94e\transformed\databinding-adapters-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa4ef47006e4b8b8308327162806eebe\transformed\databinding-ktx-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa4ef47006e4b8b8308327162806eebe\transformed\databinding-ktx-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ab49c54b76bd63e9b51dedbdd7ffaa2\transformed\databinding-runtime-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ab49c54b76bd63e9b51dedbdd7ffaa2\transformed\databinding-runtime-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69523a2d8e64b595678f05bbea41d824\transformed\viewbinding-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69523a2d8e64b595678f05bbea41d824\transformed\viewbinding-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f06757f7768abe1e62751d0ace0f97ff\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f06757f7768abe1e62751d0ace0f97ff\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\305bd98951c960dec40e72aa6b9162c1\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\305bd98951c960dec40e72aa6b9162c1\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\536a5d77f69c07dc1aedc4b2fccb6e40\transformed\navigation-common-2.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\536a5d77f69c07dc1aedc4b2fccb6e40\transformed\navigation-common-2.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25819f6a6edef9ba0843eed17bb2da45\transformed\navigation-runtime-2.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25819f6a6edef9ba0843eed17bb2da45\transformed\navigation-runtime-2.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\38dc6b0e8abdbadfbe760b3216d7d12d\transformed\navigation-common-ktx-2.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\38dc6b0e8abdbadfbe760b3216d7d12d\transformed\navigation-common-ktx-2.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e82eb3f2167345ace1b8e91ea59beec2\transformed\navigation-ui-2.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e82eb3f2167345ace1b8e91ea59beec2\transformed\navigation-ui-2.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa24054c4015fd46b5a3fc653cb09666\transformed\navigation-fragment-2.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa24054c4015fd46b5a3fc653cb09666\transformed\navigation-fragment-2.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72e9453ef828cff9a49b70f5928fd438\transformed\navigation-runtime-ktx-2.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72e9453ef828cff9a49b70f5928fd438\transformed\navigation-runtime-ktx-2.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28ec13728b3851c8f2cd1f3f23f56d9f\transformed\navigation-ui-ktx-2.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28ec13728b3851c8f2cd1f3f23f56d9f\transformed\navigation-ui-ktx-2.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60461ea28113d9f3dde31fd2e6c2a0de\transformed\navigation-fragment-ktx-2.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60461ea28113d9f3dde31fd2e6c2a0de\transformed\navigation-fragment-ktx-2.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\505bfc2505d423b2b5359ed947920737\transformed\navigation-compose-2.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\505bfc2505d423b2b5359ed947920737\transformed\navigation-compose-2.8.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75ac7f2a23ffbf48dd6409062de1d131\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75ac7f2a23ffbf48dd6409062de1d131\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc472afffedb0f448961e1c05cc4dbbf\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc472afffedb0f448961e1c05cc4dbbf\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.chrisbanes:PhotoView:2.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe15545a327dd3e628a07d26bf9a38c8\transformed\PhotoView-2.3.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.chrisbanes:PhotoView:2.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe15545a327dd3e628a07d26bf9a38c8\transformed\PhotoView-2.3.0\AndroidManifest.xml:7:5-9:41
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a44c52eb3e2d2d4c9df76b5d9814581\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a44c52eb3e2d2d4c9df76b5d9814581\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a54cd9ff86e142881ef03f497a78d3f0\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a54cd9ff86e142881ef03f497a78d3f0\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46e0541dd417aaeb2f970b774c9b8599\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46e0541dd417aaeb2f970b774c9b8599\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a385e492662b576a6d8d8112efe855\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a385e492662b576a6d8d8112efe855\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de5534398a635c9be4065ca9ebc0cd2b\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de5534398a635c9be4065ca9ebc0cd2b\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1375e70be1fe8041f38907ceec5f1093\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1375e70be1fe8041f38907ceec5f1093\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:21:5-23:151
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc3c3a73d52d53a742bae480359958fe\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc3c3a73d52d53a742bae480359958fe\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63063255e3aad00729fed87c165307c6\transformed\exoplayer-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63063255e3aad00729fed87c165307c6\transformed\exoplayer-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f9ce903e2ff26f2c616d9362ffafb64\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f9ce903e2ff26f2c616d9362ffafb64\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.paging:paging-common-android:3.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05628feb40637c25ba1c41c2d30ca1bc\transformed\paging-common-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.paging:paging-common-android:3.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05628feb40637c25ba1c41c2d30ca1bc\transformed\paging-common-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.paging:paging-runtime-ktx:3.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d25a4a00a25ea047bc8f6558ed670e6\transformed\paging-runtime-ktx-3.3.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.paging:paging-runtime-ktx:3.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d25a4a00a25ea047bc8f6558ed670e6\transformed\paging-runtime-ktx-3.3.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.paging:paging-runtime:3.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76ef5c50f8c23f7c27c8571c06f9514a\transformed\paging-runtime-3.3.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.paging:paging-runtime:3.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76ef5c50f8c23f7c27c8571c06f9514a\transformed\paging-runtime-3.3.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db3070082bb6446577445679923bae22\transformed\recyclerview-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db3070082bb6446577445679923bae22\transformed\recyclerview-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03a3627e1824ee70cf489e592292748f\transformed\viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03a3627e1824ee70cf489e592292748f\transformed\viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f697fc0f692dda17f643d9f51015b66\transformed\hilt-android-2.52\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f697fc0f692dda17f643d9f51015b66\transformed\hilt-android-2.52\AndroidManifest.xml:18:3-42
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\660371d89149d49eb6de9d074ae462ea\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\660371d89149d49eb6de9d074ae462ea\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6659db04af5d97d80d3064c8475b99cd\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6659db04af5d97d80d3064c8475b99cd\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14c2bcefa55c73856487bee5353b1cb1\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14c2bcefa55c73856487bee5353b1cb1\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f1617ad11298e9459bdbc3d95c5abe\transformed\firebase-analytics-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f1617ad11298e9459bdbc3d95c5abe\transformed\firebase-analytics-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f55701a2806ad5fbdfaf217a3797a4c8\transformed\play-services-measurement-sdk-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f55701a2806ad5fbdfaf217a3797a4c8\transformed\play-services-measurement-sdk-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\431ca28934aa0e759e1ba596c21cfdf5\transformed\recaptcha-18.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\431ca28934aa0e759e1ba596c21cfdf5\transformed\recaptcha-18.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d05001fc8405447d73c08ee91a97e6b\transformed\integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d05001fc8405447d73c08ee91a97e6b\transformed\integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ce1a7dd250c9120ea5bf8555ae67b1a\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ce1a7dd250c9120ea5bf8555ae67b1a\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d29eeb71e4b590606a79f795e337f4\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d29eeb71e4b590606a79f795e337f4\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f35fa3093855a6c92d644585e0d68472\transformed\firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f35fa3093855a6c92d644585e0d68472\transformed\firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867cfca9bc377d60b7e8af899bf51df1\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867cfca9bc377d60b7e8af899bf51df1\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\506c2d7d88d4acfac211914fc9a152c8\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\506c2d7d88d4acfac211914fc9a152c8\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46fa31863f3fabfb17c38d00da312695\transformed\play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46fa31863f3fabfb17c38d00da312695\transformed\play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [me.saket.swipe:swipe:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d46e4b7ad062911a593f57ff2088e553\transformed\swipe-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [me.saket.swipe:swipe:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d46e4b7ad062911a593f57ff2088e553\transformed\swipe-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff95595b5660c73a0c7669f7cd696712\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff95595b5660c73a0c7669f7cd696712\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37fc1bdcb99e9ca53adfd9dc62ba1cb8\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37fc1bdcb99e9ca53adfd9dc62ba1cb8\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62a1b30182b029e62d22b2dd2c857809\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62a1b30182b029e62d22b2dd2c857809\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ac8b48e17b53bc98e07f9162f8a892b\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ac8b48e17b53bc98e07f9162f8a892b\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09d506d5e9fe13c46304e6da3dfa04ec\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09d506d5e9fe13c46304e6da3dfa04ec\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44ba11e360ef7ca5413456b5c51a2c4f\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44ba11e360ef7ca5413456b5c51a2c4f\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05b18cf898fc46bb81d6dc6a059bbb55\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05b18cf898fc46bb81d6dc6a059bbb55\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebbec17bf6c104ade9cbd4874189386e\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebbec17bf6c104ade9cbd4874189386e\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1525187ec8133cbbd491972c29238be8\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1525187ec8133cbbd491972c29238be8\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1364c55b39eaf8182e00cae12bebcc59\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1364c55b39eaf8182e00cae12bebcc59\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7378be35798f99346c38131207a4d92\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7378be35798f99346c38131207a4d92\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5d37fd89a97cf267377a06b09c52112\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5d37fd89a97cf267377a06b09c52112\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e326cf46a3e0f3591896fb1f0061a989\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e326cf46a3e0f3591896fb1f0061a989\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\572ce6cba238291b12bb33f3d23310a2\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\572ce6cba238291b12bb33f3d23310a2\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba9ea08de9541b65dd4ab1b547e9b60d\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba9ea08de9541b65dd4ab1b547e9b60d\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f998e930f7cae181b32542910621c7\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f998e930f7cae181b32542910621c7\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\759b6acd1f3bc3a615e39717df962334\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\759b6acd1f3bc3a615e39717df962334\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1635c0b3dfe1dbc32ae4074e1749255b\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1635c0b3dfe1dbc32ae4074e1749255b\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc269eb2a18ceb548d2a5ea95adc8c96\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc269eb2a18ceb548d2a5ea95adc8c96\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7bf19bacce7fd4c141ebe47b25076b8\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7bf19bacce7fd4c141ebe47b25076b8\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99bc7333b09f49c249631ba3577589ba\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99bc7333b09f49c249631ba3577589ba\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5071997fcfc97d02bafc8941f239eee\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5071997fcfc97d02bafc8941f239eee\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e448ed96b6af2b004b3c7ca7af320d3d\transformed\activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e448ed96b6af2b004b3c7ca7af320d3d\transformed\activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f4b32b2d4266b520bc3135d7b2885dc\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f4b32b2d4266b520bc3135d7b2885dc\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63bcf4d35504fd2547407dc074988349\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63bcf4d35504fd2547407dc074988349\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e33bfbc537352276299afc3330dd456\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e33bfbc537352276299afc3330dd456\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\748db3a4e46b24efea46d5e564085cf2\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\748db3a4e46b24efea46d5e564085cf2\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fcfe4ac222496a2880ccbd7dce119031\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fcfe4ac222496a2880ccbd7dce119031\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\549d3916b6809f2845fb5e42208cb6be\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\549d3916b6809f2845fb5e42208cb6be\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6fb973f260208f7e6314e3dee7fad99\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6fb973f260208f7e6314e3dee7fad99\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\473ea2509b1f540a006e124452427516\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\473ea2509b1f540a006e124452427516\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c656b25fa2dc95e881345daed065935\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c656b25fa2dc95e881345daed065935\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff2de5d72fce1ffa11fecc0c6983bab7\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff2de5d72fce1ffa11fecc0c6983bab7\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb516a4a0a9943a281ce6f19605ad596\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb516a4a0a9943a281ce6f19605ad596\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1469d513bd6443e66703587865ac5ade\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1469d513bd6443e66703587865ac5ade\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0b251d390f4afb1f9ca91dbc800110d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0b251d390f4afb1f9ca91dbc800110d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d1983e8f816433307bdd0bbd836e24a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d1983e8f816433307bdd0bbd836e24a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed65d3d7a605f20c30bf5c652dd965e5\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed65d3d7a605f20c30bf5c652dd965e5\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f03f196a6ab2e86824c49d07db5ba34\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f03f196a6ab2e86824c49d07db5ba34\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6896777e83f77c6a440bcb66c31cbaf\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6896777e83f77c6a440bcb66c31cbaf\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e272b663b90dafb232bdf1f1361c364\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e272b663b90dafb232bdf1f1361c364\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d89ef1c21515073ae40a0ac1b12cafa4\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d89ef1c21515073ae40a0ac1b12cafa4\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e87b0a766c58fccb5c3caec65c96631b\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e87b0a766c58fccb5c3caec65c96631b\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1783a6756ba8fb878c7385cec384cd49\transformed\work-runtime-ktx-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1783a6756ba8fb878c7385cec384cd49\transformed\work-runtime-ktx-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eec5b5722953297dab7b2da4ce527235\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eec5b5722953297dab7b2da4ce527235\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a591881580f7036f2574d26b337679f8\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a591881580f7036f2574d26b337679f8\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\feb00527f72d755a5627314bd3f96d19\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\feb00527f72d755a5627314bd3f96d19\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd1523a13beb55e8e76f6d0d33c82918\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd1523a13beb55e8e76f6d0d33c82918\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5670563a54c53bc97f5592427a4620d3\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5670563a54c53bc97f5592427a4620d3\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44ff130bb3a3d63f1a426dff612a1f40\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44ff130bb3a3d63f1a426dff612a1f40\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd483c27f09d2683183fc5f548eeb5d9\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd483c27f09d2683183fc5f548eeb5d9\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f8c3e6828abcbffa3c9f5535dfc64a3\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f8c3e6828abcbffa3c9f5535dfc64a3\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6638fe5cf8f78eefbcd16cdca2d9e5c\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6638fe5cf8f78eefbcd16cdca2d9e5c\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f316befd3963bb027428722e4bcae7f\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f316befd3963bb027428722e4bcae7f\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\572be8aa37747fccc1b25ccc0ad34731\transformed\lifecycle-viewmodel-2.8.6\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\572be8aa37747fccc1b25ccc0ad34731\transformed\lifecycle-viewmodel-2.8.6\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a59630a39ebfd67a70a167ceaa9ba65d\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a59630a39ebfd67a70a167ceaa9ba65d\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb84a08ced79840842eaf49383a58010\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb84a08ced79840842eaf49383a58010\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5fb10182892807adb5a50c0d073a398\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5fb10182892807adb5a50c0d073a398\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a0de01b529fb2bb0e648b53a3676679\transformed\runtime-livedata-1.7.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a0de01b529fb2bb0e648b53a3676679\transformed\runtime-livedata-1.7.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c8d87b123cdc25e1157775c2004abe2\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c8d87b123cdc25e1157775c2004abe2\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a317f68135dc86eb1a707ff08cbd84a8\transformed\lifecycle-livedata-core-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a317f68135dc86eb1a707ff08cbd84a8\transformed\lifecycle-livedata-core-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\770bfe3740ffe9976cba2d0c51b81483\transformed\lifecycle-viewmodel-savedstate-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\770bfe3740ffe9976cba2d0c51b81483\transformed\lifecycle-viewmodel-savedstate-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30b5cfcb00be2cdd572d6c04b1241c09\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30b5cfcb00be2cdd572d6c04b1241c09\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ccabb738105bd78fcd4c70adbea2096\transformed\lifecycle-service-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ccabb738105bd78fcd4c70adbea2096\transformed\lifecycle-service-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aea5573deca53022a416382f3264193f\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aea5573deca53022a416382f3264193f\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca32da0058e2cfd9c70ee1e7c1adb262\transformed\lifecycle-livedata-core-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca32da0058e2cfd9c70ee1e7c1adb262\transformed\lifecycle-livedata-core-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6b877db508dcfb3e7ad410982fcb18a\transformed\lifecycle-livedata-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6b877db508dcfb3e7ad410982fcb18a\transformed\lifecycle-livedata-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d29603c3210a6d0855335cb17a945164\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d29603c3210a6d0855335cb17a945164\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0b737481420e6e821cc1bf6564e28e6\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0b737481420e6e821cc1bf6564e28e6\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2a03bf9a2c91a7cce1ae06827c08ffc\transformed\lifecycle-livedata-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2a03bf9a2c91a7cce1ae06827c08ffc\transformed\lifecycle-livedata-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4a1b7671c535604a38c60aa72e81002c\transformed\lifecycle-viewmodel-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4a1b7671c535604a38c60aa72e81002c\transformed\lifecycle-viewmodel-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3bc0680d46b752b3dba03a568ba1a3\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3bc0680d46b752b3dba03a568ba1a3\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d87d94b5c13a001fa84294ac4c30cec6\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d87d94b5c13a001fa84294ac4c30cec6\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\414e0683e18e9aaca9df3e519609757c\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\414e0683e18e9aaca9df3e519609757c\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fefa0f32ba14f41623918fd0f72addf5\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fefa0f32ba14f41623918fd0f72addf5\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9223f0e1271e59953c421b866dd50ca\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9223f0e1271e59953c421b866dd50ca\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e0672aec5af16978afd7bbd8e460675\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e0672aec5af16978afd7bbd8e460675\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd946245e66d6cf7b58e6eb126b81219\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd946245e66d6cf7b58e6eb126b81219\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1cab4525135b934e909b93124109edf\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1cab4525135b934e909b93124109edf\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd31ab42f6df184811fef9a3c088b0f0\transformed\play-services-measurement-base-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd31ab42f6df184811fef9a3c088b0f0\transformed\play-services-measurement-base-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98a7034e786415bc9e009aca9ccbea7a\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98a7034e786415bc9e009aca9ccbea7a\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.8.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d472de1097adacf621614efcbac1e21\transformed\fragment-1.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d472de1097adacf621614efcbac1e21\transformed\fragment-1.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c44a06b68f01c286a9a7efde56763d5b\transformed\fragment-ktx-1.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c44a06b68f01c286a9a7efde56763d5b\transformed\fragment-ktx-1.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8dccd7121ce23f325500264b1927919\transformed\activity-ktx-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8dccd7121ce23f325500264b1927919\transformed\activity-ktx-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f07e0e8f60f626ea9f27bd033ae12b\transformed\activity-compose-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f07e0e8f60f626ea9f27bd033ae12b\transformed\activity-compose-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d3c9c4ac15ccaa3d4f8bb593bf66c\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d3c9c4ac15ccaa3d4f8bb593bf66c\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e74249777095f16bf90043ea48d86009\transformed\timber-5.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e74249777095f16bf90043ea48d86009\transformed\timber-5.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c2ac540a0dbb432b7e96e8026104ab5\transformed\firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c2ac540a0dbb432b7e96e8026104ab5\transformed\firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e2967fd5b276966c87bd4e88ea401c8\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e2967fd5b276966c87bd4e88ea401c8\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a5ff77ed6e8796335744dd9c5829aa6\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a5ff77ed6e8796335744dd9c5829aa6\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\373d4135f1586ad81ac2cd37e6ac32ee\transformed\googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\373d4135f1586ad81ac2cd37e6ac32ee\transformed\googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b7572c291721d887a8a69c42c05f8fb\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b7572c291721d887a8a69c42c05f8fb\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89a4d9fea819de7bc2d872220c0a8f0f\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89a4d9fea819de7bc2d872220c0a8f0f\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\81e1762d7a12f7128da367a6a2a55145\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\81e1762d7a12f7128da367a6a2a55145\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5798195514dcf9f54facb6622514625d\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5798195514dcf9f54facb6622514625d\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5438095388770c65ceef79c1ed6cdce\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5438095388770c65ceef79c1ed6cdce\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d96accd3e95a849b411f6d40626c55c1\transformed\documentfile-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d96accd3e95a849b411f6d40626c55c1\transformed\documentfile-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee551ec3fd119864ca11d7dccd96f4e7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee551ec3fd119864ca11d7dccd96f4e7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\376a5cf04f9e52a43728211b447c8cba\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\376a5cf04f9e52a43728211b447c8cba\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f43bef631a3a38a9d08e025479944c3e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f43bef631a3a38a9d08e025479944c3e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a23e484f9ab2c8f2fe1c821821b8dd08\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a23e484f9ab2c8f2fe1c821821b8dd08\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d06e38b9254253d89d40561949adceca\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d06e38b9254253d89d40561949adceca\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952b95c172269eee0bea8252aa77f918\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952b95c172269eee0bea8252aa77f918\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f84f39bce60e40271bbffac62df7748d\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f84f39bce60e40271bbffac62df7748d\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26aba51db4309e78491b8b75b9ce4e8c\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26aba51db4309e78491b8b75b9ce4e8c\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c58cd8b4696f8fa5bb0d8d3c36af5768\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c58cd8b4696f8fa5bb0d8d3c36af5768\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7201e597345aa82cd022c99533bbf50\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7201e597345aa82cd022c99533bbf50\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d20d3d5f1f5a05dec5f94ea4a31a1428\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d20d3d5f1f5a05dec5f94ea4a31a1428\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea3d85634f8413586751d19babfb5cfb\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea3d85634f8413586751d19babfb5cfb\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed63fd1d6f0c32c1e430798002671246\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed63fd1d6f0c32c1e430798002671246\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\383f67f12f6be5aabe17a1a6b60ff97c\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\383f67f12f6be5aabe17a1a6b60ff97c\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63d14b553fca4381dcc9cb52bec5050b\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63d14b553fca4381dcc9cb52bec5050b\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b54da9fb788aca02c18c87741f8a5cf\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b54da9fb788aca02c18c87741f8a5cf\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44ad0f2412c7c8a71f80cba0dcd24c9e\transformed\transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44ad0f2412c7c8a71f80cba0dcd24c9e\transformed\transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9891cc273c69fca165fe759e0b5d848a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9891cc273c69fca165fe759e0b5d848a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc83f3f0f89460506f12f9fa8d48059e\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc83f3f0f89460506f12f9fa8d48059e\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d441db952f6ad88c2cd980962bd4c407\transformed\grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d441db952f6ad88c2cd980962bd4c407\transformed\grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [jp.wasabeef:richeditor-android:2.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5b4b351cbe3a8b1d05e0f2d04d73525\transformed\richeditor-android-2.0.0\AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:richeditor-android:2.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5b4b351cbe3a8b1d05e0f2d04d73525\transformed\richeditor-android-2.0.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32aace43ec0d198bec8cbb1cbaf9874c\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32aace43ec0d198bec8cbb1cbaf9874c\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:18:3-42
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\673ec8ce3066443b41d99eb75dbc1193\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\673ec8ce3066443b41d99eb75dbc1193\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed98f692eef56b9ec9255f707f2cecc8\transformed\core-common-2.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed98f692eef56b9ec9255f707f2cecc8\transformed\core-common-2.0.3\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:23:9-148
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
data
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
	android:path
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
	android:host
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
	android:scheme
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:53:13-63:29
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d29eeb71e4b590606a79f795e337f4\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d29eeb71e4b590606a79f795e337f4\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e2967fd5b276966c87bd4e88ea401c8\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e2967fd5b276966c87bd4e88ea401c8\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:68:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:67:13-84
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:70:17-109
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:34:13-89
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:21:17-111
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\506c2d7d88d4acfac211914fc9a152c8\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\506c2d7d88d4acfac211914fc9a152c8\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:23:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1cab4525135b934e909b93124109edf\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1cab4525135b934e909b93124109edf\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:25:22-65
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\506c2d7d88d4acfac211914fc9a152c8\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\506c2d7d88d4acfac211914fc9a152c8\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:40:13-87
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\506c2d7d88d4acfac211914fc9a152c8\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\506c2d7d88d4acfac211914fc9a152c8\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd946245e66d6cf7b58e6eb126b81219\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd946245e66d6cf7b58e6eb126b81219\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1cab4525135b934e909b93124109edf\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1cab4525135b934e909b93124109edf\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1cab4525135b934e909b93124109edf\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1cab4525135b934e909b93124109edf\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1cab4525135b934e909b93124109edf\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1cab4525135b934e909b93124109edf\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:27:22-79
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:38:17-139
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:16:17-126
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:19:17-115
meta-data#com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar
ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:30:13-32:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:32:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:31:17-118
meta-data#com.google.firebase.components:com.google.firebase.storage.StorageRegistrar
ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:34:17-107
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\431ca28934aa0e759e1ba596c21cfdf5\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\431ca28934aa0e759e1ba596c21cfdf5\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:22-95
service#com.google.firebase.sessions.SessionLifecycleService
ADDED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:22:9-25:40
	android:enabled
		ADDED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:24:13-35
	android:exported
		ADDED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:23:13-80
meta-data#com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar
ADDED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:30:17-117
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar
ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar
ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d29eeb71e4b590606a79f795e337f4\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d29eeb71e4b590606a79f795e337f4\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d29eeb71e4b590606a79f795e337f4\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5d37fd89a97cf267377a06b09c52112\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5d37fd89a97cf267377a06b09c52112\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5d37fd89a97cf267377a06b09c52112\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e326cf46a3e0f3591896fb1f0061a989\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e326cf46a3e0f3591896fb1f0061a989\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e326cf46a3e0f3591896fb1f0061a989\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c656b25fa2dc95e881345daed065935\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c656b25fa2dc95e881345daed065935\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c656b25fa2dc95e881345daed065935\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:25:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:26:22-74
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:25-85
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd483c27f09d2683183fc5f548eeb5d9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd483c27f09d2683183fc5f548eeb5d9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd483c27f09d2683183fc5f548eeb5d9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd483c27f09d2683183fc5f548eeb5d9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.allinone.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.allinone.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aea5573deca53022a416382f3264193f\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aea5573deca53022a416382f3264193f\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aea5573deca53022a416382f3264193f\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3bc0680d46b752b3dba03a568ba1a3\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3bc0680d46b752b3dba03a568ba1a3\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3bc0680d46b752b3dba03a568ba1a3\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3bc0680d46b752b3dba03a568ba1a3\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3bc0680d46b752b3dba03a568ba1a3\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e2967fd5b276966c87bd4e88ea401c8\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e2967fd5b276966c87bd4e88ea401c8\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e2967fd5b276966c87bd4e88ea401c8\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed98f692eef56b9ec9255f707f2cecc8\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed98f692eef56b9ec9255f707f2cecc8\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed98f692eef56b9ec9255f707f2cecc8\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed98f692eef56b9ec9255f707f2cecc8\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed98f692eef56b9ec9255f707f2cecc8\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
