{"logs": [{"outputFile": "com.example.allinone.app-mergeReleaseResources-108:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98a7034e786415bc9e009aca9ccbea7a\\transformed\\play-services-basement-18.4.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "5949", "endColumns": "144", "endOffsets": "6089"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\eec5b5722953297dab7b2da4ce527235\\transformed\\browser-1.4.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "70,77,78,79", "startColumns": "4,4,4,4", "startOffsets": "7199,7812,7911,8023", "endColumns": "114,98,111,105", "endOffsets": "7309,7906,8018,8124"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1375e70be1fe8041f38907ceec5f1093\\transformed\\appcompat-1.7.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,205", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,443,549,656,745,846,965,1050,1130,1221,1314,1409,1503,1603,1696,1791,1886,1977,2068,2153,2260,2371,2473,2581,2689,2799,2961,19949", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "438,544,651,740,841,960,1045,1125,1216,1309,1404,1498,1598,1691,1786,1881,1972,2063,2148,2255,2366,2468,2576,2684,2794,2956,3056,20030"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8e199df52e4d067d471a8ec3433b6506\\transformed\\core-1.13.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "40,41,42,43,44,45,46,213", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3719,3816,3918,4017,4117,4224,4334,20580", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3811,3913,4012,4112,4219,4329,4449,20676"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ff95595b5660c73a0c7669f7cd696712\\transformed\\material3-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,295,411,527,629,726,840,974,1092,1244,1328,1429,1524,1624,1739,1869,1975,2114,2250,2381,2547,2674,2794,2918,3038,3134,3231,3351,3467,3567,3678,3787,3927,4072,4182,4285,4371,4465,4557,4647,4736,4837,4917,5001,5102,5208,5300,5399,5487,5599,5700,5804,5923,6003,6103", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "169,290,406,522,624,721,835,969,1087,1239,1323,1424,1519,1619,1734,1864,1970,2109,2245,2376,2542,2669,2789,2913,3033,3129,3226,3346,3462,3562,3673,3782,3922,4067,4177,4280,4366,4460,4552,4642,4731,4832,4912,4996,5097,5203,5295,5394,5482,5594,5695,5799,5918,5998,6098,6190"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8448,8567,8688,8804,8920,9022,9119,9233,9367,9485,9637,9721,9822,9917,10017,10132,10262,10368,10507,10643,10774,10940,11067,11187,11311,11431,11527,11624,11744,11860,11960,12071,12180,12320,12465,12575,12678,12764,12858,12950,13040,13129,13230,13310,13394,13495,13601,13693,13792,13880,13992,14093,14197,14316,14396,14496", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "8562,8683,8799,8915,9017,9114,9228,9362,9480,9632,9716,9817,9912,10012,10127,10257,10363,10502,10638,10769,10935,11062,11182,11306,11426,11522,11619,11739,11855,11955,12066,12175,12315,12460,12570,12673,12759,12853,12945,13035,13124,13225,13305,13389,13490,13596,13688,13787,13875,13987,14088,14192,14311,14391,14491,14583"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37fc1bdcb99e9ca53adfd9dc62ba1cb8\\transformed\\foundation-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,84", "endOffsets": "133,218"}, "to": {"startLines": "217,218", "startColumns": "4,4", "startOffsets": "20937,21020", "endColumns": "82,84", "endOffsets": "21015,21100"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\75ac7f2a23ffbf48dd6409062de1d131\\transformed\\material-1.12.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,432,516,611,700,801,921,1002,1062,1126,1218,1297,1357,1447,1511,1582,1645,1720,1784,1838,1965,2023,2085,2139,2218,2359,2446,2522,2617,2698,2780,2919,3002,3086,3225,3312,3392,3448,3499,3565,3639,3719,3790,3873,3946,4023,4092,4166,4268,4356,4433,4526,4622,4696,4776,4873,4925,5009,5075,5162,5250,5312,5376,5439,5507,5616,5727,5831,5941,6001,6056,6133,6216,6293", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,77,83,94,88,100,119,80,59,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,75,94,80,81,138,82,83,138,86,79,55,50,65,73,79,70,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,108,110,103,109,59,54,76,82,76,78", "endOffsets": "268,349,427,511,606,695,796,916,997,1057,1121,1213,1292,1352,1442,1506,1577,1640,1715,1779,1833,1960,2018,2080,2134,2213,2354,2441,2517,2612,2693,2775,2914,2997,3081,3220,3307,3387,3443,3494,3560,3634,3714,3785,3868,3941,4018,4087,4161,4263,4351,4428,4521,4617,4691,4771,4868,4920,5004,5070,5157,5245,5307,5371,5434,5502,5611,5722,5826,5936,5996,6051,6128,6211,6288,6367"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,74,75,76,80,83,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,202,206,207,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3292,3373,3451,3535,3630,4454,4555,4675,7596,7656,7720,8129,8388,14588,14678,14742,14813,14876,14951,15015,15069,15196,15254,15316,15370,15449,15590,15677,15753,15848,15929,16011,16150,16233,16317,16456,16543,16623,16679,16730,16796,16870,16950,17021,17104,17177,17254,17323,17397,17499,17587,17664,17757,17853,17927,18007,18104,18156,18240,18306,18393,18481,18543,18607,18670,18738,18847,18958,19062,19172,19232,19697,20035,20118,20271", "endLines": "5,35,36,37,38,39,47,48,49,74,75,76,80,83,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,202,206,207,209", "endColumns": "12,80,77,83,94,88,100,119,80,59,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,75,94,80,81,138,82,83,138,86,79,55,50,65,73,79,70,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,108,110,103,109,59,54,76,82,76,78", "endOffsets": "318,3368,3446,3530,3625,3714,4550,4670,4751,7651,7715,7807,8203,8443,14673,14737,14808,14871,14946,15010,15064,15191,15249,15311,15365,15444,15585,15672,15748,15843,15924,16006,16145,16228,16312,16451,16538,16618,16674,16725,16791,16865,16945,17016,17099,17172,17249,17318,17392,17494,17582,17659,17752,17848,17922,18002,18099,18151,18235,18301,18388,18476,18538,18602,18665,18733,18842,18953,19057,19167,19227,19282,19769,20113,20190,20345"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cc3c3a73d52d53a742bae480359958fe\\transformed\\credentials-1.2.0-rc01\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,121", "endOffsets": "159,281"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3061,3170", "endColumns": "108,121", "endOffsets": "3165,3287"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cd483c27f09d2683183fc5f548eeb5d9\\transformed\\play-services-base-18.5.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,442,565,669,832,958,1076,1177,1343,1447,1607,1733,1886,2039,2104,2166", "endColumns": "100,143,122,103,162,125,117,100,165,103,159,125,152,152,64,61,79", "endOffsets": "297,441,564,668,831,957,1075,1176,1342,1446,1606,1732,1885,2038,2103,2165,2245"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4937,5042,5190,5317,5425,5592,5722,5844,6094,6264,6372,6536,6666,6823,6980,7049,7115", "endColumns": "104,147,126,107,166,129,121,104,169,107,163,129,156,156,68,65,83", "endOffsets": "5037,5185,5312,5420,5587,5717,5839,5944,6259,6367,6531,6661,6818,6975,7044,7110,7194"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d7bf19bacce7fd4c141ebe47b25076b8\\transformed\\ui-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,1011,1101,1177,1253,1332,1407,1483,1550", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,75,78,74,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,1006,1096,1172,1248,1327,1402,1478,1545,1658"}, "to": {"startLines": "50,51,71,72,73,81,82,200,201,203,204,208,210,211,212,214,215,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4756,4851,7314,7411,7510,8208,8291,19519,19610,19774,19859,20195,20350,20426,20505,20681,20757,20824", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,75,78,74,75,66,112", "endOffsets": "4846,4932,7406,7505,7591,8286,8383,19605,19692,19854,19944,20266,20421,20500,20575,20752,20819,20932"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e82eb3f2167345ace1b8e91ea59beec2\\transformed\\navigation-ui-2.8.2\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}, "to": {"startLines": "198,199", "startColumns": "4,4", "startOffsets": "19287,19399", "endColumns": "111,119", "endOffsets": "19394,19514"}}]}]}