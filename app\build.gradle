plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'com.google.devtools.ksp'
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    id 'kotlin-parcelize'
    id 'dagger.hilt.android.plugin'
    id 'com.diffplug.spotless'
    id 'androidx.navigation.safeargs.kotlin'
}

// Spotless configuration
spotless {
    kotlin {
        target '**/*.kt'
        ktlint('0.50.0')
        trimTrailingWhitespace()
        endWithNewline()
    }

    enforceCheck false
}

def getEnvValue(String key, String defaultVal = "NOT_SET") {
    def envFile = new File(rootDir, '.env')
    if (envFile.exists()) {
        def props = new Properties()
        envFile.withInputStream { stream ->
            props.load(stream)
        }
        return props.getProperty(key, defaultVal)
    } else {
        return System.getenv(key) ?: defaultVal
    }
}

android {
    namespace 'com.example.allinone'
    compileSdk 34

    defaultConfig {
        applicationId "com.example.allinone"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        
        // Room schema export for database versioning
        javaCompileOptions {
            annotationProcessorOptions {
                arguments += [
                    "room.schemaLocation": "$projectDir/schemas".toString(),
                    "room.incremental": "true"
                ]
            }
        }
    }

    signingConfigs {
        debug {
            storeFile file("../my-release-key.jks")
            storePassword "goktugoner2323"
            keyAlias System.getenv("KEY_ALIAS") ?: "my-alias"
            keyPassword System.getenv("KEY_PASSWORD") ?: "goktugoner2323"
        }
        release {
            storeFile file("../my-release-key.jks")
            storePassword System.getenv("KEYSTORE_PASSWORD") ?: "goktugoner2323"
            keyAlias System.getenv("KEY_ALIAS") ?: "my-alias"
            keyPassword System.getenv("KEY_PASSWORD") ?: "goktugoner2323"
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
            debuggable false
        }
        debug {
            signingConfig signingConfigs.debug
            debuggable true
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
        freeCompilerArgs += [
            "-opt-in=kotlinx.coroutines.ExperimentalCoroutinesApi",
            "-opt-in=kotlinx.coroutines.FlowPreview"
        ]
    }

    buildFeatures {
        viewBinding true
        dataBinding true
        buildConfig true
        compose true
    }

    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.15"
    }

    tasks.withType(JavaCompile) {
        options.compilerArgs << '-Xlint:unchecked'
    }

    tasks.register('cleanBuildDir', Delete) {
        delete layout.buildDirectory
    }

    lint {
        baseline = file("lint-baseline.xml")
        abortOnError = false
    }

    // KSP configuration for Room and Hilt
    ksp {
        arg("correctErrorTypes", "true")
        arg("dagger.hilt.android.internal.disableAndroidSuperclassValidation", "true")
        arg("room.schemaLocation", "$projectDir/schemas")
        arg("room.incremental", "true")
        arg("room.expandProjection", "true")
    }
}

dependencies {
    // Updated to latest stable versions for our new architecture
    def nav_version = "2.8.2"
    def lifecycle_version = "2.8.6"
    def firebase_version = "33.5.1"
    def hilt_version = "2.52"
    def room_version = "2.6.1"
    def coroutines_version = "1.8.1"
    def compose_bom_version = "2024.02.00"

    // Compose BOM - this manages all Compose library versions
    implementation platform("androidx.compose:compose-bom:$compose_bom_version")
    
    // Core Compose libraries
    implementation "androidx.compose.ui:ui"
    implementation "androidx.compose.ui:ui-tooling-preview"
    implementation "androidx.compose.material3:material3"
    implementation "androidx.compose.material:material-icons-extended"
    implementation "androidx.activity:activity-compose:1.9.2"
    
    // Navigation Compose
    implementation "androidx.navigation:navigation-compose:$nav_version"
    
    // ViewModel integration with Compose
    implementation "androidx.lifecycle:lifecycle-viewmodel-compose:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-runtime-compose:$lifecycle_version"
    implementation "androidx.compose.runtime:runtime-livedata"
    
    // Hilt integration with Compose
    implementation "androidx.hilt:hilt-navigation-compose:1.1.0"
    
    // Optional - for charts in Compose (we'll migrate from MPAndroidChart)
    implementation "com.github.PhilJay:MPAndroidChart:v3.1.0"
    
    // For Compose previews in IDE
    debugImplementation "androidx.compose.ui:ui-tooling"
    debugImplementation "androidx.compose.ui:ui-test-manifest"
    
    // Testing for Compose  
    androidTestImplementation platform("androidx.compose:compose-bom:$compose_bom_version")
    androidTestImplementation "androidx.compose.ui:ui-test-junit4"

    // Core AndroidX libraries - Updated to latest
    implementation 'androidx.core:core-ktx:1.13.1'
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'androidx.activity:activity-ktx:1.9.2'
    implementation 'androidx.fragment:fragment-ktx:1.8.4'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.0'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'

    // Core Splash Screen API
    implementation 'androidx.core:core-splashscreen:1.0.1'

    // Google Play Services - Updated
    implementation 'com.google.android.gms:play-services-auth:21.2.0'

    // Firebase - Updated to latest BOM
    implementation platform("com.google.firebase:firebase-bom:$firebase_version")
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-auth'
    implementation 'com.google.firebase:firebase-firestore'
    implementation 'com.google.firebase:firebase-storage'
    implementation 'com.google.firebase:firebase-crashlytics'
    
    // ExoPlayer for video playback
    implementation 'com.google.android.exoplayer:exoplayer:2.19.1'

    // JSON serialization - Updated for our entity JSON storage
    implementation 'com.google.code.gson:gson:2.11.0'

    // ViewPager2 - Updated
    implementation "androidx.viewpager2:viewpager2:1.1.0"

    // Navigation component - Updated for better Fragment navigation
    implementation "androidx.navigation:navigation-fragment-ktx:$nav_version"
    implementation "androidx.navigation:navigation-ui-ktx:$nav_version"

    // Lifecycle - Updated for our Repository pattern
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:$lifecycle_version"

    // Hilt for dependency injection - Updated to latest for better performance
    implementation "com.google.dagger:hilt-android:$hilt_version"
    ksp "com.google.dagger:hilt-android-compiler:$hilt_version"
    
    // Hilt testing dependencies
    testImplementation "com.google.dagger:hilt-android-testing:$hilt_version"
    kspTest "com.google.dagger:hilt-android-compiler:$hilt_version"
    androidTestImplementation "com.google.dagger:hilt-android-testing:$hilt_version"
    kspAndroidTest "com.google.dagger:hilt-android-compiler:$hilt_version"

    // Security for encryption - Updated to latest stable
    implementation "androidx.security:security-crypto:1.1.0-alpha06"

    // Paging for large datasets - Updated
    implementation "androidx.paging:paging-runtime-ktx:3.3.2"

    // Timber for better logging - Updated
    implementation 'com.jakewharton.timber:timber:5.0.1'

    // DocumentFile support for file operations
    implementation 'androidx.documentfile:documentfile:1.0.1'

    // Room database - Core of our new caching architecture
    implementation "androidx.room:room-runtime:$room_version"
    implementation "androidx.room:room-ktx:$room_version"
    ksp "androidx.room:room-compiler:$room_version"
    // Room testing support
    testImplementation "androidx.room:room-testing:$room_version"

    // Coroutines - Updated for our Flow-based reactive architecture
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:$coroutines_version"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$coroutines_version"
    testImplementation "org.jetbrains.kotlinx:kotlinx-coroutines-test:$coroutines_version"

    // Testing dependencies - Updated
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito:mockito-core:5.14.2'
    testImplementation 'org.mockito.kotlin:mockito-kotlin:5.4.0'
    testImplementation 'androidx.arch.core:core-testing:2.2.0'

    // Android testing
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'

    // Image loading and display - Updated
    implementation 'com.github.chrisbanes:PhotoView:2.3.0'
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    ksp 'com.github.bumptech.glide:compiler:4.16.0'

    // Coil for Compose image loading
    implementation 'io.coil-kt:coil-compose:2.5.0'

    // Compose swipe-to-delete functionality
    implementation 'me.saket.swipe:swipe:1.2.0'

    // WorkManager for background tasks - Updated for our sync operations
    implementation "androidx.work:work-runtime-ktx:2.9.1"

    // Charts for data visualization
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'

    // RichEditor for Android - WebView-based WYSIWYG editor
    implementation 'jp.wasabeef:richeditor-android:2.0.0'

    // Networking - Updated for external API integration
    implementation 'com.squareup.retrofit2:retrofit:2.11.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.11.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    
    // OkHttp WebSocket (replaced Java-WebSocket for consistency)
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
}