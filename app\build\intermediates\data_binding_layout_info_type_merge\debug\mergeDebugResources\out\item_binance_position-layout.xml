<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_binance_position" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_binance_position.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_binance_position_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="234" endOffset="35"/></Target><Target id="@+id/symbolText" view="TextView"><Expressions/><location startLine="18" startOffset="8" endLine="27" endOffset="34"/></Target><Target id="@+id/leverageText" view="TextView"><Expressions/><location startLine="29" startOffset="8" endLine="39" endOffset="42"/></Target><Target id="@+id/pnlLabel" view="TextView"><Expressions/><location startLine="42" startOffset="8" endLine="51" endOffset="66"/></Target><Target id="@+id/pnlValue" view="TextView"><Expressions/><location startLine="53" startOffset="8" endLine="64" endOffset="33"/></Target><Target id="@+id/roiLabel" view="TextView"><Expressions/><location startLine="66" startOffset="8" endLine="75" endOffset="61"/></Target><Target id="@+id/roiValue" view="TextView"><Expressions/><location startLine="77" startOffset="8" endLine="88" endOffset="35"/></Target><Target id="@+id/sizeLabel" view="TextView"><Expressions/><location startLine="91" startOffset="8" endLine="100" endOffset="64"/></Target><Target id="@+id/sizeValue" view="TextView"><Expressions/><location startLine="102" startOffset="8" endLine="112" endOffset="36"/></Target><Target id="@+id/marginLabel" view="TextView"><Expressions/><location startLine="114" startOffset="8" endLine="123" endOffset="62"/></Target><Target id="@+id/marginValue" view="TextView"><Expressions/><location startLine="125" startOffset="8" endLine="135" endOffset="37"/></Target><Target id="@+id/entryPriceLabel" view="TextView"><Expressions/><location startLine="138" startOffset="8" endLine="147" endOffset="65"/></Target><Target id="@+id/entryPriceValue" view="TextView"><Expressions/><location startLine="149" startOffset="8" endLine="159" endOffset="34"/></Target><Target id="@+id/markPriceLabel" view="TextView"><Expressions/><location startLine="161" startOffset="8" endLine="171" endOffset="68"/></Target><Target id="@+id/markPriceValue" view="TextView"><Expressions/><location startLine="173" startOffset="8" endLine="183" endOffset="34"/></Target><Target id="@+id/liqPriceLabel" view="TextView"><Expressions/><location startLine="185" startOffset="8" endLine="195" endOffset="67"/></Target><Target id="@+id/liqPriceValue" view="TextView"><Expressions/><location startLine="197" startOffset="8" endLine="207" endOffset="34"/></Target><Target id="@+id/tpslLabel" view="TextView"><Expressions/><location startLine="210" startOffset="8" endLine="219" endOffset="71"/></Target><Target id="@+id/tpslValue" view="TextView"><Expressions/><location startLine="221" startOffset="8" endLine="231" endOffset="39"/></Target></Targets></Layout>