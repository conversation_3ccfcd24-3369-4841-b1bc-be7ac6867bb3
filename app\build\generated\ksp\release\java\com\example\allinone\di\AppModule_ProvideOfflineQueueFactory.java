package com.example.allinone.di;

import android.content.Context;
import com.example.allinone.firebase.OfflineQueue;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class AppModule_ProvideOfflineQueueFactory implements Factory<OfflineQueue> {
  private final Provider<Context> contextProvider;

  public AppModule_ProvideOfflineQueueFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public OfflineQueue get() {
    return provideOfflineQueue(contextProvider.get());
  }

  public static AppModule_ProvideOfflineQueueFactory create(Provider<Context> contextProvider) {
    return new AppModule_ProvideOfflineQueueFactory(contextProvider);
  }

  public static OfflineQueue provideOfflineQueue(Context context) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideOfflineQueue(context));
  }
}
