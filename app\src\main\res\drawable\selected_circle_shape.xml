<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Background circle -->
    <item>
        <shape android:shape="oval">
            <solid android:color="@android:color/transparent" />
        </shape>
    </item>
    
    <!-- Inner colored circle -->
    <item android:inset="4dp">
        <shape android:shape="oval">
            <solid android:color="@android:color/transparent" />
        </shape>
    </item>
    
    <!-- Selection stroke -->
    <item>
        <shape android:shape="oval">
            <stroke
                android:width="3dp"
                android:color="?attr/colorPrimary" />
        </shape>
    </item>
</layer-list> 