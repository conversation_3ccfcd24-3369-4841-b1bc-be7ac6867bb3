<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/dayText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                style="@style/BoldText"
                android:text="Monday" />

            <TextView
                android:id="@+id/timeText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="18:00 - 19:30"
                android:textSize="14sp" />
        </LinearLayout>

        <ImageButton
            android:id="@+id/editButton"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_gravity="center_vertical"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Edit lesson"
            android:src="@android:drawable/ic_menu_edit" />

        <ImageButton
            android:id="@+id/deleteButton"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Delete lesson"
            android:src="@android:drawable/ic_menu_delete"
            app:tint="#FF0000" />

    </LinearLayout>
</androidx.cardview.widget.CardView> 