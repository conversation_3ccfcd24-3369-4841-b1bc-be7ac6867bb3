// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentFuturesTabBinding implements ViewBinding {
  @NonNull
  private final SwipeRefreshLayout rootView;

  @NonNull
  public final TextView balanceTitleText;

  @NonNull
  public final TextView balanceValueText;

  @NonNull
  public final View divider;

  @NonNull
  public final TextView emptyStateText;

  @NonNull
  public final SwipeRefreshLayout futuresSwipeRefreshLayout;

  @NonNull
  public final ProgressBar loadingProgress;

  @NonNull
  public final TextView marginBalanceTitleText;

  @NonNull
  public final TextView marginBalanceValueText;

  @NonNull
  public final TextView pnlTitleText;

  @NonNull
  public final TextView pnlValueText;

  @NonNull
  public final RecyclerView positionsRecyclerView;

  @NonNull
  public final TextView positionsTitle;

  @NonNull
  public final CardView summaryCard;

  private FragmentFuturesTabBinding(@NonNull SwipeRefreshLayout rootView,
      @NonNull TextView balanceTitleText, @NonNull TextView balanceValueText, @NonNull View divider,
      @NonNull TextView emptyStateText, @NonNull SwipeRefreshLayout futuresSwipeRefreshLayout,
      @NonNull ProgressBar loadingProgress, @NonNull TextView marginBalanceTitleText,
      @NonNull TextView marginBalanceValueText, @NonNull TextView pnlTitleText,
      @NonNull TextView pnlValueText, @NonNull RecyclerView positionsRecyclerView,
      @NonNull TextView positionsTitle, @NonNull CardView summaryCard) {
    this.rootView = rootView;
    this.balanceTitleText = balanceTitleText;
    this.balanceValueText = balanceValueText;
    this.divider = divider;
    this.emptyStateText = emptyStateText;
    this.futuresSwipeRefreshLayout = futuresSwipeRefreshLayout;
    this.loadingProgress = loadingProgress;
    this.marginBalanceTitleText = marginBalanceTitleText;
    this.marginBalanceValueText = marginBalanceValueText;
    this.pnlTitleText = pnlTitleText;
    this.pnlValueText = pnlValueText;
    this.positionsRecyclerView = positionsRecyclerView;
    this.positionsTitle = positionsTitle;
    this.summaryCard = summaryCard;
  }

  @Override
  @NonNull
  public SwipeRefreshLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentFuturesTabBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentFuturesTabBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_futures_tab, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentFuturesTabBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.balanceTitleText;
      TextView balanceTitleText = ViewBindings.findChildViewById(rootView, id);
      if (balanceTitleText == null) {
        break missingId;
      }

      id = R.id.balanceValueText;
      TextView balanceValueText = ViewBindings.findChildViewById(rootView, id);
      if (balanceValueText == null) {
        break missingId;
      }

      id = R.id.divider;
      View divider = ViewBindings.findChildViewById(rootView, id);
      if (divider == null) {
        break missingId;
      }

      id = R.id.emptyStateText;
      TextView emptyStateText = ViewBindings.findChildViewById(rootView, id);
      if (emptyStateText == null) {
        break missingId;
      }

      SwipeRefreshLayout futuresSwipeRefreshLayout = (SwipeRefreshLayout) rootView;

      id = R.id.loadingProgress;
      ProgressBar loadingProgress = ViewBindings.findChildViewById(rootView, id);
      if (loadingProgress == null) {
        break missingId;
      }

      id = R.id.marginBalanceTitleText;
      TextView marginBalanceTitleText = ViewBindings.findChildViewById(rootView, id);
      if (marginBalanceTitleText == null) {
        break missingId;
      }

      id = R.id.marginBalanceValueText;
      TextView marginBalanceValueText = ViewBindings.findChildViewById(rootView, id);
      if (marginBalanceValueText == null) {
        break missingId;
      }

      id = R.id.pnlTitleText;
      TextView pnlTitleText = ViewBindings.findChildViewById(rootView, id);
      if (pnlTitleText == null) {
        break missingId;
      }

      id = R.id.pnlValueText;
      TextView pnlValueText = ViewBindings.findChildViewById(rootView, id);
      if (pnlValueText == null) {
        break missingId;
      }

      id = R.id.positionsRecyclerView;
      RecyclerView positionsRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (positionsRecyclerView == null) {
        break missingId;
      }

      id = R.id.positionsTitle;
      TextView positionsTitle = ViewBindings.findChildViewById(rootView, id);
      if (positionsTitle == null) {
        break missingId;
      }

      id = R.id.summaryCard;
      CardView summaryCard = ViewBindings.findChildViewById(rootView, id);
      if (summaryCard == null) {
        break missingId;
      }

      return new FragmentFuturesTabBinding((SwipeRefreshLayout) rootView, balanceTitleText,
          balanceValueText, divider, emptyStateText, futuresSwipeRefreshLayout, loadingProgress,
          marginBalanceTitleText, marginBalanceValueText, pnlTitleText, pnlValueText,
          positionsRecyclerView, positionsTitle, summaryCard);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
