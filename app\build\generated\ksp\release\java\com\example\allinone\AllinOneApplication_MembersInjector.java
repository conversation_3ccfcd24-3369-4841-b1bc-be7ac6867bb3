package com.example.allinone;

import com.example.allinone.cache.CacheManager;
import com.example.allinone.utils.LogcatHelper;
import com.example.allinone.utils.NetworkUtils;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class AllinOneApplication_MembersInjector implements MembersInjector<AllinOneApplication> {
  private final Provider<NetworkUtils> networkUtilsProvider;

  private final Provider<CacheManager> cacheManagerProvider;

  private final Provider<LogcatHelper> logcatHelperProvider;

  public AllinOneApplication_MembersInjector(Provider<NetworkUtils> networkUtilsProvider,
      Provider<CacheManager> cacheManagerProvider, Provider<LogcatHelper> logcatHelperProvider) {
    this.networkUtilsProvider = networkUtilsProvider;
    this.cacheManagerProvider = cacheManagerProvider;
    this.logcatHelperProvider = logcatHelperProvider;
  }

  public static MembersInjector<AllinOneApplication> create(
      Provider<NetworkUtils> networkUtilsProvider, Provider<CacheManager> cacheManagerProvider,
      Provider<LogcatHelper> logcatHelperProvider) {
    return new AllinOneApplication_MembersInjector(networkUtilsProvider, cacheManagerProvider, logcatHelperProvider);
  }

  @Override
  public void injectMembers(AllinOneApplication instance) {
    injectNetworkUtils(instance, networkUtilsProvider.get());
    injectCacheManager(instance, cacheManagerProvider.get());
    injectLogcatHelper(instance, logcatHelperProvider.get());
  }

  @InjectedFieldSignature("com.example.allinone.AllinOneApplication.networkUtils")
  public static void injectNetworkUtils(AllinOneApplication instance, NetworkUtils networkUtils) {
    instance.networkUtils = networkUtils;
  }

  @InjectedFieldSignature("com.example.allinone.AllinOneApplication.cacheManager")
  public static void injectCacheManager(AllinOneApplication instance, CacheManager cacheManager) {
    instance.cacheManager = cacheManager;
  }

  @InjectedFieldSignature("com.example.allinone.AllinOneApplication.logcatHelper")
  public static void injectLogcatHelper(AllinOneApplication instance, LogcatHelper logcatHelper) {
    instance.logcatHelper = logcatHelper;
  }
}
