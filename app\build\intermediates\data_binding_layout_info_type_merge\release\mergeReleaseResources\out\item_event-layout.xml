<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_event" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_event.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_event_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="69" endOffset="35"/></Target><Target id="@+id/eventTime" view="TextView"><Expressions/><location startLine="22" startOffset="12" endLine="28" endOffset="38"/></Target><Target id="@+id/eventDate" view="TextView"><Expressions/><location startLine="30" startOffset="12" endLine="35" endOffset="39"/></Target><Target id="@+id/eventTitle" view="TextView"><Expressions/><location startLine="46" startOffset="12" endLine="52" endOffset="44"/></Target><Target id="@+id/eventTypeTag" view="TextView"><Expressions/><location startLine="54" startOffset="12" endLine="66" endOffset="38"/></Target></Targets></Layout>