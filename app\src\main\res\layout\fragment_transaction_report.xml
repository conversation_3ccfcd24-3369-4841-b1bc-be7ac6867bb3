<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Filter Options Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/filterCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Filter Options"
                    android:textSize="18sp"
                    android:textColor="@color/black"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp"/>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/dateRangeLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Date Range">

                    <AutoCompleteTextView
                        android:id="@+id/dateRangeAutoComplete"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none"
                        android:text="Last 30 Days"/>
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/categoryLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Category">

                    <AutoCompleteTextView
                        android:id="@+id/categoryAutoComplete"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none"
                        android:text="All Categories"/>
                </com.google.android.material.textfield.TextInputLayout>

                <Button
                    android:id="@+id/applyFiltersButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="16dp"
                    android:text="Apply"
                    android:textAllCaps="false"
                    app:cornerRadius="20dp"/>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Summary Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/summaryCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:layout_constraintTop_toBottomOf="@id/filterCard"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Summary"
                    android:textSize="18sp"
                    android:textColor="@color/black"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp"/>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Income"
                            android:textSize="14sp"
                            android:textColor="@color/gray_dark"/>

                        <TextView
                            android:id="@+id/totalIncomeText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="₺0.00"
                            android:textColor="@color/green"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:layout_marginTop="4dp"/>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Expense"
                            android:textSize="14sp"
                            android:textColor="@color/gray_dark"/>

                        <TextView
                            android:id="@+id/totalExpenseText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="₺0.00"
                            android:textColor="@color/red"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:layout_marginTop="4dp"/>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Balance"
                            android:textSize="14sp"
                            android:textColor="@color/gray_dark"/>

                        <TextView
                            android:id="@+id/balanceText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="₺0.00"
                            android:textColor="@color/navy_accent"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:layout_marginTop="4dp"/>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Spending by Category Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/categorySpendingCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:layout_constraintTop_toBottomOf="@id/summaryCard"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Spending by Category"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:layout_marginBottom="12dp"/>

                <com.github.mikephil.charting.charts.PieChart
                    android:id="@+id/categoryPieChart"
                    android:layout_width="match_parent"
                    android:layout_height="200dp"/>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/topCategoriesRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:nestedScrollingEnabled="false"/>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Transaction Insights Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/insightsCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:layout_constraintTop_toBottomOf="@id/categorySpendingCard"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Transaction Insights"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:layout_marginBottom="12dp"/>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Largest Expense:"
                            android:textSize="14sp"
                            android:textColor="@color/gray_dark"/>

                        <TextView
                            android:id="@+id/largestExpenseText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/red"
                            tools:text="₺1,250.00"/>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Most Spent Category:"
                            android:textSize="14sp"
                            android:textColor="@color/gray_dark"/>

                        <TextView
                            android:id="@+id/mostSpentCategoryText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/black"
                            tools:text="Food (₺850.00)"/>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Average Transaction:"
                            android:textSize="14sp"
                            android:textColor="@color/gray_dark"/>

                        <TextView
                            android:id="@+id/averageTransactionText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/black"
                            tools:text="₺125.00"/>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Transaction Count:"
                            android:textSize="14sp"
                            android:textColor="@color/gray_dark"/>

                        <TextView
                            android:id="@+id/transactionCountText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/black"
                            tools:text="24 transactions"/>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Cash Flow Chart Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/chartCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:layout_constraintTop_toBottomOf="@id/insightsCard"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:id="@+id/cashFlowTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Monthly Cash Flow"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:layout_marginBottom="12dp"/>

                <com.github.mikephil.charting.charts.LineChart
                    android:id="@+id/lineChart"
                    android:layout_width="match_parent"
                    android:layout_height="200dp"/>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>