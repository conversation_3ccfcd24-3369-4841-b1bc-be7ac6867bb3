<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_expense_investment" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\dialog_expense_investment.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/dialog_expense_investment_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="75" endOffset="51"/></Target><Target id="@+id/titleText" view="TextView"><Expressions/><location startLine="7" startOffset="4" endLine="17" endOffset="51"/></Target><Target id="@+id/investmentDropdownLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="19" startOffset="4" endLine="35" endOffset="59"/></Target><Target id="@+id/investmentDropdown" view="AutoCompleteTextView"><Expressions/><location startLine="30" startOffset="8" endLine="34" endOffset="38"/></Target><Target id="@+id/emptyStateText" view="TextView"><Expressions/><location startLine="37" startOffset="4" endLine="47" endOffset="51"/></Target><Target id="@+id/cancelButton" view="Button"><Expressions/><location startLine="59" startOffset="8" endLine="65" endOffset="44"/></Target><Target id="@+id/newInvestmentButton" view="Button"><Expressions/><location startLine="67" startOffset="8" endLine="72" endOffset="43"/></Target></Targets></Layout>