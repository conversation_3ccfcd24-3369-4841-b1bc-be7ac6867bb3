<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_category_summary" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_category_summary.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/item_category_summary_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="27" endOffset="51"/></Target><Target id="@+id/categoryText" view="TextView"><Expressions/><location startLine="7" startOffset="4" endLine="15" endOffset="51"/></Target><Target id="@+id/amountText" view="TextView"><Expressions/><location startLine="17" startOffset="4" endLine="25" endOffset="51"/></Target></Targets></Layout>