// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.ProgressBar;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemNoteVideoBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageButton deleteButton;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final ImageView videoThumbnail;

  private ItemNoteVideoBinding(@NonNull CardView rootView, @NonNull ImageButton deleteButton,
      @NonNull ProgressBar progressBar, @NonNull ImageView videoThumbnail) {
    this.rootView = rootView;
    this.deleteButton = deleteButton;
    this.progressBar = progressBar;
    this.videoThumbnail = videoThumbnail;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemNoteVideoBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemNoteVideoBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_note_video, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemNoteVideoBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.deleteButton;
      ImageButton deleteButton = ViewBindings.findChildViewById(rootView, id);
      if (deleteButton == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.videoThumbnail;
      ImageView videoThumbnail = ViewBindings.findChildViewById(rootView, id);
      if (videoThumbnail == null) {
        break missingId;
      }

      return new ItemNoteVideoBinding((CardView) rootView, deleteButton, progressBar,
          videoThumbnail);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
