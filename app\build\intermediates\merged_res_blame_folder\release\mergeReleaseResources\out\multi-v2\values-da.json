{"logs": [{"outputFile": "com.example.allinone.app-mergeReleaseResources-108:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37fc1bdcb99e9ca53adfd9dc62ba1cb8\\transformed\\foundation-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,86", "endOffsets": "140,227"}, "to": {"startLines": "285,286", "startColumns": "4,4", "startOffsets": "24761,24851", "endColumns": "89,86", "endOffsets": "24846,24933"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8e199df52e4d067d471a8ec3433b6506\\transformed\\core-1.13.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "57,58,59,60,61,62,63,281", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4226,4322,4424,4521,4619,4726,4835,24395", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "4317,4419,4516,4614,4721,4830,4948,24491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e82eb3f2167345ace1b8e91ea59beec2\\transformed\\navigation-ui-2.8.2\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,160", "endColumns": "104,108", "endOffsets": "155,264"}, "to": {"startLines": "266,267", "startColumns": "4,4", "startOffsets": "23163,23268", "endColumns": "104,108", "endOffsets": "23263,23372"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\eec5b5722953297dab7b2da4ce527235\\transformed\\browser-1.4.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,266,373", "endColumns": "111,98,106,96", "endOffsets": "162,261,368,465"}, "to": {"startLines": "87,145,146,147", "startColumns": "4,4,4,4", "startOffsets": "7654,12001,12100,12207", "endColumns": "111,98,106,96", "endOffsets": "7761,12095,12202,12299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cd483c27f09d2683183fc5f548eeb5d9\\transformed\\play-services-base-18.5.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,574,679,818,939,1055,1156,1308,1410,1568,1691,1832,2006,2068,2126", "endColumns": "101,155,122,104,138,120,115,100,151,101,157,122,140,173,61,57,73", "endOffsets": "294,450,573,678,817,938,1054,1155,1307,1409,1567,1690,1831,2005,2067,2125,2199"}, "to": {"startLines": "69,70,71,72,73,74,75,76,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5452,5558,5718,5845,5954,6097,6222,6342,6574,6730,6836,6998,7125,7270,7448,7514,7576", "endColumns": "105,159,126,108,142,124,119,104,155,105,161,126,144,177,65,61,77", "endOffsets": "5553,5713,5840,5949,6092,6217,6337,6442,6725,6831,6993,7120,7265,7443,7509,7571,7649"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e87b0a766c58fccb5c3caec65c96631b\\transformed\\exoplayer-core-2.19.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,189,253,322,399,473,573,664", "endColumns": "71,61,63,68,76,73,99,90,67", "endOffsets": "122,184,248,317,394,468,568,659,727"}, "to": {"startLines": "117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10094,10166,10228,10292,10361,10438,10512,10612,10703", "endColumns": "71,61,63,68,76,73,99,90,67", "endOffsets": "10161,10223,10287,10356,10433,10507,10607,10698,10766"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f9ce903e2ff26f2c616d9362ffafb64\\transformed\\exoplayer-ui-2.19.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,474,664,750,837,912,997,1084,1155,1219,1317,1413,1485,1550,1616,1686,1793,1900,2005,2078,2158,2234,2303,2382,2462,2525,2593,2646,2704,2752,2813,2883,2955,3023,3097,3161,3220,3284,3354,3420,3472,3533,3609,3684", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,74,84,86,70,63,97,95,71,64,65,69,106,106,104,72,79,75,68,78,79,62,67,52,57,47,60,69,71,67,73,63,58,63,69,65,51,60,75,74,52", "endOffsets": "280,469,659,745,832,907,992,1079,1150,1214,1312,1408,1480,1545,1611,1681,1788,1895,2000,2073,2153,2229,2298,2377,2457,2520,2588,2641,2699,2747,2808,2878,2950,3018,3092,3156,3215,3279,3349,3415,3467,3528,3604,3679,3732"}, "to": {"startLines": "2,11,15,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,569,8165,8251,8338,8413,8498,8585,8656,8720,8818,8914,8986,9051,9117,9187,9294,9401,9506,9579,9659,9735,9804,9883,9963,10026,10771,10824,10882,10930,10991,11061,11133,11201,11275,11339,11398,11462,11532,11598,11650,11711,11787,11862", "endLines": "10,14,18,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143", "endColumns": "17,12,12,85,86,74,84,86,70,63,97,95,71,64,65,69,106,106,104,72,79,75,68,78,79,62,67,52,57,47,60,69,71,67,73,63,58,63,69,65,51,60,75,74,52", "endOffsets": "375,564,754,8246,8333,8408,8493,8580,8651,8715,8813,8909,8981,9046,9112,9182,9289,9396,9501,9574,9654,9730,9799,9878,9958,10021,10089,10819,10877,10925,10986,11056,11128,11196,11270,11334,11393,11457,11527,11593,11645,11706,11782,11857,11910"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d7bf19bacce7fd4c141ebe47b25076b8\\transformed\\ui-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,277,372,471,553,630,719,808,890,971,1055,1125,1199,1271,1342,1420,1487", "endColumns": "91,79,94,98,81,76,88,88,81,80,83,69,73,71,70,77,66,119", "endOffsets": "192,272,367,466,548,625,714,803,885,966,1050,1120,1194,1266,1337,1415,1482,1602"}, "to": {"startLines": "67,68,88,89,90,149,150,268,269,271,272,276,278,279,280,282,283,284", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5280,5372,7766,7861,7960,12377,12454,23377,23466,23628,23709,24031,24178,24252,24324,24496,24574,24641", "endColumns": "91,79,94,98,81,76,88,88,81,80,83,69,73,71,70,77,66,119", "endOffsets": "5367,5447,7856,7955,8037,12449,12538,23461,23543,23704,23788,24096,24247,24319,24390,24569,24636,24756"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cc3c3a73d52d53a742bae480359958fe\\transformed\\credentials-1.2.0-rc01\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,118", "endOffsets": "165,284"}, "to": {"startLines": "50,51", "startColumns": "4,4", "startOffsets": "3583,3698", "endColumns": "114,118", "endOffsets": "3693,3812"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ff95595b5660c73a0c7669f7cd696712\\transformed\\material3-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,284,391,505,605,700,812,956,1078,1227,1311,1411,1500,1594,1708,1826,1931,2056,2176,2312,2485,2615,2732,2854,2973,3063,3161,3280,3416,3514,3632,3734,3860,3993,4098,4196,4276,4369,4462,4546,4631,4732,4812,4896,4997,5096,5191,5291,5378,5483,5585,5690,5807,5887,5989", "endColumns": "115,112,106,113,99,94,111,143,121,148,83,99,88,93,113,117,104,124,119,135,172,129,116,121,118,89,97,118,135,97,117,101,125,132,104,97,79,92,92,83,84,100,79,83,100,98,94,99,86,104,101,104,116,79,101,98", "endOffsets": "166,279,386,500,600,695,807,951,1073,1222,1306,1406,1495,1589,1703,1821,1926,2051,2171,2307,2480,2610,2727,2849,2968,3058,3156,3275,3411,3509,3627,3729,3855,3988,4093,4191,4271,4364,4457,4541,4626,4727,4807,4891,4992,5091,5186,5286,5373,5478,5580,5685,5802,5882,5984,6083"}, "to": {"startLines": "152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12603,12719,12832,12939,13053,13153,13248,13360,13504,13626,13775,13859,13959,14048,14142,14256,14374,14479,14604,14724,14860,15033,15163,15280,15402,15521,15611,15709,15828,15964,16062,16180,16282,16408,16541,16646,16744,16824,16917,17010,17094,17179,17280,17360,17444,17545,17644,17739,17839,17926,18031,18133,18238,18355,18435,18537", "endColumns": "115,112,106,113,99,94,111,143,121,148,83,99,88,93,113,117,104,124,119,135,172,129,116,121,118,89,97,118,135,97,117,101,125,132,104,97,79,92,92,83,84,100,79,83,100,98,94,99,86,104,101,104,116,79,101,98", "endOffsets": "12714,12827,12934,13048,13148,13243,13355,13499,13621,13770,13854,13954,14043,14137,14251,14369,14474,14599,14719,14855,15028,15158,15275,15397,15516,15606,15704,15823,15959,16057,16175,16277,16403,16536,16641,16739,16819,16912,17005,17089,17174,17275,17355,17439,17540,17639,17734,17834,17921,18026,18128,18233,18350,18430,18532,18631"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1375e70be1fe8041f38907ceec5f1093\\transformed\\appcompat-1.7.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,273", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "931,1031,1125,1241,1326,1426,1539,1617,1693,1784,1877,1970,2064,2158,2251,2346,2444,2535,2626,2705,2813,2920,3016,3129,3232,3333,3486,23793", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "1026,1120,1236,1321,1421,1534,1612,1688,1779,1872,1965,2059,2153,2246,2341,2439,2530,2621,2700,2808,2915,3011,3124,3227,3328,3481,3578,23868"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\75ac7f2a23ffbf48dd6409062de1d131\\transformed\\material-1.12.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,350,426,504,601,681,781,930,1008,1067,1131,1217,1290,1350,1437,1501,1563,1625,1693,1758,1812,1930,1988,2049,2105,2180,2306,2392,2469,2560,2644,2724,2865,2943,3023,3145,3231,3309,3365,3416,3482,3550,3624,3695,3770,3842,3920,3990,4063,4167,4251,4328,4416,4505,4579,4652,4737,4786,4864,4930,5010,5093,5155,5219,5282,5351,5459,5562,5663,5762,5822,5877,5957,6037,6115", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,77,96,79,99,148,77,58,63,85,72,59,86,63,61,61,67,64,53,117,57,60,55,74,125,85,76,90,83,79,140,77,79,121,85,77,55,50,65,67,73,70,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79,79,77,76", "endOffsets": "267,345,421,499,596,676,776,925,1003,1062,1126,1212,1285,1345,1432,1496,1558,1620,1688,1753,1807,1925,1983,2044,2100,2175,2301,2387,2464,2555,2639,2719,2860,2938,3018,3140,3226,3304,3360,3411,3477,3545,3619,3690,3765,3837,3915,3985,4058,4162,4246,4323,4411,4500,4574,4647,4732,4781,4859,4925,5005,5088,5150,5214,5277,5346,5454,5557,5658,5757,5817,5872,5952,6032,6110,6187"}, "to": {"startLines": "19,52,53,54,55,56,64,65,66,91,92,144,148,151,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,270,274,275,277", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "759,3817,3895,3971,4049,4146,4953,5053,5202,8042,8101,11915,12304,12543,18636,18723,18787,18849,18911,18979,19044,19098,19216,19274,19335,19391,19466,19592,19678,19755,19846,19930,20010,20151,20229,20309,20431,20517,20595,20651,20702,20768,20836,20910,20981,21056,21128,21206,21276,21349,21453,21537,21614,21702,21791,21865,21938,22023,22072,22150,22216,22296,22379,22441,22505,22568,22637,22745,22848,22949,23048,23108,23548,23873,23953,24101", "endLines": "22,52,53,54,55,56,64,65,66,91,92,144,148,151,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,270,274,275,277", "endColumns": "12,77,75,77,96,79,99,148,77,58,63,85,72,59,86,63,61,61,67,64,53,117,57,60,55,74,125,85,76,90,83,79,140,77,79,121,85,77,55,50,65,67,73,70,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79,79,77,76", "endOffsets": "926,3890,3966,4044,4141,4221,5048,5197,5275,8096,8160,11996,12372,12598,18718,18782,18844,18906,18974,19039,19093,19211,19269,19330,19386,19461,19587,19673,19750,19841,19925,20005,20146,20224,20304,20426,20512,20590,20646,20697,20763,20831,20905,20976,21051,21123,21201,21271,21344,21448,21532,21609,21697,21786,21860,21933,22018,22067,22145,22211,22291,22374,22436,22500,22563,22632,22740,22843,22944,23043,23103,23158,23623,23948,24026,24173"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98a7034e786415bc9e009aca9ccbea7a\\transformed\\play-services-basement-18.4.0\\res\\values-da\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "122", "endOffsets": "317"}, "to": {"startLines": "77", "startColumns": "4", "startOffsets": "6447", "endColumns": "126", "endOffsets": "6569"}}]}]}