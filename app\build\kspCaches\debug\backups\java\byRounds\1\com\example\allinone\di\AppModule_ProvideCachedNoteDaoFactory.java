package com.example.allinone.di;

import com.example.allinone.data.local.AppDatabase;
import com.example.allinone.data.local.dao.CachedNoteDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class AppModule_ProvideCachedNoteDaoFactory implements Factory<CachedNoteDao> {
  private final Provider<AppDatabase> databaseProvider;

  public AppModule_ProvideCachedNoteDaoFactory(Provider<AppDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public CachedNoteDao get() {
    return provideCachedNoteDao(databaseProvider.get());
  }

  public static AppModule_ProvideCachedNoteDaoFactory create(
      Provider<AppDatabase> databaseProvider) {
    return new AppModule_ProvideCachedNoteDaoFactory(databaseProvider);
  }

  public static CachedNoteDao provideCachedNoteDao(AppDatabase database) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideCachedNoteDao(database));
  }
}
