<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_calendar" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\fragment_calendar.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_calendar_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="529" endOffset="51"/></Target><Target id="@+id/calendarHeader" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="11" startOffset="4" endLine="53" endOffset="55"/></Target><Target id="@+id/prevMonthButton" view="ImageButton"><Expressions/><location startLine="19" startOffset="8" endLine="28" endOffset="55"/></Target><Target id="@+id/monthYearText" view="TextView"><Expressions/><location startLine="30" startOffset="8" endLine="40" endOffset="55"/></Target><Target id="@+id/nextMonthButton" view="ImageButton"><Expressions/><location startLine="42" startOffset="8" endLine="51" endOffset="55"/></Target><Target id="@+id/weekDaysHeader" view="LinearLayout"><Expressions/><location startLine="56" startOffset="4" endLine="120" endOffset="18"/></Target><Target id="@+id/week1" view="LinearLayout"><Expressions/><location startLine="123" startOffset="4" endLine="180" endOffset="18"/></Target><Target id="@+id/day1" view="TextView"><Expressions/><location startLine="131" startOffset="8" endLine="136" endOffset="39"/></Target><Target id="@+id/day2" view="TextView"><Expressions/><location startLine="138" startOffset="8" endLine="143" endOffset="39"/></Target><Target id="@+id/day3" view="TextView"><Expressions/><location startLine="145" startOffset="8" endLine="150" endOffset="39"/></Target><Target id="@+id/day4" view="TextView"><Expressions/><location startLine="152" startOffset="8" endLine="157" endOffset="39"/></Target><Target id="@+id/day5" view="TextView"><Expressions/><location startLine="159" startOffset="8" endLine="164" endOffset="39"/></Target><Target id="@+id/day6" view="TextView"><Expressions/><location startLine="166" startOffset="8" endLine="171" endOffset="39"/></Target><Target id="@+id/day7" view="TextView"><Expressions/><location startLine="173" startOffset="8" endLine="178" endOffset="39"/></Target><Target id="@+id/week2" view="LinearLayout"><Expressions/><location startLine="183" startOffset="4" endLine="240" endOffset="18"/></Target><Target id="@+id/day8" view="TextView"><Expressions/><location startLine="191" startOffset="8" endLine="196" endOffset="39"/></Target><Target id="@+id/day9" view="TextView"><Expressions/><location startLine="198" startOffset="8" endLine="203" endOffset="39"/></Target><Target id="@+id/day10" view="TextView"><Expressions/><location startLine="205" startOffset="8" endLine="210" endOffset="39"/></Target><Target id="@+id/day11" view="TextView"><Expressions/><location startLine="212" startOffset="8" endLine="217" endOffset="39"/></Target><Target id="@+id/day12" view="TextView"><Expressions/><location startLine="219" startOffset="8" endLine="224" endOffset="39"/></Target><Target id="@+id/day13" view="TextView"><Expressions/><location startLine="226" startOffset="8" endLine="231" endOffset="39"/></Target><Target id="@+id/day14" view="TextView"><Expressions/><location startLine="233" startOffset="8" endLine="238" endOffset="39"/></Target><Target id="@+id/week3" view="LinearLayout"><Expressions/><location startLine="243" startOffset="4" endLine="300" endOffset="18"/></Target><Target id="@+id/day15" view="TextView"><Expressions/><location startLine="251" startOffset="8" endLine="256" endOffset="39"/></Target><Target id="@+id/day16" view="TextView"><Expressions/><location startLine="258" startOffset="8" endLine="263" endOffset="39"/></Target><Target id="@+id/day17" view="TextView"><Expressions/><location startLine="265" startOffset="8" endLine="270" endOffset="39"/></Target><Target id="@+id/day18" view="TextView"><Expressions/><location startLine="272" startOffset="8" endLine="277" endOffset="39"/></Target><Target id="@+id/day19" view="TextView"><Expressions/><location startLine="279" startOffset="8" endLine="284" endOffset="39"/></Target><Target id="@+id/day20" view="TextView"><Expressions/><location startLine="286" startOffset="8" endLine="291" endOffset="39"/></Target><Target id="@+id/day21" view="TextView"><Expressions/><location startLine="293" startOffset="8" endLine="298" endOffset="39"/></Target><Target id="@+id/week4" view="LinearLayout"><Expressions/><location startLine="303" startOffset="4" endLine="360" endOffset="18"/></Target><Target id="@+id/day22" view="TextView"><Expressions/><location startLine="311" startOffset="8" endLine="316" endOffset="39"/></Target><Target id="@+id/day23" view="TextView"><Expressions/><location startLine="318" startOffset="8" endLine="323" endOffset="39"/></Target><Target id="@+id/day24" view="TextView"><Expressions/><location startLine="325" startOffset="8" endLine="330" endOffset="39"/></Target><Target id="@+id/day25" view="TextView"><Expressions/><location startLine="332" startOffset="8" endLine="337" endOffset="39"/></Target><Target id="@+id/day26" view="TextView"><Expressions/><location startLine="339" startOffset="8" endLine="344" endOffset="39"/></Target><Target id="@+id/day27" view="TextView"><Expressions/><location startLine="346" startOffset="8" endLine="351" endOffset="39"/></Target><Target id="@+id/day28" view="TextView"><Expressions/><location startLine="353" startOffset="8" endLine="358" endOffset="39"/></Target><Target id="@+id/week5" view="LinearLayout"><Expressions/><location startLine="363" startOffset="4" endLine="420" endOffset="18"/></Target><Target id="@+id/day29" view="TextView"><Expressions/><location startLine="371" startOffset="8" endLine="376" endOffset="39"/></Target><Target id="@+id/day30" view="TextView"><Expressions/><location startLine="378" startOffset="8" endLine="383" endOffset="39"/></Target><Target id="@+id/day31" view="TextView"><Expressions/><location startLine="385" startOffset="8" endLine="390" endOffset="39"/></Target><Target id="@+id/day32" view="TextView"><Expressions/><location startLine="392" startOffset="8" endLine="397" endOffset="39"/></Target><Target id="@+id/day33" view="TextView"><Expressions/><location startLine="399" startOffset="8" endLine="404" endOffset="39"/></Target><Target id="@+id/day34" view="TextView"><Expressions/><location startLine="406" startOffset="8" endLine="411" endOffset="39"/></Target><Target id="@+id/day35" view="TextView"><Expressions/><location startLine="413" startOffset="8" endLine="418" endOffset="39"/></Target><Target id="@+id/week6" view="LinearLayout"><Expressions/><location startLine="423" startOffset="4" endLine="480" endOffset="18"/></Target><Target id="@+id/day36" view="TextView"><Expressions/><location startLine="431" startOffset="8" endLine="436" endOffset="39"/></Target><Target id="@+id/day37" view="TextView"><Expressions/><location startLine="438" startOffset="8" endLine="443" endOffset="39"/></Target><Target id="@+id/day38" view="TextView"><Expressions/><location startLine="445" startOffset="8" endLine="450" endOffset="39"/></Target><Target id="@+id/day39" view="TextView"><Expressions/><location startLine="452" startOffset="8" endLine="457" endOffset="39"/></Target><Target id="@+id/day40" view="TextView"><Expressions/><location startLine="459" startOffset="8" endLine="464" endOffset="39"/></Target><Target id="@+id/day41" view="TextView"><Expressions/><location startLine="466" startOffset="8" endLine="471" endOffset="39"/></Target><Target id="@+id/day42" view="TextView"><Expressions/><location startLine="473" startOffset="8" endLine="478" endOffset="39"/></Target><Target id="@+id/infoText" view="TextView"><Expressions/><location startLine="483" startOffset="4" endLine="491" endOffset="57"/></Target><Target id="@+id/eventsHeader" view="TextView"><Expressions/><location startLine="494" startOffset="4" endLine="503" endOffset="60"/></Target><Target id="@+id/eventsRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="506" startOffset="4" endLine="515" endOffset="38"/></Target><Target id="@+id/emptyEventsText" view="TextView"><Expressions/><location startLine="518" startOffset="4" endLine="527" endOffset="64"/></Target></Targets></Layout>