// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.imageview.ShapeableImageView;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAddStudentBinding implements ViewBinding {
  @NonNull
  private final NestedScrollView rootView;

  @NonNull
  public final SwitchMaterial activeSwitch;

  @NonNull
  public final Button cancelButton;

  @NonNull
  public final TextInputEditText emailEditText;

  @NonNull
  public final TextInputLayout emailInputLayout;

  @NonNull
  public final TextInputEditText instagramEditText;

  @NonNull
  public final TextInputLayout instagramInputLayout;

  @NonNull
  public final TextInputEditText nameEditText;

  @NonNull
  public final TextInputLayout nameInputLayout;

  @NonNull
  public final TextInputEditText phoneEditText;

  @NonNull
  public final TextInputLayout phoneInputLayout;

  @NonNull
  public final ShapeableImageView profileImageView;

  @NonNull
  public final Button saveButton;

  private DialogAddStudentBinding(@NonNull NestedScrollView rootView,
      @NonNull SwitchMaterial activeSwitch, @NonNull Button cancelButton,
      @NonNull TextInputEditText emailEditText, @NonNull TextInputLayout emailInputLayout,
      @NonNull TextInputEditText instagramEditText, @NonNull TextInputLayout instagramInputLayout,
      @NonNull TextInputEditText nameEditText, @NonNull TextInputLayout nameInputLayout,
      @NonNull TextInputEditText phoneEditText, @NonNull TextInputLayout phoneInputLayout,
      @NonNull ShapeableImageView profileImageView, @NonNull Button saveButton) {
    this.rootView = rootView;
    this.activeSwitch = activeSwitch;
    this.cancelButton = cancelButton;
    this.emailEditText = emailEditText;
    this.emailInputLayout = emailInputLayout;
    this.instagramEditText = instagramEditText;
    this.instagramInputLayout = instagramInputLayout;
    this.nameEditText = nameEditText;
    this.nameInputLayout = nameInputLayout;
    this.phoneEditText = phoneEditText;
    this.phoneInputLayout = phoneInputLayout;
    this.profileImageView = profileImageView;
    this.saveButton = saveButton;
  }

  @Override
  @NonNull
  public NestedScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAddStudentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAddStudentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_add_student, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAddStudentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.activeSwitch;
      SwitchMaterial activeSwitch = ViewBindings.findChildViewById(rootView, id);
      if (activeSwitch == null) {
        break missingId;
      }

      id = R.id.cancelButton;
      Button cancelButton = ViewBindings.findChildViewById(rootView, id);
      if (cancelButton == null) {
        break missingId;
      }

      id = R.id.emailEditText;
      TextInputEditText emailEditText = ViewBindings.findChildViewById(rootView, id);
      if (emailEditText == null) {
        break missingId;
      }

      id = R.id.emailInputLayout;
      TextInputLayout emailInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (emailInputLayout == null) {
        break missingId;
      }

      id = R.id.instagramEditText;
      TextInputEditText instagramEditText = ViewBindings.findChildViewById(rootView, id);
      if (instagramEditText == null) {
        break missingId;
      }

      id = R.id.instagramInputLayout;
      TextInputLayout instagramInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (instagramInputLayout == null) {
        break missingId;
      }

      id = R.id.nameEditText;
      TextInputEditText nameEditText = ViewBindings.findChildViewById(rootView, id);
      if (nameEditText == null) {
        break missingId;
      }

      id = R.id.nameInputLayout;
      TextInputLayout nameInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (nameInputLayout == null) {
        break missingId;
      }

      id = R.id.phoneEditText;
      TextInputEditText phoneEditText = ViewBindings.findChildViewById(rootView, id);
      if (phoneEditText == null) {
        break missingId;
      }

      id = R.id.phoneInputLayout;
      TextInputLayout phoneInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (phoneInputLayout == null) {
        break missingId;
      }

      id = R.id.profileImageView;
      ShapeableImageView profileImageView = ViewBindings.findChildViewById(rootView, id);
      if (profileImageView == null) {
        break missingId;
      }

      id = R.id.saveButton;
      Button saveButton = ViewBindings.findChildViewById(rootView, id);
      if (saveButton == null) {
        break missingId;
      }

      return new DialogAddStudentBinding((NestedScrollView) rootView, activeSwitch, cancelButton,
          emailEditText, emailInputLayout, instagramEditText, instagramInputLayout, nameEditText,
          nameInputLayout, phoneEditText, phoneInputLayout, profileImageView, saveButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
