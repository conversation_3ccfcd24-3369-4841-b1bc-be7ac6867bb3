// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemCategoryDropdownBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView categoryIcon;

  @NonNull
  public final TextView categoryName;

  private ItemCategoryDropdownBinding(@NonNull LinearLayout rootView,
      @NonNull ImageView categoryIcon, @NonNull TextView categoryName) {
    this.rootView = rootView;
    this.categoryIcon = categoryIcon;
    this.categoryName = categoryName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemCategoryDropdownBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemCategoryDropdownBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_category_dropdown, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemCategoryDropdownBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.categoryIcon;
      ImageView categoryIcon = ViewBindings.findChildViewById(rootView, id);
      if (categoryIcon == null) {
        break missingId;
      }

      id = R.id.categoryName;
      TextView categoryName = ViewBindings.findChildViewById(rootView, id);
      if (categoryName == null) {
        break missingId;
      }

      return new ItemCategoryDropdownBinding((LinearLayout) rootView, categoryIcon, categoryName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
