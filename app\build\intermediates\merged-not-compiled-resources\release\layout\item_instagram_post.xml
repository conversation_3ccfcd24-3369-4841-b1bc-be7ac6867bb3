<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="8dp"
    android:layout_marginTop="8dp"
    android:layout_marginEnd="8dp"
    android:layout_marginBottom="4dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:strokeColor="@color/cardStroke"
    app:strokeWidth="1dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <TextView
            android:id="@+id/postType"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="#FFFFFF"
            android:paddingStart="8dp"
            android:paddingTop="2dp"
            android:paddingEnd="8dp"
            android:paddingBottom="2dp"
            android:textColor="@android:color/black"
            android:textSize="12sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="REELS" />
            
        <ImageView
            android:id="@+id/postThumbnail"
            android:layout_width="match_parent"
            android:layout_height="160dp"
            android:layout_marginTop="8dp"
            android:scaleType="centerCrop"
            android:background="#E0E0E0"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/postType" />

        <TextView
            android:id="@+id/postCaption"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/postThumbnail"
            tools:text="This is a sample caption for an Instagram post" />

        <TextView
            android:id="@+id/postDate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:alpha="0.7"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/postCaption"
            tools:text="Apr 08, 2023" />

        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="12dp"
            android:alpha="0.2"
            android:background="@android:color/darker_gray"
            app:layout_constraintTop_toBottomOf="@+id/postDate" />

        <TextView
            android:id="@+id/insightsLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="Insights"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/divider" />

        <TextView
            android:id="@+id/postInsights"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textSize="14sp"
            android:lineSpacingExtra="4dp"
            android:paddingBottom="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/insightsLabel"
            tools:text="👁️ Views: 731\n❤️ Likes: 22\n💬 Comments: 0\n🔖 Saved: 1" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView> 