// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTaskGroupHeaderBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final View colorIndicator;

  @NonNull
  public final ImageView expandIcon;

  @NonNull
  public final TextView groupTitle;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView taskCount;

  private ItemTaskGroupHeaderBinding(@NonNull LinearLayout rootView, @NonNull View colorIndicator,
      @NonNull ImageView expandIcon, @NonNull TextView groupTitle, @NonNull ProgressBar progressBar,
      @NonNull TextView taskCount) {
    this.rootView = rootView;
    this.colorIndicator = colorIndicator;
    this.expandIcon = expandIcon;
    this.groupTitle = groupTitle;
    this.progressBar = progressBar;
    this.taskCount = taskCount;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTaskGroupHeaderBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTaskGroupHeaderBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_task_group_header, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTaskGroupHeaderBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.colorIndicator;
      View colorIndicator = ViewBindings.findChildViewById(rootView, id);
      if (colorIndicator == null) {
        break missingId;
      }

      id = R.id.expandIcon;
      ImageView expandIcon = ViewBindings.findChildViewById(rootView, id);
      if (expandIcon == null) {
        break missingId;
      }

      id = R.id.groupTitle;
      TextView groupTitle = ViewBindings.findChildViewById(rootView, id);
      if (groupTitle == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.taskCount;
      TextView taskCount = ViewBindings.findChildViewById(rootView, id);
      if (taskCount == null) {
        break missingId;
      }

      return new ItemTaskGroupHeaderBinding((LinearLayout) rootView, colorIndicator, expandIcon,
          groupTitle, progressBar, taskCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
