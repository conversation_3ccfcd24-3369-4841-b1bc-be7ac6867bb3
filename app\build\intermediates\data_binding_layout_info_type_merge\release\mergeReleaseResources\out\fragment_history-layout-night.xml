<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_history" modulePackage="com.example.allinone" filePath="app\src\main\res\layout-night\fragment_history.xml" directory="layout-night" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout-night/fragment_history_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="109" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="50" endOffset="39"/></Target><Target id="@+id/menu_button" view="ImageButton"><Expressions/><location startLine="23" startOffset="12" endLine="33" endOffset="41"/></Target><Target id="@+id/toolbar_title" view="TextView"><Expressions/><location startLine="35" startOffset="12" endLine="46" endOffset="59"/></Target><Target id="@+id/swipe_refresh" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="52" startOffset="4" endLine="69" endOffset="59"/></Target><Target id="@+id/recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="61" startOffset="8" endLine="67" endOffset="51"/></Target><Target id="@+id/empty_state" view="LinearLayout"><Expressions/><location startLine="71" startOffset="4" endLine="107" endOffset="18"/></Target></Targets></Layout>