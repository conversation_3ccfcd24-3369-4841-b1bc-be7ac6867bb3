<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_active_workout" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\fragment_active_workout.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_active_workout_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="112" endOffset="51"/></Target><Target id="@+id/workout_name_title" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="15" endOffset="41"/></Target><Target id="@+id/timer_card" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="17" startOffset="4" endLine="92" endOffset="55"/></Target><Target id="@+id/timer_text" view="TextView"><Expressions/><location startLine="47" startOffset="16" endLine="54" endOffset="49"/></Target><Target id="@+id/timer_ms_text" view="TextView"><Expressions/><location startLine="56" startOffset="16" endLine="65" endOffset="49"/></Target><Target id="@+id/play_pause_button" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="75" startOffset="16" endLine="81" endOffset="51"/></Target><Target id="@+id/stop_button" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="83" startOffset="16" endLine="88" endOffset="50"/></Target><Target id="@+id/exercises_title" view="TextView"><Expressions/><location startLine="94" startOffset="4" endLine="101" endOffset="62"/></Target><Target id="@+id/exercises_recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="103" startOffset="4" endLine="110" endOffset="56"/></Target></Targets></Layout>