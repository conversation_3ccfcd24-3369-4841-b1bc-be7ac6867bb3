<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_wt_students" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\fragment_wt_students.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_wt_students_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="87" endOffset="51"/></Target><Target id="@+id/offline_status_view" tag="layout/fragment_wt_students_0" include="offline_status_view"><Expressions/><location startLine="7" startOffset="4" endLine="12" endOffset="51"/></Target><Target id="@+id/swipe_refresh_layout" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="14" startOffset="4" endLine="28" endOffset="59"/></Target><Target id="@+id/studentsRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="23" startOffset="8" endLine="26" endOffset="50"/></Target><Target id="@+id/emptyState" view="LinearLayout"><Expressions/><location startLine="30" startOffset="4" endLine="63" endOffset="18"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="66" startOffset="4" endLine="75" endOffset="51"/></Target><Target id="@+id/addStudentFab" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="77" startOffset="4" endLine="85" endOffset="51"/></Target></Targets></Layout>