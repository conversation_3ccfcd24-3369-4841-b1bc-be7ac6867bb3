<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_category_spending" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_category_spending.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_category_spending_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="43" endOffset="14"/></Target><Target id="@+id/categoryColorIndicator" view="View"><Expressions/><location startLine="9" startOffset="4" endLine="15" endOffset="39"/></Target><Target id="@+id/categoryNameText" view="TextView"><Expressions/><location startLine="17" startOffset="4" endLine="24" endOffset="26"/></Target><Target id="@+id/categoryAmountText" view="TextView"><Expressions/><location startLine="26" startOffset="4" endLine="33" endOffset="29"/></Target><Target id="@+id/categoryPercentText" view="TextView"><Expressions/><location startLine="35" startOffset="4" endLine="42" endOffset="25"/></Target></Targets></Layout>