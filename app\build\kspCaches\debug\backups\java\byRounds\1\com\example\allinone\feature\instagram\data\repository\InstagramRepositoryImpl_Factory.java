package com.example.allinone.feature.instagram.data.repository;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class InstagramRepositoryImpl_Factory implements Factory<InstagramRepositoryImpl> {
  @Override
  public InstagramRepositoryImpl get() {
    return newInstance();
  }

  public static InstagramRepositoryImpl_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static InstagramRepositoryImpl newInstance() {
    return new InstagramRepositoryImpl();
  }

  private static final class InstanceHolder {
    private static final InstagramRepositoryImpl_Factory INSTANCE = new InstagramRepositoryImpl_Factory();
  }
}
