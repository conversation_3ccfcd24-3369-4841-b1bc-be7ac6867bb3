<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_investments" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\fragment_investments.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_investments_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="39" endOffset="51"/></Target><Target id="@+id/investmentTabLayout" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="9" startOffset="4" endLine="17" endOffset="31"/></Target><Target id="@+id/investmentViewPager" view="androidx.viewpager2.widget.ViewPager2"><Expressions/><location startLine="20" startOffset="4" endLine="27" endOffset="51"/></Target><Target id="@+id/addInvestmentButton" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="29" startOffset="4" endLine="37" endOffset="51"/></Target></Targets></Layout>