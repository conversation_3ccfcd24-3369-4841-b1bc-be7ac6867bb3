<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_history" modulePackage="com.example.allinone" filePath="app\src\main\res\layout-night\item_history.xml" directory="layout-night" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout-night/item_history_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="92" endOffset="51"/></Target><Target id="@+id/type_icon" view="ImageView"><Expressions/><location startLine="16" startOffset="8" endLine="23" endOffset="43"/></Target><Target id="@+id/title_text" view="TextView"><Expressions/><location startLine="25" startOffset="8" endLine="39" endOffset="37"/></Target><Target id="@+id/description_text" view="TextView"><Expressions/><location startLine="41" startOffset="8" endLine="55" endOffset="112"/></Target><Target id="@+id/date_text" view="TextView"><Expressions/><location startLine="57" startOffset="8" endLine="67" endOffset="39"/></Target><Target id="@+id/amount_text" view="TextView"><Expressions/><location startLine="69" startOffset="8" endLine="78" endOffset="34"/></Target><Target id="@+id/delete_button" view="ImageButton"><Expressions/><location startLine="80" startOffset="8" endLine="88" endOffset="55"/></Target></Targets></Layout>