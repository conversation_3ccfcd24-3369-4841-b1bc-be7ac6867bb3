package com.example.allinone.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.allinone.data.local.entities.CachedNoteEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class CachedNoteDao_Impl implements CachedNoteDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<CachedNoteEntity> __insertionAdapterOfCachedNoteEntity;

  private final EntityDeletionOrUpdateAdapter<CachedNoteEntity> __deletionAdapterOfCachedNoteEntity;

  private final EntityDeletionOrUpdateAdapter<CachedNoteEntity> __updateAdapterOfCachedNoteEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteNoteById;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllNotes;

  private final SharedSQLiteStatement __preparedStmtOfDeleteExpiredNotes;

  public CachedNoteDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfCachedNoteEntity = new EntityInsertionAdapter<CachedNoteEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `cached_notes` (`id`,`title`,`content`,`date`,`imageUris`,`videoUris`,`voiceNoteUris`,`lastEdited`,`isRichText`,`cachedAt`) VALUES (?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CachedNoteEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindString(2, entity.getTitle());
        statement.bindString(3, entity.getContent());
        statement.bindLong(4, entity.getDate());
        if (entity.getImageUris() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getImageUris());
        }
        if (entity.getVideoUris() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getVideoUris());
        }
        if (entity.getVoiceNoteUris() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getVoiceNoteUris());
        }
        statement.bindLong(8, entity.getLastEdited());
        final int _tmp = entity.isRichText() ? 1 : 0;
        statement.bindLong(9, _tmp);
        statement.bindLong(10, entity.getCachedAt());
      }
    };
    this.__deletionAdapterOfCachedNoteEntity = new EntityDeletionOrUpdateAdapter<CachedNoteEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `cached_notes` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CachedNoteEntity entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfCachedNoteEntity = new EntityDeletionOrUpdateAdapter<CachedNoteEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `cached_notes` SET `id` = ?,`title` = ?,`content` = ?,`date` = ?,`imageUris` = ?,`videoUris` = ?,`voiceNoteUris` = ?,`lastEdited` = ?,`isRichText` = ?,`cachedAt` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CachedNoteEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindString(2, entity.getTitle());
        statement.bindString(3, entity.getContent());
        statement.bindLong(4, entity.getDate());
        if (entity.getImageUris() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getImageUris());
        }
        if (entity.getVideoUris() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getVideoUris());
        }
        if (entity.getVoiceNoteUris() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getVoiceNoteUris());
        }
        statement.bindLong(8, entity.getLastEdited());
        final int _tmp = entity.isRichText() ? 1 : 0;
        statement.bindLong(9, _tmp);
        statement.bindLong(10, entity.getCachedAt());
        statement.bindLong(11, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteNoteById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM cached_notes WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllNotes = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM cached_notes";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteExpiredNotes = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM cached_notes WHERE cachedAt < ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertNote(final CachedNoteEntity note,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfCachedNoteEntity.insert(note);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertAllNotes(final List<CachedNoteEntity> notes,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfCachedNoteEntity.insert(notes);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteNote(final CachedNoteEntity note,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfCachedNoteEntity.handle(note);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateNote(final CachedNoteEntity note,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfCachedNoteEntity.handle(note);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteNoteById(final long id, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteNoteById.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteNoteById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllNotes(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllNotes.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllNotes.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteExpiredNotes(final long expiredTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteExpiredNotes.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, expiredTime);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteExpiredNotes.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<CachedNoteEntity>> getAllNotes() {
    final String _sql = "SELECT `cached_notes`.`id` AS `id`, `cached_notes`.`title` AS `title`, `cached_notes`.`content` AS `content`, `cached_notes`.`date` AS `date`, `cached_notes`.`imageUris` AS `imageUris`, `cached_notes`.`videoUris` AS `videoUris`, `cached_notes`.`voiceNoteUris` AS `voiceNoteUris`, `cached_notes`.`lastEdited` AS `lastEdited`, `cached_notes`.`isRichText` AS `isRichText`, `cached_notes`.`cachedAt` AS `cachedAt` FROM cached_notes ORDER BY lastEdited DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cached_notes"}, new Callable<List<CachedNoteEntity>>() {
      @Override
      @NonNull
      public List<CachedNoteEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfTitle = 1;
          final int _cursorIndexOfContent = 2;
          final int _cursorIndexOfDate = 3;
          final int _cursorIndexOfImageUris = 4;
          final int _cursorIndexOfVideoUris = 5;
          final int _cursorIndexOfVoiceNoteUris = 6;
          final int _cursorIndexOfLastEdited = 7;
          final int _cursorIndexOfIsRichText = 8;
          final int _cursorIndexOfCachedAt = 9;
          final List<CachedNoteEntity> _result = new ArrayList<CachedNoteEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CachedNoteEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpContent;
            _tmpContent = _cursor.getString(_cursorIndexOfContent);
            final long _tmpDate;
            _tmpDate = _cursor.getLong(_cursorIndexOfDate);
            final String _tmpImageUris;
            if (_cursor.isNull(_cursorIndexOfImageUris)) {
              _tmpImageUris = null;
            } else {
              _tmpImageUris = _cursor.getString(_cursorIndexOfImageUris);
            }
            final String _tmpVideoUris;
            if (_cursor.isNull(_cursorIndexOfVideoUris)) {
              _tmpVideoUris = null;
            } else {
              _tmpVideoUris = _cursor.getString(_cursorIndexOfVideoUris);
            }
            final String _tmpVoiceNoteUris;
            if (_cursor.isNull(_cursorIndexOfVoiceNoteUris)) {
              _tmpVoiceNoteUris = null;
            } else {
              _tmpVoiceNoteUris = _cursor.getString(_cursorIndexOfVoiceNoteUris);
            }
            final long _tmpLastEdited;
            _tmpLastEdited = _cursor.getLong(_cursorIndexOfLastEdited);
            final boolean _tmpIsRichText;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsRichText);
            _tmpIsRichText = _tmp != 0;
            final long _tmpCachedAt;
            _tmpCachedAt = _cursor.getLong(_cursorIndexOfCachedAt);
            _item = new CachedNoteEntity(_tmpId,_tmpTitle,_tmpContent,_tmpDate,_tmpImageUris,_tmpVideoUris,_tmpVoiceNoteUris,_tmpLastEdited,_tmpIsRichText,_tmpCachedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getNoteById(final long id,
      final Continuation<? super CachedNoteEntity> $completion) {
    final String _sql = "SELECT * FROM cached_notes WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<CachedNoteEntity>() {
      @Override
      @Nullable
      public CachedNoteEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfContent = CursorUtil.getColumnIndexOrThrow(_cursor, "content");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfImageUris = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUris");
          final int _cursorIndexOfVideoUris = CursorUtil.getColumnIndexOrThrow(_cursor, "videoUris");
          final int _cursorIndexOfVoiceNoteUris = CursorUtil.getColumnIndexOrThrow(_cursor, "voiceNoteUris");
          final int _cursorIndexOfLastEdited = CursorUtil.getColumnIndexOrThrow(_cursor, "lastEdited");
          final int _cursorIndexOfIsRichText = CursorUtil.getColumnIndexOrThrow(_cursor, "isRichText");
          final int _cursorIndexOfCachedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "cachedAt");
          final CachedNoteEntity _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpContent;
            _tmpContent = _cursor.getString(_cursorIndexOfContent);
            final long _tmpDate;
            _tmpDate = _cursor.getLong(_cursorIndexOfDate);
            final String _tmpImageUris;
            if (_cursor.isNull(_cursorIndexOfImageUris)) {
              _tmpImageUris = null;
            } else {
              _tmpImageUris = _cursor.getString(_cursorIndexOfImageUris);
            }
            final String _tmpVideoUris;
            if (_cursor.isNull(_cursorIndexOfVideoUris)) {
              _tmpVideoUris = null;
            } else {
              _tmpVideoUris = _cursor.getString(_cursorIndexOfVideoUris);
            }
            final String _tmpVoiceNoteUris;
            if (_cursor.isNull(_cursorIndexOfVoiceNoteUris)) {
              _tmpVoiceNoteUris = null;
            } else {
              _tmpVoiceNoteUris = _cursor.getString(_cursorIndexOfVoiceNoteUris);
            }
            final long _tmpLastEdited;
            _tmpLastEdited = _cursor.getLong(_cursorIndexOfLastEdited);
            final boolean _tmpIsRichText;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsRichText);
            _tmpIsRichText = _tmp != 0;
            final long _tmpCachedAt;
            _tmpCachedAt = _cursor.getLong(_cursorIndexOfCachedAt);
            _result = new CachedNoteEntity(_tmpId,_tmpTitle,_tmpContent,_tmpDate,_tmpImageUris,_tmpVideoUris,_tmpVoiceNoteUris,_tmpLastEdited,_tmpIsRichText,_tmpCachedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<CachedNoteEntity>> searchNotes(final String searchQuery) {
    final String _sql = "SELECT * FROM cached_notes WHERE title LIKE '%' || ? || '%' OR content LIKE '%' || ? || '%' ORDER BY lastEdited DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, searchQuery);
    _argIndex = 2;
    _statement.bindString(_argIndex, searchQuery);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cached_notes"}, new Callable<List<CachedNoteEntity>>() {
      @Override
      @NonNull
      public List<CachedNoteEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfContent = CursorUtil.getColumnIndexOrThrow(_cursor, "content");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfImageUris = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUris");
          final int _cursorIndexOfVideoUris = CursorUtil.getColumnIndexOrThrow(_cursor, "videoUris");
          final int _cursorIndexOfVoiceNoteUris = CursorUtil.getColumnIndexOrThrow(_cursor, "voiceNoteUris");
          final int _cursorIndexOfLastEdited = CursorUtil.getColumnIndexOrThrow(_cursor, "lastEdited");
          final int _cursorIndexOfIsRichText = CursorUtil.getColumnIndexOrThrow(_cursor, "isRichText");
          final int _cursorIndexOfCachedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "cachedAt");
          final List<CachedNoteEntity> _result = new ArrayList<CachedNoteEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CachedNoteEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpContent;
            _tmpContent = _cursor.getString(_cursorIndexOfContent);
            final long _tmpDate;
            _tmpDate = _cursor.getLong(_cursorIndexOfDate);
            final String _tmpImageUris;
            if (_cursor.isNull(_cursorIndexOfImageUris)) {
              _tmpImageUris = null;
            } else {
              _tmpImageUris = _cursor.getString(_cursorIndexOfImageUris);
            }
            final String _tmpVideoUris;
            if (_cursor.isNull(_cursorIndexOfVideoUris)) {
              _tmpVideoUris = null;
            } else {
              _tmpVideoUris = _cursor.getString(_cursorIndexOfVideoUris);
            }
            final String _tmpVoiceNoteUris;
            if (_cursor.isNull(_cursorIndexOfVoiceNoteUris)) {
              _tmpVoiceNoteUris = null;
            } else {
              _tmpVoiceNoteUris = _cursor.getString(_cursorIndexOfVoiceNoteUris);
            }
            final long _tmpLastEdited;
            _tmpLastEdited = _cursor.getLong(_cursorIndexOfLastEdited);
            final boolean _tmpIsRichText;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsRichText);
            _tmpIsRichText = _tmp != 0;
            final long _tmpCachedAt;
            _tmpCachedAt = _cursor.getLong(_cursorIndexOfCachedAt);
            _item = new CachedNoteEntity(_tmpId,_tmpTitle,_tmpContent,_tmpDate,_tmpImageUris,_tmpVideoUris,_tmpVoiceNoteUris,_tmpLastEdited,_tmpIsRichText,_tmpCachedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CachedNoteEntity>> getNotesByType(final boolean isRichText) {
    final String _sql = "SELECT * FROM cached_notes WHERE isRichText = ? ORDER BY lastEdited DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final int _tmp = isRichText ? 1 : 0;
    _statement.bindLong(_argIndex, _tmp);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cached_notes"}, new Callable<List<CachedNoteEntity>>() {
      @Override
      @NonNull
      public List<CachedNoteEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfContent = CursorUtil.getColumnIndexOrThrow(_cursor, "content");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfImageUris = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUris");
          final int _cursorIndexOfVideoUris = CursorUtil.getColumnIndexOrThrow(_cursor, "videoUris");
          final int _cursorIndexOfVoiceNoteUris = CursorUtil.getColumnIndexOrThrow(_cursor, "voiceNoteUris");
          final int _cursorIndexOfLastEdited = CursorUtil.getColumnIndexOrThrow(_cursor, "lastEdited");
          final int _cursorIndexOfIsRichText = CursorUtil.getColumnIndexOrThrow(_cursor, "isRichText");
          final int _cursorIndexOfCachedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "cachedAt");
          final List<CachedNoteEntity> _result = new ArrayList<CachedNoteEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CachedNoteEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpContent;
            _tmpContent = _cursor.getString(_cursorIndexOfContent);
            final long _tmpDate;
            _tmpDate = _cursor.getLong(_cursorIndexOfDate);
            final String _tmpImageUris;
            if (_cursor.isNull(_cursorIndexOfImageUris)) {
              _tmpImageUris = null;
            } else {
              _tmpImageUris = _cursor.getString(_cursorIndexOfImageUris);
            }
            final String _tmpVideoUris;
            if (_cursor.isNull(_cursorIndexOfVideoUris)) {
              _tmpVideoUris = null;
            } else {
              _tmpVideoUris = _cursor.getString(_cursorIndexOfVideoUris);
            }
            final String _tmpVoiceNoteUris;
            if (_cursor.isNull(_cursorIndexOfVoiceNoteUris)) {
              _tmpVoiceNoteUris = null;
            } else {
              _tmpVoiceNoteUris = _cursor.getString(_cursorIndexOfVoiceNoteUris);
            }
            final long _tmpLastEdited;
            _tmpLastEdited = _cursor.getLong(_cursorIndexOfLastEdited);
            final boolean _tmpIsRichText;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsRichText);
            _tmpIsRichText = _tmp_1 != 0;
            final long _tmpCachedAt;
            _tmpCachedAt = _cursor.getLong(_cursorIndexOfCachedAt);
            _item = new CachedNoteEntity(_tmpId,_tmpTitle,_tmpContent,_tmpDate,_tmpImageUris,_tmpVideoUris,_tmpVoiceNoteUris,_tmpLastEdited,_tmpIsRichText,_tmpCachedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getNoteCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM cached_notes";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<CachedNoteEntity>> getRecentlyEditedNotes(final long since) {
    final String _sql = "SELECT * FROM cached_notes WHERE lastEdited >= ? ORDER BY lastEdited DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, since);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cached_notes"}, new Callable<List<CachedNoteEntity>>() {
      @Override
      @NonNull
      public List<CachedNoteEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfContent = CursorUtil.getColumnIndexOrThrow(_cursor, "content");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfImageUris = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUris");
          final int _cursorIndexOfVideoUris = CursorUtil.getColumnIndexOrThrow(_cursor, "videoUris");
          final int _cursorIndexOfVoiceNoteUris = CursorUtil.getColumnIndexOrThrow(_cursor, "voiceNoteUris");
          final int _cursorIndexOfLastEdited = CursorUtil.getColumnIndexOrThrow(_cursor, "lastEdited");
          final int _cursorIndexOfIsRichText = CursorUtil.getColumnIndexOrThrow(_cursor, "isRichText");
          final int _cursorIndexOfCachedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "cachedAt");
          final List<CachedNoteEntity> _result = new ArrayList<CachedNoteEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CachedNoteEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpContent;
            _tmpContent = _cursor.getString(_cursorIndexOfContent);
            final long _tmpDate;
            _tmpDate = _cursor.getLong(_cursorIndexOfDate);
            final String _tmpImageUris;
            if (_cursor.isNull(_cursorIndexOfImageUris)) {
              _tmpImageUris = null;
            } else {
              _tmpImageUris = _cursor.getString(_cursorIndexOfImageUris);
            }
            final String _tmpVideoUris;
            if (_cursor.isNull(_cursorIndexOfVideoUris)) {
              _tmpVideoUris = null;
            } else {
              _tmpVideoUris = _cursor.getString(_cursorIndexOfVideoUris);
            }
            final String _tmpVoiceNoteUris;
            if (_cursor.isNull(_cursorIndexOfVoiceNoteUris)) {
              _tmpVoiceNoteUris = null;
            } else {
              _tmpVoiceNoteUris = _cursor.getString(_cursorIndexOfVoiceNoteUris);
            }
            final long _tmpLastEdited;
            _tmpLastEdited = _cursor.getLong(_cursorIndexOfLastEdited);
            final boolean _tmpIsRichText;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsRichText);
            _tmpIsRichText = _tmp != 0;
            final long _tmpCachedAt;
            _tmpCachedAt = _cursor.getLong(_cursorIndexOfCachedAt);
            _item = new CachedNoteEntity(_tmpId,_tmpTitle,_tmpContent,_tmpDate,_tmpImageUris,_tmpVideoUris,_tmpVoiceNoteUris,_tmpLastEdited,_tmpIsRichText,_tmpCachedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
