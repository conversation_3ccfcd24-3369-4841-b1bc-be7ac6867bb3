#Sun Jul 13 10:08:04 TRT 2025
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_ic_indeterminate.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_ic_indeterminate.xml.flat
com.example.allinone.app-credentials-1.2.0-rc01-69\:/drawable-mdpi-v4/ic_passkey.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_ic_passkey.png.flat
com.example.allinone.app-core-1.13.1-51\:/drawable/notification_bg.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_notification_bg.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_ic_forward.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_forward.png.flat
com.example.allinone.app-credentials-1.2.0-rc01-69\:/drawable-mdpi-v4/ic_other_sign_in.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_ic_other_sign_in.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_icon_play.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_play.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_popup_background_mtrl_mult.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_popup_background_mtrl_mult.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_on_primary_disabled.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_on_primary_disabled.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_checkbox_button_icon_indeterminate_unchecked.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_checkbox_button_icon_indeterminate_unchecked.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_ref_palette_dynamic_neutral6.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_ref_palette_dynamic_neutral6.xml.flat
com.example.allinone.app-main-112\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_ic_chevron_right.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_chevron_right.png.flat
com.example.allinone.app-navigation-ui-2.8.2-87\:/anim/nav_default_pop_exit_anim.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_nav_default_pop_exit_anim.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_ic_pause_circle_filled.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_pause_circle_filled.png.flat
com.example.allinone.app-material-1.12.0-41\:/animator-v21/m3_appbar_state_list_animator.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator-v21_m3_appbar_state_list_animator.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_slider_active_track_color_legacy.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_slider_active_track_color_legacy.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_reports.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_reports.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/btn_checkbox_to_unchecked_box_inner_merged_animation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_checkbox_to_unchecked_box_inner_merged_animation.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_text_select_handle_left_mtrl.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_text_select_handle_left_mtrl.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_icon_play.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_play.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_list_pressed_holo_dark.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_list_pressed_holo_dark.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_checkbox_button_icon_unchecked_checked.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_checkbox_button_icon_unchecked_checked.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_tabs_ripple_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_tabs_ripple_color.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_text_select_handle_left_mtrl.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_text_select_handle_left_mtrl.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_textfield_search_default_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_textfield_search_default_mtrl_alpha.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_action_bar_up_container.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_action_bar_up_container.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_btn_switch_to_on_mtrl_00001.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_btn_switch_to_on_mtrl_00001.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/animator/m3_extended_fab_show_motion_spec.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_m3_extended_fab_show_motion_spec.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_checkbox_button_icon_checked_unchecked.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_checkbox_button_icon_checked_unchecked.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_ic_default_album_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_default_album_image.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_category_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_category_investment.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_text_button_ripple_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_text_button_ripple_color_selector.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_view_list.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_view_list.xml.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable/common_google_signin_btn_text_dark_normal.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_common_google_signin_btn_text_dark_normal.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_ref_palette_dynamic_neutral_variant87.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_ref_palette_dynamic_neutral_variant87.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_tonal_button_ripple_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_tonal_button_ripple_color_selector.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_list_pressed_holo_dark.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_list_pressed_holo_dark.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_filled_background_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_filled_background_color.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-night-v8/material_timepicker_clockface.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-night-v8_material_timepicker_clockface.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_timepicker_time_input_stroke_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_timepicker_time_input_stroke_color.xml.flat
com.example.allinone.app-main-112\:/drawable/simple_text_splash.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_simple_text_splash.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_slider_active_tick_marks_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_slider_active_tick_marks_color.xml.flat
com.example.allinone.app-media-1.6.0-80\:/layout/notification_template_big_media_narrow.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notification_template_big_media_narrow.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/btn_radio_to_off_mtrl_ring_outer_animation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_radio_to_off_mtrl_ring_outer_animation.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_chip_background_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_chip_background_color.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_ic_fullscreen_enter.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_fullscreen_enter.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_icon_play.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_play.png.flat
com.example.allinone.app-main-112\:/drawable/ic_database.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_database.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-hdpi-v4/ic_call_decline_low.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_ic_call_decline_low.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_ic_settings.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_settings.png.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable/common_google_signin_btn_text_disabled.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_common_google_signin_btn_text_disabled.xml.flat
com.example.allinone.app-material-1.12.0-41\:/anim/design_snackbar_in.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_design_snackbar_in.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_ic_chevron_right.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_chevron_right.png.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/dialog_student_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_student_details.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_icon_repeat_one.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_repeat_one.png.flat
com.example.allinone.app-credentials-1.2.0-rc01-69\:/drawable-xxxhdpi-v4/ic_passkey.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_ic_passkey.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxxhdpi-v4/abc_text_select_handle_left_mtrl.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_text_select_handle_left_mtrl.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_ic_ab_back_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ic_ab_back_material.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_list_menu_item_checkbox.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_list_menu_item_checkbox.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/interpolator/fast_out_slow_in.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator_fast_out_slow_in.xml.flat
com.example.allinone.app-material-1.12.0-41\:/interpolator-v21/mtrl_fast_out_linear_in.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator-v21_mtrl_fast_out_linear_in.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_textfield_default_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_textfield_default_mtrl_alpha.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_slider_thumb_color_legacy.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_slider_thumb_color_legacy.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_attach_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_attach_image.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_ic_voice_search_api_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ic_voice_search_api_material.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_call.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_call.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_cab_background_top_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_cab_background_top_mtrl_alpha.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_ref_palette_dynamic_neutral98.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_ref_palette_dynamic_neutral98.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout-land/material_clock_period_toggle_land.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-land_material_clock_period_toggle_land.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_income.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_income.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_picker_fullscreen.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_picker_fullscreen.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-mdpi-v4/ic_call_answer_video.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_ic_call_answer_video.png.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_ref_palette_dynamic_neutral_variant24.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_ref_palette_dynamic_neutral_variant24.xml.flat
com.example.allinone.app-core-1.13.1-51\:/layout-v21/notification_template_custom_big.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21_notification_template_custom_big.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_empty_state.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_empty_state.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_on_background_disabled.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_on_background_disabled.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/m3_alert_dialog_actions.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_m3_alert_dialog_actions.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/btn_radio_on_mtrl.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_btn_radio_on_mtrl.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_ic_forward.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_forward.png.flat
com.example.allinone.app-material-1.12.0-41\:/animator/design_fab_show_motion_spec.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_design_fab_show_motion_spec.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-ldpi-v4/ic_call_answer_video.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_ic_call_answer_video.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_cascading_menu_item_layout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_cascading_menu_item_layout.xml.flat
com.example.allinone.app-main-112\:/color-night/text_input_box_stroke.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-night_text_input_box_stroke.xml.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable/common_google_signin_btn_text_dark_focused.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_common_google_signin_btn_text_dark_focused.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxxhdpi-v4/abc_tab_indicator_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_tab_indicator_mtrl_alpha.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_activity_chooser_view_list_item.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_activity_chooser_view_list_item.xml.flat
com.example.allinone.app-main-112\:/drawable/rounded_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_rounded_background.xml.flat
com.example.allinone.app-main-112\:/menu/wt_student_context_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\menu_wt_student_context_menu.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_scrubber_control_off_mtrl_alpha.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_scrubber_control_off_mtrl_alpha.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxxhdpi-v4/abc_btn_radio_to_on_mtrl_000.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_btn_radio_to_on_mtrl_000.png.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_dynamic_highlighted_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_dynamic_highlighted_text.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_text_select_handle_right_mtrl.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_text_select_handle_right_mtrl.png.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/fragment_investments.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_fragment_investments.xml.flat
com.example.allinone.app-core-1.13.1-51\:/layout/ime_secondary_split_test_activity.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_ime_secondary_split_test_activity.xml.flat
com.example.allinone.app-main-112\:/drawable/bg_day_with_events.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_bg_day_with_events.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/design_navigation_item.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_design_navigation_item.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_default_color_primary_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_default_color_primary_text.xml.flat
com.example.allinone.app-main-112\:/menu/search_students.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\menu_search_students.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_navigation_bar_colored_ripple_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_navigation_bar_colored_ripple_color.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_ic_fullscreen_exit.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_fullscreen_exit.png.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-mdpi-v4/common_google_signin_btn_icon_light_normal_background.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_common_google_signin_btn_icon_light_normal_background.9.png.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-xxxhdpi-v4/ic_call_answer_low.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_ic_call_answer_low.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_personalized_color_primary_text_inverse.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_personalized_color_primary_text_inverse.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_image.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-night-v8/material_timepicker_modebutton_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-night-v8_material_timepicker_modebutton_tint.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_ic_subtitle_off.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_subtitle_off.png.flat
com.example.allinone.app-main-112\:/drawable/ic_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_student.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_icon_fastforward.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_fastforward.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_chat_ai.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_chat_ai.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_seekbar_thumb_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_seekbar_thumb_material.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-v23/abc_control_background_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v23_abc_control_background_material.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_icon_previous.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_previous.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_fab_ripple_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_fab_ripple_color_selector.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_btn_radio_to_on_mtrl_000.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_btn_radio_to_on_mtrl_000.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_ic_chevron_right.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_chevron_right.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/abc_popup_exit.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_popup_exit.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/indeterminate_static.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_indeterminate_static.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_personalized_color_secondary_text_inverse.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_personalized_color_secondary_text_inverse.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_btn_radio_to_on_mtrl_000.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_btn_radio_to_on_mtrl_000.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_ic_settings.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_settings.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_switch_thumb_pressed_checked.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_switch_thumb_pressed_checked.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/layout_page_header.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_layout_page_header.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_fab_efab_background_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_fab_efab_background_color_selector.xml.flat
com.example.allinone.app-fragment-1.8.4-29\:/animator/fragment_fade_enter.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_fragment_fade_enter.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/color-v23/abc_color_highlight_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23_abc_color_highlight_material.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_tab_indicator_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_tab_indicator_mtrl_alpha.9.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_ic_subtitle_off.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_subtitle_off.png.flat
com.example.allinone.app-main-112\:/drawable/ic_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_program.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_slider_inactive_tick_marks_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_slider_inactive_tick_marks_color.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_switch_thumb_icon_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_switch_thumb_icon_tint.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_picker_header_dialog.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_picker_header_dialog.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_navigation_bar_colored_item_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_navigation_bar_colored_item_tint.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_list_selector_disabled_holo_dark.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_list_selector_disabled_holo_dark.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_calendar_vertical.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_calendar_vertical.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_clear_data.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_clear_data.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/color-v23/abc_tint_default.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23_abc_tint_default.xml.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable/common_google_signin_btn_icon_light_normal.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_common_google_signin_btn_icon_light_normal.xml.flat
com.example.allinone.app-material-1.12.0-41\:/animator/m3_extended_fab_state_list_animator.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_m3_extended_fab_state_list_animator.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_btn_check_material_anim.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_btn_check_material_anim.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable/notification_icon_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_notification_icon_background.xml.flat
com.example.allinone.app-credentials-1.2.0-rc01-69\:/drawable-xxhdpi-v4/ic_passkey.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_ic_passkey.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_textfield_stroke_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_textfield_stroke_color.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_format_list_bulleted.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_format_list_bulleted.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_ic_skip_previous.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_skip_previous.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_icon_shuffle_on.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_shuffle_on.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_ic_chevron_left.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_chevron_left.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/layout/exo_styled_settings_list.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_exo_styled_settings_list.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_ic_checkbox_unchecked.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_ic_checkbox_unchecked.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_timepicker_secondary_text_button_ripple_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_timepicker_secondary_text_button_ripple_color.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_scrubber_control_to_pressed_mtrl_000.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_scrubber_control_to_pressed_mtrl_000.png.flat
com.example.allinone.app-navigation-ui-2.8.2-87\:/animator/nav_default_exit_anim.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_nav_default_exit_anim.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_ref_palette_dynamic_neutral_variant6.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_ref_palette_dynamic_neutral_variant6.xml.flat
com.example.allinone.app-credentials-1.2.0-rc01-69\:/drawable-hdpi-v4/ic_other_sign_in.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_ic_other_sign_in.png.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/fragment_futures_tab.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_fragment_futures_tab.xml.flat
com.example.allinone.app-core-splashscreen-1.0.1-91\:/drawable-v23/compat_splash_screen.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v23_compat_splash_screen.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_text_select_handle_middle_mtrl.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_text_select_handle_middle_mtrl.png.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-xxhdpi-v4/ic_call_decline.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_ic_call_decline.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_text_btn_text_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_text_btn_text_color_selector.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_icon_fastforward.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_fastforward.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_alert_dialog_actions.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_alert_dialog_actions.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/material_clockface_textview.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_material_clockface_textview.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-xxhdpi-v4/ic_call_answer.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_ic_call_answer.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_timepicker_modebutton_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_timepicker_modebutton_tint.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_picker_header_toggle.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_picker_header_toggle.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_close.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_close.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_ic_settings.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_settings.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_default_color_secondary_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_default_color_secondary_text.xml.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-hdpi-v4/common_google_signin_btn_icon_light_normal_background.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_common_google_signin_btn_icon_light_normal_background.9.png.flat
com.example.allinone.app-credentials-1.2.0-rc01-69\:/drawable-anydpi-v21/ic_other_sign_in.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_ic_other_sign_in.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_edit_mode_logo.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_edit_mode_logo.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_chevron_left.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_chevron_left.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_icon_pause.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_pause.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_efab_ripple_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_efab_ripple_color_selector.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_icon_repeat_all.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_repeat_all.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_icon_stop.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_stop.png.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-xhdpi-v4/common_google_signin_btn_text_dark_normal_background.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_common_google_signin_btn_text_dark_normal_background.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_ic_commit_search_api_mtrl_alpha.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_ic_commit_search_api_mtrl_alpha.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_ic_speed.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_speed.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_ref_palette_dynamic_neutral_variant96.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_ref_palette_dynamic_neutral_variant96.xml.flat
com.example.allinone.app-credentials-1.2.0-rc01-69\:/drawable-xxxhdpi-v4/ic_other_sign_in.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_ic_other_sign_in.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/abc_slide_out_top.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_slide_out_top.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_tab_indicator_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_tab_indicator_mtrl_alpha.9.png.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-xxxhdpi-v4/ic_call_decline.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_ic_call_decline.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/btn_checkbox_to_unchecked_icon_null_animation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_checkbox_to_unchecked_icon_null_animation.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/fragment_home.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_fragment_home.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_btn_check_to_on_mtrl_015.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_btn_check_to_on_mtrl_015.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_button_outline_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_button_outline_color_selector.xml.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-xxhdpi-v4/googleg_disabled_color_18.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_googleg_disabled_color_18.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_icon_previous.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_previous.png.flat
com.example.allinone.app-main-112\:/drawable/ic_share.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_share.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_switch_thumb_pressed_unchecked.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_switch_thumb_pressed_unchecked.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/abc_fade_in.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_fade_in.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_stats.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_stats.xml.flat
com.example.allinone.app-main-112\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/btn_checkbox_to_checked_box_outer_merged_animation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_checkbox_to_checked_box_outer_merged_animation.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/dialog_edit_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_edit_program.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_on_primary_emphasis_medium.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_on_primary_emphasis_medium.xml.flat
com.example.allinone.app-material-1.12.0-41\:/animator/mtrl_extended_fab_change_size_expand_motion_spec.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_mtrl_extended_fab_change_size_expand_motion_spec.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_text_select_handle_left_mtrl.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_text_select_handle_left_mtrl.png.flat
com.example.allinone.app-main-112\:/drawable/ic_format_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_format_text.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_textfield_label_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_textfield_label_color.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_ic_default_album_image.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_default_album_image.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_icon_rewind.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_rewind.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_ic_menu_selectall_mtrl_alpha.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ic_menu_selectall_mtrl_alpha.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_btn_switch_to_on_mtrl_00001.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_btn_switch_to_on_mtrl_00001.9.png.flat
com.example.allinone.app-main-112\:/drawable/ic_category_all.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_category_all.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_radiobutton_button_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_radiobutton_button_tint.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_icon_next.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_next.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_spinner_textfield_background_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_spinner_textfield_background_material.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_tabs_legacy_text_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_tabs_legacy_text_color_selector.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_auto_complete_simple_item.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_auto_complete_simple_item.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-ldrtl-xxxhdpi-v17/abc_spinner_mtrl_am_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-xxxhdpi-v17_abc_spinner_mtrl_am_alpha.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_icon_button_icon_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_icon_button_icon_color_selector.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_alert_dialog_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_alert_dialog_material.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_ic_settings.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_settings.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable/exo_rounded_rectangle.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_exo_rounded_rectangle.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_tab_indicator_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_tab_indicator_material.xml.flat
com.example.allinone.app-media-1.6.0-80\:/layout/notification_template_lines_media.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notification_template_lines_media.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-xhdpi-v4/notification_bg_low_normal.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_notification_bg_low_normal.9.png.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-ldpi-v4/ic_call_decline.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_ic_call_decline.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_hint_foreground.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_hint_foreground.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_icon_repeat_all.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_repeat_all.xml.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-hdpi-v4/common_google_signin_btn_text_dark_normal_background.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_common_google_signin_btn_text_dark_normal_background.9.png.flat
com.example.allinone.app-main-112\:/drawable/ic_graduation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_graduation.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/layout/exo_player_control_view.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_exo_player_control_view.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_tooltip.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_tooltip.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_picker_header_title_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_picker_header_title_text.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/color-v23/abc_btn_colored_text_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23_abc_btn_colored_text_material.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_switch_thumb_unchecked.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_switch_thumb_unchecked.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_icon_vr.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_vr.png.flat
com.example.allinone.app-main-112\:/drawable/ic_whatsapp.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_whatsapp.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_ic_go_search_api_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ic_go_search_api_material.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/nav_header.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_nav_header.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable-v23/m3_radiobutton_ripple.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v23_m3_radiobutton_ripple.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/ic_m3_chip_check.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_m3_chip_check.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_primary_text_disable_only.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_primary_text_disable_only.xml.flat
com.example.allinone.app-material-1.12.0-41\:/anim-v21/m3_side_sheet_exit_to_left.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21_m3_side_sheet_exit_to_left.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/layout-v23/exo_styled_player_control_ffwd_button.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v23_exo_styled_player_control_ffwd_button.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_ref_palette_dynamic_neutral96.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_ref_palette_dynamic_neutral96.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_ic_checkbox_checked.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_ic_checkbox_checked.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_instagram_posts.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_instagram_posts.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_on_surface_emphasis_medium.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_on_surface_emphasis_medium.xml.flat
com.example.allinone.app-material-1.12.0-41\:/animator/mtrl_fab_hide_motion_spec.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_mtrl_fab_hide_motion_spec.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_checkbox_button.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_checkbox_button.xml.flat
com.example.allinone.app-main-112\:/color/chip_background_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_chip_background_selector.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_choice_chip_ripple_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_choice_chip_ripple_color.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_ic_chevron_right.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_chevron_right.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_item_background_holo_light.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_item_background_holo_light.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_ic_pause_circle_filled.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_pause_circle_filled.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/support_simple_spinner_dropdown_item.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_support_simple_spinner_dropdown_item.xml.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable/common_google_signin_btn_icon_dark.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_common_google_signin_btn_icon_dark.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_ic_check.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_check.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_icon_stop.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_stop.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_btn_ripple_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_btn_ripple_color.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/material_time_chip.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_material_time_chip.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/dialog_income_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_income_investment.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_fullscreen_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_fullscreen_image.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_list_focused_holo.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_list_focused_holo.9.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_ic_subtitle_on.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_subtitle_on.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_dark_hint_foreground.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_dark_hint_foreground.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_icon_shuffle_off.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_shuffle_off.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_ic_arrow_drop_right_black_24dp.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ic_arrow_drop_right_black_24dp.xml.flat
com.example.allinone.app-main-112\:/menu/workout_bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\menu_workout_bottom_nav_menu.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_picker_text_input_date.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_picker_text_input_date.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_slider_active_track_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_slider_active_track_color.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_dynamic_default_color_primary_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_dynamic_default_color_primary_text.xml.flat
com.example.allinone.app-main-112\:/drawable/error_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_error_image.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-anydpi-v21/ic_call_answer.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_ic_call_answer.xml.flat
com.example.allinone.app-main-112\:/xml/backup_rules.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_backup_rules.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/design_ic_visibility_off.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_design_ic_visibility_off.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_textfield_search_default_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_textfield_search_default_mtrl_alpha.9.png.flat
com.example.allinone.app-main-112\:/drawable/ic_save.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_save.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_checkbox_button_icon_unchecked_indeterminate.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_checkbox_button_icon_unchecked_indeterminate.xml.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-xxhdpi-v4/common_google_signin_btn_icon_light_normal_background.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_common_google_signin_btn_icon_light_normal_background.9.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_ic_fullscreen_enter.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_fullscreen_enter.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_icon_stop.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_stop.png.flat
com.example.allinone.app-main-112\:/color/wt_bottom_nav_item_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_wt_bottom_nav_item_color.xml.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-xhdpi-v4/common_google_signin_btn_text_light_normal_background.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_common_google_signin_btn_text_light_normal_background.9.png.flat
com.example.allinone.app-media-1.6.0-80\:/layout/notification_template_big_media_custom.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notification_template_big_media_custom.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/material_time_input.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_material_time_input.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_ic_play_circle_filled.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_play_circle_filled.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_picker_header_fullscreen.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_picker_header_fullscreen.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/ic_m3_chip_checked_circle.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_m3_chip_checked_circle.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_ref_palette_dynamic_neutral_variant98.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_ref_palette_dynamic_neutral_variant98.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_appbar_overlay_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_appbar_overlay_color.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_ic_forward.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_forward.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_list_divider_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_list_divider_mtrl_alpha.9.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_icon_pause.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_pause.png.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_add_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_add_exercise.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout-v26/abc_screen_toolbar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v26_abc_screen_toolbar.xml.flat
com.example.allinone.app-material-1.12.0-41\:/animator/mtrl_extended_fab_change_size_collapse_motion_spec.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_mtrl_extended_fab_change_size_collapse_motion_spec.xml.flat
com.example.allinone.app-navigation-ui-2.8.2-87\:/animator/nav_default_enter_anim.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_nav_default_enter_anim.xml.flat
com.example.allinone.app-main-112\:/menu/instagram_bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\menu_instagram_bottom_nav_menu.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_btn_radio_to_on_mtrl_015.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_btn_radio_to_on_mtrl_015.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_icon_repeat_off.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_repeat_off.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_scrubber_control_off_mtrl_alpha.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_scrubber_control_off_mtrl_alpha.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_navigation_bar_item_with_indicator_label_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_navigation_bar_item_with_indicator_label_tint.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_ic_audiotrack.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_audiotrack.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_ic_check.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_check.png.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-hdpi-v4/notification_oversize_large_icon_bg.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_notification_oversize_large_icon_bg.png.flat
com.example.allinone.app-credentials-1.2.0-rc01-69\:/drawable-ldpi-v4/ic_password.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_ic_password.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_chip_assist_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_chip_assist_text_color.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_ic_check.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_check.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_ic_commit_search_api_mtrl_alpha.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_ic_commit_search_api_mtrl_alpha.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/btn_checkbox_to_checked_box_inner_merged_animation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_checkbox_to_checked_box_inner_merged_animation.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_icon_shuffle_off.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_shuffle_off.png.flat
com.example.allinone.app-material-1.12.0-41\:/anim-v21/m3_side_sheet_exit_to_right.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21_m3_side_sheet_exit_to_right.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_slider_thumb_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_slider_thumb_color.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_edit_mode_logo.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_edit_mode_logo.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_ic_skip_previous.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_skip_previous.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_radiobutton_ripple_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_radiobutton_ripple_tint.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_icon_repeat_off.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_repeat_off.png.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-mdpi-v4/common_google_signin_btn_text_dark_normal_background.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_common_google_signin_btn_text_dark_normal_background.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_scrubber_primary_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_scrubber_primary_mtrl_alpha.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_cab_background_top_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_cab_background_top_mtrl_alpha.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/animator/m3_card_state_list_anim.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_m3_card_state_list_anim.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/abc_tooltip_exit.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_tooltip_exit.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout-v26/mtrl_calendar_month.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v26_mtrl_calendar_month.xml.flat
com.example.allinone.app-main-112\:/color/dialog_action_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_dialog_action_button_color.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_calendar_months.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_calendar_months.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-xxhdpi-v4/ic_call_answer_video_low.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_ic_call_answer_video_low.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_icon_shuffle_on.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_shuffle_on.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/m3_alert_dialog_title.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_m3_alert_dialog_title.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/ic_search_black_24.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_search_black_24.xml.flat
com.example.allinone.app-main-112\:/color-night/text_input_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-night_text_input_text_color.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/design_menu_item_action_area.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_design_menu_item_action_area.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_bottomsheet_drag_handle.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_bottomsheet_drag_handle.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/design_text_input_start_icon.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_design_text_input_start_icon.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_spinner_mtrl_am_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_spinner_mtrl_am_alpha.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/ic_arrow_back_black_24.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_arrow_back_black_24.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_icon_play.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_play.png.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/m3_popupmenu_background_overlay.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_m3_popupmenu_background_overlay.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_btn_switch_to_on_mtrl_00012.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_btn_switch_to_on_mtrl_00012.9.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_icon_play.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_play.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_picker_dialog.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_picker_dialog.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_category_bills.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_category_bills.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_navigation_item_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_navigation_item_text_color.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/color/abc_primary_text_material_dark.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_primary_text_material_dark.xml.flat
com.example.allinone.app-material-1.12.0-41\:/animator/m3_btn_state_list_anim.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_m3_btn_state_list_anim.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_ic_forward.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_forward.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/design_bottom_navigation_item.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_design_bottom_navigation_item.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/btn_radio_to_off_mtrl_dot_group_animation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_radio_to_off_mtrl_dot_group_animation.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_personalized__highlighted_text_inverse.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_personalized__highlighted_text_inverse.xml.flat
com.example.allinone.app-navigation-ui-2.8.2-87\:/anim/nav_default_enter_anim.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_nav_default_enter_anim.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_format_list_numbered.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_format_list_numbered.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable-v23/m3_tabs_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v23_m3_tabs_background.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout-night-v8/activity_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-night_activity_backup.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_card_stroke_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_card_stroke_color.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-hdpi-v4/ic_call_decline.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_ic_call_decline.png.flat
com.example.allinone.app-material-1.12.0-41\:/animator/mtrl_extended_fab_hide_motion_spec.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_mtrl_extended_fab_hide_motion_spec.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/design_navigation_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_design_navigation_menu.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-xxxhdpi-v4/ic_call_answer.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_ic_call_answer.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/design_navigation_item_subheader.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_design_navigation_item_subheader.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_ab_share_pack_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_ab_share_pack_mtrl_alpha.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_popupmenu_overlay_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_popupmenu_overlay_color.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_investments.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_investments.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/abc_fade_out.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_fade_out.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_icon_vr.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_vr.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_switch_track_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_switch_track_mtrl_alpha.9.png.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable/common_google_signin_btn_icon_light_focused.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_common_google_signin_btn_icon_light_focused.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_icon_previous.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_previous.png.flat
com.example.allinone.app-main-112\:/drawable/ic_no_registrations.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_no_registrations.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_notification.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_notification.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/layout/exo_styled_player_view.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_exo_styled_player_view.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/layout/exo_styled_sub_settings_list_item.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_exo_styled_sub_settings_list_item.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/ic_mtrl_checked_circle.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_mtrl_checked_circle.xml.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-xhdpi-v4/common_google_signin_btn_icon_light_normal_background.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_common_google_signin_btn_icon_light_normal_background.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/design_text_input_end_icon.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_design_text_input_end_icon.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/color/abc_secondary_text_material_light.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_secondary_text_material_light.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/design_error.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_design_error.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_icon_stop.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_stop.png.flat
com.example.allinone.app-main-112\:/drawable/ic_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_exercise.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_icon_shuffle_off.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_shuffle_off.png.flat
com.example.allinone.app-main-112\:/mipmap-anydpi-v33/ic_launcher.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-anydpi-v33_ic_launcher.xml.flat
com.example.allinone.app-main-112\:/drawable/bg_day_with_registration.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_bg_day_with_registration.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_icon_pause.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_pause.png.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/theme_switch_layout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_theme_switch_layout.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_ab_share_pack_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_ab_share_pack_mtrl_alpha.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_btn_default_mtrl_shape.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_btn_default_mtrl_shape.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_pause.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_pause.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_log.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_log.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxxhdpi-v4/abc_btn_switch_to_on_mtrl_00001.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_btn_switch_to_on_mtrl_00001.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/btn_radio_to_on_mtrl_ring_outer_path_animation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_radio_to_on_mtrl_ring_outer_path_animation.xml.flat
com.example.allinone.app-credentials-1.2.0-rc01-69\:/drawable-xxxhdpi-v4/ic_password.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_ic_password.png.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/dialog_add_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_add_student.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_elevated_chip_background_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_elevated_chip_background_color.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxxhdpi-v4/abc_btn_check_to_on_mtrl_015.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_btn_check_to_on_mtrl_015.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_list_pressed_holo_light.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_list_pressed_holo_light.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_list_focused_holo.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_list_focused_holo.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/interpolator-v21/mtrl_fast_out_slow_in.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator-v21_mtrl_fast_out_slow_in.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_video_error.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_video_error.xml.flat
com.example.allinone.app-main-112\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/m3_avd_hide_password.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_m3_avd_hide_password.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_fab_ripple_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_fab_ripple_color.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_ref_palette_dynamic_neutral94.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_ref_palette_dynamic_neutral94.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_switch_thumb_unchecked_checked.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_switch_thumb_unchecked_checked.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_text_select_handle_middle_mtrl.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_text_select_handle_middle_mtrl.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/tooltip_frame_light.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_tooltip_frame_light.xml.flat
com.example.allinone.app-main-112\:/drawable/transparent.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_transparent.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_ic_default_album_image.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_default_album_image.png.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/m3_bottom_sheet_drag_handle.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_m3_bottom_sheet_drag_handle.xml.flat
com.example.allinone.app-firebase-common-21.0.0-99\:/raw/firebase_common_keep.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw_firebase_common_keep.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/select_dialog_multichoice_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_select_dialog_multichoice_material.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_icon_previous.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_previous.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_ic_cancel.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_ic_cancel.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_ic_menu_paste_mtrl_am_alpha.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ic_menu_paste_mtrl_am_alpha.xml.flat
com.example.allinone.app-main-112\:/drawable/bg_tag_blue.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_bg_tag_blue.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_switch_thumb_checked_pressed.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_switch_thumb_checked_pressed.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_activity_chooser_view.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_activity_chooser_view.xml.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-hdpi-v4/googleg_disabled_color_18.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_googleg_disabled_color_18.png.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-mdpi-v4/ic_call_decline.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_ic_call_decline.png.flat
com.example.allinone.app-material-1.12.0-41\:/animator/mtrl_fab_transformation_sheet_expand_spec.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_mtrl_fab_transformation_sheet_expand_spec.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_chevron_right.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_chevron_right.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_ic_skip_next.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_skip_next.png.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-xhdpi-v4/ic_call_answer_low.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_ic_call_answer_low.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-v21/abc_action_bar_item_background_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21_abc_action_bar_item_background_material.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_ic_speed.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_speed.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_list_pressed_holo_light.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_list_pressed_holo_light.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_list_selector_disabled_holo_dark.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_list_selector_disabled_holo_dark.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_chip_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_chip_text_color.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_format_italic.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_format_italic.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_ic_fullscreen_exit.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_fullscreen_exit.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_tabs_icon_color_secondary.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_tabs_icon_color_secondary.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/design_layout_snackbar_include.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_design_layout_snackbar_include.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_ic_audiotrack.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_audiotrack.png.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_switch_track.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_switch_track.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_list_pressed_holo_dark.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_list_pressed_holo_dark.9.png.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/dialog_loading.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_loading.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_icon_fullscreen_exit.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_fullscreen_exit.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_ratingbar_small_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ratingbar_small_material.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_ic_skip_next.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_skip_next.png.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_note_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_note_image.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_list_menu_item_layout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_list_menu_item_layout.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_choice_chip_background_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_choice_chip_background_color.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxxhdpi-v4/abc_spinner_mtrl_am_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_spinner_mtrl_am_alpha.9.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_ic_forward.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_forward.png.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-mdpi-v4/common_google_signin_btn_text_light_normal_background.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_common_google_signin_btn_text_light_normal_background.9.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_icon_next.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_next.png.flat
com.example.allinone.app-material-1.12.0-41\:/animator/mtrl_fab_show_motion_spec.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_mtrl_fab_show_motion_spec.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_ic_default_album_image.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_default_album_image.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_text_button_foreground_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_text_button_foreground_color_selector.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_fitness.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_fitness.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator_btn_checkbox_checked_mtrl_animation_interpolator_0.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_screen_simple_overlay_action_mode.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_screen_simple_overlay_action_mode.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-xxhdpi-v4/ic_call_decline_low.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_ic_call_decline_low.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/btn_checkbox_to_unchecked_check_path_merged_animation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_checkbox_to_unchecked_check_path_merged_animation.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_category_summary.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_category_summary.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/material_ic_keyboard_arrow_right_black_24dp.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_material_ic_keyboard_arrow_right_black_24dp.xml.flat
com.example.allinone.app-core-1.13.1-51\:/layout/ime_base_split_test_activity.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_ime_base_split_test_activity.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-hdpi-v4/ic_call_answer_low.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_ic_call_answer_low.png.flat
com.example.allinone.app-play-services-auth-21.2.0-63\:/drawable-watch-v20/common_google_signin_btn_text_light_normal.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-watch-v20_common_google_signin_btn_text_light_normal.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_scrubber_control_to_pressed_mtrl_005.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_scrubber_control_to_pressed_mtrl_005.png.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/m3_password_eye.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_m3_password_eye.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_ref_palette_dynamic_neutral24.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_ref_palette_dynamic_neutral24.xml.flat
com.example.allinone.app-play-services-auth-21.2.0-63\:/drawable-watch-v20/common_google_signin_btn_text_disabled.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-watch-v20_common_google_signin_btn_text_disabled.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/dialog_post_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_post_details.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_icon_stop.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_stop.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/material_clock_display.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_material_clock_display.xml.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-xxhdpi-v4/common_google_signin_btn_text_dark_normal_background.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_common_google_signin_btn_text_dark_normal_background.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/anim-v21/m3_side_sheet_enter_from_right.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21_m3_side_sheet_enter_from_right.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_no_students.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_no_students.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/material_timepicker_dialog.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_material_timepicker_dialog.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_scrubber_control_to_pressed_mtrl_000.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_scrubber_control_to_pressed_mtrl_000.png.flat
com.example.allinone.app-main-112\:/font/opensans.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\font_opensans.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_screen_simple.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_screen_simple.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_text_select_handle_middle_mtrl.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_text_select_handle_middle_mtrl.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_ic_chevron_left.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_chevron_left.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_textfield_search_default_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_textfield_search_default_mtrl_alpha.9.png.flat
com.example.allinone.app-main-112\:/drawable/ic_add.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_add.xml.flat
com.example.allinone.app-material-1.12.0-41\:/anim/mtrl_card_lowers_interpolator.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_mtrl_card_lowers_interpolator.xml.flat
com.example.allinone.app-main-112\:/drawable/bg_day_with_lesson.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_bg_day_with_lesson.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_icon_shuffle_off.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_shuffle_off.png.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/m3_tabs_line_indicator.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_m3_tabs_line_indicator.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_category_dropdown.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_category_dropdown.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxxhdpi-v4/abc_btn_check_to_on_mtrl_000.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_btn_check_to_on_mtrl_000.png.flat
com.example.allinone.app-main-112\:/color/bottom_nav_item_color_light.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_bottom_nav_item_color_light.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_layout_snackbar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_layout_snackbar.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_icon_next.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_next.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_scrubber_control_to_pressed_mtrl_005.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_scrubber_control_to_pressed_mtrl_005.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_ic_audiotrack.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_audiotrack.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_ic_subtitle_off.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_subtitle_off.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/color/abc_secondary_text_material_dark.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_secondary_text_material_dark.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_dropdown_arrow.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_dropdown_arrow.xml.flat
com.example.allinone.app-credentials-1.2.0-rc01-69\:/drawable-mdpi-v4/ic_password.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_ic_password.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_text_cursor_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_text_cursor_material.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_ic_rewind.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_rewind.png.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-hdpi-v4/notification_bg_normal.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_notification_bg_normal.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_textfield_search_activated_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_textfield_search_activated_mtrl_alpha.9.png.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-ldpi-v4/ic_call_answer_video_low.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_ic_call_answer_video_low.png.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-v21/notification_action_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21_notification_action_background.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_icon_pause.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_pause.png.flat
com.example.allinone.app-material-1.12.0-41\:/animator/m3_extended_fab_change_size_expand_motion_spec.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_m3_extended_fab_change_size_expand_motion_spec.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_ic_check.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_check.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_textfield_activated_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_textfield_activated_mtrl_alpha.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/design_navigation_item_separator.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_design_navigation_item_separator.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_list_selector_background_transition_holo_light.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_list_selector_background_transition_holo_light.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator_btn_checkbox_checked_mtrl_animation_interpolator_1.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_icon_next.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_next.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_textfield_search_activated_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_textfield_search_activated_mtrl_alpha.9.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_icon_vr.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_vr.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_ic_subtitle_on.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_subtitle_on.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_tabs_text_color_secondary.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_tabs_text_color_secondary.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_icon_repeat_off.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_repeat_off.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/btn_radio_to_on_mtrl_dot_group_animation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_radio_to_on_mtrl_dot_group_animation.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/fragment_investments_tab.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_fragment_investments_tab.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-mdpi-v4/notification_bg_low_pressed.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_notification_bg_low_pressed.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/avd_hide_password.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_avd_hide_password.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-v21/abc_dialog_material_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21_abc_dialog_material_background.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_icon_fullscreen_exit.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_fullscreen_exit.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_ic_fullscreen_exit.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_fullscreen_exit.png.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/dialog_add_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_add_program.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_ic_rewind.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_rewind.png.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/dropdown_item.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dropdown_item.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/dialog_edit_seminar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_edit_seminar.xml.flat
com.example.allinone.app-main-112\:/menu/search_register.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\menu_search_register.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_ic_skip_next.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_skip_next.xml.flat
com.example.allinone.app-material-1.12.0-41\:/anim-v21/m3_bottom_sheet_slide_out.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21_m3_bottom_sheet_slide_out.xml.flat
com.example.allinone.app-main-112\:/font/opensans_regular.ttf=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\font_opensans_regular.ttf.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_ic_subtitle_off.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_subtitle_off.xml.flat
com.example.allinone.app-appcompat-resources-1.7.0-81\:/drawable/abc_vector_test.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_vector_test.xml.flat
com.example.allinone.app-main-112\:/menu/menu_edit_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\menu_menu_edit_note.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_tabs_ripple_color_secondary.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_tabs_ripple_color_secondary.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-v21/abc_list_divider_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21_abc_list_divider_material.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_ic_fullscreen_enter.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_fullscreen_enter.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_list_selector_disabled_holo_light.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_list_selector_disabled_holo_light.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_layout_snackbar_include.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_layout_snackbar_include.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/color-v23/abc_btn_colored_borderless_text_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23_abc_btn_colored_borderless_text_material.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_dynamic_dark_hint_foreground.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_dynamic_dark_hint_foreground.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/abc_popup_enter.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_popup_enter.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_ic_fullscreen_exit.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_fullscreen_exit.xml.flat
com.example.allinone.app-material-1.12.0-41\:/anim/design_snackbar_out.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_design_snackbar_out.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_switch_track_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_switch_track_mtrl_alpha.9.png.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/dialog_edit_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_edit_investment.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_icon_repeat_off.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_repeat_off.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/material_timepicker.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_material_timepicker.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable/notification_bg_low.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_notification_bg_low.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-anydpi-v21/ic_call_answer_low.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_ic_call_answer_low.xml.flat
com.example.allinone.app-main-112\:/drawable/fully_uncompleted_exercise_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_fully_uncompleted_exercise_background.xml.flat
com.example.allinone.app-navigation-ui-2.8.2-87\:/anim/nav_default_exit_anim.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_nav_default_exit_anim.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_icon_rewind.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_rewind.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_ic_menu_cut_mtrl_alpha.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ic_menu_cut_mtrl_alpha.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_icon_previous.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_previous.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_calendar_selected_range.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_calendar_selected_range.xml.flat
com.example.allinone.app-main-112\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-xxhdpi-v4/common_google_signin_btn_text_light_normal_background.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_common_google_signin_btn_text_light_normal_background.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout-sw600dp-v13/design_layout_snackbar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-sw600dp-v13_design_layout_snackbar.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_popup_menu_header_item_layout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_popup_menu_header_item_layout.xml.flat
com.example.allinone.app-main-112\:/menu/drawer_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\menu_drawer_menu.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_cursor_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_cursor_color.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_scrubber_primary_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_scrubber_primary_mtrl_alpha.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_ref_palette_dynamic_neutral92.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_ref_palette_dynamic_neutral92.xml.flat
com.example.allinone.app-main-112\:/drawable/border_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_border_background.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_category_wing_tzun.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_category_wing_tzun.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_investment_selection.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_investment_selection.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_textfield_indicator_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_textfield_indicator_text_color.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_icon_circular_play.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_circular_play.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_icon_repeat_all.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_repeat_all.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_ic_play_circle_filled.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_play_circle_filled.png.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_switch_thumb.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_switch_thumb.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_icon_shuffle_on.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_shuffle_on.png.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-hdpi-v4/notify_panel_notification_icon_bg.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_notify_panel_notification_icon_bg.png.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-xhdpi-v4/ic_call_answer.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_ic_call_answer.png.flat
com.example.allinone.app-main-112\:/xml/data_extraction_rules.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_data_extraction_rules.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_slider_halo_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_slider_halo_color.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_search_view.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_search_view.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_registration.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_registration.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_ref_palette_dynamic_neutral4.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_ref_palette_dynamic_neutral4.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_dynamic_default_color_secondary_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_dynamic_default_color_secondary_text.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_investment_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_investment_image.xml.flat
com.example.allinone.app-browser-1.4.0-94\:/xml/image_share_filepaths.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_image_share_filepaths.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-mdpi-v4/notification_bg_normal_pressed.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_notification_bg_normal_pressed.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_timepicker_display_background_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_timepicker_display_background_color.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-hdpi-v4/notification_bg_low_pressed.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_notification_bg_low_pressed.9.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_ic_skip_previous.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_skip_previous.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_btn_text_btn_ripple_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_btn_text_btn_ripple_color.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_icon_rewind.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_rewind.png.flat
com.example.allinone.app-material-1.12.0-41\:/animator/mtrl_fab_transformation_sheet_collapse_spec.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_mtrl_fab_transformation_sheet_collapse_spec.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-xhdpi-v4/notification_bg_low_pressed.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_notification_bg_low_pressed.9.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_ic_subtitle_on.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_subtitle_on.png.flat
com.example.allinone.app-core-splashscreen-1.0.1-91\:/drawable-v23/compat_splash_screen_no_icon_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v23_compat_splash_screen_no_icon_background.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_ic_subtitle_on.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_subtitle_on.xml.flat
com.example.allinone.app-navigation-ui-2.8.2-87\:/anim/nav_default_pop_enter_anim.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_nav_default_pop_enter_anim.xml.flat
com.example.allinone.app-material-1.12.0-41\:/anim/m3_motion_fade_enter.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_m3_motion_fade_enter.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_icon_repeat_off.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_repeat_off.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_ic_chevron_left.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_chevron_left.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_slider_thumb_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_slider_thumb_color.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_stop.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_stop.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_dark_primary_text_disable_only.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_dark_primary_text_disable_only.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_expense.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_expense.xml.flat
com.example.allinone.app-main-112\:/drawable-v24/ic_launcher_foreground.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v24_ic_launcher_foreground.xml.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-hdpi-v4/common_full_open_on_phone.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_common_full_open_on_phone.png.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-xhdpi-v4/notification_bg_normal_pressed.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_notification_bg_normal_pressed.9.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_ic_skip_next.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_skip_next.png.flat
com.example.allinone.app-main-112\:/drawable/ic_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_note.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_personalized_primary_text_disable_only.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_personalized_primary_text_disable_only.xml.flat
com.example.allinone.app-material-1.12.0-41\:/anim/linear_indeterminate_line2_tail_interpolator.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_linear_indeterminate_line2_tail_interpolator.xml.flat
com.example.allinone.app-material-1.12.0-41\:/anim-v21/design_bottom_sheet_slide_in.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21_design_bottom_sheet_slide_in.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_icon_stop.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_stop.png.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_backup.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_navigation_rail_ripple_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_navigation_rail_ripple_color_selector.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_list_pressed_holo_dark.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_list_pressed_holo_dark.9.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_ic_pause_circle_filled.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_pause_circle_filled.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/color/abc_primary_text_material_light.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_primary_text_material_light.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_tabs_colored_ripple_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_tabs_colored_ripple_color.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-xhdpi-v4/notification_bg_normal.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_notification_bg_normal.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_list_divider_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_list_divider_mtrl_alpha.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_list_divider_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_list_divider_mtrl_alpha.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/interpolator/btn_radio_to_on_mtrl_animation_interpolator_0.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator_btn_radio_to_on_mtrl_animation_interpolator_0.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_delete.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_delete.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_icon_shuffle_on.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_shuffle_on.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_ic_speed.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_speed.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_navigation_item_icon_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_navigation_item_icon_tint.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_switch_thumb_unchecked_pressed.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_switch_thumb_unchecked_pressed.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/material_ic_calendar_black_24dp.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_material_ic_calendar_black_24dp.xml.flat
com.example.allinone.app-core-splashscreen-1.0.1-91\:/drawable/icon_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_icon_background.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/abc_tooltip_enter.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_tooltip_enter.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_menu_hardkey_panel_mtrl_mult.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_menu_hardkey_panel_mtrl_mult.9.png.flat
com.example.allinone.app-credentials-1.2.0-rc01-69\:/drawable-xhdpi-v4/ic_other_sign_in.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_ic_other_sign_in.png.flat
com.example.allinone.app-main-112\:/drawable/completed_exercise_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_completed_exercise_background.xml.flat
com.example.allinone.app-core-1.13.1-51\:/layout/notification_template_part_chronometer.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notification_template_part_chronometer.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_chip_surface_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_chip_surface_color.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/ic_mtrl_chip_checked_black.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_mtrl_chip_checked_black.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-mdpi-v4/ic_call_decline_low.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_ic_call_decline_low.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_assist_chip_stroke_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_assist_chip_stroke_color.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_note.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_ref_palette_dynamic_neutral22.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_ref_palette_dynamic_neutral22.xml.flat
com.example.allinone.app-material-1.12.0-41\:/animator/m3_elevated_chip_state_list_anim.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_m3_elevated_chip_state_list_anim.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/design_layout_snackbar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_design_layout_snackbar.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/fragment_transaction_report.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_fragment_transaction_report.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_ic_skip_next.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_skip_next.png.flat
com.example.allinone.app-credentials-1.2.0-rc01-69\:/drawable-xhdpi-v4/ic_password.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_ic_password.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_textfield_filled_background_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_textfield_filled_background_color.xml.flat
com.example.allinone.app-main-112\:/color-night/dialog_delete_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-night_dialog_delete_button_color.xml.flat
com.example.allinone.app-material-1.12.0-41\:/interpolator/mtrl_linear.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator_mtrl_linear.xml.flat
com.example.allinone.app-credentials-1.2.0-rc01-69\:/drawable-ldpi-v4/ic_other_sign_in.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_ic_other_sign_in.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/color/abc_primary_text_disable_only_material_dark.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_primary_text_disable_only_material_dark.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_textfield_activated_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_textfield_activated_mtrl_alpha.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_calendar_day_of_week.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_calendar_day_of_week.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_ic_chevron_left.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_chevron_left.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_navigation_rail_item.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_navigation_rail_item.xml.flat
com.example.allinone.app-fragment-1.8.4-29\:/animator/fragment_open_enter.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_fragment_open_enter.xml.flat
com.example.allinone.app-main-112\:/drawable/bg_tag_green.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_bg_tag_green.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_ic_audiotrack.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_audiotrack.png.flat
com.example.allinone.app-main-112\:/drawable/ic_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_investment.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_ic_play_circle_filled.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_play_circle_filled.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_highlighted_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_highlighted_text.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_outlined_stroke_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_outlined_stroke_color.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_scrubber_control_to_pressed_mtrl_000.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_scrubber_control_to_pressed_mtrl_000.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_list_selector_disabled_holo_light.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_list_selector_disabled_holo_light.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/drawable-v23/m3_tabs_transparent_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v23_m3_tabs_transparent_background.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/layout/exo_styled_settings_list_item.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_exo_styled_settings_list_item.xml.flat
com.example.allinone.app-main-112\:/drawable/bg_category_chip.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_bg_category_chip.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_icon_circular_play.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_circular_play.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_scrubber_control_to_pressed_mtrl_005.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_scrubber_control_to_pressed_mtrl_005.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/btn_radio_to_off_mtrl_ring_outer_path_animation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_radio_to_off_mtrl_ring_outer_path_animation.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_textfield_default_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_textfield_default_mtrl_alpha.9.png.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable/common_google_signin_btn_icon_light.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_common_google_signin_btn_icon_light.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_format_bold.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_format_bold.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/color-v23/abc_tint_seek_thumb.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23_abc_tint_seek_thumb.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_ic_menu_overflow_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ic_menu_overflow_material.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_on_surface_stroke.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_on_surface_stroke.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_ic_chevron_right.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_chevron_right.png.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_voice_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_voice_note.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/material_radial_view_group.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_material_radial_view_group.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_ref_palette_dynamic_neutral_variant17.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_ref_palette_dynamic_neutral_variant17.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_timepicker_button_ripple_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_timepicker_button_ripple_color.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_icon_circular_play.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_circular_play.png.flat
com.example.allinone.app-main-112\:/drawable/bg_tag_red.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_bg_tag_red.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_ic_audiotrack.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_audiotrack.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable-v23/m3_selection_control_ripple.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v23_m3_selection_control_ripple.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_calendar_item_disabled_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_calendar_item_disabled_text.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/color/switch_thumb_material_dark.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_switch_thumb_material_dark.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_icon_next.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_next.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_icon_repeat_one.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_repeat_one.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_wt_registers.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_wt_registers.xml.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-mdpi-v4/googleg_standard_color_18.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_googleg_standard_color_18.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/layout/exo_player_view.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_exo_player_view.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_ic_skip_previous.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_skip_previous.png.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-xhdpi-v4/notify_panel_notification_icon_bg.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_notify_panel_notification_icon_bg.png.flat
com.example.allinone.app-main-112\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_ic_fullscreen_enter.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_fullscreen_enter.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_alert_dialog_title.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_alert_dialog_title.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_ic_subtitle_on.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_subtitle_on.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_dark_default_color_secondary_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_dark_default_color_secondary_text.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_fab_efab_foreground_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_fab_efab_foreground_color_selector.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-anydpi-v21/ic_call_decline_low.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_ic_call_decline_low.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_ic_fullscreen_enter.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_fullscreen_enter.png.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_popupmenu_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_popupmenu_background.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_ic_skip_previous.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_skip_previous.png.flat
com.example.allinone.app-main-112\:/xml/file_paths.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_file_paths.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/design_icon_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_design_icon_tint.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_ic_fullscreen_exit.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_fullscreen_exit.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_action_menu_item_layout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_action_menu_item_layout.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_star_black_48dp.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_star_black_48dp.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_wt_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_wt_student.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_expanded_menu_layout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_expanded_menu_layout.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_ic_default_album_image.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_default_album_image.png.flat
com.example.allinone.app-credentials-1.2.0-rc01-69\:/drawable-hdpi-v4/ic_password.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_ic_password.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_bottom_sheet_drag_handle_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_bottom_sheet_drag_handle_color.xml.flat
com.example.allinone.app-main-112\:/color/text_input_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_text_input_text_color.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_navigation_bar_item_with_indicator_icon_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_navigation_bar_item_with_indicator_icon_tint.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_card_foreground_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_card_foreground_color.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/material_ic_menu_arrow_down_black_24dp.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_material_ic_menu_arrow_down_black_24dp.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_menu_hardkey_panel_mtrl_mult.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_menu_hardkey_panel_mtrl_mult.9.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_icon_repeat_all.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_repeat_all.png.flat
com.example.allinone.app-media-1.6.0-80\:/layout/notification_media_action.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notification_media_action.xml.flat
com.example.allinone.app-main-112\:/menu/wt_bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\menu_wt_bottom_nav_menu.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_play_circle.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_play_circle.xml.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-xhdpi-v4/common_full_open_on_phone.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_common_full_open_on_phone.png.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-xhdpi-v4/ic_call_decline_low.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_ic_call_decline_low.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_on_surface_disabled.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_on_surface_disabled.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_seminar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_seminar.xml.flat
com.example.allinone.app-core-1.13.1-51\:/layout-v21/notification_action.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21_notification_action.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_ic_subtitle_off.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_subtitle_off.png.flat
com.example.allinone.app-main-112\:/drawable/ic_category_food.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_category_food.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/material_textinput_timepicker.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_material_textinput_timepicker.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_switch_thumb_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_switch_thumb_tint.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_ic_pause_circle_filled.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_pause_circle_filled.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_popup_menu_item_layout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_popup_menu_item_layout.xml.flat
com.example.allinone.app-credentials-1.2.0-rc01-69\:/drawable-ldpi-v4/ic_passkey.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_ic_passkey.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_icon_previous.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_previous.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_assist_chip_icon_tint_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_assist_chip_icon_tint_color.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_personalized_primary_inverse_text_disable_only.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_personalized_primary_inverse_text_disable_only.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_dynamic_dark_default_color_primary_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_dynamic_dark_default_color_primary_text.xml.flat
com.example.allinone.app-material-1.12.0-41\:/anim-v21/mtrl_bottom_sheet_slide_in.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21_mtrl_bottom_sheet_slide_in.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_ic_speed.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_speed.png.flat
com.example.allinone.app-main-112\:/drawable/ic_add_photo.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_add_photo.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_ic_arrow_drop_down.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_ic_arrow_drop_down.xml.flat
com.example.allinone.app-material-1.12.0-41\:/interpolator-v21/m3_sys_motion_easing_linear.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator-v21_m3_sys_motion_easing_linear.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_ic_check.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_check.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_selection_control_ripple_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_selection_control_ripple_color_selector.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/avd_show_password.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_avd_show_password.xml.flat
com.example.allinone.app-material-1.12.0-41\:/anim-v21/m3_side_sheet_enter_from_left.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21_m3_side_sheet_enter_from_left.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_icon_rewind.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_rewind.png.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/m3_tabs_rounded_line_indicator.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_m3_tabs_rounded_line_indicator.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-ldpi-v4/ic_call_answer.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_ic_call_answer.png.flat
com.example.allinone.app-main-112\:/menu/wt_registration_context_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\menu_wt_registration_context_menu.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_icon_fastforward.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_fastforward.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_action_menu_layout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_action_menu_layout.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_ic_forward.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_forward.xml.flat
com.example.allinone.app-material-1.12.0-41\:/animator/m3_card_elevated_state_list_anim.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_m3_card_elevated_state_list_anim.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_tabs_default_indicator.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_tabs_default_indicator.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_icon_play.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_play.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/design_layout_tab_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_design_layout_tab_text.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/dialog_edit_wt_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_edit_wt_student.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_dark_highlighted_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_dark_highlighted_text.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_ic_arrow_drop_up.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_ic_arrow_drop_up.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-anydpi-v21/ic_call_decline.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_ic_call_decline.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_menu.xml.flat
com.example.allinone.app-main-112\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.allinone.app-main-112\:/menu/bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\menu_bottom_nav_menu.xml.flat
com.example.allinone.app-main-112\:/drawable/uncompleted_exercise_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_uncompleted_exercise_background.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_ref_palette_dynamic_neutral12.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_ref_palette_dynamic_neutral12.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_personalized_hint_foreground_inverse.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_personalized_hint_foreground_inverse.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_icon_fullscreen_exit.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_fullscreen_exit.png.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-anydpi-v21/ic_call_answer_video_low.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_ic_call_answer_video_low.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_calendar_horizontal.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_calendar_horizontal.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_tabs_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_tabs_text_color.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_dialog_title_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_dialog_title_material.xml.flat
com.example.allinone.app-main-112\:/drawable/circle_shape.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_circle_shape.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_chip_stroke_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_chip_stroke_color.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_card_ripple_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_card_ripple_color.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_popup_background_mtrl_mult.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_popup_background_mtrl_mult.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_scrubber_control_to_pressed_mtrl_000.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_scrubber_control_to_pressed_mtrl_000.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_ic_fullscreen_enter.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_fullscreen_enter.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_list_selector_disabled_holo_light.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_list_selector_disabled_holo_light.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_alert_select_dialog_singlechoice.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_alert_select_dialog_singlechoice.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable-v23/mtrl_popupmenu_background_overlay.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v23_mtrl_popupmenu_background_overlay.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_picker_header_selection_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_picker_header_selection_text.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_btn_radio_material_anim.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_btn_radio_material_anim.xml.flat
com.example.allinone.app-navigation-ui-2.8.2-87\:/animator/nav_default_pop_exit_anim.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_nav_default_pop_exit_anim.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/dialog_profit_loss.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_profit_loss.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_text_select_handle_right_mtrl.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_text_select_handle_right_mtrl.png.flat
com.example.allinone.app-credentials-1.2.0-rc01-69\:/drawable-hdpi-v4/ic_passkey.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_ic_passkey.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_btn_text_btn_bg_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_btn_text_btn_bg_color_selector.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_btn_switch_to_on_mtrl_00001.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_btn_switch_to_on_mtrl_00001.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_timepicker_clock_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_timepicker_clock_text_color.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_btn_switch_to_on_mtrl_00012.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_btn_switch_to_on_mtrl_00012.9.png.flat
com.example.allinone.app-main-112\:/drawable-night/bg_category_chip.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-night_bg_category_chip.xml.flat
com.example.allinone.app-media-1.6.0-80\:/layout/notification_template_big_media.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notification_template_big_media.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-mdpi-v4/notify_panel_notification_icon_bg.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_notify_panel_notification_icon_bg.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_ic_pause_circle_filled.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_pause_circle_filled.png.flat
com.example.allinone.app-main-112\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_dialog_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_dialog_background.xml.flat
com.example.allinone.app-main-112\:/drawable/circle_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_circle_background.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_search_view.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_search_view.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_lessons.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_lessons.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/fragment_transactions_overview.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_fragment_transactions_overview.xml.flat
com.example.allinone.app-main-112\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxxhdpi-v4/abc_scrubber_control_to_pressed_mtrl_000.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_scrubber_control_to_pressed_mtrl_000.png.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable/common_google_signin_btn_icon_dark_normal.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_common_google_signin_btn_icon_dark_normal.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_slider_halo_color_legacy.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_slider_halo_color_legacy.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_cab_background_top_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_cab_background_top_mtrl_alpha.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_popupmenu_overlay_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_popupmenu_overlay_color.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/material_clock_period_toggle.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_material_clock_period_toggle.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_ic_chevron_right.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_chevron_right.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_switch_thumb_pressed.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_switch_thumb_pressed.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_calendar_month.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_calendar_month.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_cab_background_internal_bg.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_cab_background_internal_bg.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_btn_check_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_btn_check_material.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxxhdpi-v4/abc_btn_radio_to_on_mtrl_015.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_btn_radio_to_on_mtrl_015.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_icon_repeat_all.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_repeat_all.png.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/m3_avd_show_password.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_m3_avd_show_password.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_category_general.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_category_general.xml.flat
com.example.allinone.app-main-112\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_ic_audiotrack.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_audiotrack.png.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable/common_google_signin_btn_text_dark.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_common_google_signin_btn_text_dark.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-ldrtl-xhdpi-v17/abc_spinner_mtrl_am_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-xhdpi-v17_abc_spinner_mtrl_am_alpha.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_btn_radio_to_on_mtrl_015.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_btn_radio_to_on_mtrl_015.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_ic_commit_search_api_mtrl_alpha.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_ic_commit_search_api_mtrl_alpha.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_indicator_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_indicator_text_color.xml.flat
com.example.allinone.app-ui-release-77\:/color/vector_tint_theme_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_vector_tint_theme_color.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_icon_fullscreen_enter.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_fullscreen_enter.png.flat
com.example.allinone.app-main-112\:/drawable/ic_notes.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_notes.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_navigation_item_ripple_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_navigation_item_ripple_color.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_card_view_foreground.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_card_view_foreground.xml.flat
com.example.allinone.app-material-1.12.0-41\:/interpolator-v21/mtrl_linear_out_slow_in.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator-v21_mtrl_linear_out_slow_in.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout-sw600dp-v13/mtrl_layout_snackbar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-sw600dp-v13_mtrl_layout_snackbar.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_launcher_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_launcher_background.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_icon_rewind.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_rewind.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_picker_actions.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_picker_actions.xml.flat
com.example.allinone.app-main-112\:/drawable/rounded_corner_bg.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_rounded_corner_bg.xml.flat
com.example.allinone.app-main-112\:/drawable/dialog_rounded_bg.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_dialog_rounded_bg.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_ic_play_circle_filled.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_play_circle_filled.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_ic_clear_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ic_clear_material.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/abc_slide_in_bottom.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_slide_in_bottom.xml.flat
com.example.allinone.app-main-112\:/drawable/default_profile.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_default_profile.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_navigation_item_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_navigation_item_text_color.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_ic_rewind.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_rewind.xml.flat
com.example.allinone.app-core-1.13.1-51\:/layout-v21/notification_action_tombstone.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21_notification_action_tombstone.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_action_mode_close_item_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_action_mode_close_item_material.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_btn_radio_to_on_mtrl_015.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_btn_radio_to_on_mtrl_015.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_ic_skip_next.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_skip_next.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_navigation_bar_ripple_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_navigation_bar_ripple_color_selector.xml.flat
com.example.allinone.app-core-1.13.1-51\:/layout/notification_template_part_time.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notification_template_part_time.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-v21/abc_edit_text_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21_abc_edit_text_material.xml.flat
com.example.allinone.app-main-112\:/color/bottom_nav_item_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_bottom_nav_item_color.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_home.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_home.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/ic_mtrl_chip_close_circle.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_mtrl_chip_close_circle.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/layout/exo_list_divider.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_exo_list_divider.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_icon_shuffle_off.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_shuffle_off.png.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/material_ic_edit_black_24dp.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_material_ic_edit_black_24dp.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_btn_borderless_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_btn_borderless_material.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_textfield_search_default_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_textfield_search_default_mtrl_alpha.9.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_icon_fullscreen_enter.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_fullscreen_enter.png.flat
com.example.allinone.app-main-112\:/drawable/bg_day_with_event.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_bg_day_with_event.xml.flat
com.example.allinone.app-material-1.12.0-41\:/anim-v21/m3_bottom_sheet_slide_in.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21_m3_bottom_sheet_slide_in.xml.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/color/common_google_signin_btn_text_dark.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_common_google_signin_btn_text_dark.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_navigation_item_icon_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_navigation_item_icon_tint.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/abc_grow_fade_in_from_bottom.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_grow_fade_in_from_bottom.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_switch_track_decoration_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_switch_track_decoration_tint.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/navigation_empty_icon.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_navigation_empty_icon.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_search_dropdown_item_icons_2line.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_search_dropdown_item_icons_2line.xml.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/color/common_google_signin_btn_text_light.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_common_google_signin_btn_text_light.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_text_select_handle_left_mtrl.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_text_select_handle_left_mtrl.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_icon_next.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_next.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_icon_shuffle_on.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_shuffle_on.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_textfield_search_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_textfield_search_material.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_icon_rewind.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_rewind.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_list_menu_item_icon.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_list_menu_item_icon.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_timepicker_button_stroke.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_timepicker_button_stroke.xml.flat
com.example.allinone.app-main-112\:/drawable/selected_circle_shape.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_selected_circle_shape.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_navigation_bar_ripple_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_navigation_bar_ripple_color.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_edit.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_edit.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable/notification_tile_bg.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_notification_tile_bg.xml.flat
com.example.allinone.app-material-1.12.0-41\:/interpolator-v21/m3_sys_motion_easing_standard.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator-v21_m3_sys_motion_easing_standard.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/dialog_edit_lesson.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_edit_lesson.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_back.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_back.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/ic_clear_black_24.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_clear_black_24.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/design_navigation_item_header.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_design_navigation_item_header.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_icon_fastforward.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_fastforward.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_calendar_month_labeled.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_calendar_month_labeled.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_ic_rewind.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_rewind.png.flat
com.example.allinone.app-main-112\:/font/opensans_bold.ttf=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\font_opensans_bold.ttf.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_checkbox_button_icon_checked_indeterminate.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_checkbox_button_icon_checked_indeterminate.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_calendar_day.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_calendar_day.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_icon_repeat_one.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_repeat_one.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_edit_mode_logo.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_edit_mode_logo.png.flat
com.example.allinone.app-credentials-1.2.0-rc01-69\:/drawable-xhdpi-v4/ic_passkey.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_ic_passkey.png.flat
com.example.allinone.app-main-112\:/drawable/ic_folder.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_folder.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_code.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_code.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/design_fab_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_design_fab_background.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_spinner_mtrl_am_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_spinner_mtrl_am_alpha.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout-watch-v20/abc_alert_dialog_title_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-watch-v20_abc_alert_dialog_title_material.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout-night-v8/dialog_student_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-night_dialog_student_details.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout-watch-v20/abc_alert_dialog_button_bar_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-watch-v20_abc_alert_dialog_button_bar_material.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout-land/material_timepicker.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-land_material_timepicker.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_action_bar_title_item.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_action_bar_title_item.xml.flat
com.example.allinone.app-material-1.12.0-41\:/animator-v21/design_appbar_state_list_animator.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator-v21_design_appbar_state_list_animator.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_slider_inactive_track_color_legacy.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_slider_inactive_track_color_legacy.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_ic_skip_next.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_skip_next.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_alert_select_dialog_multichoice.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_alert_select_dialog_multichoice.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_scrubber_control_off_mtrl_alpha.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_scrubber_control_off_mtrl_alpha.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_navigation_rail_item_with_indicator_icon_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_navigation_rail_item_with_indicator_icon_tint.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/interpolator/btn_radio_to_off_mtrl_animation_interpolator_0.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator_btn_radio_to_off_mtrl_animation_interpolator_0.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_ic_fullscreen_exit.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_fullscreen_exit.png.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/dialog_fullscreen_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_fullscreen_image.xml.flat
com.example.allinone.app-material-1.12.0-41\:/animator/mtrl_extended_fab_show_motion_spec.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_mtrl_extended_fab_show_motion_spec.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_simple_item_ripple_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_simple_item_ripple_color.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_expand_less.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_expand_less.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_error.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_error.xml.flat
com.example.allinone.app-main-112\:/menu/menu_group_options.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\menu_menu_group_options.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-xhdpi-v4/ic_call_answer_video.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_ic_call_answer_video.png.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/material_ic_keyboard_arrow_left_black_24dp.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_material_ic_keyboard_arrow_left_black_24dp.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_icon_fullscreen_enter.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_fullscreen_enter.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_scrubber_primary_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_scrubber_primary_mtrl_alpha.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_personalized_color_primary_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_personalized_color_primary_text.xml.flat
com.example.allinone.app-material-1.12.0-41\:/animator/design_fab_hide_motion_spec.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_design_fab_hide_motion_spec.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_dynamic_dark_default_color_secondary_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_dynamic_dark_default_color_secondary_text.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-mdpi-v4/ic_call_answer_low.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_ic_call_answer_low.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxxhdpi-v4/abc_btn_switch_to_on_mtrl_00012.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_btn_switch_to_on_mtrl_00012.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_switch_track_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_switch_track_tint.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_transaction.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_transaction.xml.flat
com.example.allinone.app-browser-1.4.0-94\:/layout/browser_actions_context_menu_row.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_browser_actions_context_menu_row.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_dark_default_color_primary_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_dark_default_color_primary_text.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_button_foreground_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_button_foreground_color_selector.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/dialog_futures_tp_sl.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_futures_tp_sl.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_list_focused_holo.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_list_focused_holo.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_btn_switch_to_on_mtrl_00012.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_btn_switch_to_on_mtrl_00012.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_calendar_item_stroke_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_calendar_item_stroke_color.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_dynamic_hint_foreground.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_dynamic_hint_foreground.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/material_chip_input_combo.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_material_chip_input_combo.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_icon_fastforward.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_fastforward.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_scrubber_track_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_scrubber_track_mtrl_alpha.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_on_surface_emphasis_high_type.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_on_surface_emphasis_high_type.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_note_video.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_note_video.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_timepicker_button_background_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_timepicker_button_background_color.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_icon_circular_play.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_circular_play.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_btn_check_to_on_mtrl_000.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_btn_check_to_on_mtrl_000.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_text_select_handle_right_mtrl.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_text_select_handle_right_mtrl.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_chip_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_chip_text_color.xml.flat
com.example.allinone.app-material-1.12.0-41\:/interpolator-v21/m3_sys_motion_easing_standard_accelerate.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator-v21_m3_sys_motion_easing_standard_accelerate.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable-v21/mtrl_navigation_bar_item_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21_mtrl_navigation_bar_item_background.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_btn_stroke_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_btn_stroke_color_selector.xml.flat
com.example.allinone.app-credentials-1.2.0-rc01-69\:/drawable-anydpi-v21/ic_password.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_ic_password.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/select_dialog_item_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_select_dialog_item_material.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_slider_inactive_track_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_slider_inactive_track_color.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_icon_shuffle_on.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_shuffle_on.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_textfield_activated_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_textfield_activated_mtrl_alpha.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_dynamic_primary_text_disable_only.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_dynamic_primary_text_disable_only.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_filled_icon_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_filled_icon_tint.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_ic_default_album_image.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_default_album_image.png.flat
com.example.allinone.app-material-1.12.0-41\:/interpolator-v21/m3_sys_motion_easing_emphasized_accelerate.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator-v21_m3_sys_motion_easing_emphasized_accelerate.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_alert_select_dialog_item.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_alert_select_dialog_item.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-xxxhdpi-v4/ic_call_answer_video_low.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_ic_call_answer_video_low.png.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-xxhdpi-v4/common_google_signin_btn_icon_dark_normal_background.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_common_google_signin_btn_icon_dark_normal_background.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-ldrtl-hdpi-v17/abc_spinner_mtrl_am_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-hdpi-v17_abc_spinner_mtrl_am_alpha.9.png.flat
com.example.allinone.app-main-112\:/color/text_input_box_stroke.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_text_input_box_stroke.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_format_underline.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_format_underline.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_edit_mode_logo.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_edit_mode_logo.png.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-mdpi-v4/common_google_signin_btn_icon_dark_normal_background.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_common_google_signin_btn_icon_dark_normal_background.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_list_selector_disabled_holo_light.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_list_selector_disabled_holo_light.9.png.flat
com.example.allinone.app-core-1.13.1-51\:/layout/custom_dialog.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_custom_dialog.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_cab_background_top_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_cab_background_top_mtrl_alpha.9.png.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-xhdpi-v4/googleg_disabled_color_18.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_googleg_disabled_color_18.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_list_selector_disabled_holo_dark.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_list_selector_disabled_holo_dark.9.png.flat
com.example.allinone.app-media-1.6.0-80\:/layout/notification_template_media_custom.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notification_template_media_custom.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_icon_fullscreen_enter.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_fullscreen_enter.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/color/abc_hint_foreground_material_light.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_hint_foreground_material_light.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/ic_keyboard_black_24dp.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_keyboard_black_24dp.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_ref_palette_dynamic_neutral_variant12.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_ref_palette_dynamic_neutral_variant12.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_text_button_background_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_text_button_background_color_selector.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/btn_checkbox_unchecked_to_checked_mtrl_animation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_btn_checkbox_unchecked_to_checked_mtrl_animation.xml.flat
com.example.allinone.app-main-112\:/drawable/placeholder_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_placeholder_image.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_button_background_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_button_background_color_selector.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_chat_source.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_chat_source.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_ic_chevron_left.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_chevron_left.png.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_chat_user.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_chat_user.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_ic_pause_circle_filled.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_pause_circle_filled.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_ic_commit_search_api_mtrl_alpha.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_ic_commit_search_api_mtrl_alpha.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_calendar_month_navigation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_calendar_month_navigation.xml.flat
com.example.allinone.app-fragment-1.8.4-29\:/animator/fragment_close_exit.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_fragment_close_exit.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_tabs_ripple_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_tabs_ripple_color.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_switch_track_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_switch_track_mtrl_alpha.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_list_focused_holo.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_list_focused_holo.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_filled_icon_button_container_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_filled_icon_button_container_color_selector.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_textfield_search_activated_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_textfield_search_activated_mtrl_alpha.9.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_icon_repeat_one.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_repeat_one.png.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/dialog_program_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_program_details.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_checkbox_button_icon_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_checkbox_button_icon_tint.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_icon_repeat_all.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_repeat_all.png.flat
com.example.allinone.app-fragment-1.8.4-29\:/animator/fragment_open_exit.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_fragment_open_exit.xml.flat
com.example.allinone.app-material-1.12.0-41\:/anim/linear_indeterminate_line1_head_interpolator.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_linear_indeterminate_line1_head_interpolator.xml.flat
com.example.allinone.app-navigation-ui-2.8.2-87\:/animator/nav_default_pop_enter_anim.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_nav_default_pop_enter_anim.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_ic_pause_circle_filled.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_pause_circle_filled.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_futures_position.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_futures_position.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_ic_rewind.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_rewind.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_icon_previous.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_previous.png.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/ic_m3_chip_close.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_m3_chip_close.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/abc_slide_out_bottom.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_slide_out_bottom.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_text_select_handle_right_mtrl.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_text_select_handle_right_mtrl.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_slider_inactive_track_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_slider_inactive_track_color.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_popup_background_mtrl_mult.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_popup_background_mtrl_mult.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/animator/mtrl_btn_unelevated_state_list_anim.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_mtrl_btn_unelevated_state_list_anim.xml.flat
com.example.allinone.app-play-services-auth-21.2.0-63\:/drawable-watch-v20/common_google_signin_btn_text_dark_normal.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-watch-v20_common_google_signin_btn_text_dark_normal.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/tooltip_frame_dark.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_tooltip_frame_dark.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_tab_indicator_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_tab_indicator_mtrl_alpha.9.png.flat
com.example.allinone.app-main-112\:/drawable/ic_file.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_file.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-mdpi-v4/notification_bg_normal.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_notification_bg_normal.9.png.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-hdpi-v4/common_google_signin_btn_icon_dark_normal_background.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_common_google_signin_btn_icon_dark_normal_background.9.png.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-xhdpi-v4/ic_call_answer_video_low.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_ic_call_answer_video_low.png.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-xhdpi-v4/googleg_standard_color_18.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_googleg_standard_color_18.png.flat
com.example.allinone.app-main-112\:/drawable/ic_remove.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_remove.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/ic_mtrl_chip_checked_circle.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_mtrl_chip_checked_circle.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_select_dialog_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_select_dialog_material.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_list_longpressed_holo.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_list_longpressed_holo.9.png.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/offline_status_view.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_offline_status_view.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_ab_share_pack_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_ab_share_pack_mtrl_alpha.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_switch_thumb_checked.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_switch_thumb_checked.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxxhdpi-v4/abc_text_select_handle_right_mtrl.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_text_select_handle_right_mtrl.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/material_timepicker_textinput_display.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_material_timepicker_textinput_display.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_switch_track_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_switch_track_mtrl_alpha.9.png.flat
com.example.allinone.app-main-112\:/drawable/bg_current_day.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_bg_current_day.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_scrubber_control_to_pressed_mtrl_005.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_scrubber_control_to_pressed_mtrl_005.png.flat
com.example.allinone.app-play-services-measurement-api-22.1.2-96\:/xml/ga_ad_services_config.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_ga_ad_services_config.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_chip_background_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_chip_background_color.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/m3_alert_dialog.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_m3_alert_dialog.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_scrubber_track_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_scrubber_track_mtrl_alpha.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/interpolator-v21/m3_sys_motion_easing_emphasized_decelerate.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator-v21_m3_sys_motion_easing_emphasized_decelerate.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_btn_radio_to_on_mtrl_000.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_btn_radio_to_on_mtrl_000.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_icon_pause.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_pause.xml.flat
com.example.allinone.app-credentials-1.2.0-rc01-69\:/drawable-anydpi-v24/ic_passkey.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v24_ic_passkey.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/m3_side_sheet_dialog.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_m3_side_sheet_dialog.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_ic_subtitle_on.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_subtitle_on.png.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout-night-v8/item_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-night_item_backup.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_ic_menu_copy_mtrl_am_alpha.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ic_menu_copy_mtrl_am_alpha.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_ic_check_mark.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_ic_check_mark.xml.flat
com.example.allinone.app-fragment-1.8.4-29\:/anim-v21/fragment_fast_out_extra_slow_in.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21_fragment_fast_out_extra_slow_in.xml.flat
com.example.allinone.app-material-1.12.0-41\:/anim-v21/mtrl_bottom_sheet_slide_out.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21_mtrl_bottom_sheet_slide_out.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_transactions.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_transactions.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/btn_radio_off_mtrl.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_btn_radio_off_mtrl.xml.flat
com.example.allinone.app-main-112\:/color-night/dialog_action_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-night_dialog_action_button_color.xml.flat
com.example.allinone.app-media-1.6.0-80\:/layout/notification_template_media.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notification_template_media.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_btn_check_to_on_mtrl_015.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_btn_check_to_on_mtrl_015.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_ic_settings.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_settings.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_textfield_default_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_textfield_default_mtrl_alpha.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_navigation_item_background_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_navigation_item_background_color.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_icon_circular_play.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_circular_play.png.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/ic_clock_black_24dp.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_clock_black_24dp.xml.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable/common_google_signin_btn_text_light.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_common_google_signin_btn_text_light.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_list_longpressed_holo.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_list_longpressed_holo.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_picker_text_input_date_range.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_picker_text_input_date_range.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/material_clockface_view.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_material_clockface_view.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_item_background_holo_dark.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_item_background_holo_dark.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/btn_checkbox_checked_to_unchecked_mtrl_animation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_btn_checkbox_checked_to_unchecked_mtrl_animation.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator_btn_checkbox_unchecked_mtrl_animation_interpolator_1.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_checkbox_button_checked_unchecked.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_checkbox_button_checked_unchecked.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-xxhdpi-v4/ic_call_answer_low.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_ic_call_answer_low.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_divider_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_divider_color.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_edit_mode_logo.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_edit_mode_logo.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_textfield_search_activated_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_textfield_search_activated_mtrl_alpha.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_switch_track_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_switch_track_tint.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/material_ic_menu_arrow_up_black_24dp.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_material_ic_menu_arrow_up_black_24dp.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_ic_audiotrack.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_audiotrack.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_btn_bg_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_btn_bg_color_selector.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_list_longpressed_holo.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_list_longpressed_holo.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_btn_check_to_on_mtrl_000.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_btn_check_to_on_mtrl_000.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_ratingbar_indicator_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ratingbar_indicator_material.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_switch_thumb_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_switch_thumb_material.xml.flat
com.example.allinone.app-media-1.6.0-80\:/layout/notification_media_cancel_action.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notification_media_cancel_action.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_ref_palette_dynamic_neutral_variant92.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_ref_palette_dynamic_neutral_variant92.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_tabs_icon_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_tabs_icon_color_selector.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_switch_thumb_checked_unchecked.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_switch_thumb_checked_unchecked.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_list_divider_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_list_divider_mtrl_alpha.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_search_bar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_search_bar.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_chip_close_icon_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_chip_close_icon_tint.xml.flat
com.example.allinone.app-main-112\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.allinone.app-main-112\:/drawable/ic_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_history.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_on_surface_ripple_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_on_surface_ripple_color.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-xhdpi-v4/ic_call_decline.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_ic_call_decline.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-ldrtl-mdpi-v17/abc_spinner_mtrl_am_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-mdpi-v17_abc_spinner_mtrl_am_alpha.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/design_password_eye.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_design_password_eye.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_slider_active_track_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_slider_active_track_color.xml.flat
com.example.allinone.app-core-splashscreen-1.0.1-91\:/layout/splash_screen_view.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_splash_screen_view.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-hdpi-v4/ic_call_answer_video.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_ic_call_answer_video.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxxhdpi-v4/abc_scrubber_control_to_pressed_mtrl_005.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_scrubber_control_to_pressed_mtrl_005.png.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-hdpi-v4/ic_call_answer_video_low.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_ic_call_answer_video_low.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_button_ripple_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_button_ripple_color_selector.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_ic_rewind.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_rewind.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_icon_fullscreen_enter.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_fullscreen_enter.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_btn_radio_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_btn_radio_material.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_timepicker_clockface.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_timepicker_clockface.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_spinner_mtrl_am_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_spinner_mtrl_am_alpha.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/btn_checkbox_checked_mtrl.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_btn_checkbox_checked_mtrl.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_category_game.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_category_game.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_icon_shuffle_off.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_shuffle_off.png.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-ldpi-v4/ic_call_answer_low.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_ic_call_answer_low.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/m3_auto_complete_simple_item.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_m3_auto_complete_simple_item.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_list_pressed_holo_light.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_list_pressed_holo_light.9.png.flat
com.example.allinone.app-main-112\:/drawable/ic_wt.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_wt.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_search_white.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_search_white.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/color-v23/abc_tint_spinner.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23_abc_tint_spinner.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_checkbox_button_unchecked_checked.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_checkbox_button_unchecked_checked.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-v21/abc_btn_colored_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21_abc_btn_colored_material.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/design_navigation_menu_item.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_design_navigation_menu_item.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_scrubber_primary_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_scrubber_primary_mtrl_alpha.9.png.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-mdpi-v4/ic_call_answer_video_low.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_ic_call_answer_video_low.png.flat
com.example.allinone.app-main-112\:/drawable/ic_students.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_students.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_scrubber_track_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_scrubber_track_mtrl_alpha.9.png.flat
com.example.allinone.app-main-112\:/drawable/ic_video_placeholder.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_video_placeholder.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_binance_position.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_binance_position.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/abc_slide_in_top.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_slide_in_top.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/btn_checkbox_to_checked_icon_null_animation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_checkbox_to_checked_icon_null_animation.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_ic_menu_share_mtrl_alpha.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ic_menu_share_mtrl_alpha.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/material_ic_clear_black_24dp.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_material_ic_clear_black_24dp.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_fab_bg_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_fab_bg_color_selector.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_ic_play_circle_filled.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_play_circle_filled.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_on_primary_text_btn_text_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_on_primary_text_btn_text_color_selector.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_ic_chevron_left.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_chevron_left.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_seekbar_track_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_seekbar_track_material.xml.flat
com.example.allinone.app-fragment-1.8.4-29\:/animator/fragment_fade_exit.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_fragment_fade_exit.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/color/abc_background_cache_hint_selector_material_light.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_background_cache_hint_selector_material_light.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_wt_registration.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_wt_registration.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-watch-v20/abc_dialog_material_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-watch-v20_abc_dialog_material_background.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_expand_more.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_expand_more.xml.flat
com.example.allinone.app-firebase-crashlytics-19.2.1-66\:/raw/firebase_crashlytics_keep.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw_firebase_crashlytics_keep.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_alert_dialog_button_bar_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_alert_dialog_button_bar_material.xml.flat
com.example.allinone.app-main-112\:/color/dialog_delete_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_dialog_delete_button_color.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_lesson.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_lesson.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_icon_repeat_all.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_repeat_all.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/color/abc_search_url_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_search_url_text.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_seekbar_tick_mark_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_seekbar_tick_mark_material.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_ab_share_pack_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_ab_share_pack_mtrl_alpha.9.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_icon_shuffle_off.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_shuffle_off.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_personalized__highlighted_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_personalized__highlighted_text.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/dialog_add_event.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_add_event.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-anydpi-v21/ic_call_answer_video.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_ic_call_answer_video.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_ic_check.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_check.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/layout/exo_track_selection_dialog.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_exo_track_selection_dialog.xml.flat
com.example.allinone.app-material-1.12.0-41\:/animator/m3_btn_elevated_btn_state_list_anim.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_m3_btn_elevated_btn_state_list_anim.xml.flat
com.example.allinone.app-material-1.12.0-41\:/anim/m3_motion_fade_exit.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_m3_motion_fade_exit.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_cleardata.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_cleardata.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/layout/exo_styled_player_control_view.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_exo_styled_player_control_view.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_icon_fastforward.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_fastforward.png.flat
com.example.allinone.app-material-1.12.0-41\:/animator/m3_chip_state_list_anim.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_m3_chip_state_list_anim.xml.flat
com.example.allinone.app-main-112\:/drawable/splash_layout_drawable.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_splash_layout_drawable.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_icon_repeat_one.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_repeat_one.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_switch_thumb_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_switch_thumb_tint.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_edit_mode_logo.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_edit_mode_logo.png.flat
com.example.allinone.app-material-1.12.0-41\:/anim/linear_indeterminate_line2_head_interpolator.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_linear_indeterminate_line2_head_interpolator.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-hdpi-v4/notification_bg_normal_pressed.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_notification_bg_normal_pressed.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_button_ripple_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_button_ripple_color.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_ic_fullscreen_enter.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_fullscreen_enter.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_list_menu_item_radio.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_list_menu_item_radio.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_calendar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_calendar.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/select_dialog_singlechoice_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_select_dialog_singlechoice_material.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/font/roboto_medium_numbers.ttf=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\font_roboto_medium_numbers.ttf.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/activity_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_activity_backup.xml.flat
com.example.allinone.app-ui-release-77\:/color/vector_tint_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_vector_tint_color.xml.flat
com.example.allinone.app-fragment-1.8.4-29\:/animator/fragment_close_enter.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_fragment_close_enter.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxxhdpi-v4/abc_switch_track_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_abc_switch_track_mtrl_alpha.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_screen_content_include.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_screen_content_include.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_ic_speed.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_ic_speed.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout-land/mtrl_picker_header_dialog.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-land_mtrl_picker_header_dialog.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_backup.xml.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable/common_google_signin_btn_text_light_normal.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_common_google_signin_btn_text_light_normal.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_btn_switch_to_on_mtrl_00012.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_btn_switch_to_on_mtrl_00012.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/color-v23/abc_tint_switch_track.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23_abc_tint_switch_track.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/design_layout_tab_icon.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_design_layout_tab_icon.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_icon_fullscreen_exit.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_fullscreen_exit.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_list_selector_disabled_holo_dark.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_list_selector_disabled_holo_dark.9.png.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable/common_google_signin_btn_icon_disabled.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_common_google_signin_btn_icon_disabled.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_star_half_black_48dp.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_star_half_black_48dp.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_menu_hardkey_panel_mtrl_mult.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_menu_hardkey_panel_mtrl_mult.9.png.flat
com.example.allinone.app-main-112\:/drawable/ic_category_shopping.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_category_shopping.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_icon_vr.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_vr.png.flat
com.example.allinone.app-main-112\:/drawable/ic_category_salary.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_category_salary.xml.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-xxhdpi-v4/googleg_standard_color_18.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_googleg_standard_color_18.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_scrubber_control_off_mtrl_alpha.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_scrubber_control_off_mtrl_alpha.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_outlined_icon_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_outlined_icon_tint.xml.flat
com.example.allinone.app-main-112\:/menu/menu_task_options.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\menu_menu_task_options.xml.flat
com.example.allinone.app-material-1.12.0-41\:/interpolator-v21/m3_sys_motion_easing_emphasized.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator-v21_m3_sys_motion_easing_emphasized.xml.flat
com.example.allinone.app-browser-1.4.0-94\:/layout/browser_actions_context_menu_page.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_browser_actions_context_menu_page.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_textfield_input_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_textfield_input_text_color.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_textfield_activated_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_textfield_activated_mtrl_alpha.9.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_ic_settings.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_settings.png.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/dialog_expense_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_expense_investment.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/color/abc_hint_foreground_material_dark.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_hint_foreground_material_dark.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-ldpi-v4/ic_call_decline_low.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_ic_call_decline_low.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_icon_repeat_off.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_repeat_off.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_dashboard.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_dashboard.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-night-v8/material_timepicker_button_stroke.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-night-v8_material_timepicker_button_stroke.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_icon_fullscreen_exit.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_fullscreen_exit.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_screen_toolbar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_screen_toolbar.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_icon_rewind.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_rewind.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_icon_vr.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_vr.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_timepicker_display_ripple_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_timepicker_display_ripple_color.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_ic_fullscreen_exit.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_fullscreen_exit.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_icon_fullscreen_enter.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_icon_fullscreen_enter.png.flat
com.example.allinone.app-material-1.12.0-41\:/interpolator-v21/m3_sys_motion_easing_standard_decelerate.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator-v21_m3_sys_motion_easing_standard_decelerate.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_ic_chevron_right.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_chevron_right.png.flat
com.example.allinone.app-main-112\:/menu/search_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\menu_search_history.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/dialog_pin_input.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_pin_input.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_list_selector_holo_dark.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_list_selector_holo_dark.xml.flat
com.example.allinone.app-main-112\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_checkbox_button_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_checkbox_button_tint.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-xxxhdpi-v4/ic_call_decline_low.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_ic_call_decline_low.png.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-mdpi-v4/googleg_disabled_color_18.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_googleg_disabled_color_18.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_card_view_ripple.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_card_view_ripple.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/color/switch_thumb_material_light.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_switch_thumb_material_light.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_personalized_color_secondary_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_personalized_color_secondary_text.xml.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-xhdpi-v4/common_google_signin_btn_icon_dark_normal_background.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_common_google_signin_btn_icon_dark_normal_background.9.png.flat
com.example.allinone.app-media-1.6.0-80\:/layout/notification_template_big_media_narrow_custom.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_notification_template_big_media_narrow_custom.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_ic_play_circle_filled.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_ic_play_circle_filled.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_ic_skip_previous.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_skip_previous.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_error.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_error.xml.flat
com.example.allinone.app-main-112\:/drawable/bg_selected_day.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_bg_selected_day.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/btn_radio_to_on_mtrl_ring_outer_animation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_btn_radio_to_on_mtrl_ring_outer_animation.xml.flat
com.example.allinone.app-credentials-1.2.0-rc01-69\:/drawable-xxhdpi-v4/ic_password.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_ic_password.png.flat
com.example.allinone.app-material-1.12.0-41\:/layout/design_bottom_sheet_dialog.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_design_bottom_sheet_dialog.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_wt_event.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_wt_event.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/pie_chart_tooltip.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_pie_chart_tooltip.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_list_pressed_holo_light.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_list_pressed_holo_light.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/color-v23/abc_tint_btn_checkable.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23_abc_tint_btn_checkable.xml.flat
com.example.allinone.app-credentials-1.2.0-rc01-69\:/drawable-xxhdpi-v4/ic_other_sign_in.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_ic_other_sign_in.png.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_checkbox_button_icon_indeterminate_checked.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_checkbox_button_icon_indeterminate_checked.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_icon_circular_play.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_circular_play.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_icon_play.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_play.png.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_ref_palette_dynamic_neutral17.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_ref_palette_dynamic_neutral17.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_ic_rewind.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_rewind.png.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_dynamic_dark_highlighted_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_dynamic_dark_highlighted_text.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_spinner_mtrl_am_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_spinner_mtrl_am_alpha.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_timepicker_button_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_timepicker_button_background.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/splash_text_layout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_splash_text_layout.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_scrubber_track_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_scrubber_track_mtrl_alpha.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/animator/m3_extended_fab_change_size_collapse_motion_spec.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_m3_extended_fab_change_size_collapse_motion_spec.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_textfield_default_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_textfield_default_mtrl_alpha.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/anim/abc_shrink_fade_out_from_bottom.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_abc_shrink_fade_out_from_bottom.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_timepicker_button_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_timepicker_button_text_color.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/color/abc_background_cache_hint_selector_material_dark.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_background_cache_hint_selector_material_dark.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_calendar_days_of_week.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_calendar_days_of_week.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_text_select_handle_middle_mtrl.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_text_select_handle_middle_mtrl.png.flat
com.example.allinone.app-main-112\:/drawable/ic_tasks.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_tasks.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_on_background_emphasis_high_type.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_on_background_emphasis_high_type.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_calendar_item_stroke_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_calendar_item_stroke_color.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_cab_background_top_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_cab_background_top_material.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_checkbox.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_checkbox.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_popup_background_mtrl_mult.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_popup_background_mtrl_mult.9.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-hdpi-v4/exo_icon_next.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_exo_icon_next.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_navigation_item_background_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_navigation_item_background_color.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-xxhdpi-v4/ic_call_answer_video.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_ic_call_answer_video.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/btn_radio_off_to_on_mtrl_animation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_btn_radio_off_to_on_mtrl_animation.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_ic_search_api_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ic_search_api_material.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_ic_default_album_image.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_default_album_image.png.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/color/common_google_signin_btn_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_common_google_signin_btn_tint.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_ref_palette_dynamic_neutral87.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_ref_palette_dynamic_neutral87.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/btn_checkbox_unchecked_mtrl.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_btn_checkbox_unchecked_mtrl.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_btn_check_to_on_mtrl_015.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_btn_check_to_on_mtrl_015.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_timepicker_display_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_timepicker_display_text_color.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_icon_fullscreen_exit.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_fullscreen_exit.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_tabs_icon_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_tabs_icon_color.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_ic_subtitle_off.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_subtitle_off.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_ic_forward.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_forward.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_on_background_emphasis_medium.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_on_background_emphasis_medium.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_ic_check.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_ic_check.png.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_checkbox_button_icon.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_checkbox_button_icon.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_action_mode_bar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_action_mode_bar.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_play.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_play.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/dialog_workout_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_workout_details.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-hdpi-v4/ic_call_answer.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_ic_call_answer.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/color/abc_primary_text_disable_only_material_light.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_abc_primary_text_disable_only_material_light.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/dialog_task_group.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_task_group.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_btn_check_to_on_mtrl_015.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_btn_check_to_on_mtrl_015.png.flat
com.example.allinone.app-material-1.12.0-41\:/drawable-v21/material_cursor_drawable.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21_material_cursor_drawable.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-xxxhdpi-v4/ic_call_answer_video.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_ic_call_answer_video.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_btn_text_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_btn_text_color_selector.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_on_primary_emphasis_high_type.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_on_primary_emphasis_high_type.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/design_box_stroke_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_design_box_stroke_color.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/layout/abc_alert_dialog_title_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_abc_alert_dialog_title_material.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_switch_track_decoration.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_switch_track_decoration.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_filled_stroke_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_filled_stroke_color.xml.flat
com.example.allinone.app-material-1.12.0-41\:/animator/mtrl_card_state_list_anim.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_mtrl_card_state_list_anim.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_investment.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_list_longpressed_holo.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_list_longpressed_holo.9.png.flat
com.example.allinone.app-main-112\:/menu/menu_tasks.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\menu_menu_tasks.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_ref_palette_dynamic_neutral_variant22.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_ref_palette_dynamic_neutral_variant22.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_instagram.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_instagram.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_investment_dropdown.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_investment_dropdown.xml.flat
com.example.allinone.app-main-112\:/color/drawer_item_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_drawer_item_color.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_icon_fullscreen_enter.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_fullscreen_enter.png.flat
com.example.allinone.app-main-112\:/menu/search_notes.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\menu_search_notes.xml.flat
com.example.allinone.app-main-112\:/drawable/circle_background_red.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_circle_background_red.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_category_spending.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_category_spending.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/mtrl_ic_error.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_mtrl_ic_error.xml.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable/common_google_signin_btn_icon_dark_focused.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_common_google_signin_btn_icon_dark_focused.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-hdpi-v4/abc_btn_switch_to_on_mtrl_00001.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_abc_btn_switch_to_on_mtrl_00001.9.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_ic_skip_previous.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_skip_previous.xml.flat
com.example.allinone.app-material-1.12.0-41\:/animator/mtrl_btn_state_list_anim.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_mtrl_btn_state_list_anim.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xxhdpi-v4/abc_btn_check_to_on_mtrl_000.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_abc_btn_check_to_on_mtrl_000.png.flat
com.example.allinone.app-material-1.12.0-41\:/animator/mtrl_chip_state_list_anim.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_mtrl_chip_state_list_anim.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_navigation_rail_item_with_indicator_label_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_navigation_rail_item_with_indicator_label_tint.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/test_level_drawable.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_test_level_drawable.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_tab_indicator_mtrl_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_tab_indicator_mtrl_alpha.9.png.flat
com.example.allinone.app-main-112\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_calendar_year.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_calendar_year.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-hdpi-v4/notification_bg_low_normal.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_notification_bg_low_normal.9.png.flat
com.example.allinone.app-main-112\:/drawable/ic_draw.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_draw.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_btn_check_to_on_mtrl_000.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_btn_check_to_on_mtrl_000.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_ic_play_circle_filled.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_ic_play_circle_filled.png.flat
com.example.allinone.app-main-112\:/xml/network_security_config.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_network_security_config.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_icon_shuffle_on.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_shuffle_on.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/layout-v23/exo_styled_player_control_rewind_button.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v23_exo_styled_player_control_rewind_button.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/design_snackbar_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_design_snackbar_background.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/material_clock_display_divider.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_material_clock_display_divider.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_transaction_report.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_transaction_report.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/fragment_futures.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_fragment_futures.xml.flat
com.example.allinone.app-material-1.12.0-41\:/animator/mtrl_extended_fab_state_list_animator.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_mtrl_extended_fab_state_list_animator.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_timepicker_clock_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_timepicker_clock_text_color.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_icon_pause.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_pause.png.flat
com.example.allinone.app-core-1.13.1-51\:/layout-v21/notification_template_icon_group.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21_notification_template_icon_group.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_dynamic_dark_primary_text_disable_only.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_dynamic_dark_primary_text_disable_only.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_icon_stop.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_icon_stop.xml.flat
com.example.allinone.app-material-1.12.0-41\:/layout/mtrl_alert_dialog.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_mtrl_alert_dialog.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_ic_subtitle_on.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_subtitle_on.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_icon_repeat_one.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_icon_repeat_one.png.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-hdpi-v4/googleg_standard_color_18.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_googleg_standard_color_18.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/material_personalized_hint_foreground.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_material_personalized_hint_foreground.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_ic_subtitle_off.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_subtitle_off.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-ldrtl-xxhdpi-v17/abc_spinner_mtrl_am_alpha.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-xxhdpi-v17_abc_spinner_mtrl_am_alpha.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_ratingbar_material.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_ratingbar_material.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxhdpi-v4/exo_edit_mode_logo.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4_exo_edit_mode_logo.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/btn_radio_on_to_off_mtrl_animation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_btn_radio_on_to_off_mtrl_animation.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-anydpi-v21/exo_ic_chevron_left.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21_exo_ic_chevron_left.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/color-v23/abc_tint_edittext.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23_abc_tint_edittext.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-mdpi-v4/ic_call_answer.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_ic_call_answer.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator_btn_checkbox_unchecked_mtrl_animation_interpolator_0.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/dialog_add_task.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_add_task.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_category_transport.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_category_transport.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_ref_palette_dynamic_neutral_variant94.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_ref_palette_dynamic_neutral_variant94.xml.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/item_file_structure.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_item_file_structure.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_ic_settings.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_settings.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xxxhdpi-v4/exo_ic_speed.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4_exo_ic_speed.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_ic_speed.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_ic_speed.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_icon_fastforward.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_fastforward.png.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-ldpi-v4/exo_icon_repeat_off.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4_exo_icon_repeat_off.png.flat
com.example.allinone.app-material-1.12.0-41\:/anim/linear_indeterminate_line1_tail_interpolator.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim_linear_indeterminate_line1_tail_interpolator.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_choice_chip_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_choice_chip_text_color.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_list_selector_background_transition_holo_dark.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_list_selector_background_transition_holo_dark.xml.flat
com.example.allinone.app-material-1.12.0-41\:/anim-v21/design_bottom_sheet_slide_out.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21_design_bottom_sheet_slide_out.xml.flat
com.example.allinone.app-main-112\:/drawable-night/dialog_rounded_bg.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-night_dialog_rounded_bg.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-xhdpi-v4/exo_icon_pause.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_exo_icon_pause.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-mdpi-v4/abc_menu_hardkey_panel_mtrl_mult.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_abc_menu_hardkey_panel_mtrl_mult.9.png.flat
com.example.allinone.app-material-1.12.0-41\:/color-v31/m3_ref_palette_dynamic_neutral_variant4.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31_m3_ref_palette_dynamic_neutral_variant4.xml.flat
com.example.allinone.app-material-1.12.0-41\:/animator/m3_extended_fab_hide_motion_spec.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator_m3_extended_fab_hide_motion_spec.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_category_sports.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_category_sports.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_btn_radio_to_on_mtrl_015.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_btn_radio_to_on_mtrl_015.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_tabs_icon_color_selector_colored.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_tabs_icon_color_selector_colored.xml.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable/common_google_signin_btn_text_light_focused.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_common_google_signin_btn_text_light_focused.xml.flat
com.example.allinone.app-material-1.12.0-41\:/drawable/design_ic_visibility.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_design_ic_visibility.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_icon_repeat_one.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_repeat_one.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_navigation_bar_item_tint.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_navigation_bar_item_tint.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_chip_ripple_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_chip_ripple_color.xml.flat
com.example.allinone.app-core-1.13.1-51\:/drawable-mdpi-v4/notification_bg_low_normal.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_notification_bg_low_normal.9.png.flat
com.example.allinone.app-play-services-base-18.5.0-71\:/drawable-hdpi-v4/common_google_signin_btn_text_light_normal_background.9.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4_common_google_signin_btn_text_light_normal_background.9.png.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable-xhdpi-v4/abc_btn_radio_to_on_mtrl_000.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4_abc_btn_radio_to_on_mtrl_000.png.flat
com.example.allinone.app-material-1.12.0-41\:/color/m3_timepicker_secondary_text_button_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_m3_timepicker_secondary_text_button_text_color.xml.flat
com.example.allinone.app-exoplayer-ui-2.19.1-47\:/drawable-mdpi-v4/exo_icon_fullscreen_exit.png=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4_exo_icon_fullscreen_exit.png.flat
com.example.allinone.app-mergeReleaseResources-109\:/layout/dialog_progress.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout_dialog_progress.xml.flat
com.example.allinone.app-material-1.12.0-41\:/color/mtrl_fab_icon_text_color_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color_mtrl_fab_icon_text_color_selector.xml.flat
com.example.allinone.app-appcompat-1.7.0-6\:/drawable/abc_list_selector_holo_light.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_abc_list_selector_holo_light.xml.flat
