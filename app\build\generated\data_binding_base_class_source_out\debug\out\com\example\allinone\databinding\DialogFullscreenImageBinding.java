// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import androidx.viewpager2.widget.ViewPager2;
import com.example.allinone.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogFullscreenImageBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final ViewPager2 fullscreenViewPager;

  @NonNull
  public final TextView imageCounterText;

  private DialogFullscreenImageBinding(@NonNull FrameLayout rootView,
      @NonNull ViewPager2 fullscreenViewPager, @NonNull TextView imageCounterText) {
    this.rootView = rootView;
    this.fullscreenViewPager = fullscreenViewPager;
    this.imageCounterText = imageCounterText;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogFullscreenImageBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogFullscreenImageBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_fullscreen_image, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogFullscreenImageBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.fullscreenViewPager;
      ViewPager2 fullscreenViewPager = ViewBindings.findChildViewById(rootView, id);
      if (fullscreenViewPager == null) {
        break missingId;
      }

      id = R.id.imageCounterText;
      TextView imageCounterText = ViewBindings.findChildViewById(rootView, id);
      if (imageCounterText == null) {
        break missingId;
      }

      return new DialogFullscreenImageBinding((FrameLayout) rootView, fullscreenViewPager,
          imageCounterText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
