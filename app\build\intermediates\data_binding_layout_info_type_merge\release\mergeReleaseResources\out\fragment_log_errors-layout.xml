<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_log_errors" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\fragment_log_errors.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_log_errors_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="70" endOffset="51"/></Target><Target id="@+id/recyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="7" startOffset="4" endLine="15" endOffset="51"/></Target><Target id="@+id/emptyView" view="TextView"><Expressions/><location startLine="17" startOffset="4" endLine="28" endOffset="61"/></Target><Target id="@+id/buttonLayout" view="LinearLayout"><Expressions/><location startLine="30" startOffset="4" endLine="68" endOffset="18"/></Target><Target id="@+id/refreshButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="39" startOffset="8" endLine="47" endOffset="41"/></Target><Target id="@+id/clearButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="49" startOffset="8" endLine="57" endOffset="41"/></Target><Target id="@+id/shareButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="59" startOffset="8" endLine="66" endOffset="41"/></Target></Targets></Layout>