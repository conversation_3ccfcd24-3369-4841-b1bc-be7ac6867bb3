<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Seminar Name -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/nameLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Seminar Name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/nameInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Date Field -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/dateLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:hint="Date"
            app:endIconDrawable="@android:drawable/ic_menu_today"
            app:endIconMode="custom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/nameLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/dateInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:inputType="none" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Start Time Field -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/startTimeLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="8dp"
            android:hint="Start Time"
            app:endIconDrawable="@android:drawable/ic_menu_recent_history"
            app:endIconMode="custom"
            app:layout_constraintEnd_toStartOf="@+id/endTimeLayout"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/dateLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/startTimeInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:inputType="none" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- End Time Field (auto-calculated) -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/endTimeLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="16dp"
            android:hint="End Time"
            app:endIconDrawable="@android:drawable/ic_menu_recent_history"
            app:endIconMode="custom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/startTimeLayout"
            app:layout_constraintTop_toBottomOf="@+id/dateLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/endTimeInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:inputType="none" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Description Field (Optional) -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/descriptionLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:hint="Description (Optional)"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/startTimeLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/descriptionInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="top"
                android:inputType="textMultiLine"
                android:minLines="3" />

        </com.google.android.material.textfield.TextInputLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.core.widget.NestedScrollView> 