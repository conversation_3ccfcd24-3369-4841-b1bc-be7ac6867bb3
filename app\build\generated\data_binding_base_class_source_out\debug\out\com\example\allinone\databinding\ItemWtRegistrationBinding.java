// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.chip.Chip;
import com.google.android.material.imageview.ShapeableImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemWtRegistrationBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView amount;

  @NonNull
  public final TextView endDate;

  @NonNull
  public final Chip paymentStatusChip;

  @NonNull
  public final ImageButton shareButton;

  @NonNull
  public final TextView startDate;

  @NonNull
  public final TextView studentName;

  @NonNull
  public final ShapeableImageView studentPhoto;

  private ItemWtRegistrationBinding(@NonNull MaterialCardView rootView, @NonNull TextView amount,
      @NonNull TextView endDate, @NonNull Chip paymentStatusChip, @NonNull ImageButton shareButton,
      @NonNull TextView startDate, @NonNull TextView studentName,
      @NonNull ShapeableImageView studentPhoto) {
    this.rootView = rootView;
    this.amount = amount;
    this.endDate = endDate;
    this.paymentStatusChip = paymentStatusChip;
    this.shareButton = shareButton;
    this.startDate = startDate;
    this.studentName = studentName;
    this.studentPhoto = studentPhoto;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemWtRegistrationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemWtRegistrationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_wt_registration, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemWtRegistrationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.amount;
      TextView amount = ViewBindings.findChildViewById(rootView, id);
      if (amount == null) {
        break missingId;
      }

      id = R.id.endDate;
      TextView endDate = ViewBindings.findChildViewById(rootView, id);
      if (endDate == null) {
        break missingId;
      }

      id = R.id.paymentStatusChip;
      Chip paymentStatusChip = ViewBindings.findChildViewById(rootView, id);
      if (paymentStatusChip == null) {
        break missingId;
      }

      id = R.id.shareButton;
      ImageButton shareButton = ViewBindings.findChildViewById(rootView, id);
      if (shareButton == null) {
        break missingId;
      }

      id = R.id.startDate;
      TextView startDate = ViewBindings.findChildViewById(rootView, id);
      if (startDate == null) {
        break missingId;
      }

      id = R.id.studentName;
      TextView studentName = ViewBindings.findChildViewById(rootView, id);
      if (studentName == null) {
        break missingId;
      }

      id = R.id.studentPhoto;
      ShapeableImageView studentPhoto = ViewBindings.findChildViewById(rootView, id);
      if (studentPhoto == null) {
        break missingId;
      }

      return new ItemWtRegistrationBinding((MaterialCardView) rootView, amount, endDate,
          paymentStatusChip, shareButton, startDate, studentName, studentPhoto);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
