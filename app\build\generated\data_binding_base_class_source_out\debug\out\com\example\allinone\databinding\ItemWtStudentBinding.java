// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.imageview.ShapeableImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemWtStudentBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView email;

  @NonNull
  public final TextView phoneNumber;

  @NonNull
  public final ShapeableImageView profileImage;

  @NonNull
  public final View statusIndicator;

  @NonNull
  public final TextView studentName;

  private ItemWtStudentBinding(@NonNull MaterialCardView rootView, @NonNull TextView email,
      @NonNull TextView phoneNumber, @NonNull ShapeableImageView profileImage,
      @NonNull View statusIndicator, @NonNull TextView studentName) {
    this.rootView = rootView;
    this.email = email;
    this.phoneNumber = phoneNumber;
    this.profileImage = profileImage;
    this.statusIndicator = statusIndicator;
    this.studentName = studentName;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemWtStudentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemWtStudentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_wt_student, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemWtStudentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.email;
      TextView email = ViewBindings.findChildViewById(rootView, id);
      if (email == null) {
        break missingId;
      }

      id = R.id.phoneNumber;
      TextView phoneNumber = ViewBindings.findChildViewById(rootView, id);
      if (phoneNumber == null) {
        break missingId;
      }

      id = R.id.profileImage;
      ShapeableImageView profileImage = ViewBindings.findChildViewById(rootView, id);
      if (profileImage == null) {
        break missingId;
      }

      id = R.id.statusIndicator;
      View statusIndicator = ViewBindings.findChildViewById(rootView, id);
      if (statusIndicator == null) {
        break missingId;
      }

      id = R.id.studentName;
      TextView studentName = ViewBindings.findChildViewById(rootView, id);
      if (studentName == null) {
        break missingId;
      }

      return new ItemWtStudentBinding((MaterialCardView) rootView, email, phoneNumber, profileImage,
          statusIndicator, studentName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
