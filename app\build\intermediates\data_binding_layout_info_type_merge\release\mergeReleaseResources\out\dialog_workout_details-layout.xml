<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_workout_details" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\dialog_workout_details.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_workout_details_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="49" endOffset="14"/></Target><Target id="@+id/workout_name_text" view="TextView"><Expressions/><location startLine="9" startOffset="4" endLine="14" endOffset="41"/></Target><Target id="@+id/workout_date_text" view="TextView"><Expressions/><location startLine="16" startOffset="4" endLine="22" endOffset="46"/></Target><Target id="@+id/workout_duration_text" view="TextView"><Expressions/><location startLine="24" startOffset="4" endLine="30" endOffset="39"/></Target><Target id="@+id/exercises_recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="41" startOffset="4" endLine="47" endOffset="56"/></Target></Targets></Layout>