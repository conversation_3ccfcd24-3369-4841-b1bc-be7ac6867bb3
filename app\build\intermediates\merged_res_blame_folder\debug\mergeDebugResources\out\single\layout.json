[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\dialog_futures_tp_sl.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_futures_tp_sl.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_transaction.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_transaction.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_chat_source.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_chat_source.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\dialog_edit_wt_student.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_edit_wt_student.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_add_exercise.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_add_exercise.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\offline_status_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\offline_status_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\dialog_expense_investment.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_expense_investment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\dialog_edit_seminar.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_edit_seminar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_seminar.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_seminar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\dialog_workout_details.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_workout_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\activity_backup.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\activity_backup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\dialog_add_task.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_add_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\dialog_add_event.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_add_event.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\layout_page_header.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\layout_page_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_chat_user.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_chat_user.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\fragment_futures.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\fragment_futures.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_futures_position.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_futures_position.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\dialog_add_student.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_add_student.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_investment_dropdown.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_investment_dropdown.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\fragment_investments.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\fragment_investments.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_wt_event.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_wt_event.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\dialog_student_details.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_student_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_note_image.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_note_image.xml"}, {"merged": "com.example.allinone.app-mergeDebugResources-113:/layout/item_voice_note.xml", "source": "com.example.allinone.app-main-116:/layout/item_voice_note.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\fragment_transactions_overview.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\fragment_transactions_overview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\dialog_profit_loss.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_profit_loss.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\pie_chart_tooltip.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\pie_chart_tooltip.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_lesson.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_lesson.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_binance_position.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_binance_position.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_wt_student.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_wt_student.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_category_spending.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_category_spending.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_category_dropdown.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_category_dropdown.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_investment_selection.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_investment_selection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\dialog_task_group.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_task_group.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_chat_ai.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_chat_ai.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\dialog_progress.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_progress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_backup.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_backup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\dialog_post_details.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_post_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\dialog_program_details.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_program_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\dialog_edit_lesson.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_edit_lesson.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\fragment_home.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\fragment_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\fragment_futures_tab.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\fragment_futures_tab.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_note.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_note.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\dialog_edit_investment.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_edit_investment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\theme_switch_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\theme_switch_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_wt_registration.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_wt_registration.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_voice_note.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_voice_note.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_category_summary.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_category_summary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\dialog_edit_program.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_edit_program.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_file_structure.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_file_structure.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_note_video.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_note_video.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_fullscreen_image.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_fullscreen_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\dialog_loading.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_loading.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\dropdown_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dropdown_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_investment.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_investment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\dialog_income_investment.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_income_investment.xml"}, {"merged": "com.example.allinone.app-mergeDebugResources-113:/layout/item_lesson.xml", "source": "com.example.allinone.app-main-116:/layout/item_lesson.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\fragment_transaction_report.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\fragment_transaction_report.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\dialog_pin_input.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_pin_input.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\dialog_fullscreen_image.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_fullscreen_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\nav_header.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\nav_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_investment_image.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_investment_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\splash_text_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\splash_text_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\dialog_add_program.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\dialog_add_program.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\item_transaction_report.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\item_transaction_report.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-mergeDebugResources-113:\\layout\\fragment_investments_tab.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.allinone.app-main-116:\\layout\\fragment_investments_tab.xml"}]