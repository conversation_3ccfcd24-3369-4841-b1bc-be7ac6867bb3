<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_investment" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_investment.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_investment_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="87" endOffset="51"/></Target><Target id="@+id/investmentName" view="TextView"><Expressions/><location startLine="28" startOffset="16" endLine="32" endOffset="75"/></Target><Target id="@+id/investmentType" view="TextView"><Expressions/><location startLine="34" startOffset="16" endLine="38" endOffset="71"/></Target><Target id="@+id/investmentAmount" view="TextView"><Expressions/><location startLine="40" startOffset="16" endLine="45" endOffset="45"/></Target><Target id="@+id/investmentCurrentValue" view="TextView"><Expressions/><location startLine="47" startOffset="16" endLine="52" endOffset="59"/></Target><Target id="@+id/investmentProfitLoss" view="TextView"><Expressions/><location startLine="54" startOffset="16" endLine="59" endOffset="45"/></Target><Target id="@+id/investmentDate" view="TextView"><Expressions/><location startLine="62" startOffset="12" endLine="66" endOffset="69"/></Target><Target id="@+id/investmentDescription" view="TextView"><Expressions/><location startLine="69" startOffset="8" endLine="74" endOffset="63"/></Target><Target id="@+id/imageContainer" view="LinearLayout"><Expressions/><location startLine="80" startOffset="12" endLine="84" endOffset="49"/></Target></Targets></Layout>