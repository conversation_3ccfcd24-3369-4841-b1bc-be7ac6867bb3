package com.example.allinone.data.local;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import com.example.allinone.data.local.dao.CachedNoteDao;
import com.example.allinone.data.local.dao.CachedNoteDao_Impl;
import com.example.allinone.data.local.dao.CachedProgramDao;
import com.example.allinone.data.local.dao.CachedProgramDao_Impl;
import com.example.allinone.data.local.dao.CachedTransactionDao;
import com.example.allinone.data.local.dao.CachedTransactionDao_Impl;
import com.example.allinone.data.local.dao.CachedWTStudentDao;
import com.example.allinone.data.local.dao.CachedWTStudentDao_Impl;
import com.example.allinone.data.local.dao.CachedWorkoutDao;
import com.example.allinone.data.local.dao.CachedWorkoutDao_Impl;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AppDatabase_Impl extends AppDatabase {
  private volatile CachedTransactionDao _cachedTransactionDao;

  private volatile CachedNoteDao _cachedNoteDao;

  private volatile CachedWorkoutDao _cachedWorkoutDao;

  private volatile CachedProgramDao _cachedProgramDao;

  private volatile CachedWTStudentDao _cachedWTStudentDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(3) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `cached_transactions` (`id` INTEGER NOT NULL, `amount` REAL NOT NULL, `type` TEXT NOT NULL, `description` TEXT, `isIncome` INTEGER NOT NULL, `category` TEXT NOT NULL, `relatedRegistrationId` INTEGER, `date` INTEGER NOT NULL, `cachedAt` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `cached_investments` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `amount` REAL NOT NULL, `type` TEXT NOT NULL, `description` TEXT, `imageUri` TEXT, `date` INTEGER NOT NULL, `isPast` INTEGER NOT NULL, `profitLoss` REAL NOT NULL, `currentValue` REAL NOT NULL, `cachedAt` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `cached_notes` (`id` INTEGER NOT NULL, `title` TEXT NOT NULL, `content` TEXT NOT NULL, `date` INTEGER NOT NULL, `imageUris` TEXT, `videoUris` TEXT, `voiceNoteUris` TEXT, `lastEdited` INTEGER NOT NULL, `isRichText` INTEGER NOT NULL, `cachedAt` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `cached_workouts` (`id` INTEGER NOT NULL, `programId` INTEGER, `programName` TEXT, `startTime` INTEGER NOT NULL, `endTime` INTEGER, `duration` INTEGER NOT NULL, `exercisesJson` TEXT NOT NULL, `notes` TEXT, `cachedAt` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `cached_programs` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `description` TEXT, `exercisesJson` TEXT NOT NULL, `createdDate` INTEGER NOT NULL, `lastModifiedDate` INTEGER NOT NULL, `cachedAt` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `cached_wt_students` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `phoneNumber` TEXT, `email` TEXT, `instagram` TEXT, `isActive` INTEGER NOT NULL, `deviceId` TEXT, `notes` TEXT, `photoUri` TEXT, `cachedAt` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '23e00d69400c88eb33c4e59d5ab6f70e')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `cached_transactions`");
        db.execSQL("DROP TABLE IF EXISTS `cached_investments`");
        db.execSQL("DROP TABLE IF EXISTS `cached_notes`");
        db.execSQL("DROP TABLE IF EXISTS `cached_workouts`");
        db.execSQL("DROP TABLE IF EXISTS `cached_programs`");
        db.execSQL("DROP TABLE IF EXISTS `cached_wt_students`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsCachedTransactions = new HashMap<String, TableInfo.Column>(9);
        _columnsCachedTransactions.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedTransactions.put("amount", new TableInfo.Column("amount", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedTransactions.put("type", new TableInfo.Column("type", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedTransactions.put("description", new TableInfo.Column("description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedTransactions.put("isIncome", new TableInfo.Column("isIncome", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedTransactions.put("category", new TableInfo.Column("category", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedTransactions.put("relatedRegistrationId", new TableInfo.Column("relatedRegistrationId", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedTransactions.put("date", new TableInfo.Column("date", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedTransactions.put("cachedAt", new TableInfo.Column("cachedAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysCachedTransactions = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesCachedTransactions = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoCachedTransactions = new TableInfo("cached_transactions", _columnsCachedTransactions, _foreignKeysCachedTransactions, _indicesCachedTransactions);
        final TableInfo _existingCachedTransactions = TableInfo.read(db, "cached_transactions");
        if (!_infoCachedTransactions.equals(_existingCachedTransactions)) {
          return new RoomOpenHelper.ValidationResult(false, "cached_transactions(com.example.allinone.data.local.entities.CachedTransactionEntity).\n"
                  + " Expected:\n" + _infoCachedTransactions + "\n"
                  + " Found:\n" + _existingCachedTransactions);
        }
        final HashMap<String, TableInfo.Column> _columnsCachedInvestments = new HashMap<String, TableInfo.Column>(11);
        _columnsCachedInvestments.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedInvestments.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedInvestments.put("amount", new TableInfo.Column("amount", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedInvestments.put("type", new TableInfo.Column("type", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedInvestments.put("description", new TableInfo.Column("description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedInvestments.put("imageUri", new TableInfo.Column("imageUri", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedInvestments.put("date", new TableInfo.Column("date", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedInvestments.put("isPast", new TableInfo.Column("isPast", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedInvestments.put("profitLoss", new TableInfo.Column("profitLoss", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedInvestments.put("currentValue", new TableInfo.Column("currentValue", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedInvestments.put("cachedAt", new TableInfo.Column("cachedAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysCachedInvestments = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesCachedInvestments = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoCachedInvestments = new TableInfo("cached_investments", _columnsCachedInvestments, _foreignKeysCachedInvestments, _indicesCachedInvestments);
        final TableInfo _existingCachedInvestments = TableInfo.read(db, "cached_investments");
        if (!_infoCachedInvestments.equals(_existingCachedInvestments)) {
          return new RoomOpenHelper.ValidationResult(false, "cached_investments(com.example.allinone.data.local.entities.CachedInvestmentEntity).\n"
                  + " Expected:\n" + _infoCachedInvestments + "\n"
                  + " Found:\n" + _existingCachedInvestments);
        }
        final HashMap<String, TableInfo.Column> _columnsCachedNotes = new HashMap<String, TableInfo.Column>(10);
        _columnsCachedNotes.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedNotes.put("title", new TableInfo.Column("title", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedNotes.put("content", new TableInfo.Column("content", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedNotes.put("date", new TableInfo.Column("date", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedNotes.put("imageUris", new TableInfo.Column("imageUris", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedNotes.put("videoUris", new TableInfo.Column("videoUris", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedNotes.put("voiceNoteUris", new TableInfo.Column("voiceNoteUris", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedNotes.put("lastEdited", new TableInfo.Column("lastEdited", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedNotes.put("isRichText", new TableInfo.Column("isRichText", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedNotes.put("cachedAt", new TableInfo.Column("cachedAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysCachedNotes = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesCachedNotes = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoCachedNotes = new TableInfo("cached_notes", _columnsCachedNotes, _foreignKeysCachedNotes, _indicesCachedNotes);
        final TableInfo _existingCachedNotes = TableInfo.read(db, "cached_notes");
        if (!_infoCachedNotes.equals(_existingCachedNotes)) {
          return new RoomOpenHelper.ValidationResult(false, "cached_notes(com.example.allinone.data.local.entities.CachedNoteEntity).\n"
                  + " Expected:\n" + _infoCachedNotes + "\n"
                  + " Found:\n" + _existingCachedNotes);
        }
        final HashMap<String, TableInfo.Column> _columnsCachedWorkouts = new HashMap<String, TableInfo.Column>(9);
        _columnsCachedWorkouts.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedWorkouts.put("programId", new TableInfo.Column("programId", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedWorkouts.put("programName", new TableInfo.Column("programName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedWorkouts.put("startTime", new TableInfo.Column("startTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedWorkouts.put("endTime", new TableInfo.Column("endTime", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedWorkouts.put("duration", new TableInfo.Column("duration", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedWorkouts.put("exercisesJson", new TableInfo.Column("exercisesJson", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedWorkouts.put("notes", new TableInfo.Column("notes", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedWorkouts.put("cachedAt", new TableInfo.Column("cachedAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysCachedWorkouts = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesCachedWorkouts = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoCachedWorkouts = new TableInfo("cached_workouts", _columnsCachedWorkouts, _foreignKeysCachedWorkouts, _indicesCachedWorkouts);
        final TableInfo _existingCachedWorkouts = TableInfo.read(db, "cached_workouts");
        if (!_infoCachedWorkouts.equals(_existingCachedWorkouts)) {
          return new RoomOpenHelper.ValidationResult(false, "cached_workouts(com.example.allinone.data.local.entities.CachedWorkoutEntity).\n"
                  + " Expected:\n" + _infoCachedWorkouts + "\n"
                  + " Found:\n" + _existingCachedWorkouts);
        }
        final HashMap<String, TableInfo.Column> _columnsCachedPrograms = new HashMap<String, TableInfo.Column>(7);
        _columnsCachedPrograms.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedPrograms.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedPrograms.put("description", new TableInfo.Column("description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedPrograms.put("exercisesJson", new TableInfo.Column("exercisesJson", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedPrograms.put("createdDate", new TableInfo.Column("createdDate", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedPrograms.put("lastModifiedDate", new TableInfo.Column("lastModifiedDate", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedPrograms.put("cachedAt", new TableInfo.Column("cachedAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysCachedPrograms = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesCachedPrograms = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoCachedPrograms = new TableInfo("cached_programs", _columnsCachedPrograms, _foreignKeysCachedPrograms, _indicesCachedPrograms);
        final TableInfo _existingCachedPrograms = TableInfo.read(db, "cached_programs");
        if (!_infoCachedPrograms.equals(_existingCachedPrograms)) {
          return new RoomOpenHelper.ValidationResult(false, "cached_programs(com.example.allinone.data.local.entities.CachedProgramEntity).\n"
                  + " Expected:\n" + _infoCachedPrograms + "\n"
                  + " Found:\n" + _existingCachedPrograms);
        }
        final HashMap<String, TableInfo.Column> _columnsCachedWtStudents = new HashMap<String, TableInfo.Column>(10);
        _columnsCachedWtStudents.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedWtStudents.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedWtStudents.put("phoneNumber", new TableInfo.Column("phoneNumber", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedWtStudents.put("email", new TableInfo.Column("email", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedWtStudents.put("instagram", new TableInfo.Column("instagram", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedWtStudents.put("isActive", new TableInfo.Column("isActive", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedWtStudents.put("deviceId", new TableInfo.Column("deviceId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedWtStudents.put("notes", new TableInfo.Column("notes", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedWtStudents.put("photoUri", new TableInfo.Column("photoUri", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCachedWtStudents.put("cachedAt", new TableInfo.Column("cachedAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysCachedWtStudents = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesCachedWtStudents = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoCachedWtStudents = new TableInfo("cached_wt_students", _columnsCachedWtStudents, _foreignKeysCachedWtStudents, _indicesCachedWtStudents);
        final TableInfo _existingCachedWtStudents = TableInfo.read(db, "cached_wt_students");
        if (!_infoCachedWtStudents.equals(_existingCachedWtStudents)) {
          return new RoomOpenHelper.ValidationResult(false, "cached_wt_students(com.example.allinone.data.local.entities.CachedWTStudentEntity).\n"
                  + " Expected:\n" + _infoCachedWtStudents + "\n"
                  + " Found:\n" + _existingCachedWtStudents);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "23e00d69400c88eb33c4e59d5ab6f70e", "96f5c4923db07745e4c2abd3720286ee");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "cached_transactions","cached_investments","cached_notes","cached_workouts","cached_programs","cached_wt_students");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    try {
      super.beginTransaction();
      _db.execSQL("DELETE FROM `cached_transactions`");
      _db.execSQL("DELETE FROM `cached_investments`");
      _db.execSQL("DELETE FROM `cached_notes`");
      _db.execSQL("DELETE FROM `cached_workouts`");
      _db.execSQL("DELETE FROM `cached_programs`");
      _db.execSQL("DELETE FROM `cached_wt_students`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(CachedTransactionDao.class, CachedTransactionDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(CachedNoteDao.class, CachedNoteDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(CachedWorkoutDao.class, CachedWorkoutDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(CachedProgramDao.class, CachedProgramDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(CachedWTStudentDao.class, CachedWTStudentDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    return _autoMigrations;
  }

  @Override
  public CachedTransactionDao transactionDao() {
    if (_cachedTransactionDao != null) {
      return _cachedTransactionDao;
    } else {
      synchronized(this) {
        if(_cachedTransactionDao == null) {
          _cachedTransactionDao = new CachedTransactionDao_Impl(this);
        }
        return _cachedTransactionDao;
      }
    }
  }

  @Override
  public CachedNoteDao noteDao() {
    if (_cachedNoteDao != null) {
      return _cachedNoteDao;
    } else {
      synchronized(this) {
        if(_cachedNoteDao == null) {
          _cachedNoteDao = new CachedNoteDao_Impl(this);
        }
        return _cachedNoteDao;
      }
    }
  }

  @Override
  public CachedWorkoutDao workoutDao() {
    if (_cachedWorkoutDao != null) {
      return _cachedWorkoutDao;
    } else {
      synchronized(this) {
        if(_cachedWorkoutDao == null) {
          _cachedWorkoutDao = new CachedWorkoutDao_Impl(this);
        }
        return _cachedWorkoutDao;
      }
    }
  }

  @Override
  public CachedProgramDao programDao() {
    if (_cachedProgramDao != null) {
      return _cachedProgramDao;
    } else {
      synchronized(this) {
        if(_cachedProgramDao == null) {
          _cachedProgramDao = new CachedProgramDao_Impl(this);
        }
        return _cachedProgramDao;
      }
    }
  }

  @Override
  public CachedWTStudentDao wtStudentDao() {
    if (_cachedWTStudentDao != null) {
      return _cachedWTStudentDao;
    } else {
      synchronized(this) {
        if(_cachedWTStudentDao == null) {
          _cachedWTStudentDao = new CachedWTStudentDao_Impl(this);
        }
        return _cachedWTStudentDao;
      }
    }
  }
}
