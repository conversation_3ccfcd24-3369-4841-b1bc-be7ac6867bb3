package com.example.allinone.di;

import android.content.Context;
import com.example.allinone.data.local.RoomCacheManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class AppModule_ProvideRoomCacheManagerFactory implements Factory<RoomCacheManager> {
  private final Provider<Context> contextProvider;

  public AppModule_ProvideRoomCacheManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public RoomCacheManager get() {
    return provideRoomCacheManager(contextProvider.get());
  }

  public static AppModule_ProvideRoomCacheManagerFactory create(Provider<Context> contextProvider) {
    return new AppModule_ProvideRoomCacheManagerFactory(contextProvider);
  }

  public static RoomCacheManager provideRoomCacheManager(Context context) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideRoomCacheManager(context));
  }
}
