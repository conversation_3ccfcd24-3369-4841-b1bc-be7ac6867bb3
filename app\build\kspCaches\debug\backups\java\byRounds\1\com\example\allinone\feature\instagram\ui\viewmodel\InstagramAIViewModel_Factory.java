package com.example.allinone.feature.instagram.ui.viewmodel;

import com.example.allinone.feature.instagram.domain.usecase.AnalyzeInstagramURLUseCase;
import com.example.allinone.feature.instagram.domain.usecase.AnalyzeMultimodalContentUseCase;
import com.example.allinone.feature.instagram.domain.usecase.GetMultimodalSuggestionsUseCase;
import com.example.allinone.feature.instagram.domain.usecase.ProcessAudioRecordingUseCase;
import com.example.allinone.feature.instagram.domain.usecase.QueryInstagramAIUseCase;
import com.example.allinone.feature.instagram.domain.usecase.UploadFileForAnalysisUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class InstagramAIViewModel_Factory implements Factory<InstagramAIViewModel> {
  private final Provider<QueryInstagramAIUseCase> queryInstagramAIUseCaseProvider;

  private final Provider<AnalyzeMultimodalContentUseCase> analyzeMultimodalContentUseCaseProvider;

  private final Provider<UploadFileForAnalysisUseCase> uploadFileForAnalysisUseCaseProvider;

  private final Provider<AnalyzeInstagramURLUseCase> analyzeInstagramURLUseCaseProvider;

  private final Provider<ProcessAudioRecordingUseCase> processAudioRecordingUseCaseProvider;

  private final Provider<GetMultimodalSuggestionsUseCase> getMultimodalSuggestionsUseCaseProvider;

  public InstagramAIViewModel_Factory(
      Provider<QueryInstagramAIUseCase> queryInstagramAIUseCaseProvider,
      Provider<AnalyzeMultimodalContentUseCase> analyzeMultimodalContentUseCaseProvider,
      Provider<UploadFileForAnalysisUseCase> uploadFileForAnalysisUseCaseProvider,
      Provider<AnalyzeInstagramURLUseCase> analyzeInstagramURLUseCaseProvider,
      Provider<ProcessAudioRecordingUseCase> processAudioRecordingUseCaseProvider,
      Provider<GetMultimodalSuggestionsUseCase> getMultimodalSuggestionsUseCaseProvider) {
    this.queryInstagramAIUseCaseProvider = queryInstagramAIUseCaseProvider;
    this.analyzeMultimodalContentUseCaseProvider = analyzeMultimodalContentUseCaseProvider;
    this.uploadFileForAnalysisUseCaseProvider = uploadFileForAnalysisUseCaseProvider;
    this.analyzeInstagramURLUseCaseProvider = analyzeInstagramURLUseCaseProvider;
    this.processAudioRecordingUseCaseProvider = processAudioRecordingUseCaseProvider;
    this.getMultimodalSuggestionsUseCaseProvider = getMultimodalSuggestionsUseCaseProvider;
  }

  @Override
  public InstagramAIViewModel get() {
    return newInstance(queryInstagramAIUseCaseProvider.get(), analyzeMultimodalContentUseCaseProvider.get(), uploadFileForAnalysisUseCaseProvider.get(), analyzeInstagramURLUseCaseProvider.get(), processAudioRecordingUseCaseProvider.get(), getMultimodalSuggestionsUseCaseProvider.get());
  }

  public static InstagramAIViewModel_Factory create(
      Provider<QueryInstagramAIUseCase> queryInstagramAIUseCaseProvider,
      Provider<AnalyzeMultimodalContentUseCase> analyzeMultimodalContentUseCaseProvider,
      Provider<UploadFileForAnalysisUseCase> uploadFileForAnalysisUseCaseProvider,
      Provider<AnalyzeInstagramURLUseCase> analyzeInstagramURLUseCaseProvider,
      Provider<ProcessAudioRecordingUseCase> processAudioRecordingUseCaseProvider,
      Provider<GetMultimodalSuggestionsUseCase> getMultimodalSuggestionsUseCaseProvider) {
    return new InstagramAIViewModel_Factory(queryInstagramAIUseCaseProvider, analyzeMultimodalContentUseCaseProvider, uploadFileForAnalysisUseCaseProvider, analyzeInstagramURLUseCaseProvider, processAudioRecordingUseCaseProvider, getMultimodalSuggestionsUseCaseProvider);
  }

  public static InstagramAIViewModel newInstance(QueryInstagramAIUseCase queryInstagramAIUseCase,
      AnalyzeMultimodalContentUseCase analyzeMultimodalContentUseCase,
      UploadFileForAnalysisUseCase uploadFileForAnalysisUseCase,
      AnalyzeInstagramURLUseCase analyzeInstagramURLUseCase,
      ProcessAudioRecordingUseCase processAudioRecordingUseCase,
      GetMultimodalSuggestionsUseCase getMultimodalSuggestionsUseCase) {
    return new InstagramAIViewModel(queryInstagramAIUseCase, analyzeMultimodalContentUseCase, uploadFileForAnalysisUseCase, analyzeInstagramURLUseCase, processAudioRecordingUseCase, getMultimodalSuggestionsUseCase);
  }
}
