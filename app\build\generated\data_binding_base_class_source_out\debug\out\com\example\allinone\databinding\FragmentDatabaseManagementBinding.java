// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.tabs.TabLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentDatabaseManagementBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView collectionName;

  @NonNull
  public final MaterialCardView dataCard;

  @NonNull
  public final ConstraintLayout dataContainer;

  @NonNull
  public final LinearLayout dataHeader;

  @NonNull
  public final ProgressBar dataLoadingProgress;

  @NonNull
  public final RecyclerView dataRecyclerView;

  @NonNull
  public final TextView emptyView;

  @NonNull
  public final TextView itemCount;

  @NonNull
  public final ImageButton refreshButton;

  @NonNull
  public final TabLayout tabLayout;

  @NonNull
  public final TextView titleText;

  private FragmentDatabaseManagementBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView collectionName, @NonNull MaterialCardView dataCard,
      @NonNull ConstraintLayout dataContainer, @NonNull LinearLayout dataHeader,
      @NonNull ProgressBar dataLoadingProgress, @NonNull RecyclerView dataRecyclerView,
      @NonNull TextView emptyView, @NonNull TextView itemCount, @NonNull ImageButton refreshButton,
      @NonNull TabLayout tabLayout, @NonNull TextView titleText) {
    this.rootView = rootView;
    this.collectionName = collectionName;
    this.dataCard = dataCard;
    this.dataContainer = dataContainer;
    this.dataHeader = dataHeader;
    this.dataLoadingProgress = dataLoadingProgress;
    this.dataRecyclerView = dataRecyclerView;
    this.emptyView = emptyView;
    this.itemCount = itemCount;
    this.refreshButton = refreshButton;
    this.tabLayout = tabLayout;
    this.titleText = titleText;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentDatabaseManagementBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentDatabaseManagementBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_database_management, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentDatabaseManagementBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.collection_name;
      TextView collectionName = ViewBindings.findChildViewById(rootView, id);
      if (collectionName == null) {
        break missingId;
      }

      id = R.id.data_card;
      MaterialCardView dataCard = ViewBindings.findChildViewById(rootView, id);
      if (dataCard == null) {
        break missingId;
      }

      id = R.id.data_container;
      ConstraintLayout dataContainer = ViewBindings.findChildViewById(rootView, id);
      if (dataContainer == null) {
        break missingId;
      }

      id = R.id.data_header;
      LinearLayout dataHeader = ViewBindings.findChildViewById(rootView, id);
      if (dataHeader == null) {
        break missingId;
      }

      id = R.id.data_loading_progress;
      ProgressBar dataLoadingProgress = ViewBindings.findChildViewById(rootView, id);
      if (dataLoadingProgress == null) {
        break missingId;
      }

      id = R.id.data_recycler_view;
      RecyclerView dataRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (dataRecyclerView == null) {
        break missingId;
      }

      id = R.id.empty_view;
      TextView emptyView = ViewBindings.findChildViewById(rootView, id);
      if (emptyView == null) {
        break missingId;
      }

      id = R.id.item_count;
      TextView itemCount = ViewBindings.findChildViewById(rootView, id);
      if (itemCount == null) {
        break missingId;
      }

      id = R.id.refresh_button;
      ImageButton refreshButton = ViewBindings.findChildViewById(rootView, id);
      if (refreshButton == null) {
        break missingId;
      }

      id = R.id.tab_layout;
      TabLayout tabLayout = ViewBindings.findChildViewById(rootView, id);
      if (tabLayout == null) {
        break missingId;
      }

      id = R.id.title_text;
      TextView titleText = ViewBindings.findChildViewById(rootView, id);
      if (titleText == null) {
        break missingId;
      }

      return new FragmentDatabaseManagementBinding((ConstraintLayout) rootView, collectionName,
          dataCard, dataContainer, dataHeader, dataLoadingProgress, dataRecyclerView, emptyView,
          itemCount, refreshButton, tabLayout, titleText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
