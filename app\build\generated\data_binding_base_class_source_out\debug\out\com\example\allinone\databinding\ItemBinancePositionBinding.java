// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemBinancePositionBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final TextView entryPriceLabel;

  @NonNull
  public final TextView entryPriceValue;

  @NonNull
  public final TextView leverageText;

  @NonNull
  public final TextView liqPriceLabel;

  @NonNull
  public final TextView liqPriceValue;

  @NonNull
  public final TextView marginLabel;

  @NonNull
  public final TextView marginValue;

  @NonNull
  public final TextView markPriceLabel;

  @NonNull
  public final TextView markPriceValue;

  @NonNull
  public final TextView pnlLabel;

  @NonNull
  public final TextView pnlValue;

  @NonNull
  public final TextView roiLabel;

  @NonNull
  public final TextView roiValue;

  @NonNull
  public final TextView sizeLabel;

  @NonNull
  public final TextView sizeValue;

  @NonNull
  public final TextView symbolText;

  @NonNull
  public final TextView tpslLabel;

  @NonNull
  public final TextView tpslValue;

  private ItemBinancePositionBinding(@NonNull CardView rootView, @NonNull TextView entryPriceLabel,
      @NonNull TextView entryPriceValue, @NonNull TextView leverageText,
      @NonNull TextView liqPriceLabel, @NonNull TextView liqPriceValue,
      @NonNull TextView marginLabel, @NonNull TextView marginValue,
      @NonNull TextView markPriceLabel, @NonNull TextView markPriceValue,
      @NonNull TextView pnlLabel, @NonNull TextView pnlValue, @NonNull TextView roiLabel,
      @NonNull TextView roiValue, @NonNull TextView sizeLabel, @NonNull TextView sizeValue,
      @NonNull TextView symbolText, @NonNull TextView tpslLabel, @NonNull TextView tpslValue) {
    this.rootView = rootView;
    this.entryPriceLabel = entryPriceLabel;
    this.entryPriceValue = entryPriceValue;
    this.leverageText = leverageText;
    this.liqPriceLabel = liqPriceLabel;
    this.liqPriceValue = liqPriceValue;
    this.marginLabel = marginLabel;
    this.marginValue = marginValue;
    this.markPriceLabel = markPriceLabel;
    this.markPriceValue = markPriceValue;
    this.pnlLabel = pnlLabel;
    this.pnlValue = pnlValue;
    this.roiLabel = roiLabel;
    this.roiValue = roiValue;
    this.sizeLabel = sizeLabel;
    this.sizeValue = sizeValue;
    this.symbolText = symbolText;
    this.tpslLabel = tpslLabel;
    this.tpslValue = tpslValue;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemBinancePositionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemBinancePositionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_binance_position, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemBinancePositionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.entryPriceLabel;
      TextView entryPriceLabel = ViewBindings.findChildViewById(rootView, id);
      if (entryPriceLabel == null) {
        break missingId;
      }

      id = R.id.entryPriceValue;
      TextView entryPriceValue = ViewBindings.findChildViewById(rootView, id);
      if (entryPriceValue == null) {
        break missingId;
      }

      id = R.id.leverageText;
      TextView leverageText = ViewBindings.findChildViewById(rootView, id);
      if (leverageText == null) {
        break missingId;
      }

      id = R.id.liqPriceLabel;
      TextView liqPriceLabel = ViewBindings.findChildViewById(rootView, id);
      if (liqPriceLabel == null) {
        break missingId;
      }

      id = R.id.liqPriceValue;
      TextView liqPriceValue = ViewBindings.findChildViewById(rootView, id);
      if (liqPriceValue == null) {
        break missingId;
      }

      id = R.id.marginLabel;
      TextView marginLabel = ViewBindings.findChildViewById(rootView, id);
      if (marginLabel == null) {
        break missingId;
      }

      id = R.id.marginValue;
      TextView marginValue = ViewBindings.findChildViewById(rootView, id);
      if (marginValue == null) {
        break missingId;
      }

      id = R.id.markPriceLabel;
      TextView markPriceLabel = ViewBindings.findChildViewById(rootView, id);
      if (markPriceLabel == null) {
        break missingId;
      }

      id = R.id.markPriceValue;
      TextView markPriceValue = ViewBindings.findChildViewById(rootView, id);
      if (markPriceValue == null) {
        break missingId;
      }

      id = R.id.pnlLabel;
      TextView pnlLabel = ViewBindings.findChildViewById(rootView, id);
      if (pnlLabel == null) {
        break missingId;
      }

      id = R.id.pnlValue;
      TextView pnlValue = ViewBindings.findChildViewById(rootView, id);
      if (pnlValue == null) {
        break missingId;
      }

      id = R.id.roiLabel;
      TextView roiLabel = ViewBindings.findChildViewById(rootView, id);
      if (roiLabel == null) {
        break missingId;
      }

      id = R.id.roiValue;
      TextView roiValue = ViewBindings.findChildViewById(rootView, id);
      if (roiValue == null) {
        break missingId;
      }

      id = R.id.sizeLabel;
      TextView sizeLabel = ViewBindings.findChildViewById(rootView, id);
      if (sizeLabel == null) {
        break missingId;
      }

      id = R.id.sizeValue;
      TextView sizeValue = ViewBindings.findChildViewById(rootView, id);
      if (sizeValue == null) {
        break missingId;
      }

      id = R.id.symbolText;
      TextView symbolText = ViewBindings.findChildViewById(rootView, id);
      if (symbolText == null) {
        break missingId;
      }

      id = R.id.tpslLabel;
      TextView tpslLabel = ViewBindings.findChildViewById(rootView, id);
      if (tpslLabel == null) {
        break missingId;
      }

      id = R.id.tpslValue;
      TextView tpslValue = ViewBindings.findChildViewById(rootView, id);
      if (tpslValue == null) {
        break missingId;
      }

      return new ItemBinancePositionBinding((CardView) rootView, entryPriceLabel, entryPriceValue,
          leverageText, liqPriceLabel, liqPriceValue, marginLabel, marginValue, markPriceLabel,
          markPriceValue, pnlLabel, pnlValue, roiLabel, roiValue, sizeLabel, sizeValue, symbolText,
          tpslLabel, tpslValue);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
