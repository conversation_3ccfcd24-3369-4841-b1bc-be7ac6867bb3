<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_wt_lessons" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\fragment_wt_lessons.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_wt_lessons_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="180" endOffset="51"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="9" startOffset="4" endLine="17" endOffset="51"/></Target><Target id="@+id/lessonDaysLabel" view="TextView"><Expressions/><location startLine="28" startOffset="12" endLine="35" endOffset="51"/></Target><Target id="@+id/daysChipGroup" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="37" startOffset="12" endLine="93" endOffset="56"/></Target><Target id="@+id/mondayChip" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="45" startOffset="16" endLine="50" endOffset="40"/></Target><Target id="@+id/tuesdayChip" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="52" startOffset="16" endLine="57" endOffset="40"/></Target><Target id="@+id/wednesdayChip" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="59" startOffset="16" endLine="64" endOffset="40"/></Target><Target id="@+id/thursdayChip" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="66" startOffset="16" endLine="71" endOffset="40"/></Target><Target id="@+id/fridayChip" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="73" startOffset="16" endLine="78" endOffset="40"/></Target><Target id="@+id/saturdayChip" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="80" startOffset="16" endLine="85" endOffset="40"/></Target><Target id="@+id/sundayChip" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="87" startOffset="16" endLine="92" endOffset="40"/></Target><Target id="@+id/lessonTimeLabel" view="TextView"><Expressions/><location startLine="95" startOffset="12" endLine="102" endOffset="51"/></Target><Target id="@+id/startTimeField" view="EditText"><Expressions/><location startLine="116" startOffset="16" endLine="124" endOffset="52"/></Target><Target id="@+id/endTimeField" view="EditText"><Expressions/><location startLine="136" startOffset="16" endLine="144" endOffset="52"/></Target><Target id="@+id/addLessonButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="147" startOffset="12" endLine="152" endOffset="52"/></Target><Target id="@+id/lessonsLabel" view="TextView"><Expressions/><location startLine="154" startOffset="12" endLine="161" endOffset="51"/></Target><Target id="@+id/lessonsContainer" view="LinearLayout"><Expressions/><location startLine="163" startOffset="12" endLine="167" endOffset="48"/></Target><Target id="@+id/noLessonsText" view="TextView"><Expressions/><location startLine="169" startOffset="12" endLine="176" endOffset="43"/></Target></Targets></Layout>