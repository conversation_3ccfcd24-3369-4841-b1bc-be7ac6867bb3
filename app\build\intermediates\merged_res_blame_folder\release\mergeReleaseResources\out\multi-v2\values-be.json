{"logs": [{"outputFile": "com.example.allinone.app-mergeReleaseResources-108:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\75ac7f2a23ffbf48dd6409062de1d131\\transformed\\material-1.12.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,382,459,536,618,715,807,904,1036,1119,1197,1264,1357,1434,1497,1613,1676,1745,1804,1875,1934,1988,2109,2170,2233,2287,2360,2482,2570,2646,2737,2818,2901,3053,3139,3226,3359,3450,3533,3590,3641,3707,3779,3856,3927,4010,4085,4162,4244,4320,4428,4517,4599,4690,4786,4860,4941,5036,5090,5172,5238,5325,5411,5473,5537,5600,5669,5779,5892,5995,6102,6163,6218,6298,6383,6459", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,76,76,81,96,91,96,131,82,77,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,75,90,80,82,151,85,86,132,90,82,56,50,65,71,76,70,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,112,102,106,60,54,79,84,75,78", "endOffsets": "377,454,531,613,710,802,899,1031,1114,1192,1259,1352,1429,1492,1608,1671,1740,1799,1870,1929,1983,2104,2165,2228,2282,2355,2477,2565,2641,2732,2813,2896,3048,3134,3221,3354,3445,3528,3585,3636,3702,3774,3851,3922,4005,4080,4157,4239,4315,4423,4512,4594,4685,4781,4855,4936,5031,5085,5167,5233,5320,5406,5468,5532,5595,5664,5774,5887,5990,6097,6158,6213,6293,6378,6454,6533"}, "to": {"startLines": "23,58,59,60,61,62,70,71,72,97,98,150,154,157,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,276,280,281,283", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1052,4293,4370,4447,4529,4626,5449,5546,5678,8562,8640,12487,12907,13153,19477,19593,19656,19725,19784,19855,19914,19968,20089,20150,20213,20267,20340,20462,20550,20626,20717,20798,20881,21033,21119,21206,21339,21430,21513,21570,21621,21687,21759,21836,21907,21990,22065,22142,22224,22300,22408,22497,22579,22670,22766,22840,22921,23016,23070,23152,23218,23305,23391,23453,23517,23580,23649,23759,23872,23975,24082,24143,24603,24935,25020,25168", "endLines": "28,58,59,60,61,62,70,71,72,97,98,150,154,157,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,276,280,281,283", "endColumns": "12,76,76,81,96,91,96,131,82,77,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,75,90,80,82,151,85,86,132,90,82,56,50,65,71,76,70,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,112,102,106,60,54,79,84,75,78", "endOffsets": "1329,4365,4442,4524,4621,4713,5541,5673,5756,8635,8702,12575,12979,13211,19588,19651,19720,19779,19850,19909,19963,20084,20145,20208,20262,20335,20457,20545,20621,20712,20793,20876,21028,21114,21201,21334,21425,21508,21565,21616,21682,21754,21831,21902,21985,22060,22137,22219,22295,22403,22492,22574,22665,22761,22835,22916,23011,23065,23147,23213,23300,23386,23448,23512,23575,23644,23754,23867,23970,24077,24138,24193,24678,25015,25091,25242"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cd483c27f09d2683183fc5f548eeb5d9\\transformed\\play-services-base-18.5.0\\res\\values-be\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,454,575,681,832,954,1065,1165,1323,1426,1585,1709,1858,2013,2078,2136", "endColumns": "102,157,120,105,150,121,110,99,157,102,158,123,148,154,64,57,74", "endOffsets": "295,453,574,680,831,953,1064,1164,1322,1425,1584,1708,1857,2012,2077,2135,2210"}, "to": {"startLines": "75,76,77,78,79,80,81,82,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5938,6045,6207,6332,6442,6597,6723,6838,7088,7250,7357,7520,7648,7801,7960,8029,8091", "endColumns": "106,161,124,109,154,125,114,103,161,106,162,127,152,158,68,61,78", "endOffsets": "6040,6202,6327,6437,6592,6718,6833,6937,7245,7352,7515,7643,7796,7955,8024,8086,8165"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98a7034e786415bc9e009aca9ccbea7a\\transformed\\play-services-basement-18.4.0\\res\\values-be\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "83", "startColumns": "4", "startOffsets": "6942", "endColumns": "145", "endOffsets": "7083"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\eec5b5722953297dab7b2da4ce527235\\transformed\\browser-1.4.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,272,384", "endColumns": "108,107,111,106", "endOffsets": "159,267,379,486"}, "to": {"startLines": "93,151,152,153", "startColumns": "4,4,4,4", "startOffsets": "8170,12580,12688,12800", "endColumns": "108,107,111,106", "endOffsets": "8274,12683,12795,12902"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8e199df52e4d067d471a8ec3433b6506\\transformed\\core-1.13.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "63,64,65,66,67,68,69,287", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4718,4816,4918,5018,5119,5225,5328,25485", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "4811,4913,5013,5114,5220,5323,5444,25581"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cc3c3a73d52d53a742bae480359958fe\\transformed\\credentials-1.2.0-rc01\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,118", "endOffsets": "160,279"}, "to": {"startLines": "56,57", "startColumns": "4,4", "startOffsets": "4064,4174", "endColumns": "109,118", "endOffsets": "4169,4288"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1375e70be1fe8041f38907ceec5f1093\\transformed\\appcompat-1.7.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,279", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1334,1454,1557,1673,1759,1864,1983,2063,2140,2232,2326,2421,2515,2610,2704,2800,2895,2987,3079,3160,3266,3371,3469,3577,3683,3791,3964,24853", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "1449,1552,1668,1754,1859,1978,2058,2135,2227,2321,2416,2510,2605,2699,2795,2890,2982,3074,3155,3261,3366,3464,3572,3678,3786,3959,4059,24930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37fc1bdcb99e9ca53adfd9dc62ba1cb8\\transformed\\foundation-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,102", "endOffsets": "137,240"}, "to": {"startLines": "291,292", "startColumns": "4,4", "startOffsets": "25864,25951", "endColumns": "86,102", "endOffsets": "25946,26049"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ff95595b5660c73a0c7669f7cd696712\\transformed\\material3-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,406,523,621,718,832,955,1070,1215,1299,1410,1503,1600,1714,1837,1953,2100,2246,2384,2561,2693,2818,2947,3069,3163,3261,3387,3520,3619,3730,3839,3989,4142,4250,4350,4435,4530,4626,4712,4799,4899,4986,5073,5173,5279,5375,5473,5562,5670,5766,5866,6012,6102,6220", "endColumns": "116,114,118,116,97,96,113,122,114,144,83,110,92,96,113,122,115,146,145,137,176,131,124,128,121,93,97,125,132,98,110,108,149,152,107,99,84,94,95,85,86,99,86,86,99,105,95,97,88,107,95,99,145,89,117,95", "endOffsets": "167,282,401,518,616,713,827,950,1065,1210,1294,1405,1498,1595,1709,1832,1948,2095,2241,2379,2556,2688,2813,2942,3064,3158,3256,3382,3515,3614,3725,3834,3984,4137,4245,4345,4430,4525,4621,4707,4794,4894,4981,5068,5168,5274,5370,5468,5557,5665,5761,5861,6007,6097,6215,6311"}, "to": {"startLines": "158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13216,13333,13448,13567,13684,13782,13879,13993,14116,14231,14376,14460,14571,14664,14761,14875,14998,15114,15261,15407,15545,15722,15854,15979,16108,16230,16324,16422,16548,16681,16780,16891,17000,17150,17303,17411,17511,17596,17691,17787,17873,17960,18060,18147,18234,18334,18440,18536,18634,18723,18831,18927,19027,19173,19263,19381", "endColumns": "116,114,118,116,97,96,113,122,114,144,83,110,92,96,113,122,115,146,145,137,176,131,124,128,121,93,97,125,132,98,110,108,149,152,107,99,84,94,95,85,86,99,86,86,99,105,95,97,88,107,95,99,145,89,117,95", "endOffsets": "13328,13443,13562,13679,13777,13874,13988,14111,14226,14371,14455,14566,14659,14756,14870,14993,15109,15256,15402,15540,15717,15849,15974,16103,16225,16319,16417,16543,16676,16775,16886,16995,17145,17298,17406,17506,17591,17686,17782,17868,17955,18055,18142,18229,18329,18435,18531,18629,18718,18826,18922,19022,19168,19258,19376,19472"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f9ce903e2ff26f2c616d9362ffafb64\\transformed\\exoplayer-ui-2.19.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,627,957,1040,1123,1206,1304,1402,1491,1555,1648,1742,1807,1872,1937,2005,2100,2194,2294,2371,2450,2519,2609,2702,2795,2861,2926,2979,3039,3087,3148,3221,3289,3354,3427,3492,3550,3616,3681,3747,3799,3859,3933,4007", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,82,82,82,97,97,88,63,92,93,64,64,64,67,94,93,99,76,78,68,89,92,92,65,64,52,59,47,60,72,67,64,72,64,57,65,64,65,51,59,73,73,54", "endOffsets": "283,622,952,1035,1118,1201,1299,1397,1486,1550,1643,1737,1802,1867,1932,2000,2095,2189,2289,2366,2445,2514,2604,2697,2790,2856,2921,2974,3034,3082,3143,3216,3284,3349,3422,3487,3545,3611,3676,3742,3794,3854,3928,4002,4057"}, "to": {"startLines": "2,11,17,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,383,722,8707,8790,8873,8956,9054,9152,9241,9305,9398,9492,9557,9622,9687,9755,9850,9944,10044,10121,10200,10269,10359,10452,10545,10611,11351,11404,11464,11512,11573,11646,11714,11779,11852,11917,11975,12041,12106,12172,12224,12284,12358,12432", "endLines": "10,16,22,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149", "endColumns": "17,12,12,82,82,82,97,97,88,63,92,93,64,64,64,67,94,93,99,76,78,68,89,92,92,65,64,52,59,47,60,72,67,64,72,64,57,65,64,65,51,59,73,73,54", "endOffsets": "378,717,1047,8785,8868,8951,9049,9147,9236,9300,9393,9487,9552,9617,9682,9750,9845,9939,10039,10116,10195,10264,10354,10447,10540,10606,10671,11399,11459,11507,11568,11641,11709,11774,11847,11912,11970,12036,12101,12167,12219,12279,12353,12427,12482"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d7bf19bacce7fd4c141ebe47b25076b8\\transformed\\ui-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,376,479,565,645,734,822,904,987,1074,1146,1230,1308,1384,1469,1539", "endColumns": "92,83,93,102,85,79,88,87,81,82,86,71,83,77,75,84,69,122", "endOffsets": "193,277,371,474,560,640,729,817,899,982,1069,1141,1225,1303,1379,1464,1534,1657"}, "to": {"startLines": "73,74,94,95,96,155,156,274,275,277,278,282,284,285,286,288,289,290", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5761,5854,8279,8373,8476,12984,13064,24433,24521,24683,24766,25096,25247,25331,25409,25586,25671,25741", "endColumns": "92,83,93,102,85,79,88,87,81,82,86,71,83,77,75,84,69,122", "endOffsets": "5849,5933,8368,8471,8557,13059,13148,24516,24598,24761,24848,25163,25326,25404,25480,25666,25736,25859"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e87b0a766c58fccb5c3caec65c96631b\\transformed\\exoplayer-core-2.19.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,190,257,320,397,465,564,660", "endColumns": "70,63,66,62,76,67,98,95,69", "endOffsets": "121,185,252,315,392,460,559,655,725"}, "to": {"startLines": "123,124,125,126,127,128,129,130,131", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10676,10747,10811,10878,10941,11018,11086,11185,11281", "endColumns": "70,63,66,62,76,67,98,95,69", "endOffsets": "10742,10806,10873,10936,11013,11081,11180,11276,11346"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e82eb3f2167345ace1b8e91ea59beec2\\transformed\\navigation-ui-2.8.2\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,125", "endOffsets": "159,285"}, "to": {"startLines": "272,273", "startColumns": "4,4", "startOffsets": "24198,24307", "endColumns": "108,125", "endOffsets": "24302,24428"}}]}]}