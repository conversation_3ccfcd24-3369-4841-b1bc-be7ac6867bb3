// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentWtRegisterBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final FloatingActionButton addStudentFab;

  @NonNull
  public final Button applyFilterButton;

  @NonNull
  public final LinearLayout emptyState;

  @NonNull
  public final LinearLayout filterSection;

  @NonNull
  public final AutoCompleteTextView monthDropdown;

  @NonNull
  public final TextView networkStatusBanner;

  @NonNull
  public final RecyclerView studentsRecyclerView;

  @NonNull
  public final SwipeRefreshLayout swipeRefreshLayout;

  @NonNull
  public final TextView totalAmountText;

  private FragmentWtRegisterBinding(@NonNull ConstraintLayout rootView,
      @NonNull FloatingActionButton addStudentFab, @NonNull Button applyFilterButton,
      @NonNull LinearLayout emptyState, @NonNull LinearLayout filterSection,
      @NonNull AutoCompleteTextView monthDropdown, @NonNull TextView networkStatusBanner,
      @NonNull RecyclerView studentsRecyclerView, @NonNull SwipeRefreshLayout swipeRefreshLayout,
      @NonNull TextView totalAmountText) {
    this.rootView = rootView;
    this.addStudentFab = addStudentFab;
    this.applyFilterButton = applyFilterButton;
    this.emptyState = emptyState;
    this.filterSection = filterSection;
    this.monthDropdown = monthDropdown;
    this.networkStatusBanner = networkStatusBanner;
    this.studentsRecyclerView = studentsRecyclerView;
    this.swipeRefreshLayout = swipeRefreshLayout;
    this.totalAmountText = totalAmountText;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentWtRegisterBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentWtRegisterBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_wt_register, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentWtRegisterBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addStudentFab;
      FloatingActionButton addStudentFab = ViewBindings.findChildViewById(rootView, id);
      if (addStudentFab == null) {
        break missingId;
      }

      id = R.id.applyFilterButton;
      Button applyFilterButton = ViewBindings.findChildViewById(rootView, id);
      if (applyFilterButton == null) {
        break missingId;
      }

      id = R.id.emptyState;
      LinearLayout emptyState = ViewBindings.findChildViewById(rootView, id);
      if (emptyState == null) {
        break missingId;
      }

      id = R.id.filterSection;
      LinearLayout filterSection = ViewBindings.findChildViewById(rootView, id);
      if (filterSection == null) {
        break missingId;
      }

      id = R.id.monthDropdown;
      AutoCompleteTextView monthDropdown = ViewBindings.findChildViewById(rootView, id);
      if (monthDropdown == null) {
        break missingId;
      }

      id = R.id.networkStatusBanner;
      TextView networkStatusBanner = ViewBindings.findChildViewById(rootView, id);
      if (networkStatusBanner == null) {
        break missingId;
      }

      id = R.id.studentsRecyclerView;
      RecyclerView studentsRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (studentsRecyclerView == null) {
        break missingId;
      }

      id = R.id.swipe_refresh_layout;
      SwipeRefreshLayout swipeRefreshLayout = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefreshLayout == null) {
        break missingId;
      }

      id = R.id.totalAmountText;
      TextView totalAmountText = ViewBindings.findChildViewById(rootView, id);
      if (totalAmountText == null) {
        break missingId;
      }

      return new FragmentWtRegisterBinding((ConstraintLayout) rootView, addStudentFab,
          applyFilterButton, emptyState, filterSection, monthDropdown, networkStatusBanner,
          studentsRecyclerView, swipeRefreshLayout, totalAmountText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
