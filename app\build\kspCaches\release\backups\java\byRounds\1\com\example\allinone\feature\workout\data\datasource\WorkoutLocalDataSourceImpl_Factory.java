package com.example.allinone.feature.workout.data.datasource;

import com.example.allinone.data.local.dao.CachedWorkoutDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class WorkoutLocalDataSourceImpl_Factory implements Factory<WorkoutLocalDataSourceImpl> {
  private final Provider<CachedWorkoutDao> workoutDaoProvider;

  public WorkoutLocalDataSourceImpl_Factory(Provider<CachedWorkoutDao> workoutDaoProvider) {
    this.workoutDaoProvider = workoutDaoProvider;
  }

  @Override
  public WorkoutLocalDataSourceImpl get() {
    return newInstance(workoutDaoProvider.get());
  }

  public static WorkoutLocalDataSourceImpl_Factory create(
      Provider<CachedWorkoutDao> workoutDaoProvider) {
    return new WorkoutLocalDataSourceImpl_Factory(workoutDaoProvider);
  }

  public static WorkoutLocalDataSourceImpl newInstance(CachedWorkoutDao workoutDao) {
    return new WorkoutLocalDataSourceImpl(workoutDao);
  }
}
