// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogFuturesTpSlBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton cancelButton;

  @NonNull
  public final MaterialButton closePositionButton;

  @NonNull
  public final MaterialButton confirmButton;

  @NonNull
  public final TextView positionDetailsText;

  @NonNull
  public final TextView positionSymbolText;

  @NonNull
  public final TextInputEditText stopLossInput;

  @NonNull
  public final TextInputLayout stopLossLayout;

  @NonNull
  public final TextInputEditText takeProfitInput;

  @NonNull
  public final TextInputLayout takeProfitLayout;

  private DialogFuturesTpSlBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton cancelButton, @NonNull MaterialButton closePositionButton,
      @NonNull MaterialButton confirmButton, @NonNull TextView positionDetailsText,
      @NonNull TextView positionSymbolText, @NonNull TextInputEditText stopLossInput,
      @NonNull TextInputLayout stopLossLayout, @NonNull TextInputEditText takeProfitInput,
      @NonNull TextInputLayout takeProfitLayout) {
    this.rootView = rootView;
    this.cancelButton = cancelButton;
    this.closePositionButton = closePositionButton;
    this.confirmButton = confirmButton;
    this.positionDetailsText = positionDetailsText;
    this.positionSymbolText = positionSymbolText;
    this.stopLossInput = stopLossInput;
    this.stopLossLayout = stopLossLayout;
    this.takeProfitInput = takeProfitInput;
    this.takeProfitLayout = takeProfitLayout;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogFuturesTpSlBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogFuturesTpSlBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_futures_tp_sl, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogFuturesTpSlBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cancelButton;
      MaterialButton cancelButton = ViewBindings.findChildViewById(rootView, id);
      if (cancelButton == null) {
        break missingId;
      }

      id = R.id.closePositionButton;
      MaterialButton closePositionButton = ViewBindings.findChildViewById(rootView, id);
      if (closePositionButton == null) {
        break missingId;
      }

      id = R.id.confirmButton;
      MaterialButton confirmButton = ViewBindings.findChildViewById(rootView, id);
      if (confirmButton == null) {
        break missingId;
      }

      id = R.id.positionDetailsText;
      TextView positionDetailsText = ViewBindings.findChildViewById(rootView, id);
      if (positionDetailsText == null) {
        break missingId;
      }

      id = R.id.positionSymbolText;
      TextView positionSymbolText = ViewBindings.findChildViewById(rootView, id);
      if (positionSymbolText == null) {
        break missingId;
      }

      id = R.id.stopLossInput;
      TextInputEditText stopLossInput = ViewBindings.findChildViewById(rootView, id);
      if (stopLossInput == null) {
        break missingId;
      }

      id = R.id.stopLossLayout;
      TextInputLayout stopLossLayout = ViewBindings.findChildViewById(rootView, id);
      if (stopLossLayout == null) {
        break missingId;
      }

      id = R.id.takeProfitInput;
      TextInputEditText takeProfitInput = ViewBindings.findChildViewById(rootView, id);
      if (takeProfitInput == null) {
        break missingId;
      }

      id = R.id.takeProfitLayout;
      TextInputLayout takeProfitLayout = ViewBindings.findChildViewById(rootView, id);
      if (takeProfitLayout == null) {
        break missingId;
      }

      return new DialogFuturesTpSlBinding((LinearLayout) rootView, cancelButton,
          closePositionButton, confirmButton, positionDetailsText, positionSymbolText,
          stopLossInput, stopLossLayout, takeProfitInput, takeProfitLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
