<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <com.google.android.material.checkbox.MaterialCheckBox
            android:id="@+id/taskCheckbox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:buttonTint="@color/task_checkbox_color"
            tools:checked="false" />

        <TextView
            android:id="@+id/taskDescription"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="12dp"
            android:textAppearance="?attr/textAppearanceBody1"
            android:textSize="16sp"
            tools:text="This is a sample task description that can span multiple lines if needed" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView> 