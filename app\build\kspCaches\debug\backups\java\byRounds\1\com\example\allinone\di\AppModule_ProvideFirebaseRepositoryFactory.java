package com.example.allinone.di;

import android.content.Context;
import com.example.allinone.firebase.FirebaseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class AppModule_ProvideFirebaseRepositoryFactory implements Factory<FirebaseRepository> {
  private final Provider<Context> contextProvider;

  public AppModule_ProvideFirebaseRepositoryFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public FirebaseRepository get() {
    return provideFirebaseRepository(contextProvider.get());
  }

  public static AppModule_ProvideFirebaseRepositoryFactory create(
      Provider<Context> contextProvider) {
    return new AppModule_ProvideFirebaseRepositoryFactory(contextProvider);
  }

  public static FirebaseRepository provideFirebaseRepository(Context context) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideFirebaseRepository(context));
  }
}
