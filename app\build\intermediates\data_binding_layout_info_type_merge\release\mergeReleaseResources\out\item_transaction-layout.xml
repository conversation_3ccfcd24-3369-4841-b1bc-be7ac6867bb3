<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_transaction" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_transaction.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_transaction_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="63" endOffset="51"/></Target><Target id="@+id/typeText" view="TextView"><Expressions/><location startLine="28" startOffset="16" endLine="33" endOffset="46"/></Target><Target id="@+id/dateText" view="TextView"><Expressions/><location startLine="35" startOffset="16" endLine="41" endOffset="74"/></Target><Target id="@+id/amountText" view="TextView"><Expressions/><location startLine="44" startOffset="12" endLine="49" endOffset="42"/></Target><Target id="@+id/descriptionText" view="TextView"><Expressions/><location startLine="52" startOffset="8" endLine="59" endOffset="39"/></Target></Targets></Layout>