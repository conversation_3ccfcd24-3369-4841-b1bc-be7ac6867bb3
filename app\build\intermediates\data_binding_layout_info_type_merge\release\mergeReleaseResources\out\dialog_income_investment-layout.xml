<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_income_investment" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\dialog_income_investment.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_income_investment_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="60" endOffset="14"/></Target><Target id="@+id/dialogTitle" view="TextView"><Expressions/><location startLine="7" startOffset="4" endLine="13" endOffset="43"/></Target><Target id="@+id/investmentDropdownLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="15" startOffset="4" endLine="27" endOffset="59"/></Target><Target id="@+id/investmentDropdown" view="AutoCompleteTextView"><Expressions/><location startLine="22" startOffset="8" endLine="26" endOffset="38"/></Target><Target id="@+id/emptyStateText" view="TextView"><Expressions/><location startLine="29" startOffset="4" endLine="36" endOffset="35"/></Target><Target id="@+id/cancelButton" view="Button"><Expressions/><location startLine="45" startOffset="8" endLine="51" endOffset="44"/></Target><Target id="@+id/newInvestmentButton" view="Button"><Expressions/><location startLine="53" startOffset="8" endLine="58" endOffset="43"/></Target></Targets></Layout>