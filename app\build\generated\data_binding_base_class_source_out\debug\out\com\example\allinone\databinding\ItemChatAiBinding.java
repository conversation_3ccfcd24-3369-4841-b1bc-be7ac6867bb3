// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemChatAiBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView avatarAI;

  @NonNull
  public final MaterialCardView cardAIMessage;

  @NonNull
  public final LinearLayout layoutConfidence;

  @NonNull
  public final LinearLayout layoutSources;

  @NonNull
  public final LinearLayout layoutTyping;

  @NonNull
  public final RecyclerView recyclerSources;

  @NonNull
  public final TextView textAIMessage;

  @NonNull
  public final TextView textAITimestamp;

  @NonNull
  public final TextView textConfidence;

  private ItemChatAiBinding(@NonNull ConstraintLayout rootView, @NonNull TextView avatarAI,
      @NonNull MaterialCardView cardAIMessage, @NonNull LinearLayout layoutConfidence,
      @NonNull LinearLayout layoutSources, @NonNull LinearLayout layoutTyping,
      @NonNull RecyclerView recyclerSources, @NonNull TextView textAIMessage,
      @NonNull TextView textAITimestamp, @NonNull TextView textConfidence) {
    this.rootView = rootView;
    this.avatarAI = avatarAI;
    this.cardAIMessage = cardAIMessage;
    this.layoutConfidence = layoutConfidence;
    this.layoutSources = layoutSources;
    this.layoutTyping = layoutTyping;
    this.recyclerSources = recyclerSources;
    this.textAIMessage = textAIMessage;
    this.textAITimestamp = textAITimestamp;
    this.textConfidence = textConfidence;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemChatAiBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemChatAiBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_chat_ai, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemChatAiBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.avatarAI;
      TextView avatarAI = ViewBindings.findChildViewById(rootView, id);
      if (avatarAI == null) {
        break missingId;
      }

      id = R.id.cardAIMessage;
      MaterialCardView cardAIMessage = ViewBindings.findChildViewById(rootView, id);
      if (cardAIMessage == null) {
        break missingId;
      }

      id = R.id.layoutConfidence;
      LinearLayout layoutConfidence = ViewBindings.findChildViewById(rootView, id);
      if (layoutConfidence == null) {
        break missingId;
      }

      id = R.id.layoutSources;
      LinearLayout layoutSources = ViewBindings.findChildViewById(rootView, id);
      if (layoutSources == null) {
        break missingId;
      }

      id = R.id.layoutTyping;
      LinearLayout layoutTyping = ViewBindings.findChildViewById(rootView, id);
      if (layoutTyping == null) {
        break missingId;
      }

      id = R.id.recyclerSources;
      RecyclerView recyclerSources = ViewBindings.findChildViewById(rootView, id);
      if (recyclerSources == null) {
        break missingId;
      }

      id = R.id.textAIMessage;
      TextView textAIMessage = ViewBindings.findChildViewById(rootView, id);
      if (textAIMessage == null) {
        break missingId;
      }

      id = R.id.textAITimestamp;
      TextView textAITimestamp = ViewBindings.findChildViewById(rootView, id);
      if (textAITimestamp == null) {
        break missingId;
      }

      id = R.id.textConfidence;
      TextView textConfidence = ViewBindings.findChildViewById(rootView, id);
      if (textConfidence == null) {
        break missingId;
      }

      return new ItemChatAiBinding((ConstraintLayout) rootView, avatarAI, cardAIMessage,
          layoutConfidence, layoutSources, layoutTyping, recyclerSources, textAIMessage,
          textAITimestamp, textConfidence);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
