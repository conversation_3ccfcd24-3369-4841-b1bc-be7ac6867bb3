<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_voice_note" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_voice_note.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_voice_note_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="88" endOffset="51"/></Target><Target id="@+id/voiceNoteTitle" view="TextView"><Expressions/><location startLine="25" startOffset="12" endLine="33" endOffset="41"/></Target><Target id="@+id/voiceNoteCurrentTime" view="TextView"><Expressions/><location startLine="41" startOffset="16" endLine="48" endOffset="48"/></Target><Target id="@+id/timeSlash" view="TextView"><Expressions/><location startLine="50" startOffset="16" endLine="57" endOffset="48"/></Target><Target id="@+id/voiceNoteDuration" view="TextView"><Expressions/><location startLine="59" startOffset="16" endLine="64" endOffset="42"/></Target><Target id="@+id/playPauseButton" view="ImageButton"><Expressions/><location startLine="69" startOffset="8" endLine="76" endOffset="53"/></Target><Target id="@+id/deleteVoiceNoteButton" view="ImageButton"><Expressions/><location startLine="78" startOffset="8" endLine="86" endOffset="60"/></Target></Targets></Layout>