<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res"><file name="bottom_nav_item_color" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\color\bottom_nav_item_color.xml" qualifiers="" type="color"/><file name="bottom_nav_item_color_light" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\color\bottom_nav_item_color_light.xml" qualifiers="" type="color"/><file name="chip_background_selector" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\color\chip_background_selector.xml" qualifiers="" type="color"/><file name="dialog_action_button_color" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\color\dialog_action_button_color.xml" qualifiers="" type="color"/><file name="dialog_delete_button_color" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\color\dialog_delete_button_color.xml" qualifiers="" type="color"/><file name="drawer_item_color" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\color\drawer_item_color.xml" qualifiers="" type="color"/><file name="text_input_box_stroke" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\color\text_input_box_stroke.xml" qualifiers="" type="color"/><file name="text_input_text_color" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\color\text_input_text_color.xml" qualifiers="" type="color"/><file name="wt_bottom_nav_item_color" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\color\wt_bottom_nav_item_color.xml" qualifiers="" type="color"/><file name="dialog_action_button_color" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\color-night\dialog_action_button_color.xml" qualifiers="night-v8" type="color"/><file name="dialog_delete_button_color" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\color-night\dialog_delete_button_color.xml" qualifiers="night-v8" type="color"/><file name="text_input_box_stroke" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\color-night\text_input_box_stroke.xml" qualifiers="night-v8" type="color"/><file name="text_input_text_color" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\color-night\text_input_text_color.xml" qualifiers="night-v8" type="color"/><file name="bg_category_chip" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\bg_category_chip.xml" qualifiers="" type="drawable"/><file name="bg_current_day" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\bg_current_day.xml" qualifiers="" type="drawable"/><file name="bg_day_with_event" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\bg_day_with_event.xml" qualifiers="" type="drawable"/><file name="bg_day_with_events" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\bg_day_with_events.xml" qualifiers="" type="drawable"/><file name="bg_day_with_lesson" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\bg_day_with_lesson.xml" qualifiers="" type="drawable"/><file name="bg_day_with_registration" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\bg_day_with_registration.xml" qualifiers="" type="drawable"/><file name="bg_selected_day" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\bg_selected_day.xml" qualifiers="" type="drawable"/><file name="bg_tag_blue" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\bg_tag_blue.xml" qualifiers="" type="drawable"/><file name="bg_tag_green" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\bg_tag_green.xml" qualifiers="" type="drawable"/><file name="bg_tag_red" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\bg_tag_red.xml" qualifiers="" type="drawable"/><file name="border_background" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\border_background.xml" qualifiers="" type="drawable"/><file name="circle_background" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\circle_background.xml" qualifiers="" type="drawable"/><file name="circle_background_red" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\circle_background_red.xml" qualifiers="" type="drawable"/><file name="circle_shape" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\circle_shape.xml" qualifiers="" type="drawable"/><file name="completed_exercise_background" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\completed_exercise_background.xml" qualifiers="" type="drawable"/><file name="default_profile" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\default_profile.xml" qualifiers="" type="drawable"/><file name="dialog_rounded_bg" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\dialog_rounded_bg.xml" qualifiers="" type="drawable"/><file name="error_image" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\error_image.xml" qualifiers="" type="drawable"/><file name="fully_uncompleted_exercise_background" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\fully_uncompleted_exercise_background.xml" qualifiers="" type="drawable"/><file name="ic_add" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_add_photo" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_add_photo.xml" qualifiers="" type="drawable"/><file name="ic_attach_image" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_attach_image.xml" qualifiers="" type="drawable"/><file name="ic_back" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_back.xml" qualifiers="" type="drawable"/><file name="ic_backup" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_backup.xml" qualifiers="" type="drawable"/><file name="ic_calendar" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_calendar.xml" qualifiers="" type="drawable"/><file name="ic_call" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_call.xml" qualifiers="" type="drawable"/><file name="ic_category_all" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_category_all.xml" qualifiers="" type="drawable"/><file name="ic_category_bills" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_category_bills.xml" qualifiers="" type="drawable"/><file name="ic_category_food" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_category_food.xml" qualifiers="" type="drawable"/><file name="ic_category_game" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_category_game.xml" qualifiers="" type="drawable"/><file name="ic_category_general" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_category_general.xml" qualifiers="" type="drawable"/><file name="ic_category_investment" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_category_investment.xml" qualifiers="" type="drawable"/><file name="ic_category_salary" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_category_salary.xml" qualifiers="" type="drawable"/><file name="ic_category_shopping" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_category_shopping.xml" qualifiers="" type="drawable"/><file name="ic_category_sports" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_category_sports.xml" qualifiers="" type="drawable"/><file name="ic_category_transport" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_category_transport.xml" qualifiers="" type="drawable"/><file name="ic_category_wing_tzun" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_category_wing_tzun.xml" qualifiers="" type="drawable"/><file name="ic_checkbox" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_checkbox.xml" qualifiers="" type="drawable"/><file name="ic_chevron_left" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_chevron_left.xml" qualifiers="" type="drawable"/><file name="ic_chevron_right" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_chevron_right.xml" qualifiers="" type="drawable"/><file name="ic_cleardata" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_cleardata.xml" qualifiers="" type="drawable"/><file name="ic_clear_data" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_clear_data.xml" qualifiers="" type="drawable"/><file name="ic_close" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_close.xml" qualifiers="" type="drawable"/><file name="ic_code" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_code.xml" qualifiers="" type="drawable"/><file name="ic_dashboard" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_dashboard.xml" qualifiers="" type="drawable"/><file name="ic_database" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_database.xml" qualifiers="" type="drawable"/><file name="ic_delete" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_delete.xml" qualifiers="" type="drawable"/><file name="ic_draw" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_draw.xml" qualifiers="" type="drawable"/><file name="ic_edit" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_edit.xml" qualifiers="" type="drawable"/><file name="ic_empty_state" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_empty_state.xml" qualifiers="" type="drawable"/><file name="ic_error" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_error.xml" qualifiers="" type="drawable"/><file name="ic_exercise" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_exercise.xml" qualifiers="" type="drawable"/><file name="ic_expand_less" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_expand_less.xml" qualifiers="" type="drawable"/><file name="ic_expand_more" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_expand_more.xml" qualifiers="" type="drawable"/><file name="ic_expense" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_expense.xml" qualifiers="" type="drawable"/><file name="ic_file" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_file.xml" qualifiers="" type="drawable"/><file name="ic_fitness" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_fitness.xml" qualifiers="" type="drawable"/><file name="ic_folder" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_folder.xml" qualifiers="" type="drawable"/><file name="ic_format_bold" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_format_bold.xml" qualifiers="" type="drawable"/><file name="ic_format_italic" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_format_italic.xml" qualifiers="" type="drawable"/><file name="ic_format_list_bulleted" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_format_list_bulleted.xml" qualifiers="" type="drawable"/><file name="ic_format_list_numbered" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_format_list_numbered.xml" qualifiers="" type="drawable"/><file name="ic_format_text" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_format_text.xml" qualifiers="" type="drawable"/><file name="ic_format_underline" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_format_underline.xml" qualifiers="" type="drawable"/><file name="ic_graduation" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_graduation.xml" qualifiers="" type="drawable"/><file name="ic_history" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_history.xml" qualifiers="" type="drawable"/><file name="ic_home" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_home.xml" qualifiers="" type="drawable"/><file name="ic_image" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_image.xml" qualifiers="" type="drawable"/><file name="ic_income" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_income.xml" qualifiers="" type="drawable"/><file name="ic_instagram" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_instagram.xml" qualifiers="" type="drawable"/><file name="ic_instagram_posts" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_instagram_posts.xml" qualifiers="" type="drawable"/><file name="ic_investment" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_investment.xml" qualifiers="" type="drawable"/><file name="ic_investments" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_investments.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_lessons" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_lessons.xml" qualifiers="" type="drawable"/><file name="ic_log" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_log.xml" qualifiers="" type="drawable"/><file name="ic_menu" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_menu.xml" qualifiers="" type="drawable"/><file name="ic_note" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_note.xml" qualifiers="" type="drawable"/><file name="ic_notes" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_notes.xml" qualifiers="" type="drawable"/><file name="ic_notification" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_notification.xml" qualifiers="" type="drawable"/><file name="ic_no_registrations" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_no_registrations.xml" qualifiers="" type="drawable"/><file name="ic_no_students" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_no_students.xml" qualifiers="" type="drawable"/><file name="ic_pause" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_pause.xml" qualifiers="" type="drawable"/><file name="ic_play" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_play.xml" qualifiers="" type="drawable"/><file name="ic_play_circle" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_play_circle.xml" qualifiers="" type="drawable"/><file name="ic_program" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_program.xml" qualifiers="" type="drawable"/><file name="ic_registration" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_registration.xml" qualifiers="" type="drawable"/><file name="ic_remove" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_remove.xml" qualifiers="" type="drawable"/><file name="ic_reports" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_reports.xml" qualifiers="" type="drawable"/><file name="ic_save" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_save.xml" qualifiers="" type="drawable"/><file name="ic_search_white" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_search_white.xml" qualifiers="" type="drawable"/><file name="ic_share" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_share.xml" qualifiers="" type="drawable"/><file name="ic_stats" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_stats.xml" qualifiers="" type="drawable"/><file name="ic_stop" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_stop.xml" qualifiers="" type="drawable"/><file name="ic_student" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_student.xml" qualifiers="" type="drawable"/><file name="ic_students" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_students.xml" qualifiers="" type="drawable"/><file name="ic_tasks" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_tasks.xml" qualifiers="" type="drawable"/><file name="ic_transactions" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_transactions.xml" qualifiers="" type="drawable"/><file name="ic_video_error" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_video_error.xml" qualifiers="" type="drawable"/><file name="ic_video_placeholder" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_video_placeholder.xml" qualifiers="" type="drawable"/><file name="ic_view_list" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_view_list.xml" qualifiers="" type="drawable"/><file name="ic_whatsapp" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_whatsapp.xml" qualifiers="" type="drawable"/><file name="ic_wt" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_wt.xml" qualifiers="" type="drawable"/><file name="ic_wt_registers" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\ic_wt_registers.xml" qualifiers="" type="drawable"/><file name="placeholder_image" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\placeholder_image.xml" qualifiers="" type="drawable"/><file name="rounded_background" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\rounded_background.xml" qualifiers="" type="drawable"/><file name="rounded_corner_bg" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\rounded_corner_bg.xml" qualifiers="" type="drawable"/><file name="selected_circle_shape" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\selected_circle_shape.xml" qualifiers="" type="drawable"/><file name="simple_text_splash" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\simple_text_splash.xml" qualifiers="" type="drawable"/><file name="splash_layout_drawable" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\splash_layout_drawable.xml" qualifiers="" type="drawable"/><file name="transparent" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\transparent.xml" qualifiers="" type="drawable"/><file name="uncompleted_exercise_background" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable\uncompleted_exercise_background.xml" qualifiers="" type="drawable"/><file name="bg_category_chip" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable-night\bg_category_chip.xml" qualifiers="night-v8" type="drawable"/><file name="dialog_rounded_bg" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable-night\dialog_rounded_bg.xml" qualifiers="night-v8" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\drawable-v24\ic_launcher_foreground.xml" qualifiers="v24" type="drawable"/><file name="opensans" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\font\opensans.xml" qualifiers="" type="font"/><file name="opensans_bold" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\font\opensans_bold.ttf" qualifiers="" type="font"/><file name="opensans_regular" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\font\opensans_regular.ttf" qualifiers="" type="font"/><file name="activity_backup" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\activity_backup.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="dialog_add_event" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\dialog_add_event.xml" qualifiers="" type="layout"/><file name="dialog_add_program" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\dialog_add_program.xml" qualifiers="" type="layout"/><file name="dialog_add_student" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\dialog_add_student.xml" qualifiers="" type="layout"/><file name="dialog_add_task" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\dialog_add_task.xml" qualifiers="" type="layout"/><file name="dialog_edit_investment" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\dialog_edit_investment.xml" qualifiers="" type="layout"/><file name="dialog_edit_lesson" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\dialog_edit_lesson.xml" qualifiers="" type="layout"/><file name="dialog_edit_program" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\dialog_edit_program.xml" qualifiers="" type="layout"/><file name="dialog_edit_seminar" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\dialog_edit_seminar.xml" qualifiers="" type="layout"/><file name="dialog_edit_wt_student" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\dialog_edit_wt_student.xml" qualifiers="" type="layout"/><file name="dialog_expense_investment" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\dialog_expense_investment.xml" qualifiers="" type="layout"/><file name="dialog_fullscreen_image" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\dialog_fullscreen_image.xml" qualifiers="" type="layout"/><file name="dialog_futures_tp_sl" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\dialog_futures_tp_sl.xml" qualifiers="" type="layout"/><file name="dialog_income_investment" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\dialog_income_investment.xml" qualifiers="" type="layout"/><file name="dialog_loading" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\dialog_loading.xml" qualifiers="" type="layout"/><file name="dialog_pin_input" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\dialog_pin_input.xml" qualifiers="" type="layout"/><file name="dialog_post_details" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\dialog_post_details.xml" qualifiers="" type="layout"/><file name="dialog_profit_loss" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\dialog_profit_loss.xml" qualifiers="" type="layout"/><file name="dialog_program_details" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\dialog_program_details.xml" qualifiers="" type="layout"/><file name="dialog_progress" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\dialog_progress.xml" qualifiers="" type="layout"/><file name="dialog_student_details" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\dialog_student_details.xml" qualifiers="" type="layout"/><file name="dialog_task_group" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\dialog_task_group.xml" qualifiers="" type="layout"/><file name="dialog_workout_details" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\dialog_workout_details.xml" qualifiers="" type="layout"/><file name="dropdown_item" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\dropdown_item.xml" qualifiers="" type="layout"/><file name="fragment_futures" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\fragment_futures.xml" qualifiers="" type="layout"/><file name="fragment_futures_tab" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\fragment_futures_tab.xml" qualifiers="" type="layout"/><file name="fragment_home" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\fragment_home.xml" qualifiers="" type="layout"/><file name="fragment_investments" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\fragment_investments.xml" qualifiers="" type="layout"/><file name="fragment_investments_tab" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\fragment_investments_tab.xml" qualifiers="" type="layout"/><file name="fragment_transactions_overview" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\fragment_transactions_overview.xml" qualifiers="" type="layout"/><file name="fragment_transaction_report" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\fragment_transaction_report.xml" qualifiers="" type="layout"/><file name="fragment_wt_lessons" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\fragment_wt_lessons.xml" qualifiers="" type="layout"/><file name="fragment_wt_register" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\fragment_wt_register.xml" qualifiers="" type="layout"/><file name="fragment_wt_registry" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\fragment_wt_registry.xml" qualifiers="" type="layout"/><file name="fragment_wt_seminars" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\fragment_wt_seminars.xml" qualifiers="" type="layout"/><file name="fragment_wt_students" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\fragment_wt_students.xml" qualifiers="" type="layout"/><file name="item_add_exercise" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_add_exercise.xml" qualifiers="" type="layout"/><file name="item_backup" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_backup.xml" qualifiers="" type="layout"/><file name="item_binance_position" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_binance_position.xml" qualifiers="" type="layout"/><file name="item_category_dropdown" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_category_dropdown.xml" qualifiers="" type="layout"/><file name="item_category_spending" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_category_spending.xml" qualifiers="" type="layout"/><file name="item_category_summary" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_category_summary.xml" qualifiers="" type="layout"/><file name="item_chat_ai" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_chat_ai.xml" qualifiers="" type="layout"/><file name="item_chat_source" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_chat_source.xml" qualifiers="" type="layout"/><file name="item_chat_user" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_chat_user.xml" qualifiers="" type="layout"/><file name="item_file_structure" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_file_structure.xml" qualifiers="" type="layout"/><file name="item_fullscreen_image" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_fullscreen_image.xml" qualifiers="" type="layout"/><file name="item_futures_position" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_futures_position.xml" qualifiers="" type="layout"/><file name="item_investment" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_investment.xml" qualifiers="" type="layout"/><file name="item_investment_dropdown" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_investment_dropdown.xml" qualifiers="" type="layout"/><file name="item_investment_image" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_investment_image.xml" qualifiers="" type="layout"/><file name="item_investment_selection" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_investment_selection.xml" qualifiers="" type="layout"/><file name="item_lesson" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_lesson.xml" qualifiers="" type="layout"/><file name="item_note" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_note.xml" qualifiers="" type="layout"/><file name="item_note_image" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_note_image.xml" qualifiers="" type="layout"/><file name="item_note_video" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_note_video.xml" qualifiers="" type="layout"/><file name="item_seminar" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_seminar.xml" qualifiers="" type="layout"/><file name="item_transaction" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_transaction.xml" qualifiers="" type="layout"/><file name="item_transaction_report" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_transaction_report.xml" qualifiers="" type="layout"/><file name="item_voice_note" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_voice_note.xml" qualifiers="" type="layout"/><file name="item_wt_event" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_wt_event.xml" qualifiers="" type="layout"/><file name="item_wt_registration" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_wt_registration.xml" qualifiers="" type="layout"/><file name="item_wt_student" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\item_wt_student.xml" qualifiers="" type="layout"/><file name="layout_page_header" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\layout_page_header.xml" qualifiers="" type="layout"/><file name="nav_header" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\nav_header.xml" qualifiers="" type="layout"/><file name="offline_status_view" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\offline_status_view.xml" qualifiers="" type="layout"/><file name="pie_chart_tooltip" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\pie_chart_tooltip.xml" qualifiers="" type="layout"/><file name="splash_text_layout" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\splash_text_layout.xml" qualifiers="" type="layout"/><file name="theme_switch_layout" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout\theme_switch_layout.xml" qualifiers="" type="layout"/><file name="activity_backup" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout-night\activity_backup.xml" qualifiers="night-v8" type="layout"/><file name="activity_edit_note" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout-night\activity_edit_note.xml" qualifiers="night-v8" type="layout"/><file name="activity_main" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout-night\activity_main.xml" qualifiers="night-v8" type="layout"/><file name="dialog_student_details" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout-night\dialog_student_details.xml" qualifiers="night-v8" type="layout"/><file name="fragment_history" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout-night\fragment_history.xml" qualifiers="night-v8" type="layout"/><file name="fragment_wt_registry" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout-night\fragment_wt_registry.xml" qualifiers="night-v8" type="layout"/><file name="item_backup" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout-night\item_backup.xml" qualifiers="night-v8" type="layout"/><file name="item_history" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\layout-night\item_history.xml" qualifiers="night-v8" type="layout"/><file name="bottom_nav_menu" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\menu\bottom_nav_menu.xml" qualifiers="" type="menu"/><file name="drawer_menu" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\menu\drawer_menu.xml" qualifiers="" type="menu"/><file name="instagram_bottom_nav_menu" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\menu\instagram_bottom_nav_menu.xml" qualifiers="" type="menu"/><file name="menu_edit_note" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\menu\menu_edit_note.xml" qualifiers="" type="menu"/><file name="menu_group_options" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\menu\menu_group_options.xml" qualifiers="" type="menu"/><file name="menu_tasks" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\menu\menu_tasks.xml" qualifiers="" type="menu"/><file name="menu_task_options" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\menu\menu_task_options.xml" qualifiers="" type="menu"/><file name="search_history" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\menu\search_history.xml" qualifiers="" type="menu"/><file name="search_notes" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\menu\search_notes.xml" qualifiers="" type="menu"/><file name="search_register" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\menu\search_register.xml" qualifiers="" type="menu"/><file name="search_students" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\menu\search_students.xml" qualifiers="" type="menu"/><file name="workout_bottom_nav_menu" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\menu\workout_bottom_nav_menu.xml" qualifiers="" type="menu"/><file name="wt_bottom_nav_menu" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\menu\wt_bottom_nav_menu.xml" qualifiers="" type="menu"/><file name="wt_registration_context_menu" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\menu\wt_registration_context_menu.xml" qualifiers="" type="menu"/><file name="wt_student_context_menu" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\menu\wt_student_context_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\mipmap-anydpi-v33\ic_launcher.xml" qualifiers="anydpi-v33" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\values\attrs.xml" qualifiers=""><attr format="color" name="colorNavigationItem"/><attr format="color" name="colorNavigationItemSelected"/></file><file path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="gray_dark">#FF444444</color><color name="gray_light">#FFD3D3D3</color><color name="gray">#999999</color><color name="colorPrimary">#2196F3</color><color name="colorPrimaryDark">#1976D2</color><color name="colorPrimaryLight">#BBDEFB</color><color name="colorAccent">#FF4081</color><color name="expiration_color">#FFEBEE</color><color name="start_color">#E8F5E9</color><color name="default_event_color">#FFFFFF</color><color name="lesson_event_color">#E3F2FD</color><color name="textPrimary">#212121</color><color name="red">#F44336</color><color name="dark_gray">#555555</color><color name="medium_gray">#888888</color><color name="colorError">#F44336</color><color name="colorSuccess">#4CAF50</color><color name="colorWarning">#FFC107</color><color name="navy_primary">#003566</color><color name="navy_background">#001D3D</color><color name="navy_surface">#00264D</color><color name="navy_variant">#004E92</color><color name="navy_accent">#4DA8DA</color><color name="navy_text_secondary">#A5C5E1</color><color name="bright_tab_selected">#0466C8</color><color name="bright_tab_unselected">#7FB5DE</color><color name="error_light">#FFCDD2</color><color name="warning_light">#FFF9C4</color><color name="green">#4CAF50</color><color name="purple">#9C27B0</color><color name="registration_start">#4CAF50</color><color name="registration_end">@color/red</color><color name="bg_tag_blue">#2196F3</color><color name="feedCard">#FFFFFF</color><color name="reelsCard">#F0F8FF</color><color name="storyCard">#F0FFF0</color><color name="albumCard">#F5F5F5</color><color name="cardStroke">#E0E0E0</color><color name="boldTextColor">@color/black</color><color name="reels_background">@color/reelsCard</color><color name="video_background">@color/storyCard</color><color name="carousel_background">@color/albumCard</color><color name="excellent_green">#4CAF50</color><color name="good_orange">#FF9800</color><color name="poor_red">#F44336</color><color name="reels_purple">#8E24AA</color><color name="video_blue">#2196F3</color><color name="carousel_orange">#FF9800</color><color name="image_green">#4CAF50</color><color name="light_blue">#E3F2FD</color><color name="text_primary">#212121</color><color name="error_dark">#D32F2F</color><color name="light_gray">#F0F0F0</color><color name="background_color">#F5F5F5</color><color name="surface_color">#FFFFFF</color><color name="on_surface_color">#212121</color><color name="user_message_bg">#007AFF</color><color name="ai_message_bg">#E8E8E8</color><color name="task_checkbox_color">#4CAF50</color><color name="blue_500">#2196F3</color><color name="green_500">#4CAF50</color><color name="red_500">#F44336</color><color name="orange_500">#FF9800</color><color name="purple_500">#9C27B0</color></file><file path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="task_indent_margin">32dp</dimen></file><file path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#000000</color></file><file path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\values\ids.xml" qualifiers=""><item name="offline_status_card" type="id"/><item name="offline_status_message" type="id"/><item name="pending_operations_count" type="id"/></file><file path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">AllInOne</string><string name="amount">Amount</string><string name="type">Type</string><string name="description">Description</string><string name="save_changes">Save Changes</string><string name="type_input_description">Select transaction type</string><string name="select_type">Select transaction type</string><string name="investment_name">Investment Name</string><string name="investment_type">Investment Type</string><string name="add_image">Add Image</string><string name="add_video">Add Video</string><string name="add_attachment">Add Attachment</string><string name="delete_image">Delete Image</string><string name="delete_video">Delete Video</string><string name="delete_video_confirmation">Are you sure you want to delete this video?</string><string name="delete_image_confirmation">Are you sure you want to delete this image?</string><string name="cancel">Cancel</string><string name="save">Save</string><string name="create_drawing">Create Drawing</string><string name="drawing_options">Drawing Options</string><string name="save_to_note">Save to Note Only</string><string name="save_to_note_and_gallery">Save to Note and Gallery</string><string name="drawing_added">Drawing added to note</string><string name="error_adding_drawing">Error adding drawing</string><string name="brush_size">Brush Size:</string><string name="clear">Clear</string><string name="drawing_saved_to_gallery">Drawing saved to gallery</string><string name="error_saving_drawing">Error saving drawing: %1$s</string><string name="choose_color">Choose a Color</string><string name="select_color">Select Color</string><string name="selected_color">Selected:</string><string name="select">Select</string><string name="color">Color:</string><string name="color_black">Black Color</string><string name="color_red">Red Color</string><string name="color_green">Green Color</string><string name="color_blue">Blue Color</string><string name="color_yellow">Yellow Color</string><string name="color_purple">Purple Color</string><string name="color_orange">Orange Color</string><string name="color_brown">Brown Color</string><string name="color_teal">Teal Color</string><string name="color_pink">Pink Color</string><string name="app_id">954911141967</string><string name="offline_mode">Offline Mode</string><string name="offline_message">You are currently offline. Changes will be synchronized when you reconnect.</string><string name="pending_operations">Pending operations: %1$d</string><string name="offline_status">Offline status</string><string name="history">History</string><string name="empty_history">Empty history</string><string name="no_history_items">No history items found</string><string name="pull_to_refresh">Pull down to refresh</string><string name="item_type_icon">Item type icon</string><string name="delete_item">Delete item</string><string name="title_investments">Investments</string><string name="title_notes">Notes</string><string name="title_wing_tzun_registry">Wing Tzun Registry</string><string name="title_students">Students</string><string name="title_register">Register</string><string name="title_history">History</string><string name="title_lesson_schedule">Lesson Schedule</string><string name="title_calendar">Calendar</string><string name="profile_image">Profile Image</string><string name="add_photo">Add Photo</string><string name="name">Name</string><string name="phone_number">Phone Number</string><string name="email">E-mail</string><string name="instagram">Instagram</string><string name="active_status">Active Status</string><string name="add_student">Add Student</string><string name="edit_student">Edit Student</string><string name="required_fields_missing">Name and Phone are required fields</string><string name="no_students">No Students</string><string name="add_student_prompt">Add students to start registering them for courses</string><string name="add_registration">Add New Registration</string><string name="edit_registration">Edit Registration</string><string name="select_student">Select Student</string><string name="start_date">Start Date</string><string name="end_date">End Date</string><string name="payment_received">Payment Received</string><string name="registration_success">Student registered for course successfully</string><string name="registration_updated">Registration updated successfully</string><string name="payment_receipt">Payment Receipt</string><string name="add_receipt">Add Receipt</string><string name="receipt_unavailable">Receipt unavailable</string><string name="share_registration">Share Registration Info</string><string name="please_select_student">Please select a student</string><string name="please_select_start_date">Please select start date</string><string name="please_select_end_date">Please select end date</string><string name="please_enter_valid_amount">Please enter valid amount</string><string name="attachments_only_for_paid_registrations">Attachments can only be added for paid registrations</string><string name="no_registrations">No Course Registrations</string><string name="no_registrations_prompt">Add students to courses by clicking the + button</string><string name="select_month">Select Month</string><string name="apply">Apply</string><string name="all_months">All Months</string><string name="total">Total</string><string name="total_amount_label">Total Amount:</string><string name="amount_format">₺%.2f</string><string name="select_profile_photo">Select Profile Photo</string><string name="take_photo">Take Photo</string><string name="choose_from_gallery">Choose from Gallery</string><string name="camera_permission_title">Camera Permission</string><string name="camera_permission_message">Camera permission is needed to take photos for student profiles.</string><string name="storage_permission_title">Storage Permission</string><string name="storage_permission_message">Storage permission is needed to select photos for student profiles.</string><string name="grant_permission">Grant Permission</string><string name="permission_denied">%1$s permission denied. Unable to proceed.</string><string name="error_camera">Error preparing camera: %1$s</string><string name="status_paid_desc">Paid. Click to mark as unpaid</string><string name="status_unpaid_desc">Unpaid. Click to mark as paid</string><string name="field_required">This field is required</string><string name="edit">Edit</string><string name="delete">Delete</string><string name="delete_registration">Delete Registration</string><string name="delete_registration_confirmation">Are you sure you want to delete %1$s\'s registration?</string><string name="registration_deleted">Registration deleted successfully</string><string name="error_deleting_registration">Error deleting registration</string><string name="deleting">Deleting...</string><string name="retry">Retry</string><string name="create_note">Create Note</string><string name="edit_note">Edit Note</string><string name="please_enter_title">Please enter a title</string><string name="note_saved">Note saved</string><string name="note_updated">Note updated</string><string name="note_deleted">Note deleted</string><string name="share_note">Share Note</string><string name="attachment_click_to_open">📄 Attachment: %1$s (click to open)</string><string name="tap_to_view_attachment">Tap to view attachment</string><string name="no_app_for_file_type">No app found to open this file type</string><string name="error_opening_file">Error opening file: %1$s</string><string name="title_seminars">Seminars</string><string name="status_inactive_desc">Inactive student</string><string name="status_registered_desc">Active student with current registration</string><string name="status_active_desc">Active student without current registration</string><string name="network_unavailable">Network unavailable. Using cached data.</string><string name="notes">Notes</string><string name="tasks">Tasks</string><string name="add_task">Add Task</string><string name="no_tasks_yet">No tasks yet. Add your first one!</string><string name="new_task">New Task</string><string name="enter_task_description">Enter task description:</string><string name="edit_task">Edit Task</string><string name="task_description">Task description</string><string name="mark_completed">Mark as completed</string><string name="mark_incomplete">Mark as incomplete</string><string name="delete_task">Delete Task</string><string name="delete_task_confirmation">Are you sure you want to delete this task?</string><string name="task_added">Task added</string><string name="task_updated">Task updated</string><string name="task_deleted">Task deleted</string><string name="task_completed">Task completed</string><string name="task_incomplete">Task marked as incomplete</string><string name="create_task_group">Create Task Group</string><string name="edit_task_group">Edit Task Group</string><string name="group_title">Group Title</string><string name="group_description">Group Description (Optional)</string><string name="group_color">Group Color</string><string name="task_groups">Task Groups</string><string name="manage_groups">Manage Groups</string><string name="delete_group">Delete Group</string><string name="delete_group_confirmation">Are you sure you want to delete this group? Tasks in this group will be moved to ungrouped.</string><string name="group_created">Task group created</string><string name="group_updated">Task group updated</string><string name="group_deleted">Task group deleted</string><string name="no_group">No Group</string><string name="select_group">Select Group</string><string name="expand_collapse">Expand/Collapse</string><string name="toggle_view">Toggle View</string><string name="switch_to_grouped_view">Switch to Grouped View</string><string name="switch_to_list_view">Switch to List View</string><string name="error_network">Network error occurred. Please check your connection and try again.</string><string name="error_network_unavailable">Network is unavailable. Please check your internet connection.</string><string name="error_unknown">An unexpected error occurred. Please try again later.</string><string name="error_firebase_network">Unable to connect to the server. Please check your connection.</string><string name="error_authentication">Authentication failed. Please try again.</string><string name="error_user_not_found">User account not found. Please check your credentials.</string><string name="error_wrong_password">Incorrect password. Please try again.</string><string name="error_user_disabled">This user account has been disabled.</string><string name="error_too_many_requests">Too many attempts. Please try again later.</string><string name="error_requires_recent_login">This operation requires recent authentication. Please log in again.</string><string name="error_database">Database error. Please try again later.</string><string name="error_permission_denied">You don\'t have permission to perform this action.</string><string name="error_service_unavailable">Service is temporarily unavailable. Please try again later.</string><string name="error_document_already_exists">This record already exists.</string><string name="error_document_not_found">The requested record was not found.</string><string name="error_quota_exceeded">Service limit exceeded. Please try again later.</string><string name="error_storage">Storage error. Please try again later.</string><string name="error_file_not_found">The file you requested was not found.</string><string name="error_storage_quota_exceeded">Storage quota exceeded.</string><string name="error_not_authenticated">Authentication required. Please log in.</string><string name="error_not_authorized">You are not authorized to perform this action.</string><string name="delete_student">Delete Student</string><string name="delete_student_confirmation">Are you sure you want to delete %1$s from your student list?</string><string name="student_deleted">Student deleted successfully</string><string name="cannot_delete">Cannot Delete</string><string name="student_has_active_registrations">This student has active registrations and cannot be deleted.</string><string name="ok">OK</string><string name="delete_note">Delete Note</string><string name="delete_note_confirmation">Are you sure you want to delete this note?</string><string name="voice_notes">Voice Notes</string><string name="voice_note_singular">1 voice note</string><string name="voice_note_plural">%d voice notes</string><string name="delete_voice_note">Delete Voice Note</string><string name="delete_voice_note_confirmation">Are you sure you want to delete this voice note?</string><string name="record">Record</string><string name="recording">Recording…</string><string name="stop">Stop</string><string name="recording_started">Recording started</string><string name="recording_saved">Recording saved</string><string name="recording_failed">Failed to start recording</string><string name="error_saving_recording">Failed to save recording</string><string name="voice_note_deleted">Voice note deleted</string><string name="error_playing_audio">Error playing audio</string><string name="saving">Saving</string><string name="uploading_attachments">Uploading attachments...</string><string name="search" translatable="false">Search</string><string name="search_hint" translatable="false">Search...</string><string name="brightness">Brightness:</string><string name="workout">Workout</string><string name="workout_dashboard">Dashboard</string><string name="workout_exercise">Exercise</string><string name="workout_program">Program</string><string name="workout_log">Log</string><string name="start_workout">Start Workout</string><string name="pause_workout">Pause</string><string name="stop_workout">Stop</string><string name="save_workout">Save Workout</string><string name="create_workout">Create Workout</string><string name="add_program">Add Program</string><string name="program_name">Program Name</string><string name="exercise_name">Exercise Name</string><string name="sets">Sets</string><string name="reps">Reps</string><string name="weight">Weight (kg)</string><string name="notes_optional">Notes (Optional)</string><string name="add_exercise">Add Exercise</string><string name="remove_exercise">Remove Exercise</string><string name="workout_timer">Workout Timer</string><string name="select_program">Select Program</string><string name="custom_workout">Custom Workout</string><string name="weekly_summary">Weekly Summary</string><string name="weekly_workout_count">Workouts this week: %d</string><string name="weekly_workout_duration">Total duration: %s</string><string name="most_recent_workout">Most Recent Workout</string><string name="no_recent_workouts">No recent workouts</string><string name="all_time_stats">All-Time Stats</string><string name="total_workout_count">Total workouts: %d</string><string name="total_workout_duration">Total duration: %s</string><string name="no_programs">No programs yet. Create one by tapping the + button.</string><string name="no_workout_history">No workout history yet. Complete a workout to see it here.</string><string name="stop_workout_confirmation">Are you sure you want to stop this workout?</string><string name="workout_saved">Workout saved</string><string name="program_created">Program created</string><string name="program_details">Program Details</string><string name="workout_details">Workout Details</string><string name="delete_workout">Delete Workout</string><string name="delete_workout_confirmation">Are you sure you want to delete this workout from %1$s? This action cannot be undone.</string><string name="workout_deleted">Workout deleted</string><string name="delete_program">Delete Program</string><string name="delete_program_confirmation">Are you sure you want to delete program %1$s? This action cannot be undone.</string><string name="program_deleted">Program deleted successfully</string><string name="edit_coming_soon">Edit functionality coming soon</string><string name="close">Close</string><string name="program_updated">Program updated successfully</string><plurals name="exercise_count">
        <item quantity="one">1 exercise</item>
        <item quantity="other">%d exercises</item>
    </plurals><string name="workout_stats">Stats</string><string name="workout_statistics">Workout Statistics</string><string name="total_workouts">Total Workouts</string><string name="avg_duration">Avg Duration</string><string name="total_time">Total Time</string><string name="favorite_muscle">Favorite Muscle</string><string name="workout_history">Workout History</string><string name="image_counter">%1$d/%2$d</string><string name="task_name">Task name</string><string name="not_set">Not set</string></file><file path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\values\styles.xml" qualifiers=""><style name="CalendarDayStyle">
        <item name="android:gravity">center</item>
        <item name="android:padding">8dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
        <item name="android:background">?attr/selectableItemBackground</item>
    </style><style name="BottomNavigationStyle">
        <item name="itemIconTint">@color/bottom_nav_item_color</item>
        <item name="itemTextColor">@color/bottom_nav_item_color</item>
        <item name="itemRippleColor">@color/bright_tab_selected</item>
    </style><style name="circleImageView">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style><style name="BoldText">
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@android:color/black</item>
    </style><style name="FilterButtonStyle" parent="Widget.MaterialComponents.Button">
        <item name="android:minHeight">36dp</item>
        <item name="android:layout_height">36dp</item>
        <item name="android:textSize">14sp</item>
    </style><style name="FilterDropdownStyle">
        <item name="android:minHeight">36dp</item>
        <item name="android:layout_height">36dp</item>
        <item name="android:textSize">14sp</item>
    </style></file><file path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\values\themes.xml" qualifiers=""><color name="mtrl_textinput_default_box_stroke_color">@color/gray</color><style name="Theme.AllinOne" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="android:forceDarkAllowed" ns1:targetApi="q">false</item>
        
        <item name="colorPrimary">@color/black</item>
        <item name="colorPrimaryVariant">@color/gray_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/black</item>
        <item name="colorSecondaryVariant">@color/gray_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <item name="android:statusBarColor">@color/black</item>
        
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        
        <item name="android:windowBackground">@color/white</item>
        
        <item name="colorNavigationItem">@color/black</item>
        <item name="colorNavigationItemSelected">@color/gray_dark</item>
    </style><style name="Theme.AllinOne.NoActionBar" parent="Theme.AllinOne">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:statusBarColor">@android:color/black</item>
        <item name="android:windowBackground">@android:color/white</item>
    </style><style name="Theme.AllinOne.SplashText" parent="Theme.AllinOne">
        <item name="android:windowBackground">@color/black</item>
        <item name="android:windowLayoutInDisplayCutoutMode" ns1:targetApi="o_mr1">shortEdges</item>
    </style><style name="Theme.AllinOne.Starting" parent="Theme.SplashScreen">
        
        <item name="windowSplashScreenBackground">@color/black</item>
        
        
        <item name="windowSplashScreenAnimatedIcon">@drawable/transparent</item>
        
        
        <item name="postSplashScreenTheme">@style/Theme.AllinOne</item>
        
        
        <item name="android:windowDisablePreview">true</item>
    </style></file><file path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\values-night\colors.xml" qualifiers="night-v8"><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="gray_dark">#FF444444</color><color name="gray_light">#FF666666</color><color name="colorPrimary">@color/navy_primary</color><color name="default_event_color">@color/navy_surface</color><color name="expiration_color">#3E2828</color><color name="start_color">#2E3B2E</color><color name="lesson_event_color">#2C3440</color><color name="textPrimary">@color/white</color><color name="red">#CF6679</color><color name="dark_gray">#CCCCCC</color><color name="medium_gray">#999999</color><color name="colorError">#CF6679</color><color name="colorSuccess">#03DAC6</color><color name="colorWarning">#FFB74D</color><color name="navy_primary">#4DA8DA</color><color name="navy_background">#001D3D</color><color name="navy_surface">#002B59</color><color name="navy_variant">#004E92</color><color name="navy_accent">#64B5F6</color><color name="navy_text_secondary">#B0BEC5</color><color name="bright_tab_selected">#64B5F6</color><color name="bright_tab_unselected">#4F5B62</color><color name="boldTextColor">@color/white</color></file><file path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\values-night\styles.xml" qualifiers="night-v8"><style name="circleImageView">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style><style name="BoldText">
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@android:color/white</item>
    </style></file><file path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.AllinOne" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/navy_surface</item>
        <item name="colorPrimaryVariant">@color/navy_surface</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/bright_tab_selected</item>
        <item name="colorSecondaryVariant">@color/navy_variant</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <item name="android:statusBarColor">@color/navy_surface</item>
        
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        
        <item name="android:windowBackground">@color/navy_background</item>
        
        <item name="colorSurface">@color/navy_surface</item>
        <item name="android:colorBackground">@color/navy_background</item>
        
        <item name="android:textColor">@color/white</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">@color/white</item>
        <item name="android:textColorHint">@color/white</item>
        <item name="android:editTextColor">@color/white</item>
        
        <item name="textInputStyle">@style/Widget.App.TextInputLayout</item>
        <item name="editTextStyle">@style/Widget.App.EditText</item>
        
        <item name="materialButtonStyle">@style/Widget.App.Button</item>
        
        <item name="materialAlertDialogTheme">@style/ThemeOverlay.App.MaterialAlertDialog</item>
        
        <item name="colorNavigationItem">@color/drawer_item_color</item>
        <item name="colorNavigationItemSelected">@color/bright_tab_selected</item>
        <item name="android:navigationBarColor">@color/navy_surface</item>
        <item name="android:itemBackground">@color/navy_surface</item>
        
        <item name="colorControlHighlight">@color/bright_tab_selected</item>
        <item name="android:listChoiceBackgroundIndicator">@color/bright_tab_selected</item>
    </style><style name="Widget.App.TextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="android:textColorHint">@color/white</item>
        <item name="hintTextColor">@color/white</item>
        <item name="android:textColor">@color/text_input_text_color</item>
        <item name="boxStrokeColor">@color/text_input_box_stroke</item>
        <item name="boxStrokeWidth">1dp</item>
        <item name="boxStrokeWidthFocused">2dp</item>
        <item name="endIconTint">@color/white</item>
        <item name="startIconTint">@color/white</item>
        <item name="helperTextTextColor">@color/white</item>
        <item name="errorTextColor">@color/red</item>
        <item name="counterTextColor">@color/white</item>
        <item name="android:editTextColor">@color/text_input_text_color</item>
        <item name="boxBackgroundColor">@android:color/transparent</item>
    </style><style name="Widget.App.EditText" parent="Widget.MaterialComponents.TextInputEditText.OutlinedBox">
        <item name="android:textColor">@color/text_input_text_color</item>
        <item name="android:textColorHint">@color/white</item>
        <item name="android:backgroundTint">@color/white</item>
    </style><color name="mtrl_textinput_default_box_stroke_color">@color/white</color><style name="Widget.App.Button" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/white</item>
    </style><style name="ThemeOverlay.App.MaterialAlertDialog" parent="ThemeOverlay.MaterialComponents.MaterialAlertDialog">
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">@color/white</item>
        <item name="materialAlertDialogTitleTextStyle">@style/MaterialAlertDialog.App.Title.Text</item>
        <item name="materialAlertDialogBodyTextStyle">@style/MaterialAlertDialog.App.Body.Text</item>
    </style><style name="MaterialAlertDialog.App.Title.Text" parent="MaterialAlertDialog.MaterialComponents.Title.Text">
        <item name="android:textColor">@color/white</item>
    </style><style name="MaterialAlertDialog.App.Body.Text" parent="MaterialAlertDialog.MaterialComponents.Body.Text">
        <item name="android:textColor">@color/white</item>
    </style><style name="Widget.App.BottomNavigationView" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="itemIconTint">@color/bottom_nav_item_color</item>
        <item name="itemTextColor">@color/bottom_nav_item_color</item>
        <item name="itemRippleColor">@color/bright_tab_selected</item>
        <item name="itemActiveIndicatorStyle">@null</item>
    </style><style name="Widget.App.NavigationView" parent="Widget.MaterialComponents.NavigationView">
        <item name="itemIconTint">@color/drawer_item_color</item>
        <item name="itemTextColor">@color/drawer_item_color</item>
        <item name="itemBackground">?attr/selectableItemBackground</item>
        <item name="itemIconPadding">16dp</item>
    </style><style name="Widget.App.Button.TextButton.Dialog.Delete" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="android:textColor">@color/dialog_delete_button_color</item>
        <item name="rippleColor">@color/red</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textAppearance">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="backgroundTint">@android:color/transparent</item>
    </style><style name="Widget.App.Button.TextButton.Dialog.Action" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="android:textColor">@color/dialog_action_button_color</item>
        <item name="rippleColor">@color/white</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textAppearance">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="backgroundTint">@android:color/transparent</item>
    </style></file><file name="backup_rules" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="network_security_config" path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\generated\res\processDebugGoogleServices"/><source path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\generated\res\injectCrashlyticsMappingFileIdDebug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\generated\res\processDebugGoogleServices"><file path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">954911141967</string><string name="google_api_key" translatable="false">AIzaSyCXF4JpOl3_FXEzODDslf9VeTs9BGOiO1s</string><string name="google_app_id" translatable="false">1:954911141967:android:8369e5e490f1dab9ce7a3a</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyCXF4JpOl3_FXEzODDslf9VeTs9BGOiO1s</string><string name="google_storage_bucket" translatable="false">allinone-bd6f3.firebasestorage.app</string><string name="project_id" translatable="false">allinone-bd6f3</string></file></source><source path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\generated\res\injectCrashlyticsMappingFileIdDebug"><file path="C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\generated\res\injectCrashlyticsMappingFileIdDebug\values\com_google_firebase_crashlytics_mappingfileid.xml" qualifiers=""><string name="com.google.firebase.crashlytics.mapping_file_id" ns1:ignore="UnusedResources,TypographyDashes" translatable="false">00000000000000000000000000000000</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-injectCrashlyticsMappingFileIdDebug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-injectCrashlyticsMappingFileIdDebug" generated-set="res-injectCrashlyticsMappingFileIdDebug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>