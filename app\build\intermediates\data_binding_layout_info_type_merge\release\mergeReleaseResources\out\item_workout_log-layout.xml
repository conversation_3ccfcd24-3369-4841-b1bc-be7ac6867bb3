<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_workout_log" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_workout_log.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_workout_log_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="70" endOffset="51"/></Target><Target id="@+id/workout_program_name" view="TextView"><Expressions/><location startLine="16" startOffset="8" endLine="21" endOffset="45"/></Target><Target id="@+id/workout_date" view="TextView"><Expressions/><location startLine="23" startOffset="8" endLine="29" endOffset="50"/></Target><Target id="@+id/workout_duration" view="TextView"><Expressions/><location startLine="44" startOffset="12" endLine="50" endOffset="44"/></Target><Target id="@+id/workout_exercise_count" view="TextView"><Expressions/><location startLine="59" startOffset="12" endLine="64" endOffset="32"/></Target></Targets></Layout>