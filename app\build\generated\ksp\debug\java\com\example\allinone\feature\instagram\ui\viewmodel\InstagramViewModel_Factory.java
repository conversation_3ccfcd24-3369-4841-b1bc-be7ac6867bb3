package com.example.allinone.feature.instagram.ui.viewmodel;

import com.example.allinone.feature.instagram.domain.usecase.CheckInstagramHealthUseCase;
import com.example.allinone.feature.instagram.domain.usecase.GetInstagramAnalyticsUseCase;
import com.example.allinone.feature.instagram.domain.usecase.GetInstagramPostsUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class InstagramViewModel_Factory implements Factory<InstagramViewModel> {
  private final Provider<GetInstagramPostsUseCase> getInstagramPostsUseCaseProvider;

  private final Provider<GetInstagramAnalyticsUseCase> getInstagramAnalyticsUseCaseProvider;

  private final Provider<CheckInstagramHealthUseCase> checkInstagramHealthUseCaseProvider;

  public InstagramViewModel_Factory(
      Provider<GetInstagramPostsUseCase> getInstagramPostsUseCaseProvider,
      Provider<GetInstagramAnalyticsUseCase> getInstagramAnalyticsUseCaseProvider,
      Provider<CheckInstagramHealthUseCase> checkInstagramHealthUseCaseProvider) {
    this.getInstagramPostsUseCaseProvider = getInstagramPostsUseCaseProvider;
    this.getInstagramAnalyticsUseCaseProvider = getInstagramAnalyticsUseCaseProvider;
    this.checkInstagramHealthUseCaseProvider = checkInstagramHealthUseCaseProvider;
  }

  @Override
  public InstagramViewModel get() {
    return newInstance(getInstagramPostsUseCaseProvider.get(), getInstagramAnalyticsUseCaseProvider.get(), checkInstagramHealthUseCaseProvider.get());
  }

  public static InstagramViewModel_Factory create(
      Provider<GetInstagramPostsUseCase> getInstagramPostsUseCaseProvider,
      Provider<GetInstagramAnalyticsUseCase> getInstagramAnalyticsUseCaseProvider,
      Provider<CheckInstagramHealthUseCase> checkInstagramHealthUseCaseProvider) {
    return new InstagramViewModel_Factory(getInstagramPostsUseCaseProvider, getInstagramAnalyticsUseCaseProvider, checkInstagramHealthUseCaseProvider);
  }

  public static InstagramViewModel newInstance(GetInstagramPostsUseCase getInstagramPostsUseCase,
      GetInstagramAnalyticsUseCase getInstagramAnalyticsUseCase,
      CheckInstagramHealthUseCase checkInstagramHealthUseCase) {
    return new InstagramViewModel(getInstagramPostsUseCase, getInstagramAnalyticsUseCase, checkInstagramHealthUseCase);
  }
}
