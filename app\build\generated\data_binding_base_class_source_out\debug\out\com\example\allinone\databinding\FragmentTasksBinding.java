// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentTasksBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final FloatingActionButton addTaskFab;

  @NonNull
  public final TextView emptyStateText;

  @NonNull
  public final SwipeRefreshLayout swipeRefreshLayout;

  @NonNull
  public final RecyclerView tasksRecyclerView;

  private FragmentTasksBinding(@NonNull ConstraintLayout rootView,
      @NonNull FloatingActionButton addTaskFab, @NonNull TextView emptyStateText,
      @NonNull SwipeRefreshLayout swipeRefreshLayout, @NonNull RecyclerView tasksRecyclerView) {
    this.rootView = rootView;
    this.addTaskFab = addTaskFab;
    this.emptyStateText = emptyStateText;
    this.swipeRefreshLayout = swipeRefreshLayout;
    this.tasksRecyclerView = tasksRecyclerView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentTasksBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentTasksBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_tasks, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentTasksBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addTaskFab;
      FloatingActionButton addTaskFab = ViewBindings.findChildViewById(rootView, id);
      if (addTaskFab == null) {
        break missingId;
      }

      id = R.id.emptyStateText;
      TextView emptyStateText = ViewBindings.findChildViewById(rootView, id);
      if (emptyStateText == null) {
        break missingId;
      }

      id = R.id.swipe_refresh_layout;
      SwipeRefreshLayout swipeRefreshLayout = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefreshLayout == null) {
        break missingId;
      }

      id = R.id.tasksRecyclerView;
      RecyclerView tasksRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (tasksRecyclerView == null) {
        break missingId;
      }

      return new FragmentTasksBinding((ConstraintLayout) rootView, addTaskFab, emptyStateText,
          swipeRefreshLayout, tasksRecyclerView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
