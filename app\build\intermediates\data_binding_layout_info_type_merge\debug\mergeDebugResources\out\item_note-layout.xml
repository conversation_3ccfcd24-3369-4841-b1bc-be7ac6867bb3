<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_note" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_note.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_note_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="120" endOffset="51"/></Target><Target id="@+id/noteTitle" view="TextView"><Expressions/><location startLine="15" startOffset="8" endLine="24" endOffset="37"/></Target><Target id="@+id/shareButton" view="ImageButton"><Expressions/><location startLine="26" startOffset="8" endLine="35" endOffset="55"/></Target><Target id="@+id/noteDate" view="TextView"><Expressions/><location startLine="37" startOffset="8" endLine="47" endOffset="42"/></Target><Target id="@+id/noteContent" view="TextView"><Expressions/><location startLine="49" startOffset="8" endLine="60" endOffset="143"/></Target><Target id="@+id/attachmentsSection" view="LinearLayout"><Expressions/><location startLine="63" startOffset="8" endLine="117" endOffset="22"/></Target><Target id="@+id/voiceNoteIndicator" view="LinearLayout"><Expressions/><location startLine="74" startOffset="12" endLine="101" endOffset="26"/></Target><Target id="@+id/voiceNoteCountText" view="TextView"><Expressions/><location startLine="92" startOffset="16" endLine="99" endOffset="48"/></Target><Target id="@+id/imageContainer" view="LinearLayout"><Expressions/><location startLine="108" startOffset="16" endLine="114" endOffset="48"/></Target></Targets></Layout>