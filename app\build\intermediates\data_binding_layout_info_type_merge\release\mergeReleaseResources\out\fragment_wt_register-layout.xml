<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_wt_register" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\fragment_wt_register.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_wt_register_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="142" endOffset="51"/></Target><Target id="@+id/networkStatusBanner" view="TextView"><Expressions/><location startLine="7" startOffset="4" endLine="17" endOffset="51"/></Target><Target id="@+id/filterSection" view="LinearLayout"><Expressions/><location startLine="20" startOffset="4" endLine="75" endOffset="18"/></Target><Target id="@+id/monthDropdown" view="AutoCompleteTextView"><Expressions/><location startLine="48" startOffset="16" endLine="54" endOffset="56"/></Target><Target id="@+id/applyFilterButton" view="Button"><Expressions/><location startLine="57" startOffset="12" endLine="64" endOffset="50"/></Target><Target id="@+id/totalAmountText" view="TextView"><Expressions/><location startLine="67" startOffset="8" endLine="74" endOffset="39"/></Target><Target id="@+id/swipe_refresh_layout" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="77" startOffset="4" endLine="93" endOffset="59"/></Target><Target id="@+id/studentsRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="86" startOffset="8" endLine="91" endOffset="36"/></Target><Target id="@+id/emptyState" view="LinearLayout"><Expressions/><location startLine="95" startOffset="4" endLine="128" endOffset="18"/></Target><Target id="@+id/addStudentFab" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="130" startOffset="4" endLine="140" endOffset="33"/></Target></Targets></Layout>