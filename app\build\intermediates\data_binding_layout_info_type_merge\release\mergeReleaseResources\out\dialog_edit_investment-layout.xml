<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_edit_investment" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\dialog_edit_investment.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_edit_investment_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="99" endOffset="14"/></Target><Target id="@+id/nameInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="16" startOffset="8" endLine="20" endOffset="38"/></Target><Target id="@+id/typeInput" view="AutoCompleteTextView"><Expressions/><location startLine="31" startOffset="8" endLine="37" endOffset="46"/></Target><Target id="@+id/amountInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="48" startOffset="8" endLine="52" endOffset="47"/></Target><Target id="@+id/descriptionInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="63" startOffset="8" endLine="68" endOffset="34"/></Target><Target id="@+id/imagesRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="77" startOffset="8" endLine="82" endOffset="39"/></Target><Target id="@+id/isPastInvestmentCheckbox" view="com.google.android.material.checkbox.MaterialCheckBox"><Expressions/><location startLine="85" startOffset="4" endLine="91" endOffset="33"/></Target><Target id="@+id/addImageButton" view="Button"><Expressions/><location startLine="93" startOffset="4" endLine="97" endOffset="35"/></Target></Targets></Layout>