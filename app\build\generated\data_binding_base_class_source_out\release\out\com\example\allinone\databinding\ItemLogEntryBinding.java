// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemLogEntryBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView levelTagText;

  @NonNull
  public final TextView messageText;

  @NonNull
  public final TextView timestampText;

  private ItemLogEntryBinding(@NonNull MaterialCardView rootView, @NonNull TextView levelTagText,
      @NonNull TextView messageText, @NonNull TextView timestampText) {
    this.rootView = rootView;
    this.levelTagText = levelTagText;
    this.messageText = messageText;
    this.timestampText = timestampText;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemLogEntryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemLogEntryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_log_entry, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemLogEntryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.levelTagText;
      TextView levelTagText = ViewBindings.findChildViewById(rootView, id);
      if (levelTagText == null) {
        break missingId;
      }

      id = R.id.messageText;
      TextView messageText = ViewBindings.findChildViewById(rootView, id);
      if (messageText == null) {
        break missingId;
      }

      id = R.id.timestampText;
      TextView timestampText = ViewBindings.findChildViewById(rootView, id);
      if (timestampText == null) {
        break missingId;
      }

      return new ItemLogEntryBinding((MaterialCardView) rootView, levelTagText, messageText,
          timestampText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
