package com.example.allinone;

import android.app.Activity;
import android.app.Service;
import android.view.View;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.SavedStateHandle;
import androidx.lifecycle.ViewModel;
import com.example.allinone.cache.CacheManager;
import com.example.allinone.di.AppModule_ProvideCacheManagerFactory;
import com.example.allinone.di.AppModule_ProvideFirebaseManagerFactory;
import com.example.allinone.di.AppModule_ProvideFirebaseRepositoryFactory;
import com.example.allinone.di.AppModule_ProvideLogcatHelperFactory;
import com.example.allinone.di.AppModule_ProvideNetworkUtilsFactory;
import com.example.allinone.feature.instagram.data.repository.InstagramRepositoryImpl;
import com.example.allinone.feature.instagram.di.InstagramModule_Companion_ProvideAnalyzeInstagramURLUseCaseFactory;
import com.example.allinone.feature.instagram.di.InstagramModule_Companion_ProvideAnalyzeMultimodalContentUseCaseFactory;
import com.example.allinone.feature.instagram.di.InstagramModule_Companion_ProvideGetMultimodalSuggestionsUseCaseFactory;
import com.example.allinone.feature.instagram.di.InstagramModule_Companion_ProvideProcessAudioRecordingUseCaseFactory;
import com.example.allinone.feature.instagram.di.InstagramModule_Companion_ProvideUploadFileForAnalysisUseCaseFactory;
import com.example.allinone.feature.instagram.domain.usecase.AnalyzeInstagramURLUseCase;
import com.example.allinone.feature.instagram.domain.usecase.AnalyzeMultimodalContentUseCase;
import com.example.allinone.feature.instagram.domain.usecase.CheckInstagramHealthUseCase;
import com.example.allinone.feature.instagram.domain.usecase.GetInstagramAnalyticsUseCase;
import com.example.allinone.feature.instagram.domain.usecase.GetInstagramPostsUseCase;
import com.example.allinone.feature.instagram.domain.usecase.ProcessAudioRecordingUseCase;
import com.example.allinone.feature.instagram.domain.usecase.QueryInstagramAIUseCase;
import com.example.allinone.feature.instagram.domain.usecase.UploadFileForAnalysisUseCase;
import com.example.allinone.feature.instagram.ui.viewmodel.InstagramAIViewModel;
import com.example.allinone.feature.instagram.ui.viewmodel.InstagramAIViewModel_HiltModules;
import com.example.allinone.feature.instagram.ui.viewmodel.InstagramViewModel;
import com.example.allinone.feature.instagram.ui.viewmodel.InstagramViewModel_HiltModules;
import com.example.allinone.firebase.FirebaseManager;
import com.example.allinone.firebase.FirebaseRepository;
import com.example.allinone.ui.TasksFragment;
import com.example.allinone.ui.instagram.InstagramAskAIFragment;
import com.example.allinone.ui.instagram.InstagramInsightsFragment;
import com.example.allinone.ui.instagram.InstagramPostsFragment;
import com.example.allinone.utils.LogcatHelper;
import com.example.allinone.utils.NetworkUtils;
import com.example.allinone.viewmodels.FuturesViewModel;
import com.example.allinone.viewmodels.FuturesViewModel_HiltModules;
import com.example.allinone.viewmodels.HomeViewModel;
import com.example.allinone.viewmodels.HomeViewModel_HiltModules;
import com.example.allinone.viewmodels.TasksViewModel;
import com.example.allinone.viewmodels.TasksViewModel_HiltModules;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import dagger.hilt.android.ActivityRetainedLifecycle;
import dagger.hilt.android.ViewModelLifecycle;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories_InternalFactoryFactory_Factory;
import dagger.hilt.android.internal.managers.ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory;
import dagger.hilt.android.internal.managers.SavedStateHandleHolder;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideContextFactory;
import dagger.internal.DaggerGenerated;
import dagger.internal.DoubleCheck;
import dagger.internal.IdentifierNameString;
import dagger.internal.KeepFieldType;
import dagger.internal.LazyClassKeyMap;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DaggerAllinOneApplication_HiltComponents_SingletonC {
  private DaggerAllinOneApplication_HiltComponents_SingletonC() {
  }

  public static Builder builder() {
    return new Builder();
  }

  public static final class Builder {
    private ApplicationContextModule applicationContextModule;

    private Builder() {
    }

    public Builder applicationContextModule(ApplicationContextModule applicationContextModule) {
      this.applicationContextModule = Preconditions.checkNotNull(applicationContextModule);
      return this;
    }

    public AllinOneApplication_HiltComponents.SingletonC build() {
      Preconditions.checkBuilderRequirement(applicationContextModule, ApplicationContextModule.class);
      return new SingletonCImpl(applicationContextModule);
    }
  }

  private static final class ActivityRetainedCBuilder implements AllinOneApplication_HiltComponents.ActivityRetainedC.Builder {
    private final SingletonCImpl singletonCImpl;

    private SavedStateHandleHolder savedStateHandleHolder;

    private ActivityRetainedCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ActivityRetainedCBuilder savedStateHandleHolder(
        SavedStateHandleHolder savedStateHandleHolder) {
      this.savedStateHandleHolder = Preconditions.checkNotNull(savedStateHandleHolder);
      return this;
    }

    @Override
    public AllinOneApplication_HiltComponents.ActivityRetainedC build() {
      Preconditions.checkBuilderRequirement(savedStateHandleHolder, SavedStateHandleHolder.class);
      return new ActivityRetainedCImpl(singletonCImpl, savedStateHandleHolder);
    }
  }

  private static final class ActivityCBuilder implements AllinOneApplication_HiltComponents.ActivityC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private Activity activity;

    private ActivityCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ActivityCBuilder activity(Activity activity) {
      this.activity = Preconditions.checkNotNull(activity);
      return this;
    }

    @Override
    public AllinOneApplication_HiltComponents.ActivityC build() {
      Preconditions.checkBuilderRequirement(activity, Activity.class);
      return new ActivityCImpl(singletonCImpl, activityRetainedCImpl, activity);
    }
  }

  private static final class FragmentCBuilder implements AllinOneApplication_HiltComponents.FragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private Fragment fragment;

    private FragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public FragmentCBuilder fragment(Fragment fragment) {
      this.fragment = Preconditions.checkNotNull(fragment);
      return this;
    }

    @Override
    public AllinOneApplication_HiltComponents.FragmentC build() {
      Preconditions.checkBuilderRequirement(fragment, Fragment.class);
      return new FragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragment);
    }
  }

  private static final class ViewWithFragmentCBuilder implements AllinOneApplication_HiltComponents.ViewWithFragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private View view;

    private ViewWithFragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;
    }

    @Override
    public ViewWithFragmentCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public AllinOneApplication_HiltComponents.ViewWithFragmentC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewWithFragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl, view);
    }
  }

  private static final class ViewCBuilder implements AllinOneApplication_HiltComponents.ViewC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private View view;

    private ViewCBuilder(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public ViewCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public AllinOneApplication_HiltComponents.ViewC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, view);
    }
  }

  private static final class ViewModelCBuilder implements AllinOneApplication_HiltComponents.ViewModelC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private SavedStateHandle savedStateHandle;

    private ViewModelLifecycle viewModelLifecycle;

    private ViewModelCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ViewModelCBuilder savedStateHandle(SavedStateHandle handle) {
      this.savedStateHandle = Preconditions.checkNotNull(handle);
      return this;
    }

    @Override
    public ViewModelCBuilder viewModelLifecycle(ViewModelLifecycle viewModelLifecycle) {
      this.viewModelLifecycle = Preconditions.checkNotNull(viewModelLifecycle);
      return this;
    }

    @Override
    public AllinOneApplication_HiltComponents.ViewModelC build() {
      Preconditions.checkBuilderRequirement(savedStateHandle, SavedStateHandle.class);
      Preconditions.checkBuilderRequirement(viewModelLifecycle, ViewModelLifecycle.class);
      return new ViewModelCImpl(singletonCImpl, activityRetainedCImpl, savedStateHandle, viewModelLifecycle);
    }
  }

  private static final class ServiceCBuilder implements AllinOneApplication_HiltComponents.ServiceC.Builder {
    private final SingletonCImpl singletonCImpl;

    private Service service;

    private ServiceCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ServiceCBuilder service(Service service) {
      this.service = Preconditions.checkNotNull(service);
      return this;
    }

    @Override
    public AllinOneApplication_HiltComponents.ServiceC build() {
      Preconditions.checkBuilderRequirement(service, Service.class);
      return new ServiceCImpl(singletonCImpl, service);
    }
  }

  private static final class ViewWithFragmentCImpl extends AllinOneApplication_HiltComponents.ViewWithFragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private final ViewWithFragmentCImpl viewWithFragmentCImpl = this;

    private ViewWithFragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;


    }
  }

  private static final class FragmentCImpl extends AllinOneApplication_HiltComponents.FragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl = this;

    private FragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        Fragment fragmentParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }

    @Override
    public void injectTasksFragment(TasksFragment tasksFragment) {
    }

    @Override
    public void injectInstagramAskAIFragment(InstagramAskAIFragment instagramAskAIFragment) {
    }

    @Override
    public void injectInstagramInsightsFragment(
        InstagramInsightsFragment instagramInsightsFragment) {
    }

    @Override
    public void injectInstagramPostsFragment(InstagramPostsFragment instagramPostsFragment) {
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return activityCImpl.getHiltInternalFactoryFactory();
    }

    @Override
    public ViewWithFragmentComponentBuilder viewWithFragmentComponentBuilder() {
      return new ViewWithFragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl);
    }
  }

  private static final class ViewCImpl extends AllinOneApplication_HiltComponents.ViewC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final ViewCImpl viewCImpl = this;

    private ViewCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }
  }

  private static final class ActivityCImpl extends AllinOneApplication_HiltComponents.ActivityC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl = this;

    private ActivityCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, Activity activityParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;


    }

    @Override
    public void injectMainActivity(MainActivity mainActivity) {
      injectMainActivity2(mainActivity);
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return DefaultViewModelFactories_InternalFactoryFactory_Factory.newInstance(getViewModelKeys(), new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl));
    }

    @Override
    public Map<Class<?>, Boolean> getViewModelKeys() {
      return LazyClassKeyMap.<Boolean>of(ImmutableMap.<String, Boolean>of(LazyClassKeyProvider.com_example_allinone_viewmodels_FuturesViewModel, FuturesViewModel_HiltModules.KeyModule.provide(), LazyClassKeyProvider.com_example_allinone_viewmodels_HomeViewModel, HomeViewModel_HiltModules.KeyModule.provide(), LazyClassKeyProvider.com_example_allinone_feature_instagram_ui_viewmodel_InstagramAIViewModel, InstagramAIViewModel_HiltModules.KeyModule.provide(), LazyClassKeyProvider.com_example_allinone_feature_instagram_ui_viewmodel_InstagramViewModel, InstagramViewModel_HiltModules.KeyModule.provide(), LazyClassKeyProvider.com_example_allinone_viewmodels_TasksViewModel, TasksViewModel_HiltModules.KeyModule.provide()));
    }

    @Override
    public ViewModelComponentBuilder getViewModelComponentBuilder() {
      return new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public FragmentComponentBuilder fragmentComponentBuilder() {
      return new FragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @Override
    public ViewComponentBuilder viewComponentBuilder() {
      return new ViewCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @CanIgnoreReturnValue
    private MainActivity injectMainActivity2(MainActivity instance) {
      MainActivity_MembersInjector.injectFirebaseManager(instance, singletonCImpl.provideFirebaseManagerProvider.get());
      MainActivity_MembersInjector.injectFirebaseRepository(instance, singletonCImpl.provideFirebaseRepositoryProvider.get());
      return instance;
    }

    @IdentifierNameString
    private static final class LazyClassKeyProvider {
      static String com_example_allinone_viewmodels_TasksViewModel = "com.example.allinone.viewmodels.TasksViewModel";

      static String com_example_allinone_viewmodels_FuturesViewModel = "com.example.allinone.viewmodels.FuturesViewModel";

      static String com_example_allinone_feature_instagram_ui_viewmodel_InstagramViewModel = "com.example.allinone.feature.instagram.ui.viewmodel.InstagramViewModel";

      static String com_example_allinone_feature_instagram_ui_viewmodel_InstagramAIViewModel = "com.example.allinone.feature.instagram.ui.viewmodel.InstagramAIViewModel";

      static String com_example_allinone_viewmodels_HomeViewModel = "com.example.allinone.viewmodels.HomeViewModel";

      @KeepFieldType
      TasksViewModel com_example_allinone_viewmodels_TasksViewModel2;

      @KeepFieldType
      FuturesViewModel com_example_allinone_viewmodels_FuturesViewModel2;

      @KeepFieldType
      InstagramViewModel com_example_allinone_feature_instagram_ui_viewmodel_InstagramViewModel2;

      @KeepFieldType
      InstagramAIViewModel com_example_allinone_feature_instagram_ui_viewmodel_InstagramAIViewModel2;

      @KeepFieldType
      HomeViewModel com_example_allinone_viewmodels_HomeViewModel2;
    }
  }

  private static final class ViewModelCImpl extends AllinOneApplication_HiltComponents.ViewModelC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ViewModelCImpl viewModelCImpl = this;

    private Provider<FuturesViewModel> futuresViewModelProvider;

    private Provider<HomeViewModel> homeViewModelProvider;

    private Provider<InstagramAIViewModel> instagramAIViewModelProvider;

    private Provider<InstagramViewModel> instagramViewModelProvider;

    private Provider<TasksViewModel> tasksViewModelProvider;

    private ViewModelCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, SavedStateHandle savedStateHandleParam,
        ViewModelLifecycle viewModelLifecycleParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;

      initialize(savedStateHandleParam, viewModelLifecycleParam);

    }

    private QueryInstagramAIUseCase queryInstagramAIUseCase() {
      return new QueryInstagramAIUseCase(singletonCImpl.instagramRepositoryImplProvider.get());
    }

    private GetInstagramPostsUseCase getInstagramPostsUseCase() {
      return new GetInstagramPostsUseCase(singletonCImpl.instagramRepositoryImplProvider.get());
    }

    private GetInstagramAnalyticsUseCase getInstagramAnalyticsUseCase() {
      return new GetInstagramAnalyticsUseCase(singletonCImpl.instagramRepositoryImplProvider.get());
    }

    private CheckInstagramHealthUseCase checkInstagramHealthUseCase() {
      return new CheckInstagramHealthUseCase(singletonCImpl.instagramRepositoryImplProvider.get());
    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandle savedStateHandleParam,
        final ViewModelLifecycle viewModelLifecycleParam) {
      this.futuresViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 0);
      this.homeViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 1);
      this.instagramAIViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 2);
      this.instagramViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 3);
      this.tasksViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 4);
    }

    @Override
    public Map<Class<?>, javax.inject.Provider<ViewModel>> getHiltViewModelMap() {
      return LazyClassKeyMap.<javax.inject.Provider<ViewModel>>of(ImmutableMap.<String, javax.inject.Provider<ViewModel>>of(LazyClassKeyProvider.com_example_allinone_viewmodels_FuturesViewModel, ((Provider) futuresViewModelProvider), LazyClassKeyProvider.com_example_allinone_viewmodels_HomeViewModel, ((Provider) homeViewModelProvider), LazyClassKeyProvider.com_example_allinone_feature_instagram_ui_viewmodel_InstagramAIViewModel, ((Provider) instagramAIViewModelProvider), LazyClassKeyProvider.com_example_allinone_feature_instagram_ui_viewmodel_InstagramViewModel, ((Provider) instagramViewModelProvider), LazyClassKeyProvider.com_example_allinone_viewmodels_TasksViewModel, ((Provider) tasksViewModelProvider)));
    }

    @Override
    public Map<Class<?>, Object> getHiltViewModelAssistedMap() {
      return ImmutableMap.<Class<?>, Object>of();
    }

    @IdentifierNameString
    private static final class LazyClassKeyProvider {
      static String com_example_allinone_viewmodels_HomeViewModel = "com.example.allinone.viewmodels.HomeViewModel";

      static String com_example_allinone_feature_instagram_ui_viewmodel_InstagramAIViewModel = "com.example.allinone.feature.instagram.ui.viewmodel.InstagramAIViewModel";

      static String com_example_allinone_viewmodels_FuturesViewModel = "com.example.allinone.viewmodels.FuturesViewModel";

      static String com_example_allinone_viewmodels_TasksViewModel = "com.example.allinone.viewmodels.TasksViewModel";

      static String com_example_allinone_feature_instagram_ui_viewmodel_InstagramViewModel = "com.example.allinone.feature.instagram.ui.viewmodel.InstagramViewModel";

      @KeepFieldType
      HomeViewModel com_example_allinone_viewmodels_HomeViewModel2;

      @KeepFieldType
      InstagramAIViewModel com_example_allinone_feature_instagram_ui_viewmodel_InstagramAIViewModel2;

      @KeepFieldType
      FuturesViewModel com_example_allinone_viewmodels_FuturesViewModel2;

      @KeepFieldType
      TasksViewModel com_example_allinone_viewmodels_TasksViewModel2;

      @KeepFieldType
      InstagramViewModel com_example_allinone_feature_instagram_ui_viewmodel_InstagramViewModel2;
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final ViewModelCImpl viewModelCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          ViewModelCImpl viewModelCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.viewModelCImpl = viewModelCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.example.allinone.viewmodels.FuturesViewModel 
          return (T) new FuturesViewModel();

          case 1: // com.example.allinone.viewmodels.HomeViewModel 
          return (T) new HomeViewModel(singletonCImpl.provideFirebaseRepositoryProvider.get());

          case 2: // com.example.allinone.feature.instagram.ui.viewmodel.InstagramAIViewModel 
          return (T) new InstagramAIViewModel(viewModelCImpl.queryInstagramAIUseCase(), singletonCImpl.analyzeMultimodalContentUseCase(), singletonCImpl.uploadFileForAnalysisUseCase(), singletonCImpl.analyzeInstagramURLUseCase(), singletonCImpl.processAudioRecordingUseCase(), InstagramModule_Companion_ProvideGetMultimodalSuggestionsUseCaseFactory.provideGetMultimodalSuggestionsUseCase());

          case 3: // com.example.allinone.feature.instagram.ui.viewmodel.InstagramViewModel 
          return (T) new InstagramViewModel(viewModelCImpl.getInstagramPostsUseCase(), viewModelCImpl.getInstagramAnalyticsUseCase(), viewModelCImpl.checkInstagramHealthUseCase());

          case 4: // com.example.allinone.viewmodels.TasksViewModel 
          return (T) new TasksViewModel(singletonCImpl.provideFirebaseRepositoryProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ActivityRetainedCImpl extends AllinOneApplication_HiltComponents.ActivityRetainedC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl = this;

    private Provider<ActivityRetainedLifecycle> provideActivityRetainedLifecycleProvider;

    private ActivityRetainedCImpl(SingletonCImpl singletonCImpl,
        SavedStateHandleHolder savedStateHandleHolderParam) {
      this.singletonCImpl = singletonCImpl;

      initialize(savedStateHandleHolderParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandleHolder savedStateHandleHolderParam) {
      this.provideActivityRetainedLifecycleProvider = DoubleCheck.provider(new SwitchingProvider<ActivityRetainedLifecycle>(singletonCImpl, activityRetainedCImpl, 0));
    }

    @Override
    public ActivityComponentBuilder activityComponentBuilder() {
      return new ActivityCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public ActivityRetainedLifecycle getActivityRetainedLifecycle() {
      return provideActivityRetainedLifecycleProvider.get();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // dagger.hilt.android.ActivityRetainedLifecycle 
          return (T) ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory.provideActivityRetainedLifecycle();

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ServiceCImpl extends AllinOneApplication_HiltComponents.ServiceC {
    private final SingletonCImpl singletonCImpl;

    private final ServiceCImpl serviceCImpl = this;

    private ServiceCImpl(SingletonCImpl singletonCImpl, Service serviceParam) {
      this.singletonCImpl = singletonCImpl;


    }
  }

  private static final class SingletonCImpl extends AllinOneApplication_HiltComponents.SingletonC {
    private final ApplicationContextModule applicationContextModule;

    private final SingletonCImpl singletonCImpl = this;

    private Provider<NetworkUtils> provideNetworkUtilsProvider;

    private Provider<CacheManager> provideCacheManagerProvider;

    private Provider<LogcatHelper> provideLogcatHelperProvider;

    private Provider<FirebaseManager> provideFirebaseManagerProvider;

    private Provider<FirebaseRepository> provideFirebaseRepositoryProvider;

    private Provider<InstagramRepositoryImpl> instagramRepositoryImplProvider;

    private SingletonCImpl(ApplicationContextModule applicationContextModuleParam) {
      this.applicationContextModule = applicationContextModuleParam;
      initialize(applicationContextModuleParam);

    }

    private AnalyzeMultimodalContentUseCase analyzeMultimodalContentUseCase() {
      return InstagramModule_Companion_ProvideAnalyzeMultimodalContentUseCaseFactory.provideAnalyzeMultimodalContentUseCase(instagramRepositoryImplProvider.get());
    }

    private UploadFileForAnalysisUseCase uploadFileForAnalysisUseCase() {
      return InstagramModule_Companion_ProvideUploadFileForAnalysisUseCaseFactory.provideUploadFileForAnalysisUseCase(instagramRepositoryImplProvider.get());
    }

    private AnalyzeInstagramURLUseCase analyzeInstagramURLUseCase() {
      return InstagramModule_Companion_ProvideAnalyzeInstagramURLUseCaseFactory.provideAnalyzeInstagramURLUseCase(instagramRepositoryImplProvider.get());
    }

    private ProcessAudioRecordingUseCase processAudioRecordingUseCase() {
      return InstagramModule_Companion_ProvideProcessAudioRecordingUseCaseFactory.provideProcessAudioRecordingUseCase(instagramRepositoryImplProvider.get());
    }

    @SuppressWarnings("unchecked")
    private void initialize(final ApplicationContextModule applicationContextModuleParam) {
      this.provideNetworkUtilsProvider = DoubleCheck.provider(new SwitchingProvider<NetworkUtils>(singletonCImpl, 0));
      this.provideCacheManagerProvider = DoubleCheck.provider(new SwitchingProvider<CacheManager>(singletonCImpl, 1));
      this.provideLogcatHelperProvider = DoubleCheck.provider(new SwitchingProvider<LogcatHelper>(singletonCImpl, 2));
      this.provideFirebaseManagerProvider = DoubleCheck.provider(new SwitchingProvider<FirebaseManager>(singletonCImpl, 3));
      this.provideFirebaseRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<FirebaseRepository>(singletonCImpl, 4));
      this.instagramRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<InstagramRepositoryImpl>(singletonCImpl, 5));
    }

    @Override
    public void injectAllinOneApplication(AllinOneApplication allinOneApplication) {
      injectAllinOneApplication2(allinOneApplication);
    }

    @Override
    public Set<Boolean> getDisableFragmentGetContextFix() {
      return ImmutableSet.<Boolean>of();
    }

    @Override
    public ActivityRetainedComponentBuilder retainedComponentBuilder() {
      return new ActivityRetainedCBuilder(singletonCImpl);
    }

    @Override
    public ServiceComponentBuilder serviceComponentBuilder() {
      return new ServiceCBuilder(singletonCImpl);
    }

    @CanIgnoreReturnValue
    private AllinOneApplication injectAllinOneApplication2(AllinOneApplication instance) {
      AllinOneApplication_MembersInjector.injectNetworkUtils(instance, provideNetworkUtilsProvider.get());
      AllinOneApplication_MembersInjector.injectCacheManager(instance, provideCacheManagerProvider.get());
      AllinOneApplication_MembersInjector.injectLogcatHelper(instance, provideLogcatHelperProvider.get());
      return instance;
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.example.allinone.utils.NetworkUtils 
          return (T) AppModule_ProvideNetworkUtilsFactory.provideNetworkUtils(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 1: // com.example.allinone.cache.CacheManager 
          return (T) AppModule_ProvideCacheManagerFactory.provideCacheManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 2: // com.example.allinone.utils.LogcatHelper 
          return (T) AppModule_ProvideLogcatHelperFactory.provideLogcatHelper(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 3: // com.example.allinone.firebase.FirebaseManager 
          return (T) AppModule_ProvideFirebaseManagerFactory.provideFirebaseManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 4: // com.example.allinone.firebase.FirebaseRepository 
          return (T) AppModule_ProvideFirebaseRepositoryFactory.provideFirebaseRepository(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 5: // com.example.allinone.feature.instagram.data.repository.InstagramRepositoryImpl 
          return (T) new InstagramRepositoryImpl();

          default: throw new AssertionError(id);
        }
      }
    }
  }
}
