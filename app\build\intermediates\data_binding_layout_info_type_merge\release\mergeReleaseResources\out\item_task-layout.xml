<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_task" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_task.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_task_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="36" endOffset="51"/></Target><Target id="@+id/taskCheckbox" view="com.google.android.material.checkbox.MaterialCheckBox"><Expressions/><location startLine="17" startOffset="8" endLine="22" endOffset="35"/></Target><Target id="@+id/taskDescription" view="TextView"><Expressions/><location startLine="24" startOffset="8" endLine="32" endOffset="99"/></Target></Targets></Layout>