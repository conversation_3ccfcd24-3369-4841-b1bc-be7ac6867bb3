{"logs": [{"outputFile": "com.example.allinone.app-mergeReleaseResources-108:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cd483c27f09d2683183fc5f548eeb5d9\\transformed\\play-services-base-18.5.0\\res\\values-hi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,453,575,683,830,956,1064,1172,1325,1430,1592,1718,1855,2004,2063,2126", "endColumns": "103,155,121,107,146,125,107,107,152,104,161,125,136,148,58,62,83", "endOffsets": "296,452,574,682,829,955,1063,1171,1324,1429,1591,1717,1854,2003,2062,2125,2209"}, "to": {"startLines": "69,70,71,72,73,74,75,76,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5549,5657,5817,5943,6055,6206,6336,6448,6706,6863,6972,7138,7268,7409,7562,7625,7692", "endColumns": "107,159,125,111,150,129,111,111,156,108,165,129,140,152,62,66,87", "endOffsets": "5652,5812,5938,6050,6201,6331,6443,6555,6858,6967,7133,7263,7404,7557,7620,7687,7775"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98a7034e786415bc9e009aca9ccbea7a\\transformed\\play-services-basement-18.4.0\\res\\values-hi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "77", "startColumns": "4", "startOffsets": "6560", "endColumns": "145", "endOffsets": "6701"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e87b0a766c58fccb5c3caec65c96631b\\transformed\\exoplayer-core-2.19.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,189,260,328,424,492,615,736", "endColumns": "67,65,70,67,95,67,122,120,86", "endOffsets": "118,184,255,323,419,487,610,731,818"}, "to": {"startLines": "117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10237,10305,10371,10442,10510,10606,10674,10797,10918", "endColumns": "67,65,70,67,95,67,122,120,86", "endOffsets": "10300,10366,10437,10505,10601,10669,10792,10913,11000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37fc1bdcb99e9ca53adfd9dc62ba1cb8\\transformed\\foundation-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,85", "endOffsets": "135,221"}, "to": {"startLines": "285,286", "startColumns": "4,4", "startOffsets": "25350,25435", "endColumns": "84,85", "endOffsets": "25430,25516"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ff95595b5660c73a0c7669f7cd696712\\transformed\\material3-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,291,415,531,626,722,835,973,1093,1243,1328,1431,1522,1619,1749,1869,1977,2122,2268,2398,2587,2714,2832,2954,3080,3172,3267,3395,3521,3620,3722,3834,3980,4132,4246,4346,4422,4522,4621,4707,4797,4902,4982,5066,5166,5266,5361,5463,5549,5651,5749,5853,5968,6048,6148", "endColumns": "117,117,123,115,94,95,112,137,119,149,84,102,90,96,129,119,107,144,145,129,188,126,117,121,125,91,94,127,125,98,101,111,145,151,113,99,75,99,98,85,89,104,79,83,99,99,94,101,85,101,97,103,114,79,99,93", "endOffsets": "168,286,410,526,621,717,830,968,1088,1238,1323,1426,1517,1614,1744,1864,1972,2117,2263,2393,2582,2709,2827,2949,3075,3167,3262,3390,3516,3615,3717,3829,3975,4127,4241,4341,4417,4517,4616,4702,4792,4897,4977,5061,5161,5261,5356,5458,5544,5646,5744,5848,5963,6043,6143,6237"}, "to": {"startLines": "152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12849,12967,13085,13209,13325,13420,13516,13629,13767,13887,14037,14122,14225,14316,14413,14543,14663,14771,14916,15062,15192,15381,15508,15626,15748,15874,15966,16061,16189,16315,16414,16516,16628,16774,16926,17040,17140,17216,17316,17415,17501,17591,17696,17776,17860,17960,18060,18155,18257,18343,18445,18543,18647,18762,18842,18942", "endColumns": "117,117,123,115,94,95,112,137,119,149,84,102,90,96,129,119,107,144,145,129,188,126,117,121,125,91,94,127,125,98,101,111,145,151,113,99,75,99,98,85,89,104,79,83,99,99,94,101,85,101,97,103,114,79,99,93", "endOffsets": "12962,13080,13204,13320,13415,13511,13624,13762,13882,14032,14117,14220,14311,14408,14538,14658,14766,14911,15057,15187,15376,15503,15621,15743,15869,15961,16056,16184,16310,16409,16511,16623,16769,16921,17035,17135,17211,17311,17410,17496,17586,17691,17771,17855,17955,18055,18150,18252,18338,18440,18538,18642,18757,18837,18937,19031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\75ac7f2a23ffbf48dd6409062de1d131\\transformed\\material-1.12.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,413,494,601,697,804,936,1019,1086,1151,1245,1314,1373,1458,1521,1584,1642,1707,1768,1829,1935,1993,2053,2112,2182,2298,2377,2468,2561,2659,2739,2873,2948,3024,3161,3258,3356,3413,3468,3534,3604,3681,3752,3837,3905,3981,4062,4140,4241,4327,4414,4511,4610,4684,4754,4858,4912,4999,5066,5156,5248,5310,5374,5437,5503,5608,5718,5819,5926,5987,6046,6125,6210,6290", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,80,106,95,106,131,82,66,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,90,92,97,79,133,74,75,136,96,97,56,54,65,69,76,70,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,109,100,106,60,58,78,84,79,72", "endOffsets": "254,332,408,489,596,692,799,931,1014,1081,1146,1240,1309,1368,1453,1516,1579,1637,1702,1763,1824,1930,1988,2048,2107,2177,2293,2372,2463,2556,2654,2734,2868,2943,3019,3156,3253,3351,3408,3463,3529,3599,3676,3747,3832,3900,3976,4057,4135,4236,4322,4409,4506,4605,4679,4749,4853,4907,4994,5061,5151,5243,5305,5369,5432,5498,5603,5713,5814,5921,5982,6041,6120,6205,6285,6358"}, "to": {"startLines": "19,52,53,54,55,56,64,65,66,91,92,144,148,151,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,270,274,275,277", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "794,3858,3936,4012,4093,4200,5049,5156,5288,8166,8233,12135,12546,12790,19036,19121,19184,19247,19305,19370,19431,19492,19598,19656,19716,19775,19845,19961,20040,20131,20224,20322,20402,20536,20611,20687,20824,20921,21019,21076,21131,21197,21267,21344,21415,21500,21568,21644,21725,21803,21904,21990,22077,22174,22273,22347,22417,22521,22575,22662,22729,22819,22911,22973,23037,23100,23166,23271,23381,23482,23589,23650,24101,24428,24513,24666", "endLines": "22,52,53,54,55,56,64,65,66,91,92,144,148,151,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,270,274,275,277", "endColumns": "12,77,75,80,106,95,106,131,82,66,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,90,92,97,79,133,74,75,136,96,97,56,54,65,69,76,70,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,109,100,106,60,58,78,84,79,72", "endOffsets": "948,3931,4007,4088,4195,4291,5151,5283,5366,8228,8293,12224,12610,12844,19116,19179,19242,19300,19365,19426,19487,19593,19651,19711,19770,19840,19956,20035,20126,20219,20317,20397,20531,20606,20682,20819,20916,21014,21071,21126,21192,21262,21339,21410,21495,21563,21639,21720,21798,21899,21985,22072,22169,22268,22342,22412,22516,22570,22657,22724,22814,22906,22968,23032,23095,23161,23266,23376,23477,23584,23645,23704,24175,24508,24588,24734"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1375e70be1fe8041f38907ceec5f1093\\transformed\\appcompat-1.7.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,2864"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,273", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "953,1059,1157,1267,1353,1455,1576,1654,1731,1822,1915,2010,2104,2204,2297,2392,2486,2577,2668,2749,2854,2956,3054,3164,3267,3376,3534,24346", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "1054,1152,1262,1348,1450,1571,1649,1726,1817,1910,2005,2099,2199,2292,2387,2481,2572,2663,2744,2849,2951,3049,3159,3262,3371,3529,3630,24423"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d7bf19bacce7fd4c141ebe47b25076b8\\transformed\\ui-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,376,474,563,641,738,827,912,993,1078,1151,1244,1319,1394,1475,1541", "endColumns": "94,82,92,97,88,77,96,88,84,80,84,72,92,74,74,80,65,119", "endOffsets": "195,278,371,469,558,636,733,822,907,988,1073,1146,1239,1314,1389,1470,1536,1656"}, "to": {"startLines": "67,68,88,89,90,149,150,268,269,271,272,276,278,279,280,282,283,284", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5371,5466,7886,7979,8077,12615,12693,23927,24016,24180,24261,24593,24739,24832,24907,25083,25164,25230", "endColumns": "94,82,92,97,88,77,96,88,84,80,84,72,92,74,74,80,65,119", "endOffsets": "5461,5544,7974,8072,8161,12688,12785,24011,24096,24256,24341,24661,24827,24902,24977,25159,25225,25345"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cc3c3a73d52d53a742bae480359958fe\\transformed\\credentials-1.2.0-rc01\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,111", "endOffsets": "161,273"}, "to": {"startLines": "50,51", "startColumns": "4,4", "startOffsets": "3635,3746", "endColumns": "110,111", "endOffsets": "3741,3853"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f9ce903e2ff26f2c616d9362ffafb64\\transformed\\exoplayer-ui-2.19.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,493,699,782,863,946,1041,1141,1210,1273,1359,1445,1510,1574,1638,1706,1819,1935,2047,2120,2204,2273,2342,2426,2508,2575,2638,2691,2753,2807,2868,2928,2995,3058,3128,3189,3251,3317,3380,3447,3507,3567,3641,3715", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,80,82,94,99,68,62,85,85,64,63,63,67,112,115,111,72,83,68,68,83,81,66,62,52,61,53,60,59,66,62,69,60,61,65,62,66,59,59,73,73,52", "endOffsets": "281,488,694,777,858,941,1036,1136,1205,1268,1354,1440,1505,1569,1633,1701,1814,1930,2042,2115,2199,2268,2337,2421,2503,2570,2633,2686,2748,2802,2863,2923,2990,3053,3123,3184,3246,3312,3375,3442,3502,3562,3636,3710,3763"}, "to": {"startLines": "2,11,15,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,588,8298,8381,8462,8545,8640,8740,8809,8872,8958,9044,9109,9173,9237,9305,9418,9534,9646,9719,9803,9872,9941,10025,10107,10174,11005,11058,11120,11174,11235,11295,11362,11425,11495,11556,11618,11684,11747,11814,11874,11934,12008,12082", "endLines": "10,14,18,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143", "endColumns": "17,12,12,82,80,82,94,99,68,62,85,85,64,63,63,67,112,115,111,72,83,68,68,83,81,66,62,52,61,53,60,59,66,62,69,60,61,65,62,66,59,59,73,73,52", "endOffsets": "376,583,789,8376,8457,8540,8635,8735,8804,8867,8953,9039,9104,9168,9232,9300,9413,9529,9641,9714,9798,9867,9936,10020,10102,10169,10232,11053,11115,11169,11230,11290,11357,11420,11490,11551,11613,11679,11742,11809,11869,11929,12003,12077,12130"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\eec5b5722953297dab7b2da4ce527235\\transformed\\browser-1.4.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,375", "endColumns": "105,101,111,102", "endOffsets": "156,258,370,473"}, "to": {"startLines": "87,145,146,147", "startColumns": "4,4,4,4", "startOffsets": "7780,12229,12331,12443", "endColumns": "105,101,111,102", "endOffsets": "7881,12326,12438,12541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8e199df52e4d067d471a8ec3433b6506\\transformed\\core-1.13.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "57,58,59,60,61,62,63,281", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4296,4394,4497,4602,4703,4816,4922,24982", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "4389,4492,4597,4698,4811,4917,5044,25078"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e82eb3f2167345ace1b8e91ea59beec2\\transformed\\navigation-ui-2.8.2\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,159", "endColumns": "103,113", "endOffsets": "154,268"}, "to": {"startLines": "266,267", "startColumns": "4,4", "startOffsets": "23709,23813", "endColumns": "103,113", "endOffsets": "23808,23922"}}]}]}