// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentWtRegistryBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final BottomNavigationView wtBottomNavigation;

  @NonNull
  public final FrameLayout wtFragmentContainer;

  private FragmentWtRegistryBinding(@NonNull ConstraintLayout rootView,
      @NonNull BottomNavigationView wtBottomNavigation, @NonNull FrameLayout wtFragmentContainer) {
    this.rootView = rootView;
    this.wtBottomNavigation = wtBottomNavigation;
    this.wtFragmentContainer = wtFragmentContainer;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentWtRegistryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentWtRegistryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_wt_registry, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentWtRegistryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.wtBottomNavigation;
      BottomNavigationView wtBottomNavigation = ViewBindings.findChildViewById(rootView, id);
      if (wtBottomNavigation == null) {
        break missingId;
      }

      id = R.id.wtFragmentContainer;
      FrameLayout wtFragmentContainer = ViewBindings.findChildViewById(rootView, id);
      if (wtFragmentContainer == null) {
        break missingId;
      }

      return new FragmentWtRegistryBinding((ConstraintLayout) rootView, wtBottomNavigation,
          wtFragmentContainer);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
