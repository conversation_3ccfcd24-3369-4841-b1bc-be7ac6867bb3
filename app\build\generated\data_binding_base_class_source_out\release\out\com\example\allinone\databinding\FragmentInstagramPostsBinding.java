// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentInstagramPostsBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button btnFetchInsights;

  @NonNull
  public final Button btnFetchProfileData;

  @NonNull
  public final Button btnInstagramLogin;

  @NonNull
  public final LinearLayout buttonContainer;

  @NonNull
  public final TextView emptyStateView;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerInstagramPosts;

  @NonNull
  public final ScrollView scrollView;

  @NonNull
  public final SwipeRefreshLayout swipeRefresh;

  @NonNull
  public final TextView textInstagramBusiness;

  @NonNull
  public final TextView textInstagramStats;

  @NonNull
  public final TextView textLastSync;

  private FragmentInstagramPostsBinding(@NonNull ConstraintLayout rootView,
      @NonNull Button btnFetchInsights, @NonNull Button btnFetchProfileData,
      @NonNull Button btnInstagramLogin, @NonNull LinearLayout buttonContainer,
      @NonNull TextView emptyStateView, @NonNull ProgressBar progressBar,
      @NonNull RecyclerView recyclerInstagramPosts, @NonNull ScrollView scrollView,
      @NonNull SwipeRefreshLayout swipeRefresh, @NonNull TextView textInstagramBusiness,
      @NonNull TextView textInstagramStats, @NonNull TextView textLastSync) {
    this.rootView = rootView;
    this.btnFetchInsights = btnFetchInsights;
    this.btnFetchProfileData = btnFetchProfileData;
    this.btnInstagramLogin = btnInstagramLogin;
    this.buttonContainer = buttonContainer;
    this.emptyStateView = emptyStateView;
    this.progressBar = progressBar;
    this.recyclerInstagramPosts = recyclerInstagramPosts;
    this.scrollView = scrollView;
    this.swipeRefresh = swipeRefresh;
    this.textInstagramBusiness = textInstagramBusiness;
    this.textInstagramStats = textInstagramStats;
    this.textLastSync = textLastSync;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentInstagramPostsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentInstagramPostsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_instagram_posts, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentInstagramPostsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnFetchInsights;
      Button btnFetchInsights = ViewBindings.findChildViewById(rootView, id);
      if (btnFetchInsights == null) {
        break missingId;
      }

      id = R.id.btnFetchProfileData;
      Button btnFetchProfileData = ViewBindings.findChildViewById(rootView, id);
      if (btnFetchProfileData == null) {
        break missingId;
      }

      id = R.id.btnInstagramLogin;
      Button btnInstagramLogin = ViewBindings.findChildViewById(rootView, id);
      if (btnInstagramLogin == null) {
        break missingId;
      }

      id = R.id.buttonContainer;
      LinearLayout buttonContainer = ViewBindings.findChildViewById(rootView, id);
      if (buttonContainer == null) {
        break missingId;
      }

      id = R.id.emptyStateView;
      TextView emptyStateView = ViewBindings.findChildViewById(rootView, id);
      if (emptyStateView == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recyclerInstagramPosts;
      RecyclerView recyclerInstagramPosts = ViewBindings.findChildViewById(rootView, id);
      if (recyclerInstagramPosts == null) {
        break missingId;
      }

      id = R.id.scrollView;
      ScrollView scrollView = ViewBindings.findChildViewById(rootView, id);
      if (scrollView == null) {
        break missingId;
      }

      id = R.id.swipeRefresh;
      SwipeRefreshLayout swipeRefresh = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefresh == null) {
        break missingId;
      }

      id = R.id.textInstagramBusiness;
      TextView textInstagramBusiness = ViewBindings.findChildViewById(rootView, id);
      if (textInstagramBusiness == null) {
        break missingId;
      }

      id = R.id.textInstagramStats;
      TextView textInstagramStats = ViewBindings.findChildViewById(rootView, id);
      if (textInstagramStats == null) {
        break missingId;
      }

      id = R.id.textLastSync;
      TextView textLastSync = ViewBindings.findChildViewById(rootView, id);
      if (textLastSync == null) {
        break missingId;
      }

      return new FragmentInstagramPostsBinding((ConstraintLayout) rootView, btnFetchInsights,
          btnFetchProfileData, btnInstagramLogin, buttonContainer, emptyStateView, progressBar,
          recyclerInstagramPosts, scrollView, swipeRefresh, textInstagramBusiness,
          textInstagramStats, textLastSync);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
