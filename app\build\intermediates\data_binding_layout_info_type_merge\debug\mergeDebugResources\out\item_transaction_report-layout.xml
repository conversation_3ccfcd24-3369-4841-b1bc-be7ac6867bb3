<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_transaction_report" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_transaction_report.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_transaction_report_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="87" endOffset="51"/></Target><Target id="@+id/categoryColorIndicator" view="View"><Expressions/><location startLine="19" startOffset="8" endLine="24" endOffset="43"/></Target><Target id="@+id/transactionTitle" view="TextView"><Expressions/><location startLine="32" startOffset="12" endLine="39" endOffset="36"/></Target><Target id="@+id/transactionDescription" view="TextView"><Expressions/><location startLine="41" startOffset="12" endLine="49" endOffset="52"/></Target><Target id="@+id/transactionDate" view="TextView"><Expressions/><location startLine="51" startOffset="12" endLine="57" endOffset="44"/></Target><Target id="@+id/transactionAmount" view="TextView"><Expressions/><location startLine="66" startOffset="12" endLine="74" endOffset="47"/></Target><Target id="@+id/categoryChip" view="TextView"><Expressions/><location startLine="76" startOffset="12" endLine="84" endOffset="53"/></Target></Targets></Layout>