package com.example.allinone.di;

import com.example.allinone.feature.workout.data.datasource.WorkoutRemoteDataSource;
import com.example.allinone.firebase.FirebaseManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class AppModule_ProvideWorkoutRemoteDataSourceFactory implements Factory<WorkoutRemoteDataSource> {
  private final Provider<FirebaseManager> firebaseManagerProvider;

  public AppModule_ProvideWorkoutRemoteDataSourceFactory(
      Provider<FirebaseManager> firebaseManagerProvider) {
    this.firebaseManagerProvider = firebaseManagerProvider;
  }

  @Override
  public WorkoutRemoteDataSource get() {
    return provideWorkoutRemoteDataSource(firebaseManagerProvider.get());
  }

  public static AppModule_ProvideWorkoutRemoteDataSourceFactory create(
      Provider<FirebaseManager> firebaseManagerProvider) {
    return new AppModule_ProvideWorkoutRemoteDataSourceFactory(firebaseManagerProvider);
  }

  public static WorkoutRemoteDataSource provideWorkoutRemoteDataSource(
      FirebaseManager firebaseManager) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideWorkoutRemoteDataSource(firebaseManager));
  }
}
