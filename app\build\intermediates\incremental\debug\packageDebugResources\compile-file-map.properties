#Sun Jul 13 09:58:20 TRT 2025
com.example.allinone.app-main-7\:/xml/backup_rules.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\backup_rules.xml
com.example.allinone.app-main-7\:/drawable/ic_reports.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_reports.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_wt_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_wt_student.xml
com.example.allinone.app-packageDebugResources-4\:/layout/dialog_task_group.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_task_group.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_investment_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_investment_image.xml
com.example.allinone.app-main-7\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.example.allinone.app-packageDebugResources-4\:/layout/item_futures_position.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_futures_position.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_category_dropdown.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_category_dropdown.xml
com.example.allinone.app-main-7\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher_round.xml
com.example.allinone.app-main-7\:/menu/menu_edit_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\menu_edit_note.xml
com.example.allinone.app-packageDebugResources-4\:/layout/fragment_investments.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_investments.xml
com.example.allinone.app-main-7\:/color/text_input_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\color\\text_input_text_color.xml
com.example.allinone.app-main-7\:/drawable/ic_chevron_right.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_chevron_right.xml
com.example.allinone.app-main-7\:/drawable/bg_current_day.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_current_day.xml
com.example.allinone.app-main-7\:/drawable/splash_layout_drawable.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\splash_layout_drawable.xml
com.example.allinone.app-main-7\:/drawable/transparent.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\transparent.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_fullscreen_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_fullscreen_image.xml
com.example.allinone.app-main-7\:/drawable/ic_play.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_play.xml
com.example.allinone.app-main-7\:/drawable/ic_code.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_code.xml
com.example.allinone.app-main-7\:/drawable/ic_category_all.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_category_all.xml
com.example.allinone.app-main-7\:/drawable/ic_income.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_income.xml
com.example.allinone.app-main-7\:/drawable/ic_video_placeholder.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_video_placeholder.xml
com.example.allinone.app-packageDebugResources-4\:/layout/dialog_program_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_program_details.xml
com.example.allinone.app-main-7\:/drawable/ic_view_list.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_view_list.xml
com.example.allinone.app-main-7\:/drawable/ic_format_italic.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_format_italic.xml
com.example.allinone.app-main-7\:/drawable/bg_tag_red.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_tag_red.xml
com.example.allinone.app-main-7\:/menu/menu_tasks.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\menu_tasks.xml
com.example.allinone.app-main-7\:/menu/wt_bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\wt_bottom_nav_menu.xml
com.example.allinone.app-main-7\:/drawable/ic_video_error.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_video_error.xml
com.example.allinone.app-main-7\:/font/opensans.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\opensans.xml
com.example.allinone.app-packageDebugResources-4\:/layout/dialog_pin_input.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_pin_input.xml
com.example.allinone.app-main-7\:/drawable/circle_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_background.xml
com.example.allinone.app-main-7\:/color/chip_background_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\color\\chip_background_selector.xml
com.example.allinone.app-main-7\:/drawable/ic_play_circle.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_play_circle.xml
com.example.allinone.app-main-7\:/drawable/ic_category_wing_tzun.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_category_wing_tzun.xml
com.example.allinone.app-packageDebugResources-4\:/layout/dialog_student_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_student_details.xml
com.example.allinone.app-main-7\:/drawable/ic_database.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_database.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_file_structure.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_file_structure.xml
com.example.allinone.app-main-7\:/drawable/uncompleted_exercise_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\uncompleted_exercise_background.xml
com.example.allinone.app-main-7\:/drawable/ic_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_exercise.xml
com.example.allinone.app-main-7\:/drawable/ic_pause.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_pause.xml
com.example.allinone.app-main-7\:/drawable/fully_uncompleted_exercise_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\fully_uncompleted_exercise_background.xml
com.example.allinone.app-main-7\:/drawable/ic_investments.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_investments.xml
com.example.allinone.app-main-7\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.example.allinone.app-main-7\:/drawable/ic_back.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_back.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_voice_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_voice_note.xml
com.example.allinone.app-main-7\:/menu/drawer_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\drawer_menu.xml
com.example.allinone.app-packageDebugResources-4\:/layout/splash_text_layout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\splash_text_layout.xml
com.example.allinone.app-main-7\:/drawable/ic_students.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_students.xml
com.example.allinone.app-main-7\:/menu/menu_task_options.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\menu_task_options.xml
com.example.allinone.app-main-7\:/drawable/ic_calendar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_calendar.xml
com.example.allinone.app-main-7\:/drawable/ic_launcher_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_background.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_backup.xml
com.example.allinone.app-main-7\:/color-night/text_input_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\color-night-v8\\text_input_text_color.xml
com.example.allinone.app-packageDebugResources-4\:/layout/fragment_investments_tab.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_investments_tab.xml
com.example.allinone.app-main-7\:/color-night/text_input_box_stroke.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\color-night-v8\\text_input_box_stroke.xml
com.example.allinone.app-main-7\:/drawable/dialog_rounded_bg.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\dialog_rounded_bg.xml
com.example.allinone.app-packageDebugResources-4\:/layout/dialog_add_event.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_add_event.xml
com.example.allinone.app-main-7\:/drawable/selected_circle_shape.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\selected_circle_shape.xml
com.example.allinone.app-main-7\:/menu/wt_registration_context_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\wt_registration_context_menu.xml
com.example.allinone.app-main-7\:/drawable/bg_tag_blue.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_tag_blue.xml
com.example.allinone.app-main-7\:/drawable/ic_edit.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_edit.xml
com.example.allinone.app-main-7\:/drawable/ic_category_game.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_category_game.xml
com.example.allinone.app-packageDebugResources-4\:/layout/dialog_edit_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_edit_investment.xml
com.example.allinone.app-main-7\:/drawable/ic_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_backup.xml
com.example.allinone.app-main-7\:/drawable/ic_file.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_file.xml
com.example.allinone.app-main-7\:/color-night/dialog_delete_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\color-night-v8\\dialog_delete_button_color.xml
com.example.allinone.app-main-7\:/color/text_input_box_stroke.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\color\\text_input_box_stroke.xml
com.example.allinone.app-main-7\:/drawable/ic_format_underline.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_format_underline.xml
com.example.allinone.app-main-7\:/menu/bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\bottom_nav_menu.xml
com.example.allinone.app-main-7\:/menu/search_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\search_history.xml
com.example.allinone.app-packageDebugResources-4\:/layout/dialog_progress.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_progress.xml
com.example.allinone.app-main-7\:/mipmap-anydpi-v33/ic_launcher.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v33\\ic_launcher.xml
com.example.allinone.app-packageDebugResources-4\:/layout/theme_switch_layout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\theme_switch_layout.xml
com.example.allinone.app-packageDebugResources-4\:/layout/fragment_futures_tab.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_futures_tab.xml
com.example.allinone.app-packageDebugResources-4\:/layout-night-v8/item_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-night-v8\\item_history.xml
com.example.allinone.app-main-7\:/drawable/ic_graduation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_graduation.xml
com.example.allinone.app-main-7\:/drawable-v24/ic_launcher_foreground.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable-v24\\ic_launcher_foreground.xml
com.example.allinone.app-main-7\:/drawable/bg_day_with_event.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_day_with_event.xml
com.example.allinone.app-main-7\:/drawable/rounded_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_background.xml
com.example.allinone.app-packageDebugResources-4\:/layout/offline_status_view.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\offline_status_view.xml
com.example.allinone.app-main-7\:/drawable/ic_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_image.xml
com.example.allinone.app-main-7\:/menu/search_students.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\search_students.xml
com.example.allinone.app-main-7\:/color/wt_bottom_nav_item_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\color\\wt_bottom_nav_item_color.xml
com.example.allinone.app-packageDebugResources-4\:/layout/dialog_loading.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_loading.xml
com.example.allinone.app-main-7\:/drawable/ic_category_general.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_category_general.xml
com.example.allinone.app-main-7\:/drawable/ic_notes.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_notes.xml
com.example.allinone.app-main-7\:/drawable/ic_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_program.xml
com.example.allinone.app-main-7\:/drawable/ic_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_menu.xml
com.example.allinone.app-main-7\:/drawable/ic_instagram_posts.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_instagram_posts.xml
com.example.allinone.app-main-7\:/drawable/ic_format_list_bulleted.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_format_list_bulleted.xml
com.example.allinone.app-main-7\:/drawable/ic_clear_data.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_clear_data.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_lesson.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_lesson.xml
com.example.allinone.app-main-7\:/drawable/ic_category_food.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_category_food.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_wt_event.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_wt_event.xml
com.example.allinone.app-packageDebugResources-4\:/layout/pie_chart_tooltip.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\pie_chart_tooltip.xml
com.example.allinone.app-main-7\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.example.allinone.app-packageDebugResources-4\:/layout-night-v8/fragment_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-night-v8\\fragment_history.xml
com.example.allinone.app-packageDebugResources-4\:/layout-night-v8/activity_edit_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-night-v8\\activity_edit_note.xml
com.example.allinone.app-main-7\:/drawable/ic_chevron_left.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_chevron_left.xml
com.example.allinone.app-packageDebugResources-4\:/layout/dialog_expense_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_expense_investment.xml
com.example.allinone.app-main-7\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher.webp
com.example.allinone.app-main-7\:/drawable/bg_day_with_registration.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_day_with_registration.xml
com.example.allinone.app-main-7\:/drawable/ic_empty_state.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_empty_state.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_category_summary.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_category_summary.xml
com.example.allinone.app-main-7\:/drawable/ic_category_transport.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_category_transport.xml
com.example.allinone.app-main-7\:/color-night/dialog_action_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\color-night-v8\\dialog_action_button_color.xml
com.example.allinone.app-main-7\:/drawable/ic_add_photo.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_add_photo.xml
com.example.allinone.app-main-7\:/drawable/bg_day_with_lesson.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_day_with_lesson.xml
com.example.allinone.app-packageDebugResources-4\:/layout/nav_header.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\nav_header.xml
com.example.allinone.app-main-7\:/menu/search_register.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\search_register.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_investment_selection.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_investment_selection.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_transaction.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_transaction.xml
com.example.allinone.app-packageDebugResources-4\:/layout/dialog_edit_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_edit_program.xml
com.example.allinone.app-main-7\:/drawable/ic_no_students.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_no_students.xml
com.example.allinone.app-main-7\:/drawable/ic_draw.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_draw.xml
com.example.allinone.app-main-7\:/drawable/ic_expand_more.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_expand_more.xml
com.example.allinone.app-packageDebugResources-4\:/layout-night-v8/dialog_student_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-night-v8\\dialog_student_details.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_note_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_note_image.xml
com.example.allinone.app-main-7\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.example.allinone.app-packageDebugResources-4\:/layout/item_wt_registration.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_wt_registration.xml
com.example.allinone.app-packageDebugResources-4\:/layout/fragment_home.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_home.xml
com.example.allinone.app-main-7\:/menu/workout_bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\workout_bottom_nav_menu.xml
com.example.allinone.app-main-7\:/menu/instagram_bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\instagram_bottom_nav_menu.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_investment.xml
com.example.allinone.app-main-7\:/drawable/ic_fitness.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_fitness.xml
com.example.allinone.app-main-7\:/color/bottom_nav_item_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\color\\bottom_nav_item_color.xml
com.example.allinone.app-main-7\:/drawable/bg_day_with_events.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_day_with_events.xml
com.example.allinone.app-main-7\:/drawable/ic_log.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_log.xml
com.example.allinone.app-main-7\:/drawable/default_profile.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\default_profile.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_investment_dropdown.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_investment_dropdown.xml
com.example.allinone.app-main-7\:/drawable/ic_add.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_add.xml
com.example.allinone.app-main-7\:/drawable/ic_wt.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_wt.xml
com.example.allinone.app-main-7\:/color/drawer_item_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\color\\drawer_item_color.xml
com.example.allinone.app-main-7\:/drawable/ic_close.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_close.xml
com.example.allinone.app-main-7\:/drawable/ic_whatsapp.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_whatsapp.xml
com.example.allinone.app-main-7\:/menu/search_notes.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\search_notes.xml
com.example.allinone.app-main-7\:/drawable/ic_stats.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_stats.xml
com.example.allinone.app-main-7\:/drawable/ic_remove.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_remove.xml
com.example.allinone.app-main-7\:/drawable/ic_expand_less.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_expand_less.xml
com.example.allinone.app-main-7\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.example.allinone.app-main-7\:/drawable-night/bg_category_chip.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable-night-v8\\bg_category_chip.xml
com.example.allinone.app-main-7\:/drawable/ic_expense.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_expense.xml
com.example.allinone.app-main-7\:/drawable/rounded_corner_bg.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_corner_bg.xml
com.example.allinone.app-packageDebugResources-4\:/layout/dialog_add_task.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_add_task.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_seminar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_seminar.xml
com.example.allinone.app-main-7\:/drawable/ic_home.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_home.xml
com.example.allinone.app-main-7\:/drawable/ic_format_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_format_text.xml
com.example.allinone.app-main-7\:/drawable/ic_delete.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_delete.xml
com.example.allinone.app-main-7\:/color/dialog_action_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\color\\dialog_action_button_color.xml
com.example.allinone.app-main-7\:/drawable/ic_category_shopping.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_category_shopping.xml
com.example.allinone.app-main-7\:/drawable/ic_tasks.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_tasks.xml
com.example.allinone.app-packageDebugResources-4\:/layout/fragment_transaction_report.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_transaction_report.xml
com.example.allinone.app-packageDebugResources-4\:/layout/dialog_add_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_add_student.xml
com.example.allinone.app-packageDebugResources-4\:/layout/activity_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_backup.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_binance_position.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_binance_position.xml
com.example.allinone.app-main-7\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher.webp
com.example.allinone.app-main-7\:/drawable/ic_category_salary.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_category_salary.xml
com.example.allinone.app-main-7\:/drawable/ic_no_registrations.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_no_registrations.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_note.xml
com.example.allinone.app-packageDebugResources-4\:/layout/fragment_futures.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_futures.xml
com.example.allinone.app-main-7\:/drawable/ic_folder.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_folder.xml
com.example.allinone.app-main-7\:/color/bottom_nav_item_color_light.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\color\\bottom_nav_item_color_light.xml
com.example.allinone.app-main-7\:/drawable/ic_attach_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_attach_image.xml
com.example.allinone.app-packageDebugResources-4\:/layout/dialog_profit_loss.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_profit_loss.xml
com.example.allinone.app-main-7\:/xml/file_paths.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\file_paths.xml
com.example.allinone.app-main-7\:/drawable/error_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\error_image.xml
com.example.allinone.app-main-7\:/drawable/ic_notification.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_notification.xml
com.example.allinone.app-main-7\:/drawable/ic_save.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_save.xml
com.example.allinone.app-main-7\:/drawable/ic_instagram.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_instagram.xml
com.example.allinone.app-packageDebugResources-4\:/layout-night-v8/activity_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-night-v8\\activity_backup.xml
com.example.allinone.app-main-7\:/drawable/bg_tag_green.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_tag_green.xml
com.example.allinone.app-packageDebugResources-4\:/layout/fragment_transactions_overview.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_transactions_overview.xml
com.example.allinone.app-main-7\:/drawable/ic_error.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_error.xml
com.example.allinone.app-main-7\:/xml/data_extraction_rules.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\data_extraction_rules.xml
com.example.allinone.app-main-7\:/drawable/simple_text_splash.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\simple_text_splash.xml
com.example.allinone.app-main-7\:/drawable/ic_search_white.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_search_white.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_transaction_report.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_transaction_report.xml
com.example.allinone.app-main-7\:/drawable/border_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\border_background.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_chat_source.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_chat_source.xml
com.example.allinone.app-main-7\:/color/dialog_delete_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\color\\dialog_delete_button_color.xml
com.example.allinone.app-packageDebugResources-4\:/layout/dialog_post_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_post_details.xml
com.example.allinone.app-main-7\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.example.allinone.app-main-7\:/drawable/ic_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_student.xml
com.example.allinone.app-main-7\:/drawable/circle_shape.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_shape.xml
com.example.allinone.app-main-7\:/drawable/ic_category_sports.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_category_sports.xml
com.example.allinone.app-packageDebugResources-4\:/layout/dropdown_item.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dropdown_item.xml
com.example.allinone.app-main-7\:/drawable/ic_category_bills.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_category_bills.xml
com.example.allinone.app-main-7\:/drawable/ic_format_bold.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_format_bold.xml
com.example.allinone.app-main-7\:/drawable/ic_format_list_numbered.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_format_list_numbered.xml
com.example.allinone.app-packageDebugResources-4\:/layout-night-v8/item_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-night-v8\\item_backup.xml
com.example.allinone.app-main-7\:/drawable/ic_dashboard.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_dashboard.xml
com.example.allinone.app-main-7\:/drawable/ic_lessons.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_lessons.xml
com.example.allinone.app-main-7\:/drawable/ic_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_note.xml
com.example.allinone.app-main-7\:/drawable/ic_stop.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_stop.xml
com.example.allinone.app-main-7\:/drawable/ic_registration.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_registration.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_chat_ai.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_chat_ai.xml
com.example.allinone.app-main-7\:/drawable/ic_cleardata.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_cleardata.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_add_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_add_exercise.xml
com.example.allinone.app-main-7\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.example.allinone.app-main-7\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher.webp
com.example.allinone.app-main-7\:/menu/wt_student_context_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\wt_student_context_menu.xml
com.example.allinone.app-packageDebugResources-4\:/layout/dialog_edit_wt_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_edit_wt_student.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_note_video.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_note_video.xml
com.example.allinone.app-main-7\:/drawable/ic_category_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_category_investment.xml
com.example.allinone.app-main-7\:/drawable/completed_exercise_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\completed_exercise_background.xml
com.example.allinone.app-packageDebugResources-4\:/layout/dialog_edit_seminar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_edit_seminar.xml
com.example.allinone.app-packageDebugResources-4\:/layout/layout_page_header.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\layout_page_header.xml
com.example.allinone.app-main-7\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher.xml
com.example.allinone.app-main-7\:/drawable/circle_background_red.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_background_red.xml
com.example.allinone.app-main-7\:/drawable/ic_transactions.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_transactions.xml
com.example.allinone.app-main-7\:/drawable/ic_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_history.xml
com.example.allinone.app-main-7\:/drawable/bg_category_chip.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_category_chip.xml
com.example.allinone.app-packageDebugResources-4\:/layout/dialog_add_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_add_program.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_category_spending.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_category_spending.xml
com.example.allinone.app-main-7\:/font/opensans_regular.ttf=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\opensans_regular.ttf
com.example.allinone.app-packageDebugResources-4\:/layout/dialog_fullscreen_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_fullscreen_image.xml
com.example.allinone.app-packageDebugResources-4\:/layout/dialog_income_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_income_investment.xml
com.example.allinone.app-main-7\:/drawable/ic_wt_registers.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_wt_registers.xml
com.example.allinone.app-main-7\:/drawable-night/dialog_rounded_bg.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable-night-v8\\dialog_rounded_bg.xml
com.example.allinone.app-main-7\:/drawable/bg_selected_day.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_selected_day.xml
com.example.allinone.app-packageDebugResources-4\:/layout/dialog_edit_lesson.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_edit_lesson.xml
com.example.allinone.app-main-7\:/xml/network_security_config.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\network_security_config.xml
com.example.allinone.app-main-7\:/drawable/ic_checkbox.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_checkbox.xml
com.example.allinone.app-packageDebugResources-4\:/layout/item_chat_user.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_chat_user.xml
com.example.allinone.app-main-7\:/font/opensans_bold.ttf=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\opensans_bold.ttf
com.example.allinone.app-main-7\:/drawable/ic_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_investment.xml
com.example.allinone.app-main-7\:/drawable/ic_share.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_share.xml
com.example.allinone.app-main-7\:/drawable/placeholder_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\placeholder_image.xml
com.example.allinone.app-packageDebugResources-4\:/layout/dialog_futures_tp_sl.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_futures_tp_sl.xml
com.example.allinone.app-main-7\:/drawable/ic_call.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_call.xml
com.example.allinone.app-packageDebugResources-4\:/layout/dialog_workout_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_workout_details.xml
com.example.allinone.app-main-7\:/menu/menu_group_options.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\menu_group_options.xml
