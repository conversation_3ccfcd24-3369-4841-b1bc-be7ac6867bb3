// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemInstagramPostNewBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageView imagePost;

  @NonNull
  public final TextView labelImpressions;

  @NonNull
  public final TextView labelSaves;

  @NonNull
  public final LinearLayout metricsRow1;

  @NonNull
  public final LinearLayout optionalMetrics;

  @NonNull
  public final TextView textCaption;

  @NonNull
  public final TextView textComments;

  @NonNull
  public final TextView textDate;

  @NonNull
  public final TextView textEngagement;

  @NonNull
  public final TextView textHashtags;

  @NonNull
  public final TextView textImpressions;

  @NonNull
  public final TextView textLikes;

  @NonNull
  public final TextView textMediaType;

  @NonNull
  public final TextView textReach;

  @NonNull
  public final TextView textSaves;

  private ItemInstagramPostNewBinding(@NonNull MaterialCardView rootView,
      @NonNull ImageView imagePost, @NonNull TextView labelImpressions,
      @NonNull TextView labelSaves, @NonNull LinearLayout metricsRow1,
      @NonNull LinearLayout optionalMetrics, @NonNull TextView textCaption,
      @NonNull TextView textComments, @NonNull TextView textDate, @NonNull TextView textEngagement,
      @NonNull TextView textHashtags, @NonNull TextView textImpressions,
      @NonNull TextView textLikes, @NonNull TextView textMediaType, @NonNull TextView textReach,
      @NonNull TextView textSaves) {
    this.rootView = rootView;
    this.imagePost = imagePost;
    this.labelImpressions = labelImpressions;
    this.labelSaves = labelSaves;
    this.metricsRow1 = metricsRow1;
    this.optionalMetrics = optionalMetrics;
    this.textCaption = textCaption;
    this.textComments = textComments;
    this.textDate = textDate;
    this.textEngagement = textEngagement;
    this.textHashtags = textHashtags;
    this.textImpressions = textImpressions;
    this.textLikes = textLikes;
    this.textMediaType = textMediaType;
    this.textReach = textReach;
    this.textSaves = textSaves;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemInstagramPostNewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemInstagramPostNewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_instagram_post_new, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemInstagramPostNewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.imagePost;
      ImageView imagePost = ViewBindings.findChildViewById(rootView, id);
      if (imagePost == null) {
        break missingId;
      }

      id = R.id.labelImpressions;
      TextView labelImpressions = ViewBindings.findChildViewById(rootView, id);
      if (labelImpressions == null) {
        break missingId;
      }

      id = R.id.labelSaves;
      TextView labelSaves = ViewBindings.findChildViewById(rootView, id);
      if (labelSaves == null) {
        break missingId;
      }

      id = R.id.metricsRow1;
      LinearLayout metricsRow1 = ViewBindings.findChildViewById(rootView, id);
      if (metricsRow1 == null) {
        break missingId;
      }

      id = R.id.optionalMetrics;
      LinearLayout optionalMetrics = ViewBindings.findChildViewById(rootView, id);
      if (optionalMetrics == null) {
        break missingId;
      }

      id = R.id.textCaption;
      TextView textCaption = ViewBindings.findChildViewById(rootView, id);
      if (textCaption == null) {
        break missingId;
      }

      id = R.id.textComments;
      TextView textComments = ViewBindings.findChildViewById(rootView, id);
      if (textComments == null) {
        break missingId;
      }

      id = R.id.textDate;
      TextView textDate = ViewBindings.findChildViewById(rootView, id);
      if (textDate == null) {
        break missingId;
      }

      id = R.id.textEngagement;
      TextView textEngagement = ViewBindings.findChildViewById(rootView, id);
      if (textEngagement == null) {
        break missingId;
      }

      id = R.id.textHashtags;
      TextView textHashtags = ViewBindings.findChildViewById(rootView, id);
      if (textHashtags == null) {
        break missingId;
      }

      id = R.id.textImpressions;
      TextView textImpressions = ViewBindings.findChildViewById(rootView, id);
      if (textImpressions == null) {
        break missingId;
      }

      id = R.id.textLikes;
      TextView textLikes = ViewBindings.findChildViewById(rootView, id);
      if (textLikes == null) {
        break missingId;
      }

      id = R.id.textMediaType;
      TextView textMediaType = ViewBindings.findChildViewById(rootView, id);
      if (textMediaType == null) {
        break missingId;
      }

      id = R.id.textReach;
      TextView textReach = ViewBindings.findChildViewById(rootView, id);
      if (textReach == null) {
        break missingId;
      }

      id = R.id.textSaves;
      TextView textSaves = ViewBindings.findChildViewById(rootView, id);
      if (textSaves == null) {
        break missingId;
      }

      return new ItemInstagramPostNewBinding((MaterialCardView) rootView, imagePost,
          labelImpressions, labelSaves, metricsRow1, optionalMetrics, textCaption, textComments,
          textDate, textEngagement, textHashtags, textImpressions, textLikes, textMediaType,
          textReach, textSaves);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
