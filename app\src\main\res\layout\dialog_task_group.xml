<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@drawable/dialog_rounded_bg">

    <TextView
        android:id="@+id/dialog_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/create_task_group"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="?attr/colorOnSurface"
        android:layout_marginBottom="16dp"
        android:gravity="center" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/group_title_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:boxBackgroundColor="@android:color/white"
        app:boxStrokeColor="@color/text_input_box_stroke"
        app:hintTextColor="?attr/colorPrimary">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/group_title_edit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/group_title"
            android:inputType="text"
            android:maxLines="1"
            android:textColor="@color/text_input_text_color" />
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/group_description_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:boxBackgroundColor="@android:color/white"
        app:boxStrokeColor="@color/text_input_box_stroke"
        app:hintTextColor="?attr/colorPrimary">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/group_description_edit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/group_description"
            android:inputType="textMultiLine"
            android:maxLines="3"
            android:textColor="@color/text_input_text_color" />
    </com.google.android.material.textfield.TextInputLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/group_color"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="?attr/colorOnSurface"
        android:layout_marginBottom="8dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="24dp"
        android:gravity="center">

        <View
            android:id="@+id/color_blue"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_margin="8dp"
            android:background="@drawable/circle_shape"
            android:backgroundTint="@color/blue_500"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackgroundBorderless" />

        <View
            android:id="@+id/color_green"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_margin="8dp"
            android:background="@drawable/circle_shape"
            android:backgroundTint="@color/green_500"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackgroundBorderless" />

        <View
            android:id="@+id/color_red"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_margin="8dp"
            android:background="@drawable/circle_shape"
            android:backgroundTint="@color/red_500"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackgroundBorderless" />

        <View
            android:id="@+id/color_orange"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_margin="8dp"
            android:background="@drawable/circle_shape"
            android:backgroundTint="@color/orange_500"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackgroundBorderless" />

        <View
            android:id="@+id/color_purple"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_margin="8dp"
            android:background="@drawable/circle_shape"
            android:backgroundTint="@color/purple_500"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackgroundBorderless" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_cancel"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:layout_marginEnd="8dp"
            android:text="@string/cancel"
            android:textColor="?attr/colorOnSurface"
            style="@style/Widget.Material3.Button.TextButton" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_delete"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:layout_marginEnd="8dp"
            android:text="@string/delete"
            android:textColor="@color/red_500"
            android:visibility="gone"
            style="@style/Widget.Material3.Button.TextButton" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_save"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:text="@string/save"
            style="@style/Widget.Material3.Button" />

    </LinearLayout>

</LinearLayout> 