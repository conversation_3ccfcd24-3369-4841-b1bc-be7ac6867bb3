package com.example.allinone.feature.instagram.domain.usecase;

import com.example.allinone.feature.instagram.domain.repository.InstagramRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class CheckInstagramHealthUseCase_Factory implements Factory<CheckInstagramHealthUseCase> {
  private final Provider<InstagramRepository> repositoryProvider;

  public CheckInstagramHealthUseCase_Factory(Provider<InstagramRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public CheckInstagramHealthUseCase get() {
    return newInstance(repositoryProvider.get());
  }

  public static CheckInstagramHealthUseCase_Factory create(
      Provider<InstagramRepository> repositoryProvider) {
    return new CheckInstagramHealthUseCase_Factory(repositoryProvider);
  }

  public static CheckInstagramHealthUseCase newInstance(InstagramRepository repository) {
    return new CheckInstagramHealthUseCase(repository);
  }
}
