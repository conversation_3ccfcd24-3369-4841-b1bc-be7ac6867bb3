package com.example.allinone.feature.notes.data.repository;

import com.example.allinone.feature.notes.data.datasource.NoteLocalDataSource;
import com.example.allinone.feature.notes.data.datasource.NoteRemoteDataSource;
import com.example.allinone.firebase.OfflineQueue;
import com.example.allinone.utils.NetworkUtils;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NoteRepositoryImpl_Factory implements Factory<NoteRepositoryImpl> {
  private final Provider<NoteLocalDataSource> localDataSourceProvider;

  private final Provider<NoteRemoteDataSource> remoteDataSourceProvider;

  private final Provider<NetworkUtils> networkUtilsProvider;

  private final Provider<OfflineQueue> offlineQueueProvider;

  public NoteRepositoryImpl_Factory(Provider<NoteLocalDataSource> localDataSourceProvider,
      Provider<NoteRemoteDataSource> remoteDataSourceProvider,
      Provider<NetworkUtils> networkUtilsProvider, Provider<OfflineQueue> offlineQueueProvider) {
    this.localDataSourceProvider = localDataSourceProvider;
    this.remoteDataSourceProvider = remoteDataSourceProvider;
    this.networkUtilsProvider = networkUtilsProvider;
    this.offlineQueueProvider = offlineQueueProvider;
  }

  @Override
  public NoteRepositoryImpl get() {
    return newInstance(localDataSourceProvider.get(), remoteDataSourceProvider.get(), networkUtilsProvider.get(), offlineQueueProvider.get());
  }

  public static NoteRepositoryImpl_Factory create(
      Provider<NoteLocalDataSource> localDataSourceProvider,
      Provider<NoteRemoteDataSource> remoteDataSourceProvider,
      Provider<NetworkUtils> networkUtilsProvider, Provider<OfflineQueue> offlineQueueProvider) {
    return new NoteRepositoryImpl_Factory(localDataSourceProvider, remoteDataSourceProvider, networkUtilsProvider, offlineQueueProvider);
  }

  public static NoteRepositoryImpl newInstance(NoteLocalDataSource localDataSource,
      NoteRemoteDataSource remoteDataSource, NetworkUtils networkUtils, OfflineQueue offlineQueue) {
    return new NoteRepositoryImpl(localDataSource, remoteDataSource, networkUtils, offlineQueue);
  }
}
