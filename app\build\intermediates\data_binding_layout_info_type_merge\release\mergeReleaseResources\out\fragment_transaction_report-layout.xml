<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_transaction_report" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\fragment_transaction_report.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView"><Targets><Target tag="layout/fragment_transaction_report_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="401" endOffset="39"/></Target><Target id="@+id/filterCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="15" startOffset="8" endLine="82" endOffset="59"/></Target><Target id="@+id/dateRangeLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="40" startOffset="16" endLine="54" endOffset="71"/></Target><Target id="@+id/dateRangeAutoComplete" view="AutoCompleteTextView"><Expressions/><location startLine="48" startOffset="20" endLine="53" endOffset="52"/></Target><Target id="@+id/categoryLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="56" startOffset="16" endLine="70" endOffset="71"/></Target><Target id="@+id/categoryAutoComplete" view="AutoCompleteTextView"><Expressions/><location startLine="64" startOffset="20" endLine="69" endOffset="54"/></Target><Target id="@+id/applyFiltersButton" view="Button"><Expressions/><location startLine="72" startOffset="16" endLine="80" endOffset="44"/></Target><Target id="@+id/summaryCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="85" startOffset="8" endLine="192" endOffset="59"/></Target><Target id="@+id/totalIncomeText" view="TextView"><Expressions/><location startLine="130" startOffset="24" endLine="138" endOffset="59"/></Target><Target id="@+id/totalExpenseText" view="TextView"><Expressions/><location startLine="155" startOffset="24" endLine="163" endOffset="59"/></Target><Target id="@+id/balanceText" view="TextView"><Expressions/><location startLine="180" startOffset="24" endLine="188" endOffset="59"/></Target><Target id="@+id/categorySpendingCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="195" startOffset="8" endLine="233" endOffset="59"/></Target><Target id="@+id/categoryPieChart" view="com.github.mikephil.charting.charts.PieChart"><Expressions/><location startLine="221" startOffset="16" endLine="224" endOffset="50"/></Target><Target id="@+id/topCategoriesRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="226" startOffset="16" endLine="231" endOffset="59"/></Target><Target id="@+id/insightsCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="236" startOffset="8" endLine="363" endOffset="59"/></Target><Target id="@+id/largestExpenseText" view="TextView"><Expressions/><location startLine="281" startOffset="24" endLine="288" endOffset="51"/></Target><Target id="@+id/mostSpentCategoryText" view="TextView"><Expressions/><location startLine="305" startOffset="24" endLine="312" endOffset="56"/></Target><Target id="@+id/averageTransactionText" view="TextView"><Expressions/><location startLine="329" startOffset="24" endLine="336" endOffset="49"/></Target><Target id="@+id/transactionCountText" view="TextView"><Expressions/><location startLine="352" startOffset="24" endLine="359" endOffset="57"/></Target><Target id="@+id/chartCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="366" startOffset="8" endLine="398" endOffset="59"/></Target><Target id="@+id/cashFlowTitle" view="TextView"><Expressions/><location startLine="383" startOffset="16" endLine="391" endOffset="55"/></Target><Target id="@+id/lineChart" view="com.github.mikephil.charting.charts.LineChart"><Expressions/><location startLine="393" startOffset="16" endLine="396" endOffset="50"/></Target></Targets></Layout>