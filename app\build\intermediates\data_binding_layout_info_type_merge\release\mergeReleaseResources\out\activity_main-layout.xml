<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.drawerlayout.widget.DrawerLayout" rootNodeViewId="@+id/drawerLayout"><Targets><Target id="@+id/drawerLayout" tag="layout/activity_main_0" view="androidx.drawerlayout.widget.DrawerLayout"><Expressions/><location startLine="1" startOffset="0" endLine="88" endOffset="43"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="71" startOffset="8" endLine="85" endOffset="22"/></Target><Target id="@+id/themeToggleLayout" tag="binding_1" include="theme_switch_layout"><Expressions/><location startLine="82" startOffset="12" endLine="84" endOffset="54"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="15" startOffset="8" endLine="31" endOffset="57"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="22" startOffset="12" endLine="29" endOffset="79"/></Target><Target id="@+id/nav_host_fragment" view="androidx.fragment.app.FragmentContainerView"><Expressions/><location startLine="33" startOffset="8" endLine="43" endOffset="58"/></Target><Target id="@+id/bottomNavigation" view="com.google.android.material.bottomnavigation.BottomNavigationView"><Expressions/><location startLine="45" startOffset="8" endLine="55" endOffset="46"/></Target><Target id="@+id/nav_view" view="com.google.android.material.navigation.NavigationView"><Expressions/><location startLine="60" startOffset="4" endLine="86" endOffset="59"/></Target></Targets></Layout>