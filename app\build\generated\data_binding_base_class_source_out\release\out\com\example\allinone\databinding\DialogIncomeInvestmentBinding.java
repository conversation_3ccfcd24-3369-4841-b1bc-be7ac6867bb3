// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogIncomeInvestmentBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button cancelButton;

  @NonNull
  public final TextView dialogTitle;

  @NonNull
  public final TextView emptyStateText;

  @NonNull
  public final AutoCompleteTextView investmentDropdown;

  @NonNull
  public final TextInputLayout investmentDropdownLayout;

  @NonNull
  public final Button newInvestmentButton;

  private DialogIncomeInvestmentBinding(@NonNull LinearLayout rootView,
      @NonNull Button cancelButton, @NonNull TextView dialogTitle, @NonNull TextView emptyStateText,
      @NonNull AutoCompleteTextView investmentDropdown,
      @NonNull TextInputLayout investmentDropdownLayout, @NonNull Button newInvestmentButton) {
    this.rootView = rootView;
    this.cancelButton = cancelButton;
    this.dialogTitle = dialogTitle;
    this.emptyStateText = emptyStateText;
    this.investmentDropdown = investmentDropdown;
    this.investmentDropdownLayout = investmentDropdownLayout;
    this.newInvestmentButton = newInvestmentButton;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogIncomeInvestmentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogIncomeInvestmentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_income_investment, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogIncomeInvestmentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cancelButton;
      Button cancelButton = ViewBindings.findChildViewById(rootView, id);
      if (cancelButton == null) {
        break missingId;
      }

      id = R.id.dialogTitle;
      TextView dialogTitle = ViewBindings.findChildViewById(rootView, id);
      if (dialogTitle == null) {
        break missingId;
      }

      id = R.id.emptyStateText;
      TextView emptyStateText = ViewBindings.findChildViewById(rootView, id);
      if (emptyStateText == null) {
        break missingId;
      }

      id = R.id.investmentDropdown;
      AutoCompleteTextView investmentDropdown = ViewBindings.findChildViewById(rootView, id);
      if (investmentDropdown == null) {
        break missingId;
      }

      id = R.id.investmentDropdownLayout;
      TextInputLayout investmentDropdownLayout = ViewBindings.findChildViewById(rootView, id);
      if (investmentDropdownLayout == null) {
        break missingId;
      }

      id = R.id.newInvestmentButton;
      Button newInvestmentButton = ViewBindings.findChildViewById(rootView, id);
      if (newInvestmentButton == null) {
        break missingId;
      }

      return new DialogIncomeInvestmentBinding((LinearLayout) rootView, cancelButton, dialogTitle,
          emptyStateText, investmentDropdown, investmentDropdownLayout, newInvestmentButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
