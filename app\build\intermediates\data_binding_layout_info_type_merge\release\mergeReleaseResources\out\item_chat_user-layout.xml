<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_chat_user" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_chat_user.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/item_chat_user_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="47" endOffset="51"/></Target><Target id="@+id/cardUserMessage" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="8" startOffset="4" endLine="45" endOffset="55"/></Target><Target id="@+id/textUserMessage" view="TextView"><Expressions/><location startLine="26" startOffset="12" endLine="32" endOffset="75"/></Target><Target id="@+id/textUserTimestamp" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="43" endOffset="38"/></Target></Targets></Layout>