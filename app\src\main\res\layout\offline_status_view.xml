<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/offline_status_card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:visibility="gone"
    app:cardBackgroundColor="#FFF3E0"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp">

        <ImageView
            android:id="@+id/offline_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center_vertical"
            android:contentDescription="@string/offline_status"
            android:src="@android:drawable/ic_dialog_info"
            app:tint="#FF9800" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/offline_status_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/offline_mode"
                android:textColor="#FF6F00"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/offline_status_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="@string/offline_message"
                android:textColor="#FF6F00"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/pending_operations_count"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="@string/pending_operations"
                android:textColor="#FF6F00"
                android:textSize="14sp"
                android:textStyle="italic" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView> 