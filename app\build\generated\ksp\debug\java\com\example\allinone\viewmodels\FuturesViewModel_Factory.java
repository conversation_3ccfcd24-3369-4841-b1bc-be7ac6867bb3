package com.example.allinone.viewmodels;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class FuturesViewModel_Factory implements Factory<FuturesViewModel> {
  @Override
  public FuturesViewModel get() {
    return newInstance();
  }

  public static FuturesViewModel_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static FuturesViewModel newInstance() {
    return new FuturesViewModel();
  }

  private static final class InstanceHolder {
    private static final FuturesViewModel_Factory INSTANCE = new FuturesViewModel_Factory();
  }
}
