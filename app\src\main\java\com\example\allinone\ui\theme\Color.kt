package com.example.allinone.ui.theme

import androidx.compose.ui.graphics.Color

// Primary colors
val PrimaryLight = Color(0xFF000000)
val OnPrimaryLight = Color(0xFFFFFFFF)
val PrimaryContainerLight = Color(0xFF424242)
val OnPrimaryContainerLight = Color(0xFFFFFFFF)

val PrimaryDark = Color(0xFFFFFFFF)
val OnPrimaryDark = Color(0xFF000000)
val PrimaryContainerDark = Color(0xFF616161)
val OnPrimaryContainerDark = Color(0xFF000000)

// Secondary colors
val SecondaryLight = Color(0xFF424242)
val OnSecondaryLight = Color(0xFFFFFFFF)
val SecondaryContainerLight = Color(0xFFFFFFFF)
val OnSecondaryContainerLight = Color(0xFF000000)

val SecondaryDark = Color(0xFFBDBDBD)
val OnSecondaryDark = Color(0xFF000000)
val SecondaryContainerDark = Color(0xFF424242)
val OnSecondaryContainerDark = Color(0xFFFFFFFF)

// Tertiary colors
val TertiaryLight = Color(0xFF616161)
val OnTertiaryLight = Color(0xFFFFFFFF)
val TertiaryContainerLight = Color(0xFFFFFFFF)
val OnTertiaryContainerLight = Color(0xFF000000)

val TertiaryDark = Color(0xFF9E9E9E)
val OnTertiaryDark = Color(0xFF000000)
val TertiaryContainerDark = Color(0xFF616161)
val OnTertiaryContainerDark = Color(0xFFFFFFFF)

// Error colors
val ErrorLight = Color(0xFFB71C1C)
val OnErrorLight = Color(0xFFFFFFFF)
val ErrorContainerLight = Color(0xFFFFEBEE)
val OnErrorContainerLight = Color(0xFFB71C1C)

val ErrorDark = Color(0xFFEF5350)
val OnErrorDark = Color(0xFF000000)
val ErrorContainerDark = Color(0xFF8E0000)
val OnErrorContainerDark = Color(0xFFFFFFFF)

// Background colors
val BackgroundLight = Color(0xFFFFFFFF)
val OnBackgroundLight = Color(0xFF000000)
val SurfaceLight = Color(0xFFFFFFFF)
val OnSurfaceLight = Color(0xFF000000)
val SurfaceVariantLight = Color(0xFFFFFFFF)
val OnSurfaceVariantLight = Color(0xFF424242)

val BackgroundDark = Color(0xFF121212)
val OnBackgroundDark = Color(0xFFFFFFFF)
val SurfaceDark = Color(0xFF1E1E1E)
val OnSurfaceDark = Color(0xFFFFFFFF)
val SurfaceVariantDark = Color(0xFF424242)
val OnSurfaceVariantDark = Color(0xFFBDBDBD)

// Outline colors
val OutlineLight = Color(0xFF757575)
val OutlineVariantLight = Color(0xFFBDBDBD)

val OutlineDark = Color(0xFF9E9E9E)
val OutlineVariantDark = Color(0xFF616161)

// Surface tint
val SurfaceTintLight = PrimaryLight
val SurfaceTintDark = PrimaryDark

// Investment/Transaction specific colors
val IncomeGreen = Color(0xFF2E7D32)
val ExpenseRed = Color(0xFFB71C1C)
val InvestmentBlue = Color(0xFF1976D2)
val CryptoOrange = Color(0xFFFF9800)
val StockPurple = Color(0xFF7B1FA2)
val FuturesYellow = Color(0xFFF57F17) 