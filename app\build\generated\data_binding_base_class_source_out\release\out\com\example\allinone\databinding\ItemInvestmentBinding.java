// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemInvestmentBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final LinearLayout imageContainer;

  @NonNull
  public final TextView investmentAmount;

  @NonNull
  public final TextView investmentCurrentValue;

  @NonNull
  public final TextView investmentDate;

  @NonNull
  public final TextView investmentDescription;

  @NonNull
  public final TextView investmentName;

  @NonNull
  public final TextView investmentProfitLoss;

  @NonNull
  public final TextView investmentType;

  private ItemInvestmentBinding(@NonNull MaterialCardView rootView,
      @NonNull LinearLayout imageContainer, @NonNull TextView investmentAmount,
      @NonNull TextView investmentCurrentValue, @NonNull TextView investmentDate,
      @NonNull TextView investmentDescription, @NonNull TextView investmentName,
      @NonNull TextView investmentProfitLoss, @NonNull TextView investmentType) {
    this.rootView = rootView;
    this.imageContainer = imageContainer;
    this.investmentAmount = investmentAmount;
    this.investmentCurrentValue = investmentCurrentValue;
    this.investmentDate = investmentDate;
    this.investmentDescription = investmentDescription;
    this.investmentName = investmentName;
    this.investmentProfitLoss = investmentProfitLoss;
    this.investmentType = investmentType;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemInvestmentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemInvestmentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_investment, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemInvestmentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.imageContainer;
      LinearLayout imageContainer = ViewBindings.findChildViewById(rootView, id);
      if (imageContainer == null) {
        break missingId;
      }

      id = R.id.investmentAmount;
      TextView investmentAmount = ViewBindings.findChildViewById(rootView, id);
      if (investmentAmount == null) {
        break missingId;
      }

      id = R.id.investmentCurrentValue;
      TextView investmentCurrentValue = ViewBindings.findChildViewById(rootView, id);
      if (investmentCurrentValue == null) {
        break missingId;
      }

      id = R.id.investmentDate;
      TextView investmentDate = ViewBindings.findChildViewById(rootView, id);
      if (investmentDate == null) {
        break missingId;
      }

      id = R.id.investmentDescription;
      TextView investmentDescription = ViewBindings.findChildViewById(rootView, id);
      if (investmentDescription == null) {
        break missingId;
      }

      id = R.id.investmentName;
      TextView investmentName = ViewBindings.findChildViewById(rootView, id);
      if (investmentName == null) {
        break missingId;
      }

      id = R.id.investmentProfitLoss;
      TextView investmentProfitLoss = ViewBindings.findChildViewById(rootView, id);
      if (investmentProfitLoss == null) {
        break missingId;
      }

      id = R.id.investmentType;
      TextView investmentType = ViewBindings.findChildViewById(rootView, id);
      if (investmentType == null) {
        break missingId;
      }

      return new ItemInvestmentBinding((MaterialCardView) rootView, imageContainer,
          investmentAmount, investmentCurrentValue, investmentDate, investmentDescription,
          investmentName, investmentProfitLoss, investmentType);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
