// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentInstagramBusinessBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final BottomNavigationView instagramBottomNavigation;

  @NonNull
  public final FrameLayout instagramFragmentContainer;

  private FragmentInstagramBusinessBinding(@NonNull ConstraintLayout rootView,
      @NonNull BottomNavigationView instagramBottomNavigation,
      @NonNull FrameLayout instagramFragmentContainer) {
    this.rootView = rootView;
    this.instagramBottomNavigation = instagramBottomNavigation;
    this.instagramFragmentContainer = instagramFragmentContainer;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentInstagramBusinessBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentInstagramBusinessBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_instagram_business, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentInstagramBusinessBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.instagram_bottom_navigation;
      BottomNavigationView instagramBottomNavigation = ViewBindings.findChildViewById(rootView, id);
      if (instagramBottomNavigation == null) {
        break missingId;
      }

      id = R.id.instagram_fragment_container;
      FrameLayout instagramFragmentContainer = ViewBindings.findChildViewById(rootView, id);
      if (instagramFragmentContainer == null) {
        break missingId;
      }

      return new FragmentInstagramBusinessBinding((ConstraintLayout) rootView,
          instagramBottomNavigation, instagramFragmentContainer);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
