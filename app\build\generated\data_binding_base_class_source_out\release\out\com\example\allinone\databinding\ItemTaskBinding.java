// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.checkbox.MaterialCheckBox;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTaskBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final MaterialCheckBox taskCheckbox;

  @NonNull
  public final TextView taskDescription;

  private ItemTaskBinding(@NonNull MaterialCardView rootView,
      @NonNull MaterialCheckBox taskCheckbox, @NonNull TextView taskDescription) {
    this.rootView = rootView;
    this.taskCheckbox = taskCheckbox;
    this.taskDescription = taskDescription;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTaskBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTaskBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_task, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTaskBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.taskCheckbox;
      MaterialCheckBox taskCheckbox = ViewBindings.findChildViewById(rootView, id);
      if (taskCheckbox == null) {
        break missingId;
      }

      id = R.id.taskDescription;
      TextView taskDescription = ViewBindings.findChildViewById(rootView, id);
      if (taskDescription == null) {
        break missingId;
      }

      return new ItemTaskBinding((MaterialCardView) rootView, taskCheckbox, taskDescription);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
