rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read/write access based on device ID
    match /transactions/{document=**} {
      allow read, write: if request.resource.data.deviceId == resource.data.deviceId || 
                           request.resource.data.deviceId != null;
    }
    
    match /investments/{document=**} {
      allow read, write: if request.resource.data.deviceId == resource.data.deviceId || 
                           request.resource.data.deviceId != null;
    }
    
    match /notes/{document=**} {
      allow read, write: if request.resource.data.deviceId == resource.data.deviceId || 
                           request.resource.data.deviceId != null;
    }
    
    match /students/{document=**} {
      allow read, write: if request.resource.data.deviceId == resource.data.deviceId || 
                           request.resource.data.deviceId != null;
    }
    
    match /events/{document=**} {
      allow read, write: if request.resource.data.deviceId == resource.data.deviceId || 
                           request.resource.data.deviceId != null;
    }
    
    match /wtLessons/{document=**} {
      allow read, write: if request.resource.data.deviceId == resource.data.deviceId || 
                           request.resource.data.deviceId != null;
    }
    
    // Rules for workout data
    match /workouts/{document=**} {
      allow read, write: if request.resource.data.deviceId != null;
    }
    
    match /programs/{document=**} {
      allow read, write: if request.resource.data.deviceId != null;
    }
    
    // Allow access to id_counters for ID generation
    match /counters/{document=**} {
      allow read, write: if true;
    }
    
    // Testing collection with open access
    match /test_connection/{document=**} {
      allow read, write: if true;
    }
    
    // Test collection for connectivity checks
    match /test/{document=**} {
      allow read, write: if true;
    }
  }
} 