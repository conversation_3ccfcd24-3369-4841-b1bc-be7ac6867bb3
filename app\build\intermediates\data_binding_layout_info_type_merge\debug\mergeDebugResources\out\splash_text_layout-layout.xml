<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="splash_text_layout" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\splash_text_layout.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/splash_text_layout_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="21" endOffset="51"/></Target><Target id="@+id/appNameText" view="TextView"><Expressions/><location startLine="7" startOffset="4" endLine="19" endOffset="51"/></Target></Targets></Layout>