package com.example.allinone.di;

import com.example.allinone.feature.workout.data.datasource.WorkoutLocalDataSource;
import com.example.allinone.feature.workout.data.datasource.WorkoutRemoteDataSource;
import com.example.allinone.feature.workout.domain.repository.WorkoutRepository;
import com.example.allinone.firebase.OfflineQueue;
import com.example.allinone.utils.NetworkUtils;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class AppModule_ProvideWorkoutRepositoryFactory implements Factory<WorkoutRepository> {
  private final Provider<WorkoutLocalDataSource> localDataSourceProvider;

  private final Provider<WorkoutRemoteDataSource> remoteDataSourceProvider;

  private final Provider<NetworkUtils> networkUtilsProvider;

  private final Provider<OfflineQueue> offlineQueueProvider;

  public AppModule_ProvideWorkoutRepositoryFactory(
      Provider<WorkoutLocalDataSource> localDataSourceProvider,
      Provider<WorkoutRemoteDataSource> remoteDataSourceProvider,
      Provider<NetworkUtils> networkUtilsProvider, Provider<OfflineQueue> offlineQueueProvider) {
    this.localDataSourceProvider = localDataSourceProvider;
    this.remoteDataSourceProvider = remoteDataSourceProvider;
    this.networkUtilsProvider = networkUtilsProvider;
    this.offlineQueueProvider = offlineQueueProvider;
  }

  @Override
  public WorkoutRepository get() {
    return provideWorkoutRepository(localDataSourceProvider.get(), remoteDataSourceProvider.get(), networkUtilsProvider.get(), offlineQueueProvider.get());
  }

  public static AppModule_ProvideWorkoutRepositoryFactory create(
      Provider<WorkoutLocalDataSource> localDataSourceProvider,
      Provider<WorkoutRemoteDataSource> remoteDataSourceProvider,
      Provider<NetworkUtils> networkUtilsProvider, Provider<OfflineQueue> offlineQueueProvider) {
    return new AppModule_ProvideWorkoutRepositoryFactory(localDataSourceProvider, remoteDataSourceProvider, networkUtilsProvider, offlineQueueProvider);
  }

  public static WorkoutRepository provideWorkoutRepository(WorkoutLocalDataSource localDataSource,
      WorkoutRemoteDataSource remoteDataSource, NetworkUtils networkUtils,
      OfflineQueue offlineQueue) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideWorkoutRepository(localDataSource, remoteDataSource, networkUtils, offlineQueue));
  }
}
