package com.example.allinone.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.allinone.data.local.entities.CachedWorkoutEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class CachedWorkoutDao_Impl implements CachedWorkoutDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<CachedWorkoutEntity> __insertionAdapterOfCachedWorkoutEntity;

  private final EntityDeletionOrUpdateAdapter<CachedWorkoutEntity> __deletionAdapterOfCachedWorkoutEntity;

  private final EntityDeletionOrUpdateAdapter<CachedWorkoutEntity> __updateAdapterOfCachedWorkoutEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteWorkoutById;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllWorkouts;

  private final SharedSQLiteStatement __preparedStmtOfDeleteExpiredWorkouts;

  public CachedWorkoutDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfCachedWorkoutEntity = new EntityInsertionAdapter<CachedWorkoutEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `cached_workouts` (`id`,`programId`,`programName`,`startTime`,`endTime`,`duration`,`exercisesJson`,`notes`,`cachedAt`) VALUES (?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CachedWorkoutEntity entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getProgramId() == null) {
          statement.bindNull(2);
        } else {
          statement.bindLong(2, entity.getProgramId());
        }
        if (entity.getProgramName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getProgramName());
        }
        statement.bindLong(4, entity.getStartTime());
        if (entity.getEndTime() == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, entity.getEndTime());
        }
        statement.bindLong(6, entity.getDuration());
        statement.bindString(7, entity.getExercisesJson());
        if (entity.getNotes() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getNotes());
        }
        statement.bindLong(9, entity.getCachedAt());
      }
    };
    this.__deletionAdapterOfCachedWorkoutEntity = new EntityDeletionOrUpdateAdapter<CachedWorkoutEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `cached_workouts` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CachedWorkoutEntity entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfCachedWorkoutEntity = new EntityDeletionOrUpdateAdapter<CachedWorkoutEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `cached_workouts` SET `id` = ?,`programId` = ?,`programName` = ?,`startTime` = ?,`endTime` = ?,`duration` = ?,`exercisesJson` = ?,`notes` = ?,`cachedAt` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CachedWorkoutEntity entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getProgramId() == null) {
          statement.bindNull(2);
        } else {
          statement.bindLong(2, entity.getProgramId());
        }
        if (entity.getProgramName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getProgramName());
        }
        statement.bindLong(4, entity.getStartTime());
        if (entity.getEndTime() == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, entity.getEndTime());
        }
        statement.bindLong(6, entity.getDuration());
        statement.bindString(7, entity.getExercisesJson());
        if (entity.getNotes() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getNotes());
        }
        statement.bindLong(9, entity.getCachedAt());
        statement.bindLong(10, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteWorkoutById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM cached_workouts WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllWorkouts = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM cached_workouts";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteExpiredWorkouts = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM cached_workouts WHERE cachedAt < ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertWorkout(final CachedWorkoutEntity workout,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfCachedWorkoutEntity.insert(workout);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertAllWorkouts(final List<CachedWorkoutEntity> workouts,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfCachedWorkoutEntity.insert(workouts);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteWorkout(final CachedWorkoutEntity workout,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfCachedWorkoutEntity.handle(workout);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateWorkout(final CachedWorkoutEntity workout,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfCachedWorkoutEntity.handle(workout);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteWorkoutById(final long id, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteWorkoutById.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteWorkoutById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllWorkouts(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllWorkouts.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllWorkouts.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteExpiredWorkouts(final long expiredTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteExpiredWorkouts.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, expiredTime);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteExpiredWorkouts.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<CachedWorkoutEntity>> getAllWorkouts() {
    final String _sql = "SELECT `cached_workouts`.`id` AS `id`, `cached_workouts`.`programId` AS `programId`, `cached_workouts`.`programName` AS `programName`, `cached_workouts`.`startTime` AS `startTime`, `cached_workouts`.`endTime` AS `endTime`, `cached_workouts`.`duration` AS `duration`, `cached_workouts`.`exercisesJson` AS `exercisesJson`, `cached_workouts`.`notes` AS `notes`, `cached_workouts`.`cachedAt` AS `cachedAt` FROM cached_workouts ORDER BY startTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cached_workouts"}, new Callable<List<CachedWorkoutEntity>>() {
      @Override
      @NonNull
      public List<CachedWorkoutEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfProgramId = 1;
          final int _cursorIndexOfProgramName = 2;
          final int _cursorIndexOfStartTime = 3;
          final int _cursorIndexOfEndTime = 4;
          final int _cursorIndexOfDuration = 5;
          final int _cursorIndexOfExercisesJson = 6;
          final int _cursorIndexOfNotes = 7;
          final int _cursorIndexOfCachedAt = 8;
          final List<CachedWorkoutEntity> _result = new ArrayList<CachedWorkoutEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CachedWorkoutEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final Long _tmpProgramId;
            if (_cursor.isNull(_cursorIndexOfProgramId)) {
              _tmpProgramId = null;
            } else {
              _tmpProgramId = _cursor.getLong(_cursorIndexOfProgramId);
            }
            final String _tmpProgramName;
            if (_cursor.isNull(_cursorIndexOfProgramName)) {
              _tmpProgramName = null;
            } else {
              _tmpProgramName = _cursor.getString(_cursorIndexOfProgramName);
            }
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final Long _tmpEndTime;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmpEndTime = null;
            } else {
              _tmpEndTime = _cursor.getLong(_cursorIndexOfEndTime);
            }
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpExercisesJson;
            _tmpExercisesJson = _cursor.getString(_cursorIndexOfExercisesJson);
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCachedAt;
            _tmpCachedAt = _cursor.getLong(_cursorIndexOfCachedAt);
            _item = new CachedWorkoutEntity(_tmpId,_tmpProgramId,_tmpProgramName,_tmpStartTime,_tmpEndTime,_tmpDuration,_tmpExercisesJson,_tmpNotes,_tmpCachedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getWorkoutById(final long id,
      final Continuation<? super CachedWorkoutEntity> $completion) {
    final String _sql = "SELECT * FROM cached_workouts WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<CachedWorkoutEntity>() {
      @Override
      @Nullable
      public CachedWorkoutEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfProgramId = CursorUtil.getColumnIndexOrThrow(_cursor, "programId");
          final int _cursorIndexOfProgramName = CursorUtil.getColumnIndexOrThrow(_cursor, "programName");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfExercisesJson = CursorUtil.getColumnIndexOrThrow(_cursor, "exercisesJson");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCachedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "cachedAt");
          final CachedWorkoutEntity _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final Long _tmpProgramId;
            if (_cursor.isNull(_cursorIndexOfProgramId)) {
              _tmpProgramId = null;
            } else {
              _tmpProgramId = _cursor.getLong(_cursorIndexOfProgramId);
            }
            final String _tmpProgramName;
            if (_cursor.isNull(_cursorIndexOfProgramName)) {
              _tmpProgramName = null;
            } else {
              _tmpProgramName = _cursor.getString(_cursorIndexOfProgramName);
            }
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final Long _tmpEndTime;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmpEndTime = null;
            } else {
              _tmpEndTime = _cursor.getLong(_cursorIndexOfEndTime);
            }
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpExercisesJson;
            _tmpExercisesJson = _cursor.getString(_cursorIndexOfExercisesJson);
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCachedAt;
            _tmpCachedAt = _cursor.getLong(_cursorIndexOfCachedAt);
            _result = new CachedWorkoutEntity(_tmpId,_tmpProgramId,_tmpProgramName,_tmpStartTime,_tmpEndTime,_tmpDuration,_tmpExercisesJson,_tmpNotes,_tmpCachedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<CachedWorkoutEntity>> getWorkoutsByProgram(final long programId) {
    final String _sql = "SELECT * FROM cached_workouts WHERE programId = ? ORDER BY startTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, programId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cached_workouts"}, new Callable<List<CachedWorkoutEntity>>() {
      @Override
      @NonNull
      public List<CachedWorkoutEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfProgramId = CursorUtil.getColumnIndexOrThrow(_cursor, "programId");
          final int _cursorIndexOfProgramName = CursorUtil.getColumnIndexOrThrow(_cursor, "programName");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfExercisesJson = CursorUtil.getColumnIndexOrThrow(_cursor, "exercisesJson");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCachedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "cachedAt");
          final List<CachedWorkoutEntity> _result = new ArrayList<CachedWorkoutEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CachedWorkoutEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final Long _tmpProgramId;
            if (_cursor.isNull(_cursorIndexOfProgramId)) {
              _tmpProgramId = null;
            } else {
              _tmpProgramId = _cursor.getLong(_cursorIndexOfProgramId);
            }
            final String _tmpProgramName;
            if (_cursor.isNull(_cursorIndexOfProgramName)) {
              _tmpProgramName = null;
            } else {
              _tmpProgramName = _cursor.getString(_cursorIndexOfProgramName);
            }
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final Long _tmpEndTime;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmpEndTime = null;
            } else {
              _tmpEndTime = _cursor.getLong(_cursorIndexOfEndTime);
            }
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpExercisesJson;
            _tmpExercisesJson = _cursor.getString(_cursorIndexOfExercisesJson);
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCachedAt;
            _tmpCachedAt = _cursor.getLong(_cursorIndexOfCachedAt);
            _item = new CachedWorkoutEntity(_tmpId,_tmpProgramId,_tmpProgramName,_tmpStartTime,_tmpEndTime,_tmpDuration,_tmpExercisesJson,_tmpNotes,_tmpCachedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CachedWorkoutEntity>> searchWorkouts(final String searchQuery) {
    final String _sql = "SELECT * FROM cached_workouts WHERE programName LIKE '%' || ? || '%' OR notes LIKE '%' || ? || '%' ORDER BY startTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, searchQuery);
    _argIndex = 2;
    _statement.bindString(_argIndex, searchQuery);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cached_workouts"}, new Callable<List<CachedWorkoutEntity>>() {
      @Override
      @NonNull
      public List<CachedWorkoutEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfProgramId = CursorUtil.getColumnIndexOrThrow(_cursor, "programId");
          final int _cursorIndexOfProgramName = CursorUtil.getColumnIndexOrThrow(_cursor, "programName");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfExercisesJson = CursorUtil.getColumnIndexOrThrow(_cursor, "exercisesJson");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCachedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "cachedAt");
          final List<CachedWorkoutEntity> _result = new ArrayList<CachedWorkoutEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CachedWorkoutEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final Long _tmpProgramId;
            if (_cursor.isNull(_cursorIndexOfProgramId)) {
              _tmpProgramId = null;
            } else {
              _tmpProgramId = _cursor.getLong(_cursorIndexOfProgramId);
            }
            final String _tmpProgramName;
            if (_cursor.isNull(_cursorIndexOfProgramName)) {
              _tmpProgramName = null;
            } else {
              _tmpProgramName = _cursor.getString(_cursorIndexOfProgramName);
            }
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final Long _tmpEndTime;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmpEndTime = null;
            } else {
              _tmpEndTime = _cursor.getLong(_cursorIndexOfEndTime);
            }
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpExercisesJson;
            _tmpExercisesJson = _cursor.getString(_cursorIndexOfExercisesJson);
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCachedAt;
            _tmpCachedAt = _cursor.getLong(_cursorIndexOfCachedAt);
            _item = new CachedWorkoutEntity(_tmpId,_tmpProgramId,_tmpProgramName,_tmpStartTime,_tmpEndTime,_tmpDuration,_tmpExercisesJson,_tmpNotes,_tmpCachedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CachedWorkoutEntity>> getWorkoutsByDateRange(final long startTime,
      final long endTime) {
    final String _sql = "SELECT * FROM cached_workouts WHERE startTime >= ? AND startTime <= ? ORDER BY startTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startTime);
    _argIndex = 2;
    _statement.bindLong(_argIndex, endTime);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cached_workouts"}, new Callable<List<CachedWorkoutEntity>>() {
      @Override
      @NonNull
      public List<CachedWorkoutEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfProgramId = CursorUtil.getColumnIndexOrThrow(_cursor, "programId");
          final int _cursorIndexOfProgramName = CursorUtil.getColumnIndexOrThrow(_cursor, "programName");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfExercisesJson = CursorUtil.getColumnIndexOrThrow(_cursor, "exercisesJson");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCachedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "cachedAt");
          final List<CachedWorkoutEntity> _result = new ArrayList<CachedWorkoutEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CachedWorkoutEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final Long _tmpProgramId;
            if (_cursor.isNull(_cursorIndexOfProgramId)) {
              _tmpProgramId = null;
            } else {
              _tmpProgramId = _cursor.getLong(_cursorIndexOfProgramId);
            }
            final String _tmpProgramName;
            if (_cursor.isNull(_cursorIndexOfProgramName)) {
              _tmpProgramName = null;
            } else {
              _tmpProgramName = _cursor.getString(_cursorIndexOfProgramName);
            }
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final Long _tmpEndTime;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmpEndTime = null;
            } else {
              _tmpEndTime = _cursor.getLong(_cursorIndexOfEndTime);
            }
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpExercisesJson;
            _tmpExercisesJson = _cursor.getString(_cursorIndexOfExercisesJson);
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCachedAt;
            _tmpCachedAt = _cursor.getLong(_cursorIndexOfCachedAt);
            _item = new CachedWorkoutEntity(_tmpId,_tmpProgramId,_tmpProgramName,_tmpStartTime,_tmpEndTime,_tmpDuration,_tmpExercisesJson,_tmpNotes,_tmpCachedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CachedWorkoutEntity>> getCompletedWorkouts() {
    final String _sql = "SELECT `cached_workouts`.`id` AS `id`, `cached_workouts`.`programId` AS `programId`, `cached_workouts`.`programName` AS `programName`, `cached_workouts`.`startTime` AS `startTime`, `cached_workouts`.`endTime` AS `endTime`, `cached_workouts`.`duration` AS `duration`, `cached_workouts`.`exercisesJson` AS `exercisesJson`, `cached_workouts`.`notes` AS `notes`, `cached_workouts`.`cachedAt` AS `cachedAt` FROM cached_workouts WHERE endTime IS NOT NULL ORDER BY startTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cached_workouts"}, new Callable<List<CachedWorkoutEntity>>() {
      @Override
      @NonNull
      public List<CachedWorkoutEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfProgramId = 1;
          final int _cursorIndexOfProgramName = 2;
          final int _cursorIndexOfStartTime = 3;
          final int _cursorIndexOfEndTime = 4;
          final int _cursorIndexOfDuration = 5;
          final int _cursorIndexOfExercisesJson = 6;
          final int _cursorIndexOfNotes = 7;
          final int _cursorIndexOfCachedAt = 8;
          final List<CachedWorkoutEntity> _result = new ArrayList<CachedWorkoutEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CachedWorkoutEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final Long _tmpProgramId;
            if (_cursor.isNull(_cursorIndexOfProgramId)) {
              _tmpProgramId = null;
            } else {
              _tmpProgramId = _cursor.getLong(_cursorIndexOfProgramId);
            }
            final String _tmpProgramName;
            if (_cursor.isNull(_cursorIndexOfProgramName)) {
              _tmpProgramName = null;
            } else {
              _tmpProgramName = _cursor.getString(_cursorIndexOfProgramName);
            }
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final Long _tmpEndTime;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmpEndTime = null;
            } else {
              _tmpEndTime = _cursor.getLong(_cursorIndexOfEndTime);
            }
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpExercisesJson;
            _tmpExercisesJson = _cursor.getString(_cursorIndexOfExercisesJson);
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCachedAt;
            _tmpCachedAt = _cursor.getLong(_cursorIndexOfCachedAt);
            _item = new CachedWorkoutEntity(_tmpId,_tmpProgramId,_tmpProgramName,_tmpStartTime,_tmpEndTime,_tmpDuration,_tmpExercisesJson,_tmpNotes,_tmpCachedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CachedWorkoutEntity>> getIncompleteWorkouts() {
    final String _sql = "SELECT `cached_workouts`.`id` AS `id`, `cached_workouts`.`programId` AS `programId`, `cached_workouts`.`programName` AS `programName`, `cached_workouts`.`startTime` AS `startTime`, `cached_workouts`.`endTime` AS `endTime`, `cached_workouts`.`duration` AS `duration`, `cached_workouts`.`exercisesJson` AS `exercisesJson`, `cached_workouts`.`notes` AS `notes`, `cached_workouts`.`cachedAt` AS `cachedAt` FROM cached_workouts WHERE endTime IS NULL ORDER BY startTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cached_workouts"}, new Callable<List<CachedWorkoutEntity>>() {
      @Override
      @NonNull
      public List<CachedWorkoutEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfProgramId = 1;
          final int _cursorIndexOfProgramName = 2;
          final int _cursorIndexOfStartTime = 3;
          final int _cursorIndexOfEndTime = 4;
          final int _cursorIndexOfDuration = 5;
          final int _cursorIndexOfExercisesJson = 6;
          final int _cursorIndexOfNotes = 7;
          final int _cursorIndexOfCachedAt = 8;
          final List<CachedWorkoutEntity> _result = new ArrayList<CachedWorkoutEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CachedWorkoutEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final Long _tmpProgramId;
            if (_cursor.isNull(_cursorIndexOfProgramId)) {
              _tmpProgramId = null;
            } else {
              _tmpProgramId = _cursor.getLong(_cursorIndexOfProgramId);
            }
            final String _tmpProgramName;
            if (_cursor.isNull(_cursorIndexOfProgramName)) {
              _tmpProgramName = null;
            } else {
              _tmpProgramName = _cursor.getString(_cursorIndexOfProgramName);
            }
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final Long _tmpEndTime;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmpEndTime = null;
            } else {
              _tmpEndTime = _cursor.getLong(_cursorIndexOfEndTime);
            }
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpExercisesJson;
            _tmpExercisesJson = _cursor.getString(_cursorIndexOfExercisesJson);
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCachedAt;
            _tmpCachedAt = _cursor.getLong(_cursorIndexOfCachedAt);
            _item = new CachedWorkoutEntity(_tmpId,_tmpProgramId,_tmpProgramName,_tmpStartTime,_tmpEndTime,_tmpDuration,_tmpExercisesJson,_tmpNotes,_tmpCachedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getWorkoutCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM cached_workouts";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCompletedWorkoutCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM cached_workouts WHERE endTime IS NOT NULL";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalWorkoutDuration(final Continuation<? super Long> $completion) {
    final String _sql = "SELECT SUM(duration) FROM cached_workouts WHERE endTime IS NOT NULL";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Long>() {
      @Override
      @Nullable
      public Long call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Long _result;
          if (_cursor.moveToFirst()) {
            final Long _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<CachedWorkoutEntity>> getWeeklyWorkouts(final long weekStart,
      final long weekEnd) {
    final String _sql = "SELECT * FROM cached_workouts WHERE startTime >= ? AND startTime <= ? ORDER BY startTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, weekStart);
    _argIndex = 2;
    _statement.bindLong(_argIndex, weekEnd);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cached_workouts"}, new Callable<List<CachedWorkoutEntity>>() {
      @Override
      @NonNull
      public List<CachedWorkoutEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfProgramId = CursorUtil.getColumnIndexOrThrow(_cursor, "programId");
          final int _cursorIndexOfProgramName = CursorUtil.getColumnIndexOrThrow(_cursor, "programName");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfExercisesJson = CursorUtil.getColumnIndexOrThrow(_cursor, "exercisesJson");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCachedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "cachedAt");
          final List<CachedWorkoutEntity> _result = new ArrayList<CachedWorkoutEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CachedWorkoutEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final Long _tmpProgramId;
            if (_cursor.isNull(_cursorIndexOfProgramId)) {
              _tmpProgramId = null;
            } else {
              _tmpProgramId = _cursor.getLong(_cursorIndexOfProgramId);
            }
            final String _tmpProgramName;
            if (_cursor.isNull(_cursorIndexOfProgramName)) {
              _tmpProgramName = null;
            } else {
              _tmpProgramName = _cursor.getString(_cursorIndexOfProgramName);
            }
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final Long _tmpEndTime;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmpEndTime = null;
            } else {
              _tmpEndTime = _cursor.getLong(_cursorIndexOfEndTime);
            }
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpExercisesJson;
            _tmpExercisesJson = _cursor.getString(_cursorIndexOfExercisesJson);
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCachedAt;
            _tmpCachedAt = _cursor.getLong(_cursorIndexOfCachedAt);
            _item = new CachedWorkoutEntity(_tmpId,_tmpProgramId,_tmpProgramName,_tmpStartTime,_tmpEndTime,_tmpDuration,_tmpExercisesJson,_tmpNotes,_tmpCachedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
