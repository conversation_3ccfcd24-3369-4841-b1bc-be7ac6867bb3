<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="1dp"
    app:cardBackgroundColor="@android:color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/textSourceEmoji"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="📝"
            android:textSize="14sp"
            android:layout_marginEnd="8dp" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textSourcePost"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Post content snippet..."
                android:textSize="12sp"
                android:maxLines="2"
                android:ellipsize="end"
                tools:text="Wing Chun training session..." />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="2dp">

                <TextView
                    android:id="@+id/textSourceMetrics"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="❤️ 24 💬 1"
                    android:textSize="10sp"
                    android:alpha="0.7"
                    tools:text="❤️ 24 💬 1 📈 5.7%" />

                <TextView
                    android:id="@+id/textSourceScore"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="95%"
                    android:textSize="10sp"
                    android:textStyle="bold"
                    android:textColor="@color/excellent_green"
                    tools:text="95%" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView> 