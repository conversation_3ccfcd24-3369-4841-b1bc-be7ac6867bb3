package com.example.allinone.di;

import com.example.allinone.data.local.AppDatabase;
import com.example.allinone.data.local.dao.CachedProgramDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class AppModule_ProvideCachedProgramDaoFactory implements Factory<CachedProgramDao> {
  private final Provider<AppDatabase> databaseProvider;

  public AppModule_ProvideCachedProgramDaoFactory(Provider<AppDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public CachedProgramDao get() {
    return provideCachedProgramDao(databaseProvider.get());
  }

  public static AppModule_ProvideCachedProgramDaoFactory create(
      Provider<AppDatabase> databaseProvider) {
    return new AppModule_ProvideCachedProgramDaoFactory(databaseProvider);
  }

  public static CachedProgramDao provideCachedProgramDao(AppDatabase database) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideCachedProgramDao(database));
  }
}
