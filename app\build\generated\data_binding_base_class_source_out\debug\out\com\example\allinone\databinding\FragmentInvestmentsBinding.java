// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import androidx.viewpager2.widget.ViewPager2;
import com.example.allinone.R;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.tabs.TabLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentInvestmentsBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final FloatingActionButton addInvestmentButton;

  @NonNull
  public final TabLayout investmentTabLayout;

  @NonNull
  public final ViewPager2 investmentViewPager;

  private FragmentInvestmentsBinding(@NonNull ConstraintLayout rootView,
      @NonNull FloatingActionButton addInvestmentButton, @NonNull TabLayout investmentTabLayout,
      @NonNull ViewPager2 investmentViewPager) {
    this.rootView = rootView;
    this.addInvestmentButton = addInvestmentButton;
    this.investmentTabLayout = investmentTabLayout;
    this.investmentViewPager = investmentViewPager;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentInvestmentsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentInvestmentsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_investments, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentInvestmentsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addInvestmentButton;
      FloatingActionButton addInvestmentButton = ViewBindings.findChildViewById(rootView, id);
      if (addInvestmentButton == null) {
        break missingId;
      }

      id = R.id.investmentTabLayout;
      TabLayout investmentTabLayout = ViewBindings.findChildViewById(rootView, id);
      if (investmentTabLayout == null) {
        break missingId;
      }

      id = R.id.investmentViewPager;
      ViewPager2 investmentViewPager = ViewBindings.findChildViewById(rootView, id);
      if (investmentViewPager == null) {
        break missingId;
      }

      return new FragmentInvestmentsBinding((ConstraintLayout) rootView, addInvestmentButton,
          investmentTabLayout, investmentViewPager);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
