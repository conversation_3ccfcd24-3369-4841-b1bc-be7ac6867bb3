<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_student_details" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\dialog_student_details.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_student_details_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="101" endOffset="14"/></Target><Target id="@+id/profileImageView" view="com.google.android.material.imageview.ShapeableImageView"><Expressions/><location startLine="8" startOffset="4" endLine="17" endOffset="61"/></Target><Target id="@+id/nameTextView" view="TextView"><Expressions/><location startLine="19" startOffset="4" endLine="27" endOffset="50"/></Target><Target id="@+id/detailsTextView" view="TextView"><Expressions/><location startLine="29" startOffset="4" endLine="34" endOffset="40"/></Target><Target id="@+id/callButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="43" startOffset="8" endLine="60" endOffset="47"/></Target><Target id="@+id/whatsappButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="62" startOffset="8" endLine="79" endOffset="34"/></Target><Target id="@+id/instagramButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="81" startOffset="8" endLine="98" endOffset="34"/></Target></Targets></Layout>