// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemInstagramPostBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final View divider;

  @NonNull
  public final TextView insightsLabel;

  @NonNull
  public final TextView postCaption;

  @NonNull
  public final TextView postDate;

  @NonNull
  public final TextView postInsights;

  @NonNull
  public final ImageView postThumbnail;

  @NonNull
  public final TextView postType;

  private ItemInstagramPostBinding(@NonNull MaterialCardView rootView, @NonNull View divider,
      @NonNull TextView insightsLabel, @NonNull TextView postCaption, @NonNull TextView postDate,
      @NonNull TextView postInsights, @NonNull ImageView postThumbnail,
      @NonNull TextView postType) {
    this.rootView = rootView;
    this.divider = divider;
    this.insightsLabel = insightsLabel;
    this.postCaption = postCaption;
    this.postDate = postDate;
    this.postInsights = postInsights;
    this.postThumbnail = postThumbnail;
    this.postType = postType;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemInstagramPostBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemInstagramPostBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_instagram_post, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemInstagramPostBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.divider;
      View divider = ViewBindings.findChildViewById(rootView, id);
      if (divider == null) {
        break missingId;
      }

      id = R.id.insightsLabel;
      TextView insightsLabel = ViewBindings.findChildViewById(rootView, id);
      if (insightsLabel == null) {
        break missingId;
      }

      id = R.id.postCaption;
      TextView postCaption = ViewBindings.findChildViewById(rootView, id);
      if (postCaption == null) {
        break missingId;
      }

      id = R.id.postDate;
      TextView postDate = ViewBindings.findChildViewById(rootView, id);
      if (postDate == null) {
        break missingId;
      }

      id = R.id.postInsights;
      TextView postInsights = ViewBindings.findChildViewById(rootView, id);
      if (postInsights == null) {
        break missingId;
      }

      id = R.id.postThumbnail;
      ImageView postThumbnail = ViewBindings.findChildViewById(rootView, id);
      if (postThumbnail == null) {
        break missingId;
      }

      id = R.id.postType;
      TextView postType = ViewBindings.findChildViewById(rootView, id);
      if (postType == null) {
        break missingId;
      }

      return new ItemInstagramPostBinding((MaterialCardView) rootView, divider, insightsLabel,
          postCaption, postDate, postInsights, postThumbnail, postType);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
