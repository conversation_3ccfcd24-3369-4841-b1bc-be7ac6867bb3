<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_chat_source" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_chat_source.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_chat_source_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="70" endOffset="51"/></Target><Target id="@+id/textSourceEmoji" view="TextView"><Expressions/><location startLine="18" startOffset="8" endLine="24" endOffset="44"/></Target><Target id="@+id/textSourcePost" view="TextView"><Expressions/><location startLine="32" startOffset="12" endLine="40" endOffset="60"/></Target><Target id="@+id/textSourceMetrics" view="TextView"><Expressions/><location startLine="48" startOffset="16" endLine="56" endOffset="61"/></Target><Target id="@+id/textSourceScore" view="TextView"><Expressions/><location startLine="58" startOffset="16" endLine="66" endOffset="38"/></Target></Targets></Layout>