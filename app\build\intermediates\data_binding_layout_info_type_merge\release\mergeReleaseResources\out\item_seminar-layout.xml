<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_seminar" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_seminar.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_seminar_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="75" endOffset="51"/></Target><Target id="@+id/seminarNameText" view="TextView"><Expressions/><location startLine="16" startOffset="8" endLine="25" endOffset="39"/></Target><Target id="@+id/seminarDateText" view="TextView"><Expressions/><location startLine="27" startOffset="8" endLine="36" endOffset="38"/></Target><Target id="@+id/seminarTimeText" view="TextView"><Expressions/><location startLine="38" startOffset="8" endLine="47" endOffset="40"/></Target><Target id="@+id/seminarDescriptionText" view="TextView"><Expressions/><location startLine="49" startOffset="8" endLine="60" endOffset="40"/></Target><Target id="@+id/shareButton" view="ImageButton"><Expressions/><location startLine="62" startOffset="8" endLine="71" endOffset="61"/></Target></Targets></Layout>