// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentInvestmentsTabBinding implements ViewBinding {
  @NonNull
  private final SwipeRefreshLayout rootView;

  @NonNull
  public final TextView emptyStateText;

  @NonNull
  public final TextView investmentCountText;

  @NonNull
  public final RecyclerView investmentsRecyclerView;

  @NonNull
  public final MaterialCardView summaryCard;

  @NonNull
  public final SwipeRefreshLayout swipeRefreshLayout;

  @NonNull
  public final TextView totalInvestmentsText;

  private FragmentInvestmentsTabBinding(@NonNull SwipeRefreshLayout rootView,
      @NonNull TextView emptyStateText, @NonNull TextView investmentCountText,
      @NonNull RecyclerView investmentsRecyclerView, @NonNull MaterialCardView summaryCard,
      @NonNull SwipeRefreshLayout swipeRefreshLayout, @NonNull TextView totalInvestmentsText) {
    this.rootView = rootView;
    this.emptyStateText = emptyStateText;
    this.investmentCountText = investmentCountText;
    this.investmentsRecyclerView = investmentsRecyclerView;
    this.summaryCard = summaryCard;
    this.swipeRefreshLayout = swipeRefreshLayout;
    this.totalInvestmentsText = totalInvestmentsText;
  }

  @Override
  @NonNull
  public SwipeRefreshLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentInvestmentsTabBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentInvestmentsTabBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_investments_tab, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentInvestmentsTabBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.emptyStateText;
      TextView emptyStateText = ViewBindings.findChildViewById(rootView, id);
      if (emptyStateText == null) {
        break missingId;
      }

      id = R.id.investmentCountText;
      TextView investmentCountText = ViewBindings.findChildViewById(rootView, id);
      if (investmentCountText == null) {
        break missingId;
      }

      id = R.id.investmentsRecyclerView;
      RecyclerView investmentsRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (investmentsRecyclerView == null) {
        break missingId;
      }

      id = R.id.summaryCard;
      MaterialCardView summaryCard = ViewBindings.findChildViewById(rootView, id);
      if (summaryCard == null) {
        break missingId;
      }

      SwipeRefreshLayout swipeRefreshLayout = (SwipeRefreshLayout) rootView;

      id = R.id.totalInvestmentsText;
      TextView totalInvestmentsText = ViewBindings.findChildViewById(rootView, id);
      if (totalInvestmentsText == null) {
        break missingId;
      }

      return new FragmentInvestmentsTabBinding((SwipeRefreshLayout) rootView, emptyStateText,
          investmentCountText, investmentsRecyclerView, summaryCard, swipeRefreshLayout,
          totalInvestmentsText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
