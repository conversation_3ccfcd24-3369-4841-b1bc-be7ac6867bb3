<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_note_video" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_note_video.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_note_video_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="53" endOffset="35"/></Target><Target id="@+id/videoThumbnail" view="ImageView"><Expressions/><location startLine="13" startOffset="8" endLine="19" endOffset="58"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="31" startOffset="8" endLine="36" endOffset="39"/></Target><Target id="@+id/deleteButton" view="ImageButton"><Expressions/><location startLine="39" startOffset="8" endLine="49" endOffset="45"/></Target></Targets></Layout>