package com.example.allinone.feature.workout.data.repository;

import com.example.allinone.feature.workout.data.datasource.WorkoutLocalDataSource;
import com.example.allinone.feature.workout.data.datasource.WorkoutRemoteDataSource;
import com.example.allinone.firebase.OfflineQueue;
import com.example.allinone.utils.NetworkUtils;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class WorkoutRepositoryImpl_Factory implements Factory<WorkoutRepositoryImpl> {
  private final Provider<WorkoutLocalDataSource> localDataSourceProvider;

  private final Provider<WorkoutRemoteDataSource> remoteDataSourceProvider;

  private final Provider<NetworkUtils> networkUtilsProvider;

  private final Provider<OfflineQueue> offlineQueueProvider;

  public WorkoutRepositoryImpl_Factory(Provider<WorkoutLocalDataSource> localDataSourceProvider,
      Provider<WorkoutRemoteDataSource> remoteDataSourceProvider,
      Provider<NetworkUtils> networkUtilsProvider, Provider<OfflineQueue> offlineQueueProvider) {
    this.localDataSourceProvider = localDataSourceProvider;
    this.remoteDataSourceProvider = remoteDataSourceProvider;
    this.networkUtilsProvider = networkUtilsProvider;
    this.offlineQueueProvider = offlineQueueProvider;
  }

  @Override
  public WorkoutRepositoryImpl get() {
    return newInstance(localDataSourceProvider.get(), remoteDataSourceProvider.get(), networkUtilsProvider.get(), offlineQueueProvider.get());
  }

  public static WorkoutRepositoryImpl_Factory create(
      Provider<WorkoutLocalDataSource> localDataSourceProvider,
      Provider<WorkoutRemoteDataSource> remoteDataSourceProvider,
      Provider<NetworkUtils> networkUtilsProvider, Provider<OfflineQueue> offlineQueueProvider) {
    return new WorkoutRepositoryImpl_Factory(localDataSourceProvider, remoteDataSourceProvider, networkUtilsProvider, offlineQueueProvider);
  }

  public static WorkoutRepositoryImpl newInstance(WorkoutLocalDataSource localDataSource,
      WorkoutRemoteDataSource remoteDataSource, NetworkUtils networkUtils,
      OfflineQueue offlineQueue) {
    return new WorkoutRepositoryImpl(localDataSource, remoteDataSource, networkUtils, offlineQueue);
  }
}
