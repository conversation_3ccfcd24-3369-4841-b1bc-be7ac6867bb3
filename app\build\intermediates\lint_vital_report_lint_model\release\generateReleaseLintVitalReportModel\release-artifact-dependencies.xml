<dependencies>
  <compile
      roots="androidx.databinding:databinding-runtime:8.8.2@aar,androidx.databinding:viewbinding:8.8.2@aar,androidx.databinding:databinding-adapters:8.8.2@aar,androidx.databinding:databinding-common:8.8.2@jar,androidx.databinding:databinding-ktx:8.8.2@aar,androidx.hilt:hilt-navigation-compose:1.1.0@aar,androidx.hilt:hilt-navigation:1.1.0@aar,androidx.navigation:navigation-common:2.8.2@aar,androidx.navigation:navigation-runtime:2.8.2@aar,androidx.navigation:navigation-common-ktx:2.8.2@aar,androidx.navigation:navigation-ui:2.8.2@aar,androidx.navigation:navigation-fragment:2.8.2@aar,androidx.navigation:navigation-runtime-ktx:2.8.2@aar,androidx.navigation:navigation-ui-ktx:2.8.2@aar,androidx.navigation:navigation-fragment-ktx:2.8.2@aar,androidx.navigation:navigation-compose:2.8.2@aar,com.google.android.material:material:1.12.0@aar,androidx.appcompat:appcompat-resources:1.7.0@aar,androidx.appcompat:appcompat:1.7.0@aar,com.google.dagger:hilt-android:2.52@aar,androidx.fragment:fragment-ktx:1.8.4@aar,com.google.android.gms:play-services-auth:21.2.0@aar,com.google.firebase:firebase-auth:23.1.0@aar,androidx.viewpager2:viewpager2:1.1.0@aar,com.github.bumptech.glide:glide:4.16.0@aar,com.google.android.gms:play-services-auth-api-phone:18.0.2@aar,com.google.android.gms:play-services-auth-base:18.0.10@aar,com.google.android.gms:play-services-fido:20.1.0@aar,com.google.firebase:firebase-firestore:25.1.1@aar,com.google.firebase:firebase-storage:21.0.1@aar,com.google.firebase:firebase-appcheck:18.0.0@aar,com.google.firebase:firebase-appcheck-interop:17.1.0@aar,com.google.firebase:firebase-database-collection:18.0.1@aar,com.google.firebase:firebase-analytics:22.1.2@aar,com.google.android.gms:play-services-measurement:22.1.2@aar,com.google.android.gms:play-services-measurement-sdk:22.1.2@aar,com.google.android.gms:play-services-measurement-impl:22.1.2@aar,com.google.android.gms:play-services-base:18.5.0@aar,com.google.firebase:firebase-crashlytics:19.2.1@aar,com.google.android.gms:play-services-measurement-api:22.1.2@aar,com.google.android.recaptcha:recaptcha:18.5.1@aar,com.google.android.play:integrity:1.3.0@aar,com.google.firebase:firebase-auth-interop:20.0.0@aar,com.google.firebase:firebase-installations:18.0.0@aar,com.google.firebase:firebase-sessions:2.0.6@aar,com.google.firebase:firebase-installations-interop:17.2.0@aar,com.google.firebase:firebase-common-ktx:21.0.0@aar,com.google.firebase:firebase-common:21.0.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.paging:paging-common-android:3.3.2@aar,androidx.paging:paging-common-ktx:3.3.2@jar,androidx.paging:paging-runtime-ktx:3.3.2@aar,androidx.paging:paging-runtime:3.3.2@aar,androidx.recyclerview:recyclerview:1.3.1@aar,androidx.transition:transition:1.5.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,com.google.android.gms:play-services-stats:17.0.2@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.1.0@aar,androidx.browser:browser:1.4.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.customview:customview:1.1.0@aar,com.google.android.exoplayer:exoplayer:2.19.1@aar,com.google.android.exoplayer:exoplayer-ui:2.19.1@aar,androidx.media:media:1.6.0@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.lifecycle:lifecycle-common-jvm:2.8.6@jar,androidx.lifecycle:lifecycle-viewmodel:2.8.6@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.6@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.6@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.6@aar,io.coil-kt:coil-compose:2.5.0@aar,io.coil-kt:coil-compose-base:2.5.0@aar,io.coil-kt:coil:2.5.0@aar,io.coil-kt:coil-base:2.5.0@aar,androidx.lifecycle:lifecycle-runtime-android:2.8.6@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.6@aar,androidx.lifecycle:lifecycle-runtime-compose-android:2.8.6@aar,androidx.lifecycle:lifecycle-service:2.8.6@aar,androidx.lifecycle:lifecycle-process:2.8.6@aar,androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6@aar,androidx.compose.material3:material3-android:1.2.0@aar,androidx.compose.foundation:foundation-layout-android:1.7.2@aar,androidx.compose.material:material-icons-core-android:1.6.1@aar,androidx.compose.material:material-icons-extended-android:1.6.1@aar,androidx.compose.material:material-ripple-android:1.6.1@aar,androidx.compose.foundation:foundation-android:1.7.2@aar,androidx.compose.animation:animation-core-android:1.7.2@aar,androidx.compose.animation:animation-android:1.7.2@aar,androidx.compose.ui:ui-util-android:1.7.2@aar,androidx.compose.ui:ui-unit-android:1.7.2@aar,androidx.compose.ui:ui-text-android:1.7.2@aar,androidx.compose.ui:ui-graphics-android:1.7.2@aar,androidx.compose.ui:ui-geometry-android:1.7.2@aar,androidx.compose.ui:ui-android:1.7.2@aar,androidx.compose.ui:ui-tooling-preview-android:1.7.2@aar,androidx.compose.runtime:runtime-saveable-android:1.7.2@aar,androidx.compose.runtime:runtime-android:1.7.2@aar,androidx.compose.runtime:runtime-livedata:1.7.2@aar,androidx.work:work-runtime-ktx:2.9.1@aar,androidx.work:work-runtime:2.9.1@aar,androidx.lifecycle:lifecycle-livedata:2.8.6@aar,androidx.lifecycle:lifecycle-livedata-ktx:2.8.6@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6@aar,androidx.room:room-common:2.6.1@jar,androidx.room:room-runtime:2.6.1@aar,androidx.room:room-ktx:2.6.1@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1@jar,androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar,androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.8.1@jar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.firebase:firebase-measurement-connector:20.0.1@aar,com.google.android.gms:play-services-ads-identifier:18.0.0@aar,com.google.android.gms:play-services-measurement-sdk-api:22.1.2@aar,com.google.android.gms:play-services-measurement-base:22.1.2@aar,com.google.android.gms:play-services-basement:18.4.0@aar,androidx.fragment:fragment:1.8.4@aar,androidx.fragment:fragment:1.8.4@aar,androidx.activity:activity:1.9.2@aar,androidx.activity:activity-compose:1.9.2@aar,androidx.activity:activity-ktx:1.9.2@aar,androidx.core:core-ktx:1.13.1@aar,androidx.core:core-splashscreen:1.0.1@aar,com.jakewharton.timber:timber:5.0.1@aar,org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.25@jar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.annotation:annotation-experimental:1.4.1@aar,androidx.credentials:credentials-play-services-auth:1.2.0-rc01@aar,androidx.credentials:credentials:1.2.0-rc01@aar,androidx.credentials:credentials:1.2.0-rc01@aar,me.saket.swipe:swipe:1.2.0@aar,com.squareup.okhttp3:logging-interceptor:4.12.0@jar,com.squareup.retrofit2:converter-gson:2.11.0@jar,com.squareup.retrofit2:retrofit:2.11.0@jar,com.squareup.okhttp3:okhttp:4.12.0@jar,com.google.firebase:firebase-config-interop:16.0.1@aar,com.google.firebase:firebase-encoders-json:18.0.1@aar,com.squareup.okio:okio-jvm:3.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21@jar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.25@jar,androidx.security:security-crypto:1.1.0-alpha06@aar,androidx.documentfile:documentfile:1.0.1@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.cardview:cardview:1.0.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,com.google.firebase:firebase-encoders:17.0.0@jar,androidx.sqlite:sqlite-framework:2.4.0@aar,androidx.sqlite:sqlite:2.4.0@aar,com.github.bumptech.glide:gifdecoder:4.16.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection-ktx:1.4.4@jar,androidx.collection:collection-jvm:1.4.4@jar,androidx.arch.core:core-common:2.2.0@jar,androidx.print:print:1.0.0@aar,androidx.annotation:annotation-jvm:1.8.1@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21@jar,org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar,com.github.PhilJay:MPAndroidChart:v3.1.0@aar,androidx.constraintlayout:constraintlayout:2.2.0@aar,com.google.code.gson:gson:2.11.0@jar,com.github.chrisbanes:PhotoView:2.3.0@aar,jp.wasabeef:richeditor-android:2.0.0@aar,org.jetbrains:annotations:23.0.0@jar,com.google.android.exoplayer:exoplayer-core:2.19.1@aar,com.google.android.exoplayer:exoplayer-common:2.19.1@aar,com.google.guava:guava:32.1.3-android@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.0.0@aar,com.google.errorprone:error_prone_annotations:2.27.0@jar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,com.google.dagger:hilt-core:2.52@jar,com.google.dagger:dagger:2.52@jar,jakarta.inject:jakarta.inject-api:2.0.1@jar,com.google.firebase:firebase-components:18.0.0@aar,com.google.firebase:firebase-annotations:16.2.0@jar,javax.inject:javax.inject:1@jar,com.google.dagger:dagger-lint-aar:2.52@aar,com.google.code.findbugs:jsr305:3.0.2@jar,com.google.guava:failureaccess:1.0.1@jar,org.checkerframework:checker-qual:3.37.0@jar,com.google.android.play:core-common:2.0.3@aar,com.google.firebase:protolite-well-known-types:18.0.0@aar,com.google.protobuf:protobuf-javalite:3.25.1@jar,com.google.android.exoplayer:exoplayer-database:2.19.1@aar,com.google.android.exoplayer:exoplayer-datasource:2.19.1@aar,com.google.android.exoplayer:exoplayer-decoder:2.19.1@aar,com.google.android.exoplayer:exoplayer-extractor:2.19.1@aar,com.google.android.exoplayer:exoplayer-container:2.19.1@aar,com.google.android.exoplayer:exoplayer-dash:2.19.1@aar,com.google.android.exoplayer:exoplayer-hls:2.19.1@aar,com.google.android.exoplayer:exoplayer-rtsp:2.19.1@aar,com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1@aar,com.github.bumptech.glide:disklrucache:4.16.0@jar,com.github.bumptech.glide:annotations:4.16.0@jar,androidx.exifinterface:exifinterface:1.3.6@aar,com.google.j2objc:j2objc-annotations:2.8@jar">
    <dependency
        name="androidx.databinding:databinding-runtime:8.8.2@aar"
        simpleName="androidx.databinding:databinding-runtime"/>
    <dependency
        name="androidx.databinding:viewbinding:8.8.2@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="androidx.databinding:databinding-adapters:8.8.2@aar"
        simpleName="androidx.databinding:databinding-adapters"/>
    <dependency
        name="androidx.databinding:databinding-common:8.8.2@jar"
        simpleName="androidx.databinding:databinding-common"/>
    <dependency
        name="androidx.databinding:databinding-ktx:8.8.2@aar"
        simpleName="androidx.databinding:databinding-ktx"/>
    <dependency
        name="androidx.hilt:hilt-navigation-compose:1.1.0@aar"
        simpleName="androidx.hilt:hilt-navigation-compose"/>
    <dependency
        name="androidx.hilt:hilt-navigation:1.1.0@aar"
        simpleName="androidx.hilt:hilt-navigation"/>
    <dependency
        name="androidx.navigation:navigation-common:2.8.2@aar"
        simpleName="androidx.navigation:navigation-common"/>
    <dependency
        name="androidx.navigation:navigation-runtime:2.8.2@aar"
        simpleName="androidx.navigation:navigation-runtime"/>
    <dependency
        name="androidx.navigation:navigation-common-ktx:2.8.2@aar"
        simpleName="androidx.navigation:navigation-common-ktx"/>
    <dependency
        name="androidx.navigation:navigation-ui:2.8.2@aar"
        simpleName="androidx.navigation:navigation-ui"/>
    <dependency
        name="androidx.navigation:navigation-fragment:2.8.2@aar"
        simpleName="androidx.navigation:navigation-fragment"/>
    <dependency
        name="androidx.navigation:navigation-runtime-ktx:2.8.2@aar"
        simpleName="androidx.navigation:navigation-runtime-ktx"/>
    <dependency
        name="androidx.navigation:navigation-ui-ktx:2.8.2@aar"
        simpleName="androidx.navigation:navigation-ui-ktx"/>
    <dependency
        name="androidx.navigation:navigation-fragment-ktx:2.8.2@aar"
        simpleName="androidx.navigation:navigation-fragment-ktx"/>
    <dependency
        name="androidx.navigation:navigation-compose:2.8.2@aar"
        simpleName="androidx.navigation:navigation-compose"/>
    <dependency
        name="com.google.android.material:material:1.12.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.google.dagger:hilt-android:2.52@aar"
        simpleName="com.google.dagger:hilt-android"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.8.4@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="com.google.android.gms:play-services-auth:21.2.0@aar"
        simpleName="com.google.android.gms:play-services-auth"/>
    <dependency
        name="com.google.firebase:firebase-auth:23.1.0@aar"
        simpleName="com.google.firebase:firebase-auth"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.1.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.16.0@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-auth-api-phone"/>
    <dependency
        name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
        simpleName="com.google.android.gms:play-services-auth-base"/>
    <dependency
        name="com.google.android.gms:play-services-fido:20.1.0@aar"
        simpleName="com.google.android.gms:play-services-fido"/>
    <dependency
        name="com.google.firebase:firebase-firestore:25.1.1@aar"
        simpleName="com.google.firebase:firebase-firestore"/>
    <dependency
        name="com.google.firebase:firebase-storage:21.0.1@aar"
        simpleName="com.google.firebase:firebase-storage"/>
    <dependency
        name="com.google.firebase:firebase-appcheck:18.0.0@aar"
        simpleName="com.google.firebase:firebase-appcheck"/>
    <dependency
        name="com.google.firebase:firebase-appcheck-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-appcheck-interop"/>
    <dependency
        name="com.google.firebase:firebase-database-collection:18.0.1@aar"
        simpleName="com.google.firebase:firebase-database-collection"/>
    <dependency
        name="com.google.firebase:firebase-analytics:22.1.2@aar"
        simpleName="com.google.firebase:firebase-analytics"/>
    <dependency
        name="com.google.android.gms:play-services-measurement:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-impl:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-impl"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.firebase:firebase-crashlytics:19.2.1@aar"
        simpleName="com.google.firebase:firebase-crashlytics"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-api:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-api"/>
    <dependency
        name="com.google.android.recaptcha:recaptcha:18.5.1@aar"
        simpleName="com.google.android.recaptcha:recaptcha"/>
    <dependency
        name="com.google.android.play:integrity:1.3.0@aar"
        simpleName="com.google.android.play:integrity"/>
    <dependency
        name="com.google.firebase:firebase-auth-interop:20.0.0@aar"
        simpleName="com.google.firebase:firebase-auth-interop"/>
    <dependency
        name="com.google.firebase:firebase-installations:18.0.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-sessions:2.0.6@aar"
        simpleName="com.google.firebase:firebase-sessions"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.2.0@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.google.firebase:firebase-common:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.paging:paging-common-android:3.3.2@aar"
        simpleName="androidx.paging:paging-common-android"/>
    <dependency
        name="androidx.paging:paging-common-ktx:3.3.2@jar"
        simpleName="androidx.paging:paging-common-ktx"/>
    <dependency
        name="androidx.paging:paging-runtime-ktx:3.3.2@aar"
        simpleName="androidx.paging:paging-runtime-ktx"/>
    <dependency
        name="androidx.paging:paging-runtime:3.3.2@aar"
        simpleName="androidx.paging:paging-runtime"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.3.1@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.transition:transition:1.5.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.1.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.browser:browser:1.4.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-ui:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-ui"/>
    <dependency
        name="androidx.media:media:1.6.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.6@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="io.coil-kt:coil-compose:2.5.0@aar"
        simpleName="io.coil-kt:coil-compose"/>
    <dependency
        name="io.coil-kt:coil-compose-base:2.5.0@aar"
        simpleName="io.coil-kt:coil-compose-base"/>
    <dependency
        name="io.coil-kt:coil:2.5.0@aar"
        simpleName="io.coil-kt:coil"/>
    <dependency
        name="io.coil-kt:coil-base:2.5.0@aar"
        simpleName="io.coil-kt:coil-base"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-compose-android"/>
    <dependency
        name="androidx.compose.material3:material3-android:1.2.0@aar"
        simpleName="androidx.compose.material3:material3-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.7.2@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.material:material-icons-core-android:1.6.1@aar"
        simpleName="androidx.compose.material:material-icons-core-android"/>
    <dependency
        name="androidx.compose.material:material-icons-extended-android:1.6.1@aar"
        simpleName="androidx.compose.material:material-icons-extended-android"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.6.1@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.7.2@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.7.2@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.7.2@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.7.2@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.7.2@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.7.2@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.7.2@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.7.2@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.7.2@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.7.2@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.7.2@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.7.2@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-livedata:1.7.2@aar"
        simpleName="androidx.compose.runtime:runtime-livedata"/>
    <dependency
        name="androidx.work:work-runtime-ktx:2.9.1@aar"
        simpleName="androidx.work:work-runtime-ktx"/>
    <dependency
        name="androidx.work:work-runtime:2.9.1@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-ktx:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.room:room-common:2.6.1@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.room:room-runtime:2.6.1@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.room:room-ktx:2.6.1@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices-java"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.8.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:20.0.1@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
        simpleName="com.google.android.gms:play-services-ads-identifier"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk-api:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk-api"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-base:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-base"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.4.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.8.4@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.9.2@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.activity:activity-compose:1.9.2@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="androidx.activity:activity-ktx:1.9.2@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.core:core-splashscreen:1.0.1@aar"
        simpleName="androidx.core:core-splashscreen"/>
    <dependency
        name="com.jakewharton.timber:timber:5.0.1@aar"
        simpleName="com.jakewharton.timber:timber"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.25@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.credentials:credentials-play-services-auth:1.2.0-rc01@aar"
        simpleName="androidx.credentials:credentials-play-services-auth"/>
    <dependency
        name="androidx.credentials:credentials:1.2.0-rc01@aar"
        simpleName="androidx.credentials:credentials"/>
    <dependency
        name="me.saket.swipe:swipe:1.2.0@aar"
        simpleName="me.saket.swipe:swipe"/>
    <dependency
        name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
        simpleName="com.squareup.okhttp3:logging-interceptor"/>
    <dependency
        name="com.squareup.retrofit2:converter-gson:2.11.0@jar"
        simpleName="com.squareup.retrofit2:converter-gson"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:2.11.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.google.firebase:firebase-config-interop:16.0.1@aar"
        simpleName="com.google.firebase:firebase-config-interop"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.1@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.25@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="androidx.security:security-crypto:1.1.0-alpha06@aar"
        simpleName="androidx.security:security-crypto"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.1@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.sqlite:sqlite:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection-ktx:1.4.4@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.4@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="com.github.PhilJay:MPAndroidChart:v3.1.0@aar"
        simpleName="com.github.PhilJay:MPAndroidChart"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.2.0@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="com.google.code.gson:gson:2.11.0@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="com.github.chrisbanes:PhotoView:2.3.0@aar"
        simpleName="com.github.chrisbanes:PhotoView"/>
    <dependency
        name="jp.wasabeef:richeditor-android:2.0.0@aar"
        simpleName="jp.wasabeef:richeditor-android"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-core:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-core"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-common:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-common"/>
    <dependency
        name="com.google.guava:guava:32.1.3-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.27.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="com.google.dagger:hilt-core:2.52@jar"
        simpleName="com.google.dagger:hilt-core"/>
    <dependency
        name="com.google.dagger:dagger:2.52@jar"
        simpleName="com.google.dagger:dagger"/>
    <dependency
        name="jakarta.inject:jakarta.inject-api:2.0.1@jar"
        simpleName="jakarta.inject:jakarta.inject-api"/>
    <dependency
        name="com.google.firebase:firebase-components:18.0.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.dagger:dagger-lint-aar:2.52@aar"
        simpleName="com.google.dagger:dagger-lint-aar"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="org.checkerframework:checker-qual:3.37.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
    <dependency
        name="com.google.android.play:core-common:2.0.3@aar"
        simpleName="com.google.android.play:core-common"/>
    <dependency
        name="com.google.firebase:protolite-well-known-types:18.0.0@aar"
        simpleName="com.google.firebase:protolite-well-known-types"/>
    <dependency
        name="com.google.protobuf:protobuf-javalite:3.25.1@jar"
        simpleName="com.google.protobuf:protobuf-javalite"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-database:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-database"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-datasource:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-datasource"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-decoder:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-decoder"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-extractor:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-extractor"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-container:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-container"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-dash:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-dash"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-hls:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-hls"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-rtsp:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-rtsp"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-smoothstreaming"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.16.0@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.6@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="com.google.j2objc:j2objc-annotations:2.8@jar"
        simpleName="com.google.j2objc:j2objc-annotations"/>
  </compile>
  <package
      roots="androidx.databinding:databinding-adapters:8.8.2@aar,androidx.databinding:databinding-ktx:8.8.2@aar,androidx.databinding:databinding-runtime:8.8.2@aar,androidx.databinding:viewbinding:8.8.2@aar,androidx.databinding:databinding-common:8.8.2@jar,androidx.hilt:hilt-navigation-compose:1.1.0@aar,androidx.hilt:hilt-navigation:1.1.0@aar,androidx.navigation:navigation-common:2.8.2@aar,androidx.navigation:navigation-runtime:2.8.2@aar,androidx.navigation:navigation-common-ktx:2.8.2@aar,androidx.navigation:navigation-ui:2.8.2@aar,androidx.navigation:navigation-fragment:2.8.2@aar,androidx.navigation:navigation-runtime-ktx:2.8.2@aar,androidx.navigation:navigation-ui-ktx:2.8.2@aar,androidx.navigation:navigation-fragment-ktx:2.8.2@aar,androidx.navigation:navigation-compose:2.8.2@aar,com.google.android.material:material:1.12.0@aar,androidx.constraintlayout:constraintlayout:2.2.0@aar,com.github.chrisbanes:PhotoView:2.3.0@aar,io.coil-kt:coil-compose:2.5.0@aar,io.coil-kt:coil-compose-base:2.5.0@aar,io.coil-kt:coil:2.5.0@aar,io.coil-kt:coil-base:2.5.0@aar,androidx.appcompat:appcompat-resources:1.7.0@aar,androidx.appcompat:appcompat:1.7.0@aar,com.google.firebase:firebase-auth:23.1.0@aar,androidx.credentials:credentials:1.2.0-rc01@aar,androidx.credentials:credentials:1.2.0-rc01@aar,androidx.credentials:credentials-play-services-auth:1.2.0-rc01@aar,com.google.android.gms:play-services-auth:21.2.0@aar,com.google.android.exoplayer:exoplayer:2.19.1@aar,com.google.android.exoplayer:exoplayer-ui:2.19.1@aar,androidx.paging:paging-common-android:3.3.2@aar,androidx.paging:paging-common-ktx:3.3.2@jar,androidx.paging:paging-runtime-ktx:3.3.2@aar,androidx.paging:paging-runtime:3.3.2@aar,androidx.recyclerview:recyclerview:1.3.1@aar,androidx.viewpager2:viewpager2:1.1.0@aar,com.google.dagger:hilt-android:2.52@aar,com.github.bumptech.glide:glide:4.16.0@aar,com.google.firebase:firebase-firestore:25.1.1@aar,com.google.android.gms:play-services-auth-api-phone:18.0.2@aar,com.google.android.gms:play-services-auth-base:18.0.10@aar,com.google.firebase:firebase-analytics:22.1.2@aar,com.google.android.gms:play-services-measurement:22.1.2@aar,com.google.android.gms:play-services-measurement-api:22.1.2@aar,com.google.android.gms:play-services-measurement-sdk:22.1.2@aar,com.google.firebase:firebase-crashlytics:19.2.1@aar,com.google.firebase:firebase-storage:21.0.1@aar,com.google.android.recaptcha:recaptcha:18.5.1@aar,com.google.android.play:integrity:1.3.0@aar,com.google.firebase:firebase-auth-interop:20.0.0@aar,com.google.firebase:firebase-sessions:2.0.6@aar,com.google.firebase:firebase-installations:18.0.0@aar,com.google.firebase:firebase-appcheck:18.0.0@aar,com.google.firebase:firebase-common-ktx:21.0.0@aar,com.google.firebase:firebase-common:21.0.0@aar,com.google.firebase:firebase-installations-interop:17.2.0@aar,com.google.firebase:firebase-appcheck-interop:17.1.0@aar,com.google.android.gms:play-services-measurement-impl:22.1.2@aar,com.google.android.gms:play-services-fido:20.1.0@aar,me.saket.swipe:swipe:1.2.0@aar,androidx.compose.material3:material3-android:1.2.0@aar,androidx.compose.material:material-icons-core-android:1.6.1@aar,androidx.compose.material:material-icons-extended-android:1.6.1@aar,androidx.compose.material:material-ripple-android:1.6.1@aar,androidx.compose.foundation:foundation-layout-android:1.7.2@aar,androidx.compose.foundation:foundation-android:1.7.2@aar,androidx.compose.animation:animation-core-android:1.7.2@aar,androidx.compose.animation:animation-android:1.7.2@aar,com.google.accompanist:accompanist-drawablepainter:0.32.0@aar,androidx.compose.ui:ui-unit-android:1.7.2@aar,androidx.compose.ui:ui-geometry-android:1.7.2@aar,androidx.compose.ui:ui-tooling-preview-android:1.7.2@aar,androidx.compose.ui:ui-util-android:1.7.2@aar,androidx.compose.ui:ui-text-android:1.7.2@aar,androidx.compose.ui:ui-graphics-android:1.7.2@aar,androidx.compose.ui:ui-android:1.7.2@aar,androidx.emoji2:emoji2-views-helper:1.3.0@aar,androidx.emoji2:emoji2:1.3.0@aar,androidx.activity:activity:1.9.2@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.transition:transition:1.5.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,com.google.android.gms:play-services-stats:17.0.2@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.1.0@aar,androidx.customview:customview-poolingcontainer:1.0.0@aar,androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar,androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar,androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar,androidx.core:core-ktx:1.13.1@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,com.google.android.exoplayer:exoplayer-dash:2.19.1@aar,com.google.android.exoplayer:exoplayer-hls:2.19.1@aar,com.google.android.exoplayer:exoplayer-rtsp:2.19.1@aar,com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1@aar,com.google.android.exoplayer:exoplayer-core:2.19.1@aar,androidx.work:work-runtime-ktx:2.9.1@aar,androidx.work:work-runtime:2.9.1@aar,androidx.browser:browser:1.4.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.media:media:1.6.0@aar,androidx.autofill:autofill:1.0.0@aar,androidx.window:window:1.0.0@aar,com.google.firebase:firebase-database-collection:18.0.1@aar,com.google.android.gms:play-services-base:18.5.0@aar,androidx.graphics:graphics-path:1.0.1@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.lifecycle:lifecycle-common-java8:2.8.6@jar,androidx.lifecycle:lifecycle-common-jvm:2.8.6@jar,androidx.lifecycle:lifecycle-viewmodel:2.8.6@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.6@aar,androidx.compose.runtime:runtime-saveable-android:1.7.2@aar,androidx.compose.runtime:runtime-android:1.7.2@aar,androidx.compose.runtime:runtime-livedata:1.7.2@aar,androidx.lifecycle:lifecycle-runtime-android:2.8.6@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.6@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.6@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6@aar,androidx.lifecycle:lifecycle-service:2.8.6@aar,androidx.lifecycle:lifecycle-process:2.8.6@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.6@aar,androidx.lifecycle:lifecycle-livedata:2.8.6@aar,androidx.lifecycle:lifecycle-runtime-compose-android:2.8.6@aar,androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6@aar,androidx.lifecycle:lifecycle-livedata-ktx:2.8.6@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6@aar,androidx.room:room-common:2.6.1@jar,androidx.room:room-runtime:2.6.1@aar,androidx.room:room-ktx:2.6.1@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1@jar,androidx.datastore:datastore-preferences:1.0.0@aar,androidx.datastore:datastore:1.0.0@aar,androidx.datastore:datastore-preferences-core:1.0.0@jar,androidx.datastore:datastore-core:1.0.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.8.1@jar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.firebase:firebase-measurement-connector:20.0.1@aar,com.google.android.gms:play-services-ads-identifier:18.0.0@aar,com.google.android.gms:play-services-measurement-sdk-api:22.1.2@aar,com.google.android.gms:play-services-measurement-base:22.1.2@aar,com.google.android.gms:play-services-basement:18.4.0@aar,androidx.fragment:fragment:1.8.4@aar,androidx.fragment:fragment:1.8.4@aar,androidx.fragment:fragment-ktx:1.8.4@aar,androidx.activity:activity-ktx:1.9.2@aar,androidx.activity:activity-compose:1.9.2@aar,androidx.core:core-splashscreen:1.0.1@aar,com.jakewharton.timber:timber:5.0.1@aar,org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.25@jar,com.squareup.okhttp3:logging-interceptor:4.12.0@jar,com.squareup.retrofit2:converter-gson:2.11.0@jar,com.squareup.retrofit2:retrofit:2.11.0@jar,com.squareup.okhttp3:okhttp:4.12.0@jar,com.google.firebase:firebase-config-interop:16.0.1@aar,com.google.firebase:firebase-datatransport:19.0.0@aar,com.google.android.datatransport:transport-backend-cct:3.3.0@aar,com.google.firebase:firebase-encoders-json:18.0.1@aar,io.grpc:grpc-okhttp:1.62.2@jar,com.squareup.okio:okio-jvm:3.6.0@jar,com.google.android.libraries.identity.googleid:googleid:1.1.0@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21@jar,androidx.sqlite:sqlite-framework:2.4.0@aar,androidx.sqlite:sqlite:2.4.0@aar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.25@jar,org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.6.3@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21@jar,androidx.annotation:annotation-experimental:1.4.1@aar,com.github.PhilJay:MPAndroidChart:v3.1.0@aar,androidx.security:security-crypto:1.1.0-alpha06@aar,androidx.documentfile:documentfile:1.0.1@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.cardview:cardview:1.0.0@aar,androidx.profileinstaller:profileinstaller:1.4.0@aar,androidx.constraintlayout:constraintlayout-core:1.1.0@jar,androidx.interpolator:interpolator:1.0.0@aar,com.google.android.exoplayer:exoplayer-datasource:2.19.1@aar,com.google.android.exoplayer:exoplayer-database:2.19.1@aar,com.google.android.exoplayer:exoplayer-extractor:2.19.1@aar,com.google.android.exoplayer:exoplayer-decoder:2.19.1@aar,com.google.android.exoplayer:exoplayer-container:2.19.1@aar,com.google.android.exoplayer:exoplayer-common:2.19.1@aar,androidx.arch.core:core-runtime:2.2.0@aar,com.github.bumptech.glide:gifdecoder:4.16.0@aar,androidx.exifinterface:exifinterface:1.3.6@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,com.google.firebase:firebase-components:18.0.0@aar,com.google.android.datatransport:transport-runtime:3.3.0@aar,com.google.android.datatransport:transport-api:3.2.0@aar,com.google.firebase:firebase-encoders-proto:16.0.0@jar,com.google.firebase:firebase-encoders:17.0.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.arch.core:core-common:2.2.0@jar,androidx.print:print:1.0.0@aar,androidx.collection:collection-ktx:1.4.4@jar,androidx.collection:collection-jvm:1.4.4@jar,androidx.annotation:annotation-jvm:1.8.1@jar,org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar,com.google.crypto.tink:tink-android:1.8.0@jar,io.grpc:grpc-android:1.62.2@aar,io.grpc:grpc-util:1.62.2@jar,io.grpc:grpc-core:1.62.2@jar,com.google.code.gson:gson:2.11.0@jar,jp.wasabeef:richeditor-android:2.0.0@aar,org.jetbrains:annotations:23.0.0@jar,io.grpc:grpc-stub:1.62.2@jar,io.grpc:grpc-protobuf-lite:1.62.2@jar,io.grpc:grpc-context:1.62.2@jar,io.grpc:grpc-api:1.62.2@jar,com.google.guava:guava:32.1.3-android@jar,com.google.errorprone:error_prone_annotations:2.27.0@jar,com.google.dagger:hilt-core:2.52@jar,com.google.dagger:dagger:2.52@jar,com.google.dagger:dagger-lint-aar:2.52@aar,com.google.code.findbugs:jsr305:3.0.2@jar,com.google.firebase:firebase-annotations:16.2.0@jar,javax.inject:javax.inject:1@jar,com.github.bumptech.glide:disklrucache:4.16.0@jar,com.github.bumptech.glide:annotations:4.16.0@jar,com.google.firebase:protolite-well-known-types:18.0.0@aar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,jakarta.inject:jakarta.inject-api:2.0.1@jar,com.google.android.play:core-common:2.0.3@aar,com.google.protobuf:protobuf-javalite:3.25.1@jar,io.perfmark:perfmark-api:0.26.0@jar,com.google.guava:failureaccess:1.0.1@jar,org.checkerframework:checker-qual:3.37.0@jar,com.google.android:annotations:4.1.1.4@jar,org.codehaus.mojo:animal-sniffer-annotations:1.23@jar">
    <dependency
        name="androidx.databinding:databinding-adapters:8.8.2@aar"
        simpleName="androidx.databinding:databinding-adapters"/>
    <dependency
        name="androidx.databinding:databinding-ktx:8.8.2@aar"
        simpleName="androidx.databinding:databinding-ktx"/>
    <dependency
        name="androidx.databinding:databinding-runtime:8.8.2@aar"
        simpleName="androidx.databinding:databinding-runtime"/>
    <dependency
        name="androidx.databinding:viewbinding:8.8.2@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="androidx.databinding:databinding-common:8.8.2@jar"
        simpleName="androidx.databinding:databinding-common"/>
    <dependency
        name="androidx.hilt:hilt-navigation-compose:1.1.0@aar"
        simpleName="androidx.hilt:hilt-navigation-compose"/>
    <dependency
        name="androidx.hilt:hilt-navigation:1.1.0@aar"
        simpleName="androidx.hilt:hilt-navigation"/>
    <dependency
        name="androidx.navigation:navigation-common:2.8.2@aar"
        simpleName="androidx.navigation:navigation-common"/>
    <dependency
        name="androidx.navigation:navigation-runtime:2.8.2@aar"
        simpleName="androidx.navigation:navigation-runtime"/>
    <dependency
        name="androidx.navigation:navigation-common-ktx:2.8.2@aar"
        simpleName="androidx.navigation:navigation-common-ktx"/>
    <dependency
        name="androidx.navigation:navigation-ui:2.8.2@aar"
        simpleName="androidx.navigation:navigation-ui"/>
    <dependency
        name="androidx.navigation:navigation-fragment:2.8.2@aar"
        simpleName="androidx.navigation:navigation-fragment"/>
    <dependency
        name="androidx.navigation:navigation-runtime-ktx:2.8.2@aar"
        simpleName="androidx.navigation:navigation-runtime-ktx"/>
    <dependency
        name="androidx.navigation:navigation-ui-ktx:2.8.2@aar"
        simpleName="androidx.navigation:navigation-ui-ktx"/>
    <dependency
        name="androidx.navigation:navigation-fragment-ktx:2.8.2@aar"
        simpleName="androidx.navigation:navigation-fragment-ktx"/>
    <dependency
        name="androidx.navigation:navigation-compose:2.8.2@aar"
        simpleName="androidx.navigation:navigation-compose"/>
    <dependency
        name="com.google.android.material:material:1.12.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.2.0@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="com.github.chrisbanes:PhotoView:2.3.0@aar"
        simpleName="com.github.chrisbanes:PhotoView"/>
    <dependency
        name="io.coil-kt:coil-compose:2.5.0@aar"
        simpleName="io.coil-kt:coil-compose"/>
    <dependency
        name="io.coil-kt:coil-compose-base:2.5.0@aar"
        simpleName="io.coil-kt:coil-compose-base"/>
    <dependency
        name="io.coil-kt:coil:2.5.0@aar"
        simpleName="io.coil-kt:coil"/>
    <dependency
        name="io.coil-kt:coil-base:2.5.0@aar"
        simpleName="io.coil-kt:coil-base"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.google.firebase:firebase-auth:23.1.0@aar"
        simpleName="com.google.firebase:firebase-auth"/>
    <dependency
        name="androidx.credentials:credentials:1.2.0-rc01@aar"
        simpleName="androidx.credentials:credentials"/>
    <dependency
        name="androidx.credentials:credentials-play-services-auth:1.2.0-rc01@aar"
        simpleName="androidx.credentials:credentials-play-services-auth"/>
    <dependency
        name="com.google.android.gms:play-services-auth:21.2.0@aar"
        simpleName="com.google.android.gms:play-services-auth"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-ui:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-ui"/>
    <dependency
        name="androidx.paging:paging-common-android:3.3.2@aar"
        simpleName="androidx.paging:paging-common-android"/>
    <dependency
        name="androidx.paging:paging-common-ktx:3.3.2@jar"
        simpleName="androidx.paging:paging-common-ktx"/>
    <dependency
        name="androidx.paging:paging-runtime-ktx:3.3.2@aar"
        simpleName="androidx.paging:paging-runtime-ktx"/>
    <dependency
        name="androidx.paging:paging-runtime:3.3.2@aar"
        simpleName="androidx.paging:paging-runtime"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.3.1@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.1.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="com.google.dagger:hilt-android:2.52@aar"
        simpleName="com.google.dagger:hilt-android"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.16.0@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="com.google.firebase:firebase-firestore:25.1.1@aar"
        simpleName="com.google.firebase:firebase-firestore"/>
    <dependency
        name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-auth-api-phone"/>
    <dependency
        name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
        simpleName="com.google.android.gms:play-services-auth-base"/>
    <dependency
        name="com.google.firebase:firebase-analytics:22.1.2@aar"
        simpleName="com.google.firebase:firebase-analytics"/>
    <dependency
        name="com.google.android.gms:play-services-measurement:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-api:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-api"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk"/>
    <dependency
        name="com.google.firebase:firebase-crashlytics:19.2.1@aar"
        simpleName="com.google.firebase:firebase-crashlytics"/>
    <dependency
        name="com.google.firebase:firebase-storage:21.0.1@aar"
        simpleName="com.google.firebase:firebase-storage"/>
    <dependency
        name="com.google.android.recaptcha:recaptcha:18.5.1@aar"
        simpleName="com.google.android.recaptcha:recaptcha"/>
    <dependency
        name="com.google.android.play:integrity:1.3.0@aar"
        simpleName="com.google.android.play:integrity"/>
    <dependency
        name="com.google.firebase:firebase-auth-interop:20.0.0@aar"
        simpleName="com.google.firebase:firebase-auth-interop"/>
    <dependency
        name="com.google.firebase:firebase-sessions:2.0.6@aar"
        simpleName="com.google.firebase:firebase-sessions"/>
    <dependency
        name="com.google.firebase:firebase-installations:18.0.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-appcheck:18.0.0@aar"
        simpleName="com.google.firebase:firebase-appcheck"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.google.firebase:firebase-common:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.2.0@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="com.google.firebase:firebase-appcheck-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-appcheck-interop"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-impl:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-impl"/>
    <dependency
        name="com.google.android.gms:play-services-fido:20.1.0@aar"
        simpleName="com.google.android.gms:play-services-fido"/>
    <dependency
        name="me.saket.swipe:swipe:1.2.0@aar"
        simpleName="me.saket.swipe:swipe"/>
    <dependency
        name="androidx.compose.material3:material3-android:1.2.0@aar"
        simpleName="androidx.compose.material3:material3-android"/>
    <dependency
        name="androidx.compose.material:material-icons-core-android:1.6.1@aar"
        simpleName="androidx.compose.material:material-icons-core-android"/>
    <dependency
        name="androidx.compose.material:material-icons-extended-android:1.6.1@aar"
        simpleName="androidx.compose.material:material-icons-extended-android"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.6.1@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.7.2@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.7.2@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.7.2@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.7.2@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="com.google.accompanist:accompanist-drawablepainter:0.32.0@aar"
        simpleName="com.google.accompanist:accompanist-drawablepainter"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.7.2@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.7.2@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.7.2@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.7.2@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.7.2@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.7.2@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.7.2@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.activity:activity:1.9.2@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.transition:transition:1.5.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.1.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
        simpleName="androidx.customview:customview-poolingcontainer"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices-java"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-dash:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-dash"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-hls:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-hls"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-rtsp:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-rtsp"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-smoothstreaming"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-core:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-core"/>
    <dependency
        name="androidx.work:work-runtime-ktx:2.9.1@aar"
        simpleName="androidx.work:work-runtime-ktx"/>
    <dependency
        name="androidx.work:work-runtime:2.9.1@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.browser:browser:1.4.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.media:media:1.6.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.autofill:autofill:1.0.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="androidx.window:window:1.0.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="com.google.firebase:firebase-database-collection:18.0.1@aar"
        simpleName="com.google.firebase:firebase-database-collection"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="androidx.graphics:graphics-path:1.0.1@aar"
        simpleName="androidx.graphics:graphics-path"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.8.6@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.6@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.7.2@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.7.2@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-livedata:1.7.2@aar"
        simpleName="androidx.compose.runtime:runtime-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-compose-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-ktx:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.room:room-common:2.6.1@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.room:room-runtime:2.6.1@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.room:room-ktx:2.6.1@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.datastore:datastore-preferences:1.0.0@aar"
        simpleName="androidx.datastore:datastore-preferences"/>
    <dependency
        name="androidx.datastore:datastore:1.0.0@aar"
        simpleName="androidx.datastore:datastore"/>
    <dependency
        name="androidx.datastore:datastore-preferences-core:1.0.0@jar"
        simpleName="androidx.datastore:datastore-preferences-core"/>
    <dependency
        name="androidx.datastore:datastore-core:1.0.0@jar"
        simpleName="androidx.datastore:datastore-core"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.8.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:20.0.1@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
        simpleName="com.google.android.gms:play-services-ads-identifier"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk-api:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk-api"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-base:22.1.2@aar"
        simpleName="com.google.android.gms:play-services-measurement-base"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.4.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.8.4@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.8.4@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.activity:activity-ktx:1.9.2@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity-compose:1.9.2@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="androidx.core:core-splashscreen:1.0.1@aar"
        simpleName="androidx.core:core-splashscreen"/>
    <dependency
        name="com.jakewharton.timber:timber:5.0.1@aar"
        simpleName="com.jakewharton.timber:timber"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.25@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
        simpleName="com.squareup.okhttp3:logging-interceptor"/>
    <dependency
        name="com.squareup.retrofit2:converter-gson:2.11.0@jar"
        simpleName="com.squareup.retrofit2:converter-gson"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:2.11.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.google.firebase:firebase-config-interop:16.0.1@aar"
        simpleName="com.google.firebase:firebase-config-interop"/>
    <dependency
        name="com.google.firebase:firebase-datatransport:19.0.0@aar"
        simpleName="com.google.firebase:firebase-datatransport"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:3.3.0@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.1@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="io.grpc:grpc-okhttp:1.62.2@jar"
        simpleName="io.grpc:grpc-okhttp"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="com.google.android.libraries.identity.googleid:googleid:1.1.0@aar"
        simpleName="com.google.android.libraries.identity.googleid:googleid"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.sqlite:sqlite:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.25@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.6.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="com.github.PhilJay:MPAndroidChart:v3.1.0@aar"
        simpleName="com.github.PhilJay:MPAndroidChart"/>
    <dependency
        name="androidx.security:security-crypto:1.1.0-alpha06@aar"
        simpleName="androidx.security:security-crypto"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.1@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-core:1.1.0@jar"
        simpleName="androidx.constraintlayout:constraintlayout-core"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-datasource:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-datasource"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-database:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-database"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-extractor:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-extractor"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-decoder:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-decoder"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-container:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-container"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-common:2.19.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-common"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.6@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="com.google.firebase:firebase-components:18.0.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:3.3.0@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.android.datatransport:transport-api:3.2.0@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders-proto"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.collection:collection-ktx:1.4.4@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.4@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="com.google.crypto.tink:tink-android:1.8.0@jar"
        simpleName="com.google.crypto.tink:tink-android"/>
    <dependency
        name="io.grpc:grpc-android:1.62.2@aar"
        simpleName="io.grpc:grpc-android"/>
    <dependency
        name="io.grpc:grpc-util:1.62.2@jar"
        simpleName="io.grpc:grpc-util"/>
    <dependency
        name="io.grpc:grpc-core:1.62.2@jar"
        simpleName="io.grpc:grpc-core"/>
    <dependency
        name="com.google.code.gson:gson:2.11.0@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="jp.wasabeef:richeditor-android:2.0.0@aar"
        simpleName="jp.wasabeef:richeditor-android"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="io.grpc:grpc-stub:1.62.2@jar"
        simpleName="io.grpc:grpc-stub"/>
    <dependency
        name="io.grpc:grpc-protobuf-lite:1.62.2@jar"
        simpleName="io.grpc:grpc-protobuf-lite"/>
    <dependency
        name="io.grpc:grpc-context:1.62.2@jar"
        simpleName="io.grpc:grpc-context"/>
    <dependency
        name="io.grpc:grpc-api:1.62.2@jar"
        simpleName="io.grpc:grpc-api"/>
    <dependency
        name="com.google.guava:guava:32.1.3-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.27.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="com.google.dagger:hilt-core:2.52@jar"
        simpleName="com.google.dagger:hilt-core"/>
    <dependency
        name="com.google.dagger:dagger:2.52@jar"
        simpleName="com.google.dagger:dagger"/>
    <dependency
        name="com.google.dagger:dagger-lint-aar:2.52@aar"
        simpleName="com.google.dagger:dagger-lint-aar"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.16.0@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="com.google.firebase:protolite-well-known-types:18.0.0@aar"
        simpleName="com.google.firebase:protolite-well-known-types"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="jakarta.inject:jakarta.inject-api:2.0.1@jar"
        simpleName="jakarta.inject:jakarta.inject-api"/>
    <dependency
        name="com.google.android.play:core-common:2.0.3@aar"
        simpleName="com.google.android.play:core-common"/>
    <dependency
        name="com.google.protobuf:protobuf-javalite:3.25.1@jar"
        simpleName="com.google.protobuf:protobuf-javalite"/>
    <dependency
        name="io.perfmark:perfmark-api:0.26.0@jar"
        simpleName="io.perfmark:perfmark-api"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="org.checkerframework:checker-qual:3.37.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
    <dependency
        name="com.google.android:annotations:4.1.1.4@jar"
        simpleName="com.google.android:annotations"/>
    <dependency
        name="org.codehaus.mojo:animal-sniffer-annotations:1.23@jar"
        simpleName="org.codehaus.mojo:animal-sniffer-annotations"/>
  </package>
</dependencies>
