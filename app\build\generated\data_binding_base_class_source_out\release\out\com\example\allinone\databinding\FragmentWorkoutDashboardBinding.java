// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentWorkoutDashboardBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView recentWorkoutDate;

  @NonNull
  public final TextView recentWorkoutName;

  @NonNull
  public final SwipeRefreshLayout swipeRefreshLayout;

  @NonNull
  public final TextView totalWorkoutCount;

  @NonNull
  public final TextView totalWorkoutDuration;

  @NonNull
  public final TextView weeklyWorkoutCount;

  @NonNull
  public final TextView weeklyWorkoutDuration;

  private FragmentWorkoutDashboardBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView recentWorkoutDate, @NonNull TextView recentWorkoutName,
      @NonNull SwipeRefreshLayout swipeRefreshLayout, @NonNull TextView totalWorkoutCount,
      @NonNull TextView totalWorkoutDuration, @NonNull TextView weeklyWorkoutCount,
      @NonNull TextView weeklyWorkoutDuration) {
    this.rootView = rootView;
    this.recentWorkoutDate = recentWorkoutDate;
    this.recentWorkoutName = recentWorkoutName;
    this.swipeRefreshLayout = swipeRefreshLayout;
    this.totalWorkoutCount = totalWorkoutCount;
    this.totalWorkoutDuration = totalWorkoutDuration;
    this.weeklyWorkoutCount = weeklyWorkoutCount;
    this.weeklyWorkoutDuration = weeklyWorkoutDuration;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentWorkoutDashboardBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentWorkoutDashboardBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_workout_dashboard, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentWorkoutDashboardBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.recent_workout_date;
      TextView recentWorkoutDate = ViewBindings.findChildViewById(rootView, id);
      if (recentWorkoutDate == null) {
        break missingId;
      }

      id = R.id.recent_workout_name;
      TextView recentWorkoutName = ViewBindings.findChildViewById(rootView, id);
      if (recentWorkoutName == null) {
        break missingId;
      }

      id = R.id.swipe_refresh_layout;
      SwipeRefreshLayout swipeRefreshLayout = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefreshLayout == null) {
        break missingId;
      }

      id = R.id.total_workout_count;
      TextView totalWorkoutCount = ViewBindings.findChildViewById(rootView, id);
      if (totalWorkoutCount == null) {
        break missingId;
      }

      id = R.id.total_workout_duration;
      TextView totalWorkoutDuration = ViewBindings.findChildViewById(rootView, id);
      if (totalWorkoutDuration == null) {
        break missingId;
      }

      id = R.id.weekly_workout_count;
      TextView weeklyWorkoutCount = ViewBindings.findChildViewById(rootView, id);
      if (weeklyWorkoutCount == null) {
        break missingId;
      }

      id = R.id.weekly_workout_duration;
      TextView weeklyWorkoutDuration = ViewBindings.findChildViewById(rootView, id);
      if (weeklyWorkoutDuration == null) {
        break missingId;
      }

      return new FragmentWorkoutDashboardBinding((ConstraintLayout) rootView, recentWorkoutDate,
          recentWorkoutName, swipeRefreshLayout, totalWorkoutCount, totalWorkoutDuration,
          weeklyWorkoutCount, weeklyWorkoutDuration);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
