{"options": {"hasObfuscationDictionary": false, "hasClassObfuscationDictionary": false, "hasPackageObfuscationDictionary": false, "keepAttributes": {"isAnnotationDefaultKept": true, "isEnclosingMethodKept": true, "isExceptionsKept": false, "isInnerClassesKept": true, "isLocalVariableTableKept": false, "isLocalVariableTypeTableKept": false, "isMethodParametersKept": false, "isPermittedSubclassesKept": false, "isRuntimeInvisibleAnnotationsKept": true, "isRuntimeInvisibleParameterAnnotationsKept": true, "isRuntimeInvisibleTypeAnnotationsKept": true, "isRuntimeVisibleAnnotationsKept": true, "isRuntimeVisibleParameterAnnotationsKept": true, "isRuntimeVisibleTypeAnnotationsKept": true, "isSignatureKept": true, "isSourceDebugExtensionKept": false, "isSourceDirKept": false, "isSourceFileKept": false, "isStackMapTableKept": false}, "isAccessModificationEnabled": true, "isFlattenPackageHierarchyEnabled": false, "isObfuscationEnabled": true, "isOptimizationsEnabled": true, "isProGuardCompatibilityModeEnabled": false, "isProtoLiteOptimizationEnabled": false, "isRepackageClassesEnabled": false, "isShrinkingEnabled": true, "apiModeling": {}, "minApiLevel": "24", "isDebugModeEnabled": false}, "baselineProfileRewriting": {}, "compilation": {"buildTimeNs": 149267412400, "numberOfThreads": 6}, "dexFiles": [{"checksum": "12cd44f734b65e6807fa214f47f12b0692582de76765e28ff17517fa2c20897b", "startup": false}, {"checksum": "f6ffa06a4ccc4614fd3b0de0a90b01af794e878e40193444d75f12ab9f20f0ad", "startup": false}, {"checksum": "cb4780c8199e597db5c6c14213c80a9874ae259d04bbbb871f464b66c0db2cc5", "startup": false}], "stats": {"noObfuscationPercentage": 63.56, "noOptimizationPercentage": 65.77, "noShrinkingPercentage": 63.85}, "version": "8.8.34"}