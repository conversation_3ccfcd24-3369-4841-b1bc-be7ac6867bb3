// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.radiobutton.MaterialRadioButton;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogProfitLossBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialRadioButton lossRadio;

  @NonNull
  public final TextInputEditText profitLossAmountInput;

  @NonNull
  public final MaterialRadioButton profitRadio;

  private DialogProfitLossBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialRadioButton lossRadio, @NonNull TextInputEditText profitLossAmountInput,
      @NonNull MaterialRadioButton profitRadio) {
    this.rootView = rootView;
    this.lossRadio = lossRadio;
    this.profitLossAmountInput = profitLossAmountInput;
    this.profitRadio = profitRadio;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogProfitLossBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogProfitLossBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_profit_loss, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogProfitLossBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.lossRadio;
      MaterialRadioButton lossRadio = ViewBindings.findChildViewById(rootView, id);
      if (lossRadio == null) {
        break missingId;
      }

      id = R.id.profitLossAmountInput;
      TextInputEditText profitLossAmountInput = ViewBindings.findChildViewById(rootView, id);
      if (profitLossAmountInput == null) {
        break missingId;
      }

      id = R.id.profitRadio;
      MaterialRadioButton profitRadio = ViewBindings.findChildViewById(rootView, id);
      if (profitRadio == null) {
        break missingId;
      }

      return new DialogProfitLossBinding((LinearLayout) rootView, lossRadio, profitLossAmountInput,
          profitRadio);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
