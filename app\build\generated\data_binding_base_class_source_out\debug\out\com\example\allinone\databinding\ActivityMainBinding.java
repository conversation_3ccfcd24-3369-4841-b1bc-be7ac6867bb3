// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.FragmentContainerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.navigation.NavigationView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final DrawerLayout rootView;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final BottomNavigationView bottomNavigation;

  @NonNull
  public final DrawerLayout drawerLayout;

  @NonNull
  public final FragmentContainerView navHostFragment;

  @NonNull
  public final NavigationView navView;

  @NonNull
  public final ThemeSwitchLayoutBinding themeToggleLayout;

  @NonNull
  public final MaterialToolbar toolbar;

  private ActivityMainBinding(@NonNull DrawerLayout rootView, @NonNull AppBarLayout appBarLayout,
      @NonNull BottomNavigationView bottomNavigation, @NonNull DrawerLayout drawerLayout,
      @NonNull FragmentContainerView navHostFragment, @NonNull NavigationView navView,
      @NonNull ThemeSwitchLayoutBinding themeToggleLayout, @NonNull MaterialToolbar toolbar) {
    this.rootView = rootView;
    this.appBarLayout = appBarLayout;
    this.bottomNavigation = bottomNavigation;
    this.drawerLayout = drawerLayout;
    this.navHostFragment = navHostFragment;
    this.navView = navView;
    this.themeToggleLayout = themeToggleLayout;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public DrawerLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appBarLayout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.bottomNavigation;
      BottomNavigationView bottomNavigation = ViewBindings.findChildViewById(rootView, id);
      if (bottomNavigation == null) {
        break missingId;
      }

      DrawerLayout drawerLayout = (DrawerLayout) rootView;

      id = R.id.nav_host_fragment;
      FragmentContainerView navHostFragment = ViewBindings.findChildViewById(rootView, id);
      if (navHostFragment == null) {
        break missingId;
      }

      id = R.id.nav_view;
      NavigationView navView = ViewBindings.findChildViewById(rootView, id);
      if (navView == null) {
        break missingId;
      }

      id = R.id.themeToggleLayout;
      View themeToggleLayout = ViewBindings.findChildViewById(rootView, id);
      if (themeToggleLayout == null) {
        break missingId;
      }
      ThemeSwitchLayoutBinding binding_themeToggleLayout = ThemeSwitchLayoutBinding.bind(themeToggleLayout);

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityMainBinding((DrawerLayout) rootView, appBarLayout, bottomNavigation,
          drawerLayout, navHostFragment, navView, binding_themeToggleLayout, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
