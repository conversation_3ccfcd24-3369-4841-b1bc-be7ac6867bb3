<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="Amount"
        app:boxStrokeColor="@color/text_input_box_stroke"
        app:hintTextColor="@color/text_input_text_color">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/profitLossAmountInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="numberDecimal"
            android:textColor="@color/text_input_text_color" />
    </com.google.android.material.textfield.TextInputLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="Type:"
        android:textAppearance="?attr/textAppearanceBody1" />

    <RadioGroup
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <com.google.android.material.radiobutton.MaterialRadioButton
            android:id="@+id/profitRadio"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:checked="true"
            android:text="Profit"
            android:textColor="@android:color/holo_green_dark" />

        <com.google.android.material.radiobutton.MaterialRadioButton
            android:id="@+id/lossRadio"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Loss"
            android:textColor="@android:color/holo_red_dark" />
    </RadioGroup>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Note: This will only update the investment's profit/loss tracking and won't affect your transaction balance."
        android:textAppearance="?attr/textAppearanceCaption"
        android:textColor="?attr/colorOnSurfaceVariant" />

</LinearLayout> 