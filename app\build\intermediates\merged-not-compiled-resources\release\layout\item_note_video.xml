<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="120dp"
    android:layout_height="120dp"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/videoThumbnail"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_video_placeholder"
            android:contentDescription="Video thumbnail" />

        <!-- Play button overlay -->
        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_play_circle"
            android:contentDescription="Play video"
            android:alpha="0.8" />

        <!-- Progress bar for loading -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center"
            android:visibility="gone" />

        <!-- Delete button -->
        <ImageButton
            android:id="@+id/deleteButton"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_gravity="top|end"
            android:layout_margin="4dp"
            android:background="@drawable/circle_background_red"
            android:src="@drawable/ic_close"
            android:contentDescription="Delete video"
            android:padding="6dp"
            app:tint="@android:color/white" />

    </FrameLayout>

</androidx.cardview.widget.CardView> 