  ExampleUnitTest com.example.allinone  assertEquals com.example.allinone  Test $com.example.allinone.ExampleUnitTest  assertEquals $com.example.allinone.ExampleUnitTest  getASSERTEquals $com.example.allinone.ExampleUnitTest  getAssertEquals $com.example.allinone.ExampleUnitTest  assertEquals 	java.lang  Int kotlin  assertEquals kotlin  assertEquals kotlin.annotation  assertEquals kotlin.collections  assertEquals kotlin.comparisons  assertEquals 	kotlin.io  assertEquals 
kotlin.jvm  assertEquals 
kotlin.ranges  assertEquals kotlin.sequences  assertEquals kotlin.text  Assert 	org.junit  Test 	org.junit  assertEquals org.junit.Assert                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   