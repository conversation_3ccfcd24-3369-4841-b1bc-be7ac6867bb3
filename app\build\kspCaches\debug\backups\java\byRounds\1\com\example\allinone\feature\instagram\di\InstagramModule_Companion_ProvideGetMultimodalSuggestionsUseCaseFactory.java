package com.example.allinone.feature.instagram.di;

import com.example.allinone.feature.instagram.domain.usecase.GetMultimodalSuggestionsUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class InstagramModule_Companion_ProvideGetMultimodalSuggestionsUseCaseFactory implements Factory<GetMultimodalSuggestionsUseCase> {
  @Override
  public GetMultimodalSuggestionsUseCase get() {
    return provideGetMultimodalSuggestionsUseCase();
  }

  public static InstagramModule_Companion_ProvideGetMultimodalSuggestionsUseCaseFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static GetMultimodalSuggestionsUseCase provideGetMultimodalSuggestionsUseCase() {
    return Preconditions.checkNotNullFromProvides(InstagramModule.Companion.provideGetMultimodalSuggestionsUseCase());
  }

  private static final class InstanceHolder {
    private static final InstagramModule_Companion_ProvideGetMultimodalSuggestionsUseCaseFactory INSTANCE = new InstagramModule_Companion_ProvideGetMultimodalSuggestionsUseCaseFactory();
  }
}
