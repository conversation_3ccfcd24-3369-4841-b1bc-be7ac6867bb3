<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">AllInOne</string>
    <string name="amount">Amount</string>
    <string name="type">Type</string>
    <string name="description">Description</string>
    <string name="save_changes">Save Changes</string>
    <string name="type_input_description">Select transaction type</string>
    <string name="select_type">Select transaction type</string>
    <string name="investment_name">Investment Name</string>
    <string name="investment_type">Investment Type</string>
    <string name="add_image">Add Image</string>
    <string name="add_video">Add Video</string>
    <string name="add_attachment">Add Attachment</string>
    <string name="delete_image">Delete Image</string>
    <string name="delete_video">Delete Video</string>
    <string name="delete_video_confirmation">Are you sure you want to delete this video?</string>
    <string name="delete_image_confirmation">Are you sure you want to delete this image?</string>
    <string name="cancel">Cancel</string>
    <string name="save">Save</string>

    <!-- Drawing tool -->
    <string name="create_drawing">Create Drawing</string>
    <string name="drawing_options">Drawing Options</string>
    <string name="save_to_note">Save to Note Only</string>
    <string name="save_to_note_and_gallery">Save to Note and Gallery</string>
    <string name="drawing_added">Drawing added to note</string>
    <string name="error_adding_drawing">Error adding drawing</string>
    <string name="brush_size">Brush Size:</string>
    <string name="clear">Clear</string>
    <string name="drawing_saved_to_gallery">Drawing saved to gallery</string>
    <string name="error_saving_drawing">Error saving drawing: %1$s</string>
    <string name="choose_color">Choose a Color</string>
    <string name="select_color">Select Color</string>
    <string name="selected_color">Selected:</string>
    <string name="select">Select</string>
    <string name="color">Color:</string>

    <!-- Drawing color names -->
    <string name="color_black">Black Color</string>
    <string name="color_red">Red Color</string>
    <string name="color_green">Green Color</string>
    <string name="color_blue">Blue Color</string>
    <string name="color_yellow">Yellow Color</string>
    <string name="color_purple">Purple Color</string>
    <string name="color_orange">Orange Color</string>
    <string name="color_brown">Brown Color</string>
    <string name="color_teal">Teal Color</string>
    <string name="color_pink">Pink Color</string>

    <!-- Google Play Services -->
    <string name="app_id">954911141967</string>

    <!-- Offline status -->
    <string name="offline_mode">Offline Mode</string>
    <string name="offline_message">You are currently offline. Changes will be synchronized when you reconnect.</string>
    <string name="pending_operations">Pending operations: %1$d</string>
    <string name="offline_status">Offline status</string>

    <!-- History page -->
    <string name="history">History</string>
    <string name="empty_history">Empty history</string>
    <string name="no_history_items">No history items found</string>
    <string name="pull_to_refresh">Pull down to refresh</string>
    <string name="item_type_icon">Item type icon</string>
    <string name="delete_item">Delete item</string>

    <!-- Navigation Titles -->
    <string name="title_investments">Investments</string>
    <string name="title_notes">Notes</string>
    <string name="title_wing_tzun_registry">Wing Tzun Registry</string>
    <string name="title_students">Students</string>
    <string name="title_register">Register</string>
    <string name="title_history">History</string>
    <string name="title_lesson_schedule">Lesson Schedule</string>
    <string name="title_calendar">Calendar</string>
    <string name="profile_image">Profile Image</string>
    <string name="add_photo">Add Photo</string>
    <string name="name">Name</string>
    <string name="phone_number">Phone Number</string>
    <string name="email">E-mail</string>
    <string name="instagram">Instagram</string>
    <string name="active_status">Active Status</string>
    <string name="add_student">Add Student</string>
    <string name="edit_student">Edit Student</string>
    <string name="required_fields_missing">Name and Phone are required fields</string>
    <string name="no_students">No Students</string>
    <string name="add_student_prompt">Add students to start registering them for courses</string>

    <!-- Course Registration -->
    <string name="add_registration">Add New Registration</string>
    <string name="edit_registration">Edit Registration</string>
    <string name="select_student">Select Student</string>
    <string name="start_date">Start Date</string>
    <string name="end_date">End Date</string>
    <string name="payment_received">Payment Received</string>
    <string name="registration_success">Student registered for course successfully</string>
    <string name="registration_updated">Registration updated successfully</string>
    <string name="payment_receipt">Payment Receipt</string>
    <string name="add_receipt">Add Receipt</string>
    <string name="receipt_unavailable">Receipt unavailable</string>
    <string name="share_registration">Share Registration Info</string>
    <string name="please_select_student">Please select a student</string>
    <string name="please_select_start_date">Please select start date</string>
    <string name="please_select_end_date">Please select end date</string>
    <string name="please_enter_valid_amount">Please enter valid amount</string>
    <string name="attachments_only_for_paid_registrations">Attachments can only be added for paid registrations</string>
    <string name="no_registrations">No Course Registrations</string>
    <string name="no_registrations_prompt">Add students to courses by clicking the + button</string>
    <string name="select_month">Select Month</string>
    <string name="apply">Apply</string>
    <string name="all_months">All Months</string>
    <string name="total">Total</string>
    <string name="total_amount_label">Total Amount:</string>
    <string name="amount_format">₺%.2f</string>

    <!-- Photo Selection -->
    <string name="select_profile_photo">Select Profile Photo</string>
    <string name="take_photo">Take Photo</string>
    <string name="choose_from_gallery">Choose from Gallery</string>
    <string name="camera_permission_title">Camera Permission</string>
    <string name="camera_permission_message">Camera permission is needed to take photos for student profiles.</string>
    <string name="storage_permission_title">Storage Permission</string>
    <string name="storage_permission_message">Storage permission is needed to select photos for student profiles.</string>
    <string name="grant_permission">Grant Permission</string>
    <string name="permission_denied">%1$s permission denied. Unable to proceed.</string>
    <string name="error_camera">Error preparing camera: %1$s</string>
    <string name="status_paid_desc">Paid. Click to mark as unpaid</string>
    <string name="status_unpaid_desc">Unpaid. Click to mark as paid</string>

    <!-- Form validation -->
    <string name="field_required">This field is required</string>

    <!-- New strings from the code block -->
    <string name="edit">Edit</string>
    <string name="delete">Delete</string>
    <string name="delete_registration">Delete Registration</string>
    <string name="delete_registration_confirmation">Are you sure you want to delete %1$s\'s registration?</string>
    <string name="registration_deleted">Registration deleted successfully</string>
    <string name="error_deleting_registration">Error deleting registration</string>
    <string name="deleting">Deleting...</string>
    <string name="retry">Retry</string>
    <string name="create_note">Create Note</string>
    <string name="edit_note">Edit Note</string>
    <string name="please_enter_title">Please enter a title</string>
    <string name="note_saved">Note saved</string>
    <string name="note_updated">Note updated</string>
    <string name="note_deleted">Note deleted</string>
    <string name="share_note">Share Note</string>
    <string name="attachment_click_to_open">📄 Attachment: %1$s (click to open)</string>
    <string name="tap_to_view_attachment">Tap to view attachment</string>
    <string name="no_app_for_file_type">No app found to open this file type</string>
    <string name="error_opening_file">Error opening file: %1$s</string>
    <string name="title_seminars">Seminars</string>
    <string name="status_inactive_desc">Inactive student</string>
    <string name="status_registered_desc">Active student with current registration</string>
    <string name="status_active_desc">Active student without current registration</string>
    <string name="network_unavailable">Network unavailable. Using cached data.</string>
    <string name="notes">Notes</string>
    <string name="tasks">Tasks</string>
    <string name="add_task">Add Task</string>
    <string name="no_tasks_yet">No tasks yet. Add your first one!</string>
    <string name="new_task">New Task</string>
    <string name="enter_task_description">Enter task description:</string>
    <string name="edit_task">Edit Task</string>
    <string name="task_description">Task description</string>
    <string name="mark_completed">Mark as completed</string>
    <string name="mark_incomplete">Mark as incomplete</string>
    <string name="delete_task">Delete Task</string>
    <string name="delete_task_confirmation">Are you sure you want to delete this task?</string>
    <string name="task_added">Task added</string>
    <string name="task_updated">Task updated</string>
    <string name="task_deleted">Task deleted</string>
    <string name="task_completed">Task completed</string>
    <string name="task_incomplete">Task marked as incomplete</string>

    <!-- Task Groups -->
    <string name="create_task_group">Create Task Group</string>
    <string name="edit_task_group">Edit Task Group</string>
    <string name="group_title">Group Title</string>
    <string name="group_description">Group Description (Optional)</string>
    <string name="group_color">Group Color</string>
    <string name="task_groups">Task Groups</string>
    <string name="manage_groups">Manage Groups</string>
    <string name="delete_group">Delete Group</string>
    <string name="delete_group_confirmation">Are you sure you want to delete this group? Tasks in this group will be moved to ungrouped.</string>
    <string name="group_created">Task group created</string>
    <string name="group_updated">Task group updated</string>
    <string name="group_deleted">Task group deleted</string>
    <string name="no_group">No Group</string>
    <string name="select_group">Select Group</string>
    <string name="expand_collapse">Expand/Collapse</string>
    <string name="toggle_view">Toggle View</string>
    <string name="switch_to_grouped_view">Switch to Grouped View</string>
    <string name="switch_to_list_view">Switch to List View</string>

    <!-- Error Messages -->
    <string name="error_network">Network error occurred. Please check your connection and try again.</string>
    <string name="error_network_unavailable">Network is unavailable. Please check your internet connection.</string>
    <string name="error_unknown">An unexpected error occurred. Please try again later.</string>
    <string name="error_firebase_network">Unable to connect to the server. Please check your connection.</string>

    <!-- Firebase Auth Errors -->
    <string name="error_authentication">Authentication failed. Please try again.</string>
    <string name="error_user_not_found">User account not found. Please check your credentials.</string>
    <string name="error_wrong_password">Incorrect password. Please try again.</string>
    <string name="error_user_disabled">This user account has been disabled.</string>
    <string name="error_too_many_requests">Too many attempts. Please try again later.</string>
    <string name="error_requires_recent_login">This operation requires recent authentication. Please log in again.</string>

    <!-- Firestore Errors -->
    <string name="error_database">Database error. Please try again later.</string>
    <string name="error_permission_denied">You don\'t have permission to perform this action.</string>
    <string name="error_service_unavailable">Service is temporarily unavailable. Please try again later.</string>
    <string name="error_document_already_exists">This record already exists.</string>
    <string name="error_document_not_found">The requested record was not found.</string>
    <string name="error_quota_exceeded">Service limit exceeded. Please try again later.</string>

    <!-- Storage Errors -->
    <string name="error_storage">Storage error. Please try again later.</string>
    <string name="error_file_not_found">The file you requested was not found.</string>
    <string name="error_storage_quota_exceeded">Storage quota exceeded.</string>
    <string name="error_not_authenticated">Authentication required. Please log in.</string>
    <string name="error_not_authorized">You are not authorized to perform this action.</string>

    <!-- Student Deletion -->
    <string name="delete_student">Delete Student</string>
    <string name="delete_student_confirmation">Are you sure you want to delete %1$s from your student list?</string>
    <string name="student_deleted">Student deleted successfully</string>
    <string name="cannot_delete">Cannot Delete</string>
    <string name="student_has_active_registrations">This student has active registrations and cannot be deleted.</string>
    <string name="ok">OK</string>

    <!-- Note Deletion -->
    <string name="delete_note">Delete Note</string>
    <string name="delete_note_confirmation">Are you sure you want to delete this note?</string>

    <!-- Voice Notes -->
    <string name="voice_notes">Voice Notes</string>
    <string name="voice_note_singular">1 voice note</string>
    <string name="voice_note_plural">%d voice notes</string>
    <string name="delete_voice_note">Delete Voice Note</string>
    <string name="delete_voice_note_confirmation">Are you sure you want to delete this voice note?</string>
    <string name="record">Record</string>
    <string name="recording">Recording…</string>
    <string name="stop">Stop</string>
    <string name="recording_started">Recording started</string>
    <string name="recording_saved">Recording saved</string>
    <string name="recording_failed">Failed to start recording</string>
    <string name="error_saving_recording">Failed to save recording</string>
    <string name="voice_note_deleted">Voice note deleted</string>
    <string name="error_playing_audio">Error playing audio</string>

    <!-- Upload and Saving -->
    <string name="saving">Saving</string>
    <string name="uploading_attachments">Uploading attachments...</string>

    <!-- Search -->
    <string name="search" translatable="false">Search</string>
    <string name="search_hint" translatable="false">Search...</string>

    <!-- New string from the code block -->
    <string name="brightness">Brightness:</string>

    <!-- Workout Feature -->
    <string name="workout">Workout</string>
    <string name="workout_dashboard">Dashboard</string>
    <string name="workout_exercise">Exercise</string>
    <string name="workout_program">Program</string>
    <string name="workout_log">Log</string>
    <string name="start_workout">Start Workout</string>
    <string name="pause_workout">Pause</string>
    <string name="stop_workout">Stop</string>
    <string name="save_workout">Save Workout</string>
    <string name="create_workout">Create Workout</string>
    <string name="add_program">Add Program</string>
    <string name="program_name">Program Name</string>
    <string name="exercise_name">Exercise Name</string>
    <string name="sets">Sets</string>
    <string name="reps">Reps</string>
    <string name="weight">Weight (kg)</string>
    <string name="notes_optional">Notes (Optional)</string>
    <string name="add_exercise">Add Exercise</string>
    <string name="remove_exercise">Remove Exercise</string>
    <string name="workout_timer">Workout Timer</string>
    <string name="select_program">Select Program</string>
    <string name="custom_workout">Custom Workout</string>
    <string name="weekly_summary">Weekly Summary</string>
    <string name="weekly_workout_count">Workouts this week: %d</string>
    <string name="weekly_workout_duration">Total duration: %s</string>
    <string name="most_recent_workout">Most Recent Workout</string>
    <string name="no_recent_workouts">No recent workouts</string>
    <string name="all_time_stats">All-Time Stats</string>
    <string name="total_workout_count">Total workouts: %d</string>
    <string name="total_workout_duration">Total duration: %s</string>
    <string name="no_programs">No programs yet. Create one by tapping the + button.</string>
    <string name="no_workout_history">No workout history yet. Complete a workout to see it here.</string>
    <string name="stop_workout_confirmation">Are you sure you want to stop this workout?</string>
    <string name="workout_saved">Workout saved</string>
    <string name="program_created">Program created</string>
    <string name="program_details">Program Details</string>
    <string name="workout_details">Workout Details</string>
    <string name="delete_workout">Delete Workout</string>
    <string name="delete_workout_confirmation">Are you sure you want to delete this workout from %1$s? This action cannot be undone.</string>
    <string name="workout_deleted">Workout deleted</string>
    <string name="delete_program">Delete Program</string>
    <string name="delete_program_confirmation">Are you sure you want to delete program %1$s? This action cannot be undone.</string>
    <string name="program_deleted">Program deleted successfully</string>
    <string name="edit_coming_soon">Edit functionality coming soon</string>
    <string name="close">Close</string>
    <string name="program_updated">Program updated successfully</string>

    <!-- Plurals -->
    <plurals name="exercise_count">
        <item quantity="one">1 exercise</item>
        <item quantity="other">%d exercises</item>
    </plurals>

    <!-- New strings from the code block -->
    <string name="workout_stats">Stats</string>
    <string name="workout_statistics">Workout Statistics</string>
    <string name="total_workouts">Total Workouts</string>
    <string name="avg_duration">Avg Duration</string>
    <string name="total_time">Total Time</string>
    <string name="favorite_muscle">Favorite Muscle</string>
    <string name="workout_history">Workout History</string>

    <!-- Image Viewer -->
    <string name="image_counter">%1$d/%2$d</string>

    <!-- New string from the code block -->
    <string name="task_name">Task name</string>

    <!-- New string from the code block -->
    <string name="not_set">Not set</string>
</resources>