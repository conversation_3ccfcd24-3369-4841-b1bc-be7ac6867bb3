// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import androidx.viewpager2.widget.ViewPager2;
import com.example.allinone.R;
import com.google.android.material.tabs.TabLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentFuturesBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TabLayout futuresTabLayout;

  @NonNull
  public final ViewPager2 futuresViewPager;

  private FragmentFuturesBinding(@NonNull ConstraintLayout rootView,
      @NonNull TabLayout futuresTabLayout, @NonNull ViewPager2 futuresViewPager) {
    this.rootView = rootView;
    this.futuresTabLayout = futuresTabLayout;
    this.futuresViewPager = futuresViewPager;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentFuturesBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentFuturesBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_futures, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentFuturesBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.futuresTabLayout;
      TabLayout futuresTabLayout = ViewBindings.findChildViewById(rootView, id);
      if (futuresTabLayout == null) {
        break missingId;
      }

      id = R.id.futuresViewPager;
      ViewPager2 futuresViewPager = ViewBindings.findChildViewById(rootView, id);
      if (futuresViewPager == null) {
        break missingId;
      }

      return new FragmentFuturesBinding((ConstraintLayout) rootView, futuresTabLayout,
          futuresViewPager);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
