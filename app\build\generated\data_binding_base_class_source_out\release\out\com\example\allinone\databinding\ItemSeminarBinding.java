// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemSeminarBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView seminarDateText;

  @NonNull
  public final TextView seminarDescriptionText;

  @NonNull
  public final TextView seminarNameText;

  @NonNull
  public final TextView seminarTimeText;

  @NonNull
  public final ImageButton shareButton;

  private ItemSeminarBinding(@NonNull MaterialCardView rootView, @NonNull TextView seminarDateText,
      @NonNull TextView seminarDescriptionText, @NonNull TextView seminarNameText,
      @NonNull TextView seminarTimeText, @NonNull ImageButton shareButton) {
    this.rootView = rootView;
    this.seminarDateText = seminarDateText;
    this.seminarDescriptionText = seminarDescriptionText;
    this.seminarNameText = seminarNameText;
    this.seminarTimeText = seminarTimeText;
    this.shareButton = shareButton;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemSeminarBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemSeminarBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_seminar, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemSeminarBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.seminarDateText;
      TextView seminarDateText = ViewBindings.findChildViewById(rootView, id);
      if (seminarDateText == null) {
        break missingId;
      }

      id = R.id.seminarDescriptionText;
      TextView seminarDescriptionText = ViewBindings.findChildViewById(rootView, id);
      if (seminarDescriptionText == null) {
        break missingId;
      }

      id = R.id.seminarNameText;
      TextView seminarNameText = ViewBindings.findChildViewById(rootView, id);
      if (seminarNameText == null) {
        break missingId;
      }

      id = R.id.seminarTimeText;
      TextView seminarTimeText = ViewBindings.findChildViewById(rootView, id);
      if (seminarTimeText == null) {
        break missingId;
      }

      id = R.id.shareButton;
      ImageButton shareButton = ViewBindings.findChildViewById(rootView, id);
      if (shareButton == null) {
        break missingId;
      }

      return new ItemSeminarBinding((MaterialCardView) rootView, seminarDateText,
          seminarDescriptionText, seminarNameText, seminarTimeText, shareButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
