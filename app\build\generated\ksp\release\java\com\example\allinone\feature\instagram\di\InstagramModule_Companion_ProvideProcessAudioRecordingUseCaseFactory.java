package com.example.allinone.feature.instagram.di;

import com.example.allinone.feature.instagram.domain.repository.InstagramRepository;
import com.example.allinone.feature.instagram.domain.usecase.ProcessAudioRecordingUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class InstagramModule_Companion_ProvideProcessAudioRecordingUseCaseFactory implements Factory<ProcessAudioRecordingUseCase> {
  private final Provider<InstagramRepository> repositoryProvider;

  public InstagramModule_Companion_ProvideProcessAudioRecordingUseCaseFactory(
      Provider<InstagramRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public ProcessAudioRecordingUseCase get() {
    return provideProcessAudioRecordingUseCase(repositoryProvider.get());
  }

  public static InstagramModule_Companion_ProvideProcessAudioRecordingUseCaseFactory create(
      Provider<InstagramRepository> repositoryProvider) {
    return new InstagramModule_Companion_ProvideProcessAudioRecordingUseCaseFactory(repositoryProvider);
  }

  public static ProcessAudioRecordingUseCase provideProcessAudioRecordingUseCase(
      InstagramRepository repository) {
    return Preconditions.checkNotNullFromProvides(InstagramModule.Companion.provideProcessAudioRecordingUseCase(repository));
  }
}
