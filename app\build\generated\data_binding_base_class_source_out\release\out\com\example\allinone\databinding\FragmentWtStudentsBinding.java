// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentWtStudentsBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final FloatingActionButton addStudentFab;

  @NonNull
  public final LinearLayout emptyState;

  @NonNull
  public final OfflineStatusViewBinding offlineStatusView;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView studentsRecyclerView;

  @NonNull
  public final SwipeRefreshLayout swipeRefreshLayout;

  private FragmentWtStudentsBinding(@NonNull ConstraintLayout rootView,
      @NonNull FloatingActionButton addStudentFab, @NonNull LinearLayout emptyState,
      @NonNull OfflineStatusViewBinding offlineStatusView, @NonNull ProgressBar progressBar,
      @NonNull RecyclerView studentsRecyclerView, @NonNull SwipeRefreshLayout swipeRefreshLayout) {
    this.rootView = rootView;
    this.addStudentFab = addStudentFab;
    this.emptyState = emptyState;
    this.offlineStatusView = offlineStatusView;
    this.progressBar = progressBar;
    this.studentsRecyclerView = studentsRecyclerView;
    this.swipeRefreshLayout = swipeRefreshLayout;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentWtStudentsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentWtStudentsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_wt_students, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentWtStudentsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addStudentFab;
      FloatingActionButton addStudentFab = ViewBindings.findChildViewById(rootView, id);
      if (addStudentFab == null) {
        break missingId;
      }

      id = R.id.emptyState;
      LinearLayout emptyState = ViewBindings.findChildViewById(rootView, id);
      if (emptyState == null) {
        break missingId;
      }

      id = R.id.offline_status_view;
      View offlineStatusView = ViewBindings.findChildViewById(rootView, id);
      if (offlineStatusView == null) {
        break missingId;
      }
      OfflineStatusViewBinding binding_offlineStatusView = OfflineStatusViewBinding.bind(offlineStatusView);

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.studentsRecyclerView;
      RecyclerView studentsRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (studentsRecyclerView == null) {
        break missingId;
      }

      id = R.id.swipe_refresh_layout;
      SwipeRefreshLayout swipeRefreshLayout = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefreshLayout == null) {
        break missingId;
      }

      return new FragmentWtStudentsBinding((ConstraintLayout) rootView, addStudentFab, emptyState,
          binding_offlineStatusView, progressBar, studentsRecyclerView, swipeRefreshLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
