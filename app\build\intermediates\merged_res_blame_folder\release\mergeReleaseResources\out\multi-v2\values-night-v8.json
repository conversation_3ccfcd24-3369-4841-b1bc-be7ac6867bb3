{"logs": [{"outputFile": "com.example.allinone.app-mergeReleaseResources-108:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ed4d3c9c4ac15ccaa3d4f8bb593bf66c\\transformed\\core-splashscreen-1.0.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "85", "endOffsets": "136"}, "to": {"startLines": "28", "startColumns": "4", "startOffsets": "1358", "endColumns": "85", "endOffsets": "1439"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\75ac7f2a23ffbf48dd6409062de1d131\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1081,1169,1293,1395,1497,1613,1715,1829,1957,2073,2195,2331,2451,2585,2705,2817,2943,3060,3184,3314,3436,3574,3708,3824", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1076,1164,1288,1390,1492,1608,1710,1824,1952,2068,2190,2326,2446,2580,2700,2812,2938,3055,3179,3309,3431,3569,3703,3819,3939"}, "to": {"startLines": "87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,120,170,171,172,173,174,175,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4683,4758,4869,4958,5059,5166,5273,5372,5479,5582,5709,5797,5921,6023,6125,6241,6343,6457,6585,6701,6823,6959,7079,7213,7333,7445,8130,11358,11482,11612,11734,11872,12006,12122", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "4753,4864,4953,5054,5161,5268,5367,5474,5577,5704,5792,5916,6018,6120,6236,6338,6452,6580,6696,6818,6954,7074,7208,7328,7440,7566,8242,11477,11607,11729,11867,12001,12117,12237"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1375e70be1fe8041f38907ceec5f1093\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "80,81,82,83,84,85,86,119", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4051,4121,4205,4289,4385,4487,4589,8041", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "4116,4200,4284,4380,4482,4584,4678,8125"}}, {"source": "C:\\Users\\<USER>\\Documents\\GitHub\\AllinOne\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "70,89,85,3,78,94,73,118,110,63,102,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4074,5078,4888,142,4410,5326,4205,6674,6184,3706,5770,2716", "endLines": "70,91,87,43,83,99,75,124,116,67,107,60", "endColumns": "78,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "4148,5258,5070,2646,4880,5717,4353,7158,6666,4024,6134,3647"}, "to": {"startLines": "17,33,36,39,113,121,127,130,137,144,149,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "811,1603,1786,1971,7571,8247,8638,8789,9272,9753,10072,10436", "endLines": "17,35,38,79,118,126,129,136,143,148,154,169", "endColumns": "78,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "885,1781,1966,4046,8036,8633,8784,9267,9748,10067,10431,11353"}}, {"source": "C:\\Users\\<USER>\\Documents\\GitHub\\AllinOne\\app\\src\\main\\res\\values-night\\styles.xml", "from": {"startLines": "9,3", "startColumns": "4,4", "startOffsets": "314,109", "endLines": "12,6", "endColumns": "12,12", "endOffsets": "471,247"}, "to": {"startLines": "29,177", "startColumns": "4,4", "startOffsets": "1444,12242", "endLines": "32,180", "endColumns": "12,12", "endOffsets": "1598,12377"}}, {"source": "C:\\Users\\<USER>\\Documents\\GitHub\\AllinOne\\app\\src\\main\\res\\values-night\\colors.xml", "from": {"startLines": "2,42,38,39,25,8,26,27,21,9,12,4,5,14,22,34,31,30,32,35,33,20,13,17,3", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "57,2299,2063,2159,1129,281,1222,1317,936,341,456,143,190,621,1025,1831,1579,1483,1661,1929,1757,843,538,751,100", "endColumns": "41,52,53,55,44,58,46,46,43,65,50,45,46,52,45,45,49,46,46,53,46,37,45,50,41", "endOffsets": "94,2347,2112,2210,1169,335,1264,1359,975,402,502,184,232,669,1066,1872,1624,1525,1703,1978,1799,876,579,797,137"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,97,150,204,260,305,364,411,458,502,568,619,665,712,765,890,936,986,1033,1080,1134,1181,1219,1265,1316", "endColumns": "41,52,53,55,44,58,46,46,43,65,50,45,46,52,45,45,49,46,46,53,46,37,45,50,41", "endOffsets": "92,145,199,255,300,359,406,453,497,563,614,660,707,760,806,931,981,1028,1075,1129,1176,1214,1260,1311,1353"}}]}]}