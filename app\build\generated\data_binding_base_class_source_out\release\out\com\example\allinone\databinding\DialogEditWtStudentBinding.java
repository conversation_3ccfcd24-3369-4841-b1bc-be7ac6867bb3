// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogEditWtStudentBinding implements ViewBinding {
  @NonNull
  private final NestedScrollView rootView;

  @NonNull
  public final Button addAttachmentButton;

  @NonNull
  public final TextInputEditText amountInput;

  @NonNull
  public final TextInputLayout amountLayout;

  @NonNull
  public final TextView attachmentNameText;

  @NonNull
  public final ImageView attachmentPreview;

  @NonNull
  public final TextInputEditText endDateInput;

  @NonNull
  public final TextInputEditText notesEditText;

  @NonNull
  public final SwitchMaterial paidSwitch;

  @NonNull
  public final TextInputEditText startDateInput;

  @NonNull
  public final AutoCompleteTextView studentDropdown;

  private DialogEditWtStudentBinding(@NonNull NestedScrollView rootView,
      @NonNull Button addAttachmentButton, @NonNull TextInputEditText amountInput,
      @NonNull TextInputLayout amountLayout, @NonNull TextView attachmentNameText,
      @NonNull ImageView attachmentPreview, @NonNull TextInputEditText endDateInput,
      @NonNull TextInputEditText notesEditText, @NonNull SwitchMaterial paidSwitch,
      @NonNull TextInputEditText startDateInput, @NonNull AutoCompleteTextView studentDropdown) {
    this.rootView = rootView;
    this.addAttachmentButton = addAttachmentButton;
    this.amountInput = amountInput;
    this.amountLayout = amountLayout;
    this.attachmentNameText = attachmentNameText;
    this.attachmentPreview = attachmentPreview;
    this.endDateInput = endDateInput;
    this.notesEditText = notesEditText;
    this.paidSwitch = paidSwitch;
    this.startDateInput = startDateInput;
    this.studentDropdown = studentDropdown;
  }

  @Override
  @NonNull
  public NestedScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogEditWtStudentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogEditWtStudentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_edit_wt_student, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogEditWtStudentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addAttachmentButton;
      Button addAttachmentButton = ViewBindings.findChildViewById(rootView, id);
      if (addAttachmentButton == null) {
        break missingId;
      }

      id = R.id.amountInput;
      TextInputEditText amountInput = ViewBindings.findChildViewById(rootView, id);
      if (amountInput == null) {
        break missingId;
      }

      id = R.id.amountLayout;
      TextInputLayout amountLayout = ViewBindings.findChildViewById(rootView, id);
      if (amountLayout == null) {
        break missingId;
      }

      id = R.id.attachmentNameText;
      TextView attachmentNameText = ViewBindings.findChildViewById(rootView, id);
      if (attachmentNameText == null) {
        break missingId;
      }

      id = R.id.attachmentPreview;
      ImageView attachmentPreview = ViewBindings.findChildViewById(rootView, id);
      if (attachmentPreview == null) {
        break missingId;
      }

      id = R.id.endDateInput;
      TextInputEditText endDateInput = ViewBindings.findChildViewById(rootView, id);
      if (endDateInput == null) {
        break missingId;
      }

      id = R.id.notesEditText;
      TextInputEditText notesEditText = ViewBindings.findChildViewById(rootView, id);
      if (notesEditText == null) {
        break missingId;
      }

      id = R.id.paidSwitch;
      SwitchMaterial paidSwitch = ViewBindings.findChildViewById(rootView, id);
      if (paidSwitch == null) {
        break missingId;
      }

      id = R.id.startDateInput;
      TextInputEditText startDateInput = ViewBindings.findChildViewById(rootView, id);
      if (startDateInput == null) {
        break missingId;
      }

      id = R.id.studentDropdown;
      AutoCompleteTextView studentDropdown = ViewBindings.findChildViewById(rootView, id);
      if (studentDropdown == null) {
        break missingId;
      }

      return new DialogEditWtStudentBinding((NestedScrollView) rootView, addAttachmentButton,
          amountInput, amountLayout, attachmentNameText, attachmentPreview, endDateInput,
          notesEditText, paidSwitch, startDateInput, studentDropdown);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
