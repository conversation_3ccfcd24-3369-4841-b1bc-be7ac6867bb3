1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.allinone"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission
11-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:5:5-107
12        android:name="android.permission.READ_EXTERNAL_STORAGE"
12-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:5:22-77
13        android:maxSdkVersion="32" />
13-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:5:78-104
14    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
14-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:6:5-76
14-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:6:22-73
15    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
15-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:7:5-75
15-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:7:22-72
16    <uses-permission
16-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:8:5-108
17        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
17-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:8:22-78
18        android:maxSdkVersion="28" />
18-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:8:79-105
19    <uses-permission android:name="android.permission.CAMERA" />
19-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:9:5-65
19-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:9:22-62
20    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
20-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:10:5-77
20-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:10:22-74
21    <uses-permission android:name="android.permission.INTERNET" />
21-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:11:5-67
21-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:11:22-64
22    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
22-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:12:5-79
22-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:12:22-76
23    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
23-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:13:5-71
23-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:13:22-68
24    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
24-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:14:5-74
24-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:14:22-71
25    <uses-permission android:name="android.permission.RECORD_AUDIO" />
25-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:15:5-71
25-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:15:22-68
26    <uses-permission android:name="android.permission.WAKE_LOCK" />
26-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:25:5-68
26-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:25:22-65
27    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
27-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:26:5-110
27-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:26:22-107
28    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
28-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:25:5-79
28-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:25:22-76
29    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
29-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:26:5-88
29-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:26:22-85
30    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
30-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:27:5-82
30-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:27:22-79
31    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
31-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\431ca28934aa0e759e1ba596c21cfdf5\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:5-98
31-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\431ca28934aa0e759e1ba596c21cfdf5\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:22-95
32    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
32-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:25:5-81
32-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:25:22-78
33    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
33-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:26:5-77
33-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:26:22-74
34
35    <permission
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
36        android:name="com.example.allinone.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
37        android:protectionLevel="signature" />
37-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
38
39    <uses-permission android:name="com.example.allinone.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
40
41    <application
41-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:17:5-94:19
42        android:name="com.example.allinone.AllinOneApplication"
42-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:18:9-44
43        android:allowBackup="true"
43-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:19:9-35
44        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
45        android:dataExtractionRules="@xml/data_extraction_rules"
45-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:20:9-65
46        android:debuggable="true"
47        android:extractNativeLibs="false"
48        android:fullBackupContent="@xml/backup_rules"
48-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:21:9-54
49        android:icon="@mipmap/ic_launcher"
49-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:22:9-43
50        android:label="@string/app_name"
50-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:23:9-41
51        android:networkSecurityConfig="@xml/network_security_config"
51-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:27:9-69
52        android:roundIcon="@mipmap/ic_launcher_round"
52-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:24:9-54
53        android:supportsRtl="true"
53-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:25:9-35
54        android:theme="@style/Theme.AllinOne"
54-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:26:9-46
55        android:usesCleartextTraffic="true" >
55-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:28:9-44
56        <activity
56-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:38:9-47:20
57            android:name="com.example.allinone.MainActivity"
57-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:39:13-41
58            android:exported="true"
58-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:40:13-36
59            android:screenOrientation="portrait"
59-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:42:13-49
60            android:theme="@style/Theme.AllinOne.Starting" >
60-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:41:13-59
61            <intent-filter>
61-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:43:13-46:29
62                <action android:name="android.intent.action.MAIN" />
62-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:44:17-69
62-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:44:25-66
63
64                <category android:name="android.intent.category.LAUNCHER" />
64-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:45:17-77
64-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:45:27-74
65            </intent-filter>
66        </activity>
67        <activity
67-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:49:9-55:58
68            android:name="com.example.allinone.backup.BackupActivity"
68-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:50:13-50
69            android:exported="false"
69-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:51:13-37
70            android:label="Backup and Restore"
70-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:54:13-47
71            android:parentActivityName="com.example.allinone.MainActivity"
71-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:55:13-55
72            android:screenOrientation="portrait"
72-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:53:13-49
73            android:theme="@style/Theme.AllinOne" />
73-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:52:13-50
74
75        <!-- Add FileProvider for sharing files -->
76        <provider
77            android:name="androidx.core.content.FileProvider"
77-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:59:13-62
78            android:authorities="com.example.allinone.provider"
78-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:60:13-60
79            android:exported="false"
79-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:61:13-37
80            android:grantUriPermissions="true" >
80-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:62:13-47
81            <meta-data
81-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:63:13-65:54
82                android:name="android.support.FILE_PROVIDER_PATHS"
82-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:64:17-67
83                android:resource="@xml/file_paths" />
83-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:65:17-51
84        </provider>
85
86        <meta-data
86-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:68:9-70:69
87            android:name="com.google.android.gms.version"
87-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:69:13-58
88            android:value="@integer/google_play_services_version" />
88-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:70:13-66
89        <meta-data
89-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:72:9-74:46
90            android:name="com.google.android.gms.games.APP_ID"
90-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:73:13-63
91            android:value="@string/app_id" />
91-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:74:13-43
92        <meta-data
92-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:76:9-78:36
93            android:name="com.google.android.gms.wallet.api.enabled"
93-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:77:13-69
94            android:value="true" />
94-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:78:13-33
95
96        <!-- Other activities -->
97
98        <activity
98-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:82:9-86:58
99            android:name="com.example.allinone.ui.EditNoteActivity"
99-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:83:13-48
100            android:exported="false"
100-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:84:13-37
101            android:parentActivityName="com.example.allinone.MainActivity"
101-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:86:13-55
102            android:windowSoftInputMode="adjustResize" />
102-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:85:13-55
103
104        <!-- Drawing Activity -->
105        <activity
105-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:89:9-93:52
106            android:name="com.example.allinone.ui.drawing.DrawingActivity"
106-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:90:13-55
107            android:exported="false"
107-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:91:13-37
108            android:screenOrientation="portrait"
108-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:93:13-49
109            android:theme="@style/Theme.AllinOne.NoActionBar" />
109-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:92:13-62
110        <activity
110-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:29:9-46:20
111            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
111-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:30:13-80
112            android:excludeFromRecents="true"
112-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:31:13-46
113            android:exported="true"
113-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:32:13-36
114            android:launchMode="singleTask"
114-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:33:13-44
115            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
115-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:34:13-72
116            <intent-filter>
116-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:35:13-45:29
117                <action android:name="android.intent.action.VIEW" />
117-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
117-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
118
119                <category android:name="android.intent.category.DEFAULT" />
119-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
119-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
120                <category android:name="android.intent.category.BROWSABLE" />
120-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
120-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
121
122                <data
122-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
123                    android:host="firebase.auth"
123-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
124                    android:path="/"
124-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
125                    android:scheme="genericidp" />
125-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
126            </intent-filter>
127        </activity>
128        <activity
128-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:47:9-64:20
129            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
129-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:48:13-79
130            android:excludeFromRecents="true"
130-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:49:13-46
131            android:exported="true"
131-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:50:13-36
132            android:launchMode="singleTask"
132-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:51:13-44
133            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
133-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:52:13-72
134            <intent-filter>
134-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:53:13-63:29
135                <action android:name="android.intent.action.VIEW" />
135-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
135-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
136
137                <category android:name="android.intent.category.DEFAULT" />
137-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
137-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
138                <category android:name="android.intent.category.BROWSABLE" />
138-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
138-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
139
140                <data
140-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
141                    android:host="firebase.auth"
141-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
142                    android:path="/"
142-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
143                    android:scheme="recaptcha" />
143-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
144            </intent-filter>
145        </activity>
146
147        <service
147-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:66:9-72:19
148            android:name="com.google.firebase.components.ComponentDiscoveryService"
148-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:67:13-84
149            android:directBootAware="true"
149-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
150            android:exported="false" >
150-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:68:13-37
151            <meta-data
151-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:69:13-71:85
152                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
152-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:70:17-109
153                android:value="com.google.firebase.components.ComponentRegistrar" />
153-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:71:17-82
154            <meta-data
154-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:17:13-19:85
155                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
155-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:18:17-122
156                android:value="com.google.firebase.components.ComponentRegistrar" />
156-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:19:17-82
157            <meta-data
157-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:20:13-22:85
158                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
158-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:21:17-111
159                android:value="com.google.firebase.components.ComponentRegistrar" />
159-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:22:17-82
160            <meta-data
160-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:37:13-39:85
161                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
161-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:38:17-139
162                android:value="com.google.firebase.components.ComponentRegistrar" />
162-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:39:17-82
163            <meta-data
163-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:15:13-17:85
164                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
164-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:16:17-126
165                android:value="com.google.firebase.components.ComponentRegistrar" />
165-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:17:17-82
166            <meta-data
166-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:18:13-20:85
167                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
167-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:19:17-115
168                android:value="com.google.firebase.components.ComponentRegistrar" />
168-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:20:17-82
169            <meta-data
169-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:30:13-32:85
170                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
170-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:31:17-118
171                android:value="com.google.firebase.components.ComponentRegistrar" />
171-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:32:17-82
172            <meta-data
172-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:33:13-35:85
173                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
173-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:34:17-107
174                android:value="com.google.firebase.components.ComponentRegistrar" />
174-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:35:17-82
175            <meta-data
175-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:29:13-31:85
176                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
176-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:30:17-117
177                android:value="com.google.firebase.components.ComponentRegistrar" />
177-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:31:17-82
178            <meta-data
178-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
179                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
179-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
180                android:value="com.google.firebase.components.ComponentRegistrar" />
180-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
181            <meta-data
181-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
182                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
182-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
183                android:value="com.google.firebase.components.ComponentRegistrar" />
183-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
184            <meta-data
184-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
185                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
185-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
186                android:value="com.google.firebase.components.ComponentRegistrar" />
186-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
187            <meta-data
187-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
188                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
188-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
189                android:value="com.google.firebase.components.ComponentRegistrar" />
189-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
190            <meta-data
190-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d29eeb71e4b590606a79f795e337f4\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
191                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
191-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d29eeb71e4b590606a79f795e337f4\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
192                android:value="com.google.firebase.components.ComponentRegistrar" />
192-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d29eeb71e4b590606a79f795e337f4\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
193            <meta-data
193-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
194                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
194-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
195                android:value="com.google.firebase.components.ComponentRegistrar" />
195-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
196            <meta-data
196-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e2967fd5b276966c87bd4e88ea401c8\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
197                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
197-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e2967fd5b276966c87bd4e88ea401c8\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
198                android:value="com.google.firebase.components.ComponentRegistrar" />
198-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e2967fd5b276966c87bd4e88ea401c8\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
199        </service>
200        <service
200-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
201            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
201-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
202            android:enabled="true"
202-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
203            android:exported="false" >
203-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
204            <meta-data
204-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
205                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
205-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
206                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
206-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
207        </service>
208
209        <activity
209-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
210            android:name="androidx.credentials.playservices.HiddenActivity"
210-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
211            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
211-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
212            android:enabled="true"
212-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
213            android:exported="false"
213-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
214            android:fitsSystemWindows="true"
214-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
215            android:theme="@style/Theme.Hidden" >
215-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
216        </activity>
217        <activity
217-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:23:9-27:75
218            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
218-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:24:13-93
219            android:excludeFromRecents="true"
219-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:25:13-46
220            android:exported="false"
220-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:26:13-37
221            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
221-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:27:13-72
222        <!--
223            Service handling Google Sign-In user revocation. For apps that do not integrate with
224            Google Sign-In, this service will never be started.
225        -->
226        <service
226-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:33:9-37:51
227            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
227-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:34:13-89
228            android:exported="true"
228-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:35:13-36
229            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
229-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:36:13-107
230            android:visibleToInstantApps="true" />
230-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:37:13-48
231
232        <receiver
232-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:29:9-33:20
233            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
233-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:30:13-85
234            android:enabled="true"
234-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:31:13-35
235            android:exported="false" >
235-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:32:13-37
236        </receiver>
237
238        <service
238-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:35:9-38:40
239            android:name="com.google.android.gms.measurement.AppMeasurementService"
239-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:36:13-84
240            android:enabled="true"
240-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:37:13-35
241            android:exported="false" />
241-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:38:13-37
242        <service
242-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:39:9-43:72
243            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
243-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:40:13-87
244            android:enabled="true"
244-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:41:13-35
245            android:exported="false"
245-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:42:13-37
246            android:permission="android.permission.BIND_JOB_SERVICE" />
246-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:43:13-69
247
248        <property
248-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:30:9-32:61
249            android:name="android.adservices.AD_SERVICES_CONFIG"
249-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:31:13-65
250            android:resource="@xml/ga_ad_services_config" />
250-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:32:13-58
251
252        <service
252-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:22:9-25:40
253            android:name="com.google.firebase.sessions.SessionLifecycleService"
253-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:23:13-80
254            android:enabled="true"
254-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:24:13-35
255            android:exported="false" />
255-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:25:13-37
256
257        <provider
257-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
258            android:name="com.google.firebase.provider.FirebaseInitProvider"
258-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
259            android:authorities="com.example.allinone.firebaseinitprovider"
259-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
260            android:directBootAware="true"
260-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
261            android:exported="false"
261-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
262            android:initOrder="100" />
262-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
263
264        <activity
264-->[androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5d37fd89a97cf267377a06b09c52112\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:23:9-25:39
265            android:name="androidx.activity.ComponentActivity"
265-->[androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5d37fd89a97cf267377a06b09c52112\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:24:13-63
266            android:exported="true" />
266-->[androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5d37fd89a97cf267377a06b09c52112\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:25:13-36
267        <activity
267-->[androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e326cf46a3e0f3591896fb1f0061a989\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
268            android:name="androidx.compose.ui.tooling.PreviewActivity"
268-->[androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e326cf46a3e0f3591896fb1f0061a989\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
269            android:exported="true" />
269-->[androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e326cf46a3e0f3591896fb1f0061a989\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
270
271        <uses-library
271-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c656b25fa2dc95e881345daed065935\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
272            android:name="android.ext.adservices"
272-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c656b25fa2dc95e881345daed065935\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
273            android:required="false" />
273-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c656b25fa2dc95e881345daed065935\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
274
275        <service
275-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:39:9-45:35
276            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
276-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:40:13-88
277            android:directBootAware="false"
277-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:41:13-44
278            android:enabled="@bool/enable_system_alarm_service_default"
278-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:42:13-72
279            android:exported="false" />
279-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:43:13-37
280        <service
280-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:46:9-52:35
281            android:name="androidx.work.impl.background.systemjob.SystemJobService"
281-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:47:13-84
282            android:directBootAware="false"
282-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:48:13-44
283            android:enabled="@bool/enable_system_job_service_default"
283-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:49:13-70
284            android:exported="true"
284-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:50:13-36
285            android:permission="android.permission.BIND_JOB_SERVICE" />
285-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:51:13-69
286        <service
286-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:53:9-59:35
287            android:name="androidx.work.impl.foreground.SystemForegroundService"
287-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:54:13-81
288            android:directBootAware="false"
288-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:55:13-44
289            android:enabled="@bool/enable_system_foreground_service_default"
289-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:56:13-77
290            android:exported="false" />
290-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:57:13-37
291
292        <receiver
292-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:61:9-66:35
293            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
293-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:62:13-88
294            android:directBootAware="false"
294-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:63:13-44
295            android:enabled="true"
295-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:64:13-35
296            android:exported="false" />
296-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:65:13-37
297        <receiver
297-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:67:9-77:20
298            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
298-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:68:13-106
299            android:directBootAware="false"
299-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:69:13-44
300            android:enabled="false"
300-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:70:13-36
301            android:exported="false" >
301-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:71:13-37
302            <intent-filter>
302-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:73:13-76:29
303                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
303-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:17-87
303-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:25-84
304                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
304-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:17-90
304-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:25-87
305            </intent-filter>
306        </receiver>
307        <receiver
307-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:78:9-88:20
308            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
308-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:79:13-104
309            android:directBootAware="false"
309-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:80:13-44
310            android:enabled="false"
310-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:81:13-36
311            android:exported="false" >
311-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:82:13-37
312            <intent-filter>
312-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:84:13-87:29
313                <action android:name="android.intent.action.BATTERY_OKAY" />
313-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:17-77
313-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:25-74
314                <action android:name="android.intent.action.BATTERY_LOW" />
314-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:17-76
314-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:25-73
315            </intent-filter>
316        </receiver>
317        <receiver
317-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:89:9-99:20
318            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
318-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:90:13-104
319            android:directBootAware="false"
319-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:91:13-44
320            android:enabled="false"
320-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:92:13-36
321            android:exported="false" >
321-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:93:13-37
322            <intent-filter>
322-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:95:13-98:29
323                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
323-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:17-83
323-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:25-80
324                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
324-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:17-82
324-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:25-79
325            </intent-filter>
326        </receiver>
327        <receiver
327-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:100:9-109:20
328            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
328-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:101:13-103
329            android:directBootAware="false"
329-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:102:13-44
330            android:enabled="false"
330-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:103:13-36
331            android:exported="false" >
331-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:104:13-37
332            <intent-filter>
332-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:106:13-108:29
333                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
333-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:17-79
333-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:25-76
334            </intent-filter>
335        </receiver>
336        <receiver
336-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:110:9-121:20
337            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
337-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:111:13-88
338            android:directBootAware="false"
338-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:112:13-44
339            android:enabled="false"
339-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:113:13-36
340            android:exported="false" >
340-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:114:13-37
341            <intent-filter>
341-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:116:13-120:29
342                <action android:name="android.intent.action.BOOT_COMPLETED" />
342-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:117:17-79
342-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:117:25-76
343                <action android:name="android.intent.action.TIME_SET" />
343-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:17-73
343-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:25-70
344                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
344-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:17-81
344-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:25-78
345            </intent-filter>
346        </receiver>
347        <receiver
347-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:122:9-131:20
348            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
348-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:123:13-99
349            android:directBootAware="false"
349-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:124:13-44
350            android:enabled="@bool/enable_system_alarm_service_default"
350-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:125:13-72
351            android:exported="false" >
351-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:126:13-37
352            <intent-filter>
352-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:128:13-130:29
353                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
353-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:17-98
353-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:25-95
354            </intent-filter>
355        </receiver>
356        <receiver
356-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:132:9-142:20
357            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
357-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:133:13-78
358            android:directBootAware="false"
358-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:134:13-44
359            android:enabled="true"
359-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:135:13-35
360            android:exported="true"
360-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:136:13-36
361            android:permission="android.permission.DUMP" >
361-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:137:13-57
362            <intent-filter>
362-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:139:13-141:29
363                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
363-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:17-88
363-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:25-85
364            </intent-filter>
365        </receiver>
366
367        <uses-library
367-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
368            android:name="androidx.window.extensions"
368-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
369            android:required="false" />
369-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
370        <uses-library
370-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
371            android:name="androidx.window.sidecar"
371-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
372            android:required="false" />
372-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
373
374        <activity
374-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd483c27f09d2683183fc5f548eeb5d9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
375            android:name="com.google.android.gms.common.api.GoogleApiActivity"
375-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd483c27f09d2683183fc5f548eeb5d9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
376            android:exported="false"
376-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd483c27f09d2683183fc5f548eeb5d9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
377            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
377-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd483c27f09d2683183fc5f548eeb5d9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
378
379        <service
379-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3bc0680d46b752b3dba03a568ba1a3\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
380            android:name="androidx.room.MultiInstanceInvalidationService"
380-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3bc0680d46b752b3dba03a568ba1a3\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
381            android:directBootAware="true"
381-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3bc0680d46b752b3dba03a568ba1a3\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
382            android:exported="false" />
382-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3bc0680d46b752b3dba03a568ba1a3\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
383        <service
383-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
384            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
384-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
385            android:exported="false" >
385-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
386            <meta-data
386-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
387                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
387-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
388                android:value="cct" />
388-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
389        </service>
390
391        <receiver
391-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
392            android:name="androidx.profileinstaller.ProfileInstallReceiver"
392-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
393            android:directBootAware="false"
393-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
394            android:enabled="true"
394-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
395            android:exported="true"
395-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
396            android:permission="android.permission.DUMP" >
396-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
397            <intent-filter>
397-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
398                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
398-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
398-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
399            </intent-filter>
400            <intent-filter>
400-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
401                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
401-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
401-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
402            </intent-filter>
403            <intent-filter>
403-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
404                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
404-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
404-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
405            </intent-filter>
406            <intent-filter>
406-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
407                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
407-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
407-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
408            </intent-filter>
409        </receiver>
410
411        <service
411-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
412            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
412-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
413            android:exported="false"
413-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
414            android:permission="android.permission.BIND_JOB_SERVICE" >
414-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
415        </service>
416
417        <receiver
417-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
418            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
418-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
419            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
419-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
420        <activity
420-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed98f692eef56b9ec9255f707f2cecc8\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
421            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
421-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed98f692eef56b9ec9255f707f2cecc8\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
422            android:exported="false"
422-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed98f692eef56b9ec9255f707f2cecc8\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
423            android:stateNotNeeded="true"
423-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed98f692eef56b9ec9255f707f2cecc8\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
424            android:theme="@style/Theme.PlayCore.Transparent" />
424-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed98f692eef56b9ec9255f707f2cecc8\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
425    </application>
426
427</manifest>
