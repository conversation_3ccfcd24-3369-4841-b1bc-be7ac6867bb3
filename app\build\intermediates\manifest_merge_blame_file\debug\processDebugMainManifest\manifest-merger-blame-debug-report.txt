1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.allinone"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission
11-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:5:5-107
12        android:name="android.permission.READ_EXTERNAL_STORAGE"
12-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:5:22-77
13        android:maxSdkVersion="32" />
13-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:5:78-104
14    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
14-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:6:5-76
14-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:6:22-73
15    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
15-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:7:5-75
15-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:7:22-72
16    <uses-permission
16-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:8:5-108
17        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
17-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:8:22-78
18        android:maxSdkVersion="28" />
18-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:8:79-105
19    <uses-permission android:name="android.permission.CAMERA" />
19-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:9:5-65
19-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:9:22-62
20    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
20-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:10:5-77
20-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:10:22-74
21    <uses-permission android:name="android.permission.INTERNET" />
21-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:11:5-67
21-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:11:22-64
22    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
22-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:12:5-79
22-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:12:22-76
23    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
23-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:13:5-71
23-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:13:22-68
24    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
24-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:14:5-74
24-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:14:22-71
25    <uses-permission android:name="android.permission.RECORD_AUDIO" />
25-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:15:5-71
25-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:15:22-68
26    <uses-permission android:name="android.permission.WAKE_LOCK" />
26-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:25:5-68
26-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:25:22-65
27    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
27-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:26:5-110
27-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:26:22-107
28    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
28-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:25:5-79
28-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:25:22-76
29    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
29-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:26:5-88
29-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:26:22-85
30    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
30-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:27:5-82
30-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:27:22-79
31    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
31-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\431ca28934aa0e759e1ba596c21cfdf5\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:5-98
31-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\431ca28934aa0e759e1ba596c21cfdf5\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:22-95
32    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
32-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:25:5-81
32-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:25:22-78
33    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
33-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:26:5-77
33-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:26:22-74
34
35    <permission
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
36        android:name="com.example.allinone.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
37        android:protectionLevel="signature" />
37-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
38
39    <uses-permission android:name="com.example.allinone.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
40
41    <application
41-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:17:5-90:19
42        android:name="com.example.allinone.AllinOneApplication"
42-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:18:9-44
43        android:allowBackup="true"
43-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:19:9-35
44        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
45        android:dataExtractionRules="@xml/data_extraction_rules"
45-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:20:9-65
46        android:debuggable="true"
47        android:extractNativeLibs="false"
48        android:fullBackupContent="@xml/backup_rules"
48-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:21:9-54
49        android:icon="@mipmap/ic_launcher"
49-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:22:9-43
50        android:label="@string/app_name"
50-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:23:9-41
51        android:networkSecurityConfig="@xml/network_security_config"
51-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:27:9-69
52        android:roundIcon="@mipmap/ic_launcher_round"
52-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:24:9-54
53        android:supportsRtl="true"
53-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:25:9-35
54        android:theme="@style/Theme.AllinOne"
54-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:26:9-46
55        android:usesCleartextTraffic="true" >
55-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:28:9-44
56        <activity
56-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:38:9-47:20
57            android:name="com.example.allinone.MainActivity"
57-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:39:13-41
58            android:exported="true"
58-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:40:13-36
59            android:screenOrientation="portrait"
59-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:42:13-49
60            android:theme="@style/Theme.AllinOne.Starting" >
60-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:41:13-59
61            <intent-filter>
61-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:43:13-46:29
62                <action android:name="android.intent.action.MAIN" />
62-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:44:17-69
62-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:44:25-66
63
64                <category android:name="android.intent.category.LAUNCHER" />
64-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:45:17-77
64-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:45:27-74
65            </intent-filter>
66        </activity>
67        <activity
67-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:49:9-55:58
68            android:name="com.example.allinone.backup.BackupActivity"
68-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:50:13-50
69            android:exported="false"
69-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:51:13-37
70            android:label="Backup and Restore"
70-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:54:13-47
71            android:parentActivityName="com.example.allinone.MainActivity"
71-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:55:13-55
72            android:screenOrientation="portrait"
72-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:53:13-49
73            android:theme="@style/Theme.AllinOne" />
73-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:52:13-50
74
75        <!-- Add FileProvider for sharing files -->
76        <provider
77            android:name="androidx.core.content.FileProvider"
77-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:59:13-62
78            android:authorities="com.example.allinone.provider"
78-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:60:13-60
79            android:exported="false"
79-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:61:13-37
80            android:grantUriPermissions="true" >
80-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:62:13-47
81            <meta-data
81-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:63:13-65:54
82                android:name="android.support.FILE_PROVIDER_PATHS"
82-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:64:17-67
83                android:resource="@xml/file_paths" />
83-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:65:17-51
84        </provider>
85
86        <meta-data
86-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:68:9-70:69
87            android:name="com.google.android.gms.version"
87-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:69:13-58
88            android:value="@integer/google_play_services_version" />
88-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:70:13-66
89        <meta-data
89-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:72:9-74:46
90            android:name="com.google.android.gms.games.APP_ID"
90-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:73:13-63
91            android:value="@string/app_id" />
91-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:74:13-43
92        <meta-data
92-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:76:9-78:36
93            android:name="com.google.android.gms.wallet.api.enabled"
93-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:77:13-69
94            android:value="true" />
94-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:78:13-33
95
96        <!-- Other activities -->
97
98
99        <!-- Drawing Activity -->
100        <activity
100-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:85:9-89:52
101            android:name="com.example.allinone.ui.drawing.DrawingActivity"
101-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:86:13-55
102            android:exported="false"
102-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:87:13-37
103            android:screenOrientation="portrait"
103-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:89:13-49
104            android:theme="@style/Theme.AllinOne.NoActionBar" />
104-->C:\Users\<USER>\Documents\GitHub\AllinOne\app\src\main\AndroidManifest.xml:88:13-62
105        <activity
105-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:29:9-46:20
106            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
106-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:30:13-80
107            android:excludeFromRecents="true"
107-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:31:13-46
108            android:exported="true"
108-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:32:13-36
109            android:launchMode="singleTask"
109-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:33:13-44
110            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
110-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:34:13-72
111            <intent-filter>
111-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:35:13-45:29
112                <action android:name="android.intent.action.VIEW" />
112-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
112-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
113
114                <category android:name="android.intent.category.DEFAULT" />
114-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
114-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
115                <category android:name="android.intent.category.BROWSABLE" />
115-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
115-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
116
117                <data
117-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
118                    android:host="firebase.auth"
118-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
119                    android:path="/"
119-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
120                    android:scheme="genericidp" />
120-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
121            </intent-filter>
122        </activity>
123        <activity
123-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:47:9-64:20
124            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
124-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:48:13-79
125            android:excludeFromRecents="true"
125-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:49:13-46
126            android:exported="true"
126-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:50:13-36
127            android:launchMode="singleTask"
127-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:51:13-44
128            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
128-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:52:13-72
129            <intent-filter>
129-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:53:13-63:29
130                <action android:name="android.intent.action.VIEW" />
130-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
130-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
131
132                <category android:name="android.intent.category.DEFAULT" />
132-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
132-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
133                <category android:name="android.intent.category.BROWSABLE" />
133-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
133-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
134
135                <data
135-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
136                    android:host="firebase.auth"
136-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
137                    android:path="/"
137-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
138                    android:scheme="recaptcha" />
138-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
139            </intent-filter>
140        </activity>
141
142        <service
142-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:66:9-72:19
143            android:name="com.google.firebase.components.ComponentDiscoveryService"
143-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:67:13-84
144            android:directBootAware="true"
144-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
145            android:exported="false" >
145-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:68:13-37
146            <meta-data
146-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:69:13-71:85
147                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
147-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:70:17-109
148                android:value="com.google.firebase.components.ComponentRegistrar" />
148-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\AndroidManifest.xml:71:17-82
149            <meta-data
149-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:17:13-19:85
150                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
150-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:18:17-122
151                android:value="com.google.firebase.components.ComponentRegistrar" />
151-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:19:17-82
152            <meta-data
152-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:20:13-22:85
153                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
153-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:21:17-111
154                android:value="com.google.firebase.components.ComponentRegistrar" />
154-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:22:17-82
155            <meta-data
155-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:37:13-39:85
156                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
156-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:38:17-139
157                android:value="com.google.firebase.components.ComponentRegistrar" />
157-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:39:17-82
158            <meta-data
158-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:15:13-17:85
159                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
159-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:16:17-126
160                android:value="com.google.firebase.components.ComponentRegistrar" />
160-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:17:17-82
161            <meta-data
161-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:18:13-20:85
162                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
162-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:19:17-115
163                android:value="com.google.firebase.components.ComponentRegistrar" />
163-->[com.google.firebase:firebase-crashlytics:19.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\AndroidManifest.xml:20:17-82
164            <meta-data
164-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:30:13-32:85
165                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
165-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:31:17-118
166                android:value="com.google.firebase.components.ComponentRegistrar" />
166-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:32:17-82
167            <meta-data
167-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:33:13-35:85
168                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
168-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:34:17-107
169                android:value="com.google.firebase.components.ComponentRegistrar" />
169-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:35:17-82
170            <meta-data
170-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:29:13-31:85
171                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
171-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:30:17-117
172                android:value="com.google.firebase.components.ComponentRegistrar" />
172-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:31:17-82
173            <meta-data
173-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
174                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
174-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
175                android:value="com.google.firebase.components.ComponentRegistrar" />
175-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
176            <meta-data
176-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
177                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
177-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
178                android:value="com.google.firebase.components.ComponentRegistrar" />
178-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
179            <meta-data
179-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
180                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
180-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
181                android:value="com.google.firebase.components.ComponentRegistrar" />
181-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
182            <meta-data
182-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
183                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
183-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
184                android:value="com.google.firebase.components.ComponentRegistrar" />
184-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
185            <meta-data
185-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d29eeb71e4b590606a79f795e337f4\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
186                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
186-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d29eeb71e4b590606a79f795e337f4\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
187                android:value="com.google.firebase.components.ComponentRegistrar" />
187-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d29eeb71e4b590606a79f795e337f4\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
188            <meta-data
188-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
189                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
189-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
190                android:value="com.google.firebase.components.ComponentRegistrar" />
190-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
191            <meta-data
191-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e2967fd5b276966c87bd4e88ea401c8\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
192                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
192-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e2967fd5b276966c87bd4e88ea401c8\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
193                android:value="com.google.firebase.components.ComponentRegistrar" />
193-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e2967fd5b276966c87bd4e88ea401c8\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
194        </service>
195        <service
195-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
196            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
196-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
197            android:enabled="true"
197-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
198            android:exported="false" >
198-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
199            <meta-data
199-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
200                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
200-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
201                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
201-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
202        </service>
203
204        <activity
204-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
205            android:name="androidx.credentials.playservices.HiddenActivity"
205-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
206            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
206-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
207            android:enabled="true"
207-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
208            android:exported="false"
208-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
209            android:fitsSystemWindows="true"
209-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
210            android:theme="@style/Theme.Hidden" >
210-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
211        </activity>
212        <activity
212-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:23:9-27:75
213            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
213-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:24:13-93
214            android:excludeFromRecents="true"
214-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:25:13-46
215            android:exported="false"
215-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:26:13-37
216            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
216-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:27:13-72
217        <!--
218            Service handling Google Sign-In user revocation. For apps that do not integrate with
219            Google Sign-In, this service will never be started.
220        -->
221        <service
221-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:33:9-37:51
222            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
222-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:34:13-89
223            android:exported="true"
223-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:35:13-36
224            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
224-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:36:13-107
225            android:visibleToInstantApps="true" />
225-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\AndroidManifest.xml:37:13-48
226
227        <receiver
227-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:29:9-33:20
228            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
228-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:30:13-85
229            android:enabled="true"
229-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:31:13-35
230            android:exported="false" >
230-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:32:13-37
231        </receiver>
232
233        <service
233-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:35:9-38:40
234            android:name="com.google.android.gms.measurement.AppMeasurementService"
234-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:36:13-84
235            android:enabled="true"
235-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:37:13-35
236            android:exported="false" />
236-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:38:13-37
237        <service
237-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:39:9-43:72
238            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
238-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:40:13-87
239            android:enabled="true"
239-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:41:13-35
240            android:exported="false"
240-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:42:13-37
241            android:permission="android.permission.BIND_JOB_SERVICE" />
241-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:43:13-69
242
243        <property
243-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:30:9-32:61
244            android:name="android.adservices.AD_SERVICES_CONFIG"
244-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:31:13-65
245            android:resource="@xml/ga_ad_services_config" />
245-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:32:13-58
246
247        <service
247-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:22:9-25:40
248            android:name="com.google.firebase.sessions.SessionLifecycleService"
248-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:23:13-80
249            android:enabled="true"
249-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:24:13-35
250            android:exported="false" />
250-->[com.google.firebase:firebase-sessions:2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\AndroidManifest.xml:25:13-37
251
252        <provider
252-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
253            android:name="com.google.firebase.provider.FirebaseInitProvider"
253-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
254            android:authorities="com.example.allinone.firebaseinitprovider"
254-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
255            android:directBootAware="true"
255-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
256            android:exported="false"
256-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
257            android:initOrder="100" />
257-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
258
259        <activity
259-->[androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5d37fd89a97cf267377a06b09c52112\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:23:9-25:39
260            android:name="androidx.activity.ComponentActivity"
260-->[androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5d37fd89a97cf267377a06b09c52112\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:24:13-63
261            android:exported="true" />
261-->[androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5d37fd89a97cf267377a06b09c52112\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:25:13-36
262        <activity
262-->[androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e326cf46a3e0f3591896fb1f0061a989\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
263            android:name="androidx.compose.ui.tooling.PreviewActivity"
263-->[androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e326cf46a3e0f3591896fb1f0061a989\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
264            android:exported="true" />
264-->[androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e326cf46a3e0f3591896fb1f0061a989\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
265
266        <uses-library
266-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c656b25fa2dc95e881345daed065935\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
267            android:name="android.ext.adservices"
267-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c656b25fa2dc95e881345daed065935\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
268            android:required="false" />
268-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c656b25fa2dc95e881345daed065935\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
269
270        <service
270-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:39:9-45:35
271            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
271-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:40:13-88
272            android:directBootAware="false"
272-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:41:13-44
273            android:enabled="@bool/enable_system_alarm_service_default"
273-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:42:13-72
274            android:exported="false" />
274-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:43:13-37
275        <service
275-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:46:9-52:35
276            android:name="androidx.work.impl.background.systemjob.SystemJobService"
276-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:47:13-84
277            android:directBootAware="false"
277-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:48:13-44
278            android:enabled="@bool/enable_system_job_service_default"
278-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:49:13-70
279            android:exported="true"
279-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:50:13-36
280            android:permission="android.permission.BIND_JOB_SERVICE" />
280-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:51:13-69
281        <service
281-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:53:9-59:35
282            android:name="androidx.work.impl.foreground.SystemForegroundService"
282-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:54:13-81
283            android:directBootAware="false"
283-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:55:13-44
284            android:enabled="@bool/enable_system_foreground_service_default"
284-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:56:13-77
285            android:exported="false" />
285-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:57:13-37
286
287        <receiver
287-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:61:9-66:35
288            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
288-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:62:13-88
289            android:directBootAware="false"
289-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:63:13-44
290            android:enabled="true"
290-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:64:13-35
291            android:exported="false" />
291-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:65:13-37
292        <receiver
292-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:67:9-77:20
293            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
293-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:68:13-106
294            android:directBootAware="false"
294-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:69:13-44
295            android:enabled="false"
295-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:70:13-36
296            android:exported="false" >
296-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:71:13-37
297            <intent-filter>
297-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:73:13-76:29
298                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
298-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:17-87
298-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:25-84
299                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
299-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:17-90
299-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:25-87
300            </intent-filter>
301        </receiver>
302        <receiver
302-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:78:9-88:20
303            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
303-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:79:13-104
304            android:directBootAware="false"
304-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:80:13-44
305            android:enabled="false"
305-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:81:13-36
306            android:exported="false" >
306-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:82:13-37
307            <intent-filter>
307-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:84:13-87:29
308                <action android:name="android.intent.action.BATTERY_OKAY" />
308-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:17-77
308-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:25-74
309                <action android:name="android.intent.action.BATTERY_LOW" />
309-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:17-76
309-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:25-73
310            </intent-filter>
311        </receiver>
312        <receiver
312-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:89:9-99:20
313            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
313-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:90:13-104
314            android:directBootAware="false"
314-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:91:13-44
315            android:enabled="false"
315-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:92:13-36
316            android:exported="false" >
316-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:93:13-37
317            <intent-filter>
317-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:95:13-98:29
318                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
318-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:17-83
318-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:25-80
319                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
319-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:17-82
319-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:25-79
320            </intent-filter>
321        </receiver>
322        <receiver
322-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:100:9-109:20
323            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
323-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:101:13-103
324            android:directBootAware="false"
324-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:102:13-44
325            android:enabled="false"
325-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:103:13-36
326            android:exported="false" >
326-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:104:13-37
327            <intent-filter>
327-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:106:13-108:29
328                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
328-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:17-79
328-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:25-76
329            </intent-filter>
330        </receiver>
331        <receiver
331-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:110:9-121:20
332            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
332-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:111:13-88
333            android:directBootAware="false"
333-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:112:13-44
334            android:enabled="false"
334-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:113:13-36
335            android:exported="false" >
335-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:114:13-37
336            <intent-filter>
336-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:116:13-120:29
337                <action android:name="android.intent.action.BOOT_COMPLETED" />
337-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:117:17-79
337-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:117:25-76
338                <action android:name="android.intent.action.TIME_SET" />
338-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:17-73
338-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:25-70
339                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
339-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:17-81
339-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:25-78
340            </intent-filter>
341        </receiver>
342        <receiver
342-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:122:9-131:20
343            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
343-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:123:13-99
344            android:directBootAware="false"
344-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:124:13-44
345            android:enabled="@bool/enable_system_alarm_service_default"
345-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:125:13-72
346            android:exported="false" >
346-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:126:13-37
347            <intent-filter>
347-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:128:13-130:29
348                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
348-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:17-98
348-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:25-95
349            </intent-filter>
350        </receiver>
351        <receiver
351-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:132:9-142:20
352            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
352-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:133:13-78
353            android:directBootAware="false"
353-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:134:13-44
354            android:enabled="true"
354-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:135:13-35
355            android:exported="true"
355-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:136:13-36
356            android:permission="android.permission.DUMP" >
356-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:137:13-57
357            <intent-filter>
357-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:139:13-141:29
358                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
358-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:17-88
358-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:25-85
359            </intent-filter>
360        </receiver>
361
362        <uses-library
362-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
363            android:name="androidx.window.extensions"
363-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
364            android:required="false" />
364-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
365        <uses-library
365-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
366            android:name="androidx.window.sidecar"
366-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
367            android:required="false" />
367-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
368
369        <activity
369-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd483c27f09d2683183fc5f548eeb5d9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
370            android:name="com.google.android.gms.common.api.GoogleApiActivity"
370-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd483c27f09d2683183fc5f548eeb5d9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
371            android:exported="false"
371-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd483c27f09d2683183fc5f548eeb5d9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
372            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
372-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd483c27f09d2683183fc5f548eeb5d9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
373
374        <service
374-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3bc0680d46b752b3dba03a568ba1a3\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
375            android:name="androidx.room.MultiInstanceInvalidationService"
375-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3bc0680d46b752b3dba03a568ba1a3\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
376            android:directBootAware="true"
376-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3bc0680d46b752b3dba03a568ba1a3\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
377            android:exported="false" />
377-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3bc0680d46b752b3dba03a568ba1a3\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
378        <service
378-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
379            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
379-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
380            android:exported="false" >
380-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
381            <meta-data
381-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
382                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
382-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
383                android:value="cct" />
383-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
384        </service>
385
386        <receiver
386-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
387            android:name="androidx.profileinstaller.ProfileInstallReceiver"
387-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
388            android:directBootAware="false"
388-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
389            android:enabled="true"
389-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
390            android:exported="true"
390-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
391            android:permission="android.permission.DUMP" >
391-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
392            <intent-filter>
392-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
393                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
393-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
393-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
394            </intent-filter>
395            <intent-filter>
395-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
396                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
396-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
396-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
397            </intent-filter>
398            <intent-filter>
398-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
399                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
399-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
399-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
400            </intent-filter>
401            <intent-filter>
401-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
402                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
402-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
402-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
403            </intent-filter>
404        </receiver>
405
406        <service
406-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
407            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
407-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
408            android:exported="false"
408-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
409            android:permission="android.permission.BIND_JOB_SERVICE" >
409-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
410        </service>
411
412        <receiver
412-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
413            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
413-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
414            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
414-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
415        <activity
415-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed98f692eef56b9ec9255f707f2cecc8\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
416            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
416-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed98f692eef56b9ec9255f707f2cecc8\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
417            android:exported="false"
417-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed98f692eef56b9ec9255f707f2cecc8\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
418            android:stateNotNeeded="true"
418-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed98f692eef56b9ec9255f707f2cecc8\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
419            android:theme="@style/Theme.PlayCore.Transparent" />
419-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed98f692eef56b9ec9255f707f2cecc8\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
420    </application>
421
422</manifest>
