// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemProgramBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView programDescription;

  @NonNull
  public final TextView programExerciseCount;

  @NonNull
  public final TextView programName;

  private ItemProgramBinding(@NonNull MaterialCardView rootView,
      @NonNull TextView programDescription, @NonNull TextView programExerciseCount,
      @NonNull TextView programName) {
    this.rootView = rootView;
    this.programDescription = programDescription;
    this.programExerciseCount = programExerciseCount;
    this.programName = programName;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemProgramBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemProgramBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_program, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemProgramBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.program_description;
      TextView programDescription = ViewBindings.findChildViewById(rootView, id);
      if (programDescription == null) {
        break missingId;
      }

      id = R.id.program_exercise_count;
      TextView programExerciseCount = ViewBindings.findChildViewById(rootView, id);
      if (programExerciseCount == null) {
        break missingId;
      }

      id = R.id.program_name;
      TextView programName = ViewBindings.findChildViewById(rootView, id);
      if (programName == null) {
        break missingId;
      }

      return new ItemProgramBinding((MaterialCardView) rootView, programDescription,
          programExerciseCount, programName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
