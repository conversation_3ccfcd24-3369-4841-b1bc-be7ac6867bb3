<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="16dp"
    android:background="?attr/selectableItemBackground"
    android:gravity="center_vertical"
    android:minHeight="56dp">

    <!-- Color indicator -->
    <View
        android:id="@+id/colorIndicator"
        android:layout_width="4dp"
        android:layout_height="32dp"
        android:layout_marginEnd="12dp"
        android:background="@color/blue_500" />

    <!-- Main content -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- Group title and task count -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/groupTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="?attr/colorOnSurface"
                tools:text="App Development" />

            <TextView
                android:id="@+id/taskCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:layout_marginStart="8dp"
                tools:text="3/5 completed" />

        </LinearLayout>

        <!-- Progress bar -->
        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="4dp"
            android:layout_marginTop="4dp"
            android:max="100"
            android:progressTint="?attr/colorPrimary"
            android:progressBackgroundTint="?attr/colorSurfaceVariant"
            tools:progress="60" />

    </LinearLayout>

    <!-- Expand/collapse icon -->
    <ImageView
        android:id="@+id/expandIcon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="8dp"
        android:src="@drawable/ic_expand_more"
        android:contentDescription="@string/expand_collapse"
        app:tint="?attr/colorOnSurfaceVariant" />

</LinearLayout> 