<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_investment_selection" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_investment_selection.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_investment_selection_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="38" endOffset="51"/></Target><Target id="@+id/investmentName" view="TextView"><Expressions/><location startLine="16" startOffset="8" endLine="21" endOffset="44"/></Target><Target id="@+id/investmentType" view="TextView"><Expressions/><location startLine="23" startOffset="8" endLine="28" endOffset="44"/></Target><Target id="@+id/investmentAmount" view="TextView"><Expressions/><location startLine="30" startOffset="8" endLine="36" endOffset="34"/></Target></Targets></Layout>