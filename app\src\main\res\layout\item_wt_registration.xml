<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="6dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="10dp">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/studentPhoto"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:contentDescription="@string/profile_image"
            android:scaleType="centerCrop"
            android:src="@drawable/default_profile"
            android:elevation="2dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="@id/amount"
            app:strokeColor="@color/colorPrimaryLight"
            app:strokeWidth="1dp"
            app:shapeAppearanceOverlay="@style/circleImageView" />

        <TextView
            android:id="@+id/studentName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textAppearance="?attr/textAppearanceSubtitle1"
            android:textStyle="bold"
            android:layout_marginStart="12dp"
            app:layout_constraintEnd_toStartOf="@+id/shareButton"
            app:layout_constraintStart_toEndOf="@+id/studentPhoto"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Student Name" />

        <ImageButton
            android:id="@+id/shareButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Share"
            android:padding="4dp"
            android:src="@drawable/ic_share"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/startDate"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:textAppearance="?attr/textAppearanceCaption"
            app:layout_constraintEnd_toStartOf="@+id/endDate"
            app:layout_constraintStart_toEndOf="@+id/studentPhoto"
            app:layout_constraintStart_toStartOf="@+id/studentName"
            app:layout_constraintTop_toBottomOf="@id/studentName"
            tools:text="Start: Jan 1, 2024" />

        <TextView
            android:id="@+id/endDate"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textAppearance="?attr/textAppearanceCaption"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/startDate"
            app:layout_constraintTop_toTopOf="@id/startDate"
            tools:text="End: Feb 1, 2024" />

        <TextView
            android:id="@+id/amount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/paymentStatusChip"
            app:layout_constraintStart_toStartOf="@+id/studentName"
            app:layout_constraintTop_toBottomOf="@id/startDate"
            tools:text="₺1000.00" />

        <com.google.android.material.chip.Chip
            android:id="@+id/paymentStatusChip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="10sp"
            android:minHeight="28dp"
            android:textColor="@color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/endDate"
            tools:text="Paid" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>