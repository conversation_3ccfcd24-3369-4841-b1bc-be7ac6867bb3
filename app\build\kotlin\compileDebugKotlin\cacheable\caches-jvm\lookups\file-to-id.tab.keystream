=app/src/main/java/com/example/allinone/AllinOneApplication.kt6app/src/main/java/com/example/allinone/MainActivity.ktHapp/src/main/java/com/example/allinone/adapters/BinanceFuturesAdapter.ktIapp/src/main/java/com/example/allinone/adapters/BinancePositionAdapter.ktJapp/src/main/java/com/example/allinone/adapters/CategoryDropdownAdapter.ktJapp/src/main/java/com/example/allinone/adapters/CategorySpendingAdapter.ktIapp/src/main/java/com/example/allinone/adapters/CategorySummaryAdapter.kt?app/src/main/java/com/example/allinone/adapters/EventAdapter.ktIapp/src/main/java/com/example/allinone/adapters/FullscreenImageAdapter.ktFapp/src/main/java/com/example/allinone/adapters/GroupedTasksAdapter.ktAapp/src/main/java/com/example/allinone/adapters/HistoryAdapter.ktDapp/src/main/java/com/example/allinone/adapters/InvestmentAdapter.ktLapp/src/main/java/com/example/allinone/adapters/InvestmentDropdownAdapter.ktIapp/src/main/java/com/example/allinone/adapters/InvestmentImageAdapter.ktIapp/src/main/java/com/example/allinone/adapters/InvestmentPagerAdapter.ktMapp/src/main/java/com/example/allinone/adapters/InvestmentSelectionAdapter.ktBapp/src/main/java/com/example/allinone/adapters/LogEntryAdapter.ktCapp/src/main/java/com/example/allinone/adapters/NoteImageAdapter.ktCapp/src/main/java/com/example/allinone/adapters/NoteVideoAdapter.kt?app/src/main/java/com/example/allinone/adapters/NotesAdapter.ktAapp/src/main/java/com/example/allinone/adapters/SeminarAdapter.kt?app/src/main/java/com/example/allinone/adapters/TasksAdapter.ktEapp/src/main/java/com/example/allinone/adapters/TransactionAdapter.ktKapp/src/main/java/com/example/allinone/adapters/TransactionReportAdapter.ktCapp/src/main/java/com/example/allinone/adapters/VoiceNoteAdapter.ktAapp/src/main/java/com/example/allinone/adapters/WTEventAdapter.ktHapp/src/main/java/com/example/allinone/adapters/WTRegistrationAdapter.ktCapp/src/main/java/com/example/allinone/adapters/WTStudentAdapter.ktDapp/src/main/java/com/example/allinone/api/BinanceExternalService.ktDapp/src/main/java/com/example/allinone/api/BinanceWebSocketClient.kt?app/src/main/java/com/example/allinone/api/ExternalApiModels.ktFapp/src/main/java/com/example/allinone/api/ExternalBinanceApiClient.ktGapp/src/main/java/com/example/allinone/api/ExternalBinanceRepository.kt?app/src/main/java/com/example/allinone/backup/BackupActivity.kt>app/src/main/java/com/example/allinone/backup/BackupAdapter.kt<app/src/main/java/com/example/allinone/cache/CacheManager.kt=app/src/main/java/com/example/allinone/config/MuscleGroups.ktFapp/src/main/java/com/example/allinone/config/TransactionCategories.ktIapp/src/main/java/com/example/allinone/core/data/datasource/DataSource.ktMapp/src/main/java/com/example/allinone/core/data/repository/BaseRepository.kt=app/src/main/java/com/example/allinone/data/BinanceBalance.kt<app/src/main/java/com/example/allinone/data/BinanceFuture.kt=app/src/main/java/com/example/allinone/data/BinanceFutures.kt;app/src/main/java/com/example/allinone/data/BinanceOrder.kt>app/src/main/java/com/example/allinone/data/BinancePosition.kt>app/src/main/java/com/example/allinone/data/CategorySummary.kt4app/src/main/java/com/example/allinone/data/Event.kt7app/src/main/java/com/example/allinone/data/Exercise.kt:app/src/main/java/com/example/allinone/data/HistoryItem.kt9app/src/main/java/com/example/allinone/data/Investment.kt3app/src/main/java/com/example/allinone/data/Note.kt6app/src/main/java/com/example/allinone/data/Program.kt3app/src/main/java/com/example/allinone/data/Task.kt8app/src/main/java/com/example/allinone/data/TaskGroup.kt:app/src/main/java/com/example/allinone/data/Transaction.kt8app/src/main/java/com/example/allinone/data/VoiceNote.kt7app/src/main/java/com/example/allinone/data/WTLesson.kt=app/src/main/java/com/example/allinone/data/WTRegistration.kt8app/src/main/java/com/example/allinone/data/WTSeminar.kt8app/src/main/java/com/example/allinone/data/WTStudent.kt6app/src/main/java/com/example/allinone/data/Workout.ktCapp/src/main/java/com/example/allinone/data/common/BaseViewModel.kt=app/src/main/java/com/example/allinone/data/common/UiState.kt@app/src/main/java/com/example/allinone/data/local/AppDatabase.ktEapp/src/main/java/com/example/allinone/data/local/RoomCacheManager.ktFapp/src/main/java/com/example/allinone/data/local/dao/CachedNoteDao.ktIapp/src/main/java/com/example/allinone/data/local/dao/CachedProgramDao.ktMapp/src/main/java/com/example/allinone/data/local/dao/CachedTransactionDao.ktKapp/src/main/java/com/example/allinone/data/local/dao/CachedWTStudentDao.ktIapp/src/main/java/com/example/allinone/data/local/dao/CachedWorkoutDao.ktTapp/src/main/java/com/example/allinone/data/local/entities/CachedInvestmentEntity.ktNapp/src/main/java/com/example/allinone/data/local/entities/CachedNoteEntity.ktQapp/src/main/java/com/example/allinone/data/local/entities/CachedProgramEntity.ktUapp/src/main/java/com/example/allinone/data/local/entities/CachedTransactionEntity.ktSapp/src/main/java/com/example/allinone/data/local/entities/CachedWTStudentEntity.ktQapp/src/main/java/com/example/allinone/data/local/entities/CachedWorkoutEntity.kt6app/src/main/java/com/example/allinone/di/AppModule.ktWapp/src/main/java/com/example/allinone/feature/instagram/data/api/InstagramApiClient.ktXapp/src/main/java/com/example/allinone/feature/instagram/data/api/InstagramApiService.ktVapp/src/main/java/com/example/allinone/feature/instagram/data/model/InstagramModels.ktcapp/src/main/java/com/example/allinone/feature/instagram/data/repository/InstagramRepositoryImpl.ktNapp/src/main/java/com/example/allinone/feature/instagram/di/InstagramModule.ktaapp/src/main/java/com/example/allinone/feature/instagram/domain/repository/InstagramRepository.kt\app/src/main/java/com/example/allinone/feature/instagram/domain/usecase/InstagramUseCases.ktRapp/src/main/java/com/example/allinone/feature/instagram/ui/adapter/ChatAdapter.ktSapp/src/main/java/com/example/allinone/feature/instagram/ui/adapter/PostsAdapter.kt]app/src/main/java/com/example/allinone/feature/instagram/ui/viewmodel/InstagramAIViewModel.kt[app/src/main/java/com/example/allinone/feature/instagram/ui/viewmodel/InstagramViewModel.ktVapp/src/main/java/com/example/allinone/feature/notes/data/datasource/NoteDataSource.kt_app/src/main/java/com/example/allinone/feature/notes/data/datasource/NoteLocalDataSourceImpl.kt`app/src/main/java/com/example/allinone/feature/notes/data/datasource/NoteRemoteDataSourceImpl.ktZapp/src/main/java/com/example/allinone/feature/notes/data/repository/NoteRepositoryImpl.ktXapp/src/main/java/com/example/allinone/feature/notes/domain/repository/NoteRepository.kt[app/src/main/java/com/example/allinone/feature/program/data/datasource/ProgramDataSource.kthapp/src/main/java/com/example/allinone/feature/transactions/data/repository/TransactionRepositoryImpl.ktfapp/src/main/java/com/example/allinone/feature/transactions/domain/repository/TransactionRepository.kt^app/src/main/java/com/example/allinone/feature/wingtzun/data/datasource/WTStudentDataSource.kt[app/src/main/java/com/example/allinone/feature/workout/data/datasource/WorkoutDataSource.ktdapp/src/main/java/com/example/allinone/feature/workout/data/datasource/WorkoutLocalDataSourceImpl.kteapp/src/main/java/com/example/allinone/feature/workout/data/datasource/WorkoutRemoteDataSourceImpl.kt_app/src/main/java/com/example/allinone/feature/workout/data/repository/WorkoutRepositoryImpl.kt]app/src/main/java/com/example/allinone/feature/workout/domain/repository/WorkoutRepository.ktEapp/src/main/java/com/example/allinone/firebase/DataChangeNotifier.ktDapp/src/main/java/com/example/allinone/firebase/FirebaseIdManager.ktBapp/src/main/java/com/example/allinone/firebase/FirebaseManager.ktEapp/src/main/java/com/example/allinone/firebase/FirebaseRepository.ktFapp/src/main/java/com/example/allinone/firebase/FirebaseStorageUtil.ktLapp/src/main/java/com/example/allinone/firebase/GenericDataChangeNotifier.ktOapp/src/main/java/com/example/allinone/firebase/GenericOfflineQueueProcessor.kt?app/src/main/java/com/example/allinone/firebase/OfflineQueue.kt9app/src/main/java/com/example/allinone/ui/BaseFragment.kt<app/src/main/java/com/example/allinone/ui/CalendarDialogs.kt=app/src/main/java/com/example/allinone/ui/CalendarFragment.kt;app/src/main/java/com/example/allinone/ui/CalendarScreen.ktAapp/src/main/java/com/example/allinone/ui/CoinMFuturesFragment.ktGapp/src/main/java/com/example/allinone/ui/DatabaseManagementFragment.kt;app/src/main/java/com/example/allinone/ui/EditNoteScreen.ktDapp/src/main/java/com/example/allinone/ui/ExternalFuturesFragment.kt<app/src/main/java/com/example/allinone/ui/FuturesFragment.kt<app/src/main/java/com/example/allinone/ui/HistoryFragment.ktFapp/src/main/java/com/example/allinone/ui/InstagramBusinessFragment.kt@app/src/main/java/com/example/allinone/ui/InvestmentsFragment.ktCapp/src/main/java/com/example/allinone/ui/InvestmentsTabFragment.kt>app/src/main/java/com/example/allinone/ui/LogErrorsFragment.kt8app/src/main/java/com/example/allinone/ui/NotesScreen.kt8app/src/main/java/com/example/allinone/ui/TaskDialogs.kt:app/src/main/java/com/example/allinone/ui/TasksFragment.kt8app/src/main/java/com/example/allinone/ui/TasksScreen.ktFapp/src/main/java/com/example/allinone/ui/TransactionReportFragment.kt@app/src/main/java/com/example/allinone/ui/UsdMFuturesFragment.kt:app/src/main/java/com/example/allinone/ui/WorkoutScreen.ktEapp/src/main/java/com/example/allinone/ui/components/DrawingCanvas.ktKapp/src/main/java/com/example/allinone/ui/components/InteractiveHtmlText.ktHapp/src/main/java/com/example/allinone/ui/components/MediaAttachments.ktCapp/src/main/java/com/example/allinone/ui/components/MediaViewer.ktFapp/src/main/java/com/example/allinone/ui/components/RichTextEditor.ktEapp/src/main/java/com/example/allinone/ui/components/VoiceRecorder.ktDapp/src/main/java/com/example/allinone/ui/compose/wt/WTComponents.ktAapp/src/main/java/com/example/allinone/ui/compose/wt/WTDialogs.ktHapp/src/main/java/com/example/allinone/ui/compose/wt/WTRegistryScreen.ktKapp/src/main/java/com/example/allinone/ui/dialogs/TaskGroupDialogManager.ktDapp/src/main/java/com/example/allinone/ui/drawing/DrawingActivity.ktBapp/src/main/java/com/example/allinone/ui/futures/FuturesScreen.ktMapp/src/main/java/com/example/allinone/ui/instagram/InstagramAskAIFragment.ktPapp/src/main/java/com/example/allinone/ui/instagram/InstagramInsightsFragment.ktMapp/src/main/java/com/example/allinone/ui/instagram/InstagramPostsFragment.ktIapp/src/main/java/com/example/allinone/ui/investments/InvestmentScreen.ktGapp/src/main/java/com/example/allinone/ui/navigation/NavigationItems.kt8app/src/main/java/com/example/allinone/ui/theme/Color.kt8app/src/main/java/com/example/allinone/ui/theme/Theme.kt7app/src/main/java/com/example/allinone/ui/theme/Type.ktSapp/src/main/java/com/example/allinone/ui/transactions/TransactionOverviewScreen.ktQapp/src/main/java/com/example/allinone/ui/transactions/TransactionReportScreen.ktUapp/src/main/java/com/example/allinone/ui/transactions/TransactionsDashboardScreen.ktJapp/src/main/java/com/example/allinone/ui/workout/ActiveWorkoutFragment.ktMapp/src/main/java/com/example/allinone/ui/workout/WorkoutDashboardFragment.ktLapp/src/main/java/com/example/allinone/ui/workout/WorkoutExerciseFragment.ktDapp/src/main/java/com/example/allinone/ui/workout/WorkoutFragment.ktKapp/src/main/java/com/example/allinone/ui/workout/WorkoutProgramFragment.ktIapp/src/main/java/com/example/allinone/ui/workout/WorkoutStatsFragment.ktEapp/src/main/java/com/example/allinone/ui/workout/WorkoutViewModel.ktLapp/src/main/java/com/example/allinone/ui/workout/adapters/ProgramAdapter.ktTapp/src/main/java/com/example/allinone/ui/workout/adapters/ProgramExerciseAdapter.ktTapp/src/main/java/com/example/allinone/ui/workout/adapters/WorkoutExerciseAdapter.ktOapp/src/main/java/com/example/allinone/ui/workout/adapters/WorkoutLogAdapter.ktAapp/src/main/java/com/example/allinone/ui/wt/WTLessonsFragment.kt>app/src/main/java/com/example/allinone/ui/wt/WTPagerAdapter.ktIapp/src/main/java/com/example/allinone/ui/wt/WTRegisterContentFragment.ktBapp/src/main/java/com/example/allinone/ui/wt/WTRegisterFragment.ktBapp/src/main/java/com/example/allinone/ui/wt/WTRegistryFragment.ktBapp/src/main/java/com/example/allinone/ui/wt/WTSeminarsFragment.ktBapp/src/main/java/com/example/allinone/ui/wt/WTStudentsFragment.kt=app/src/main/java/com/example/allinone/utils/ApiKeyManager.kt<app/src/main/java/com/example/allinone/utils/BackupHelper.ktCapp/src/main/java/com/example/allinone/utils/ConnectivityMonitor.kt<app/src/main/java/com/example/allinone/utils/ErrorHandler.ktHapp/src/main/java/com/example/allinone/utils/GooglePlayServicesHelper.kt<app/src/main/java/com/example/allinone/utils/LogcatHelper.kt<app/src/main/java/com/example/allinone/utils/NetworkUtils.ktAapp/src/main/java/com/example/allinone/utils/NumberFormatUtils.ktCapp/src/main/java/com/example/allinone/utils/OfflineStatusHelper.kt>app/src/main/java/com/example/allinone/utils/TextStyleUtils.kt<app/src/main/java/com/example/allinone/utils/TradingUtils.ktMapp/src/main/java/com/example/allinone/utils/security/SecureStorageManager.ktFapp/src/main/java/com/example/allinone/viewmodels/CalendarViewModel.ktEapp/src/main/java/com/example/allinone/viewmodels/FuturesViewModel.ktEapp/src/main/java/com/example/allinone/viewmodels/HistoryViewModel.ktBapp/src/main/java/com/example/allinone/viewmodels/HomeViewModel.ktIapp/src/main/java/com/example/allinone/viewmodels/InvestmentsViewModel.ktFapp/src/main/java/com/example/allinone/viewmodels/LogErrorViewModel.ktCapp/src/main/java/com/example/allinone/viewmodels/NotesViewModel.ktCapp/src/main/java/com/example/allinone/viewmodels/TasksViewModel.ktGapp/src/main/java/com/example/allinone/viewmodels/WTLessonsViewModel.ktHapp/src/main/java/com/example/allinone/viewmodels/WTRegisterViewModel.ktHapp/src/main/java/com/example/allinone/viewmodels/WTSeminarsViewModel.kt>app/src/main/java/com/example/allinone/workers/BackupWorker.ktNapp/src/main/java/com/example/allinone/workers/ExpirationNotificationWorker.ktEapp/src/main/java/com/example/allinone/workers/LogcatCaptureWorker.kt:app/src/main/java/com/example/allinone/ui/HistoryScreen.kt<app/src/main/java/com/example/allinone/ui/InstagramScreen.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               