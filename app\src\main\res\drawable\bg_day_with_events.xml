<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <solid android:color="@color/colorPrimary" />
            <stroke
                android:width="2dp"
                android:color="@color/colorPrimary" />
            <padding
                android:bottom="4dp"
                android:left="4dp"
                android:right="4dp"
                android:top="4dp" />
        </shape>
    </item>
    
    <!-- Default state -->
    <item>
        <shape android:shape="oval">
            <stroke
                android:width="2dp"
                android:color="@color/colorPrimary" />
            <padding
                android:bottom="4dp"
                android:left="4dp"
                android:right="4dp"
                android:top="4dp" />
        </shape>
    </item>
</selector> 