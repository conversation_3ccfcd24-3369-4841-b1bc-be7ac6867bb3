<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_transactions_overview" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\fragment_transactions_overview.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_transactions_overview_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="256" endOffset="12"/></Target><Target id="@+id/balanceText" view="TextView"><Expressions/><location startLine="35" startOffset="16" endLine="42" endOffset="46"/></Target><Target id="@+id/incomeText" view="TextView"><Expressions/><location startLine="44" startOffset="16" endLine="49" endOffset="72"/></Target><Target id="@+id/expenseText" view="TextView"><Expressions/><location startLine="51" startOffset="16" endLine="56" endOffset="72"/></Target><Target id="@+id/pieChart" view="com.github.mikephil.charting.charts.PieChart"><Expressions/><location startLine="81" startOffset="16" endLine="85" endOffset="53"/></Target><Target id="@+id/amountLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="103" startOffset="16" endLine="115" endOffset="71"/></Target><Target id="@+id/amountInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="110" startOffset="20" endLine="114" endOffset="59"/></Target><Target id="@+id/typeLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="117" startOffset="16" endLine="130" endOffset="71"/></Target><Target id="@+id/typeDropdown" view="AutoCompleteTextView"><Expressions/><location startLine="125" startOffset="20" endLine="129" endOffset="50"/></Target><Target id="@+id/descriptionLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="132" startOffset="16" endLine="145" endOffset="71"/></Target><Target id="@+id/descriptionInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="140" startOffset="20" endLine="144" endOffset="50"/></Target><Target id="@+id/addExpenseButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="153" startOffset="20" endLine="162" endOffset="48"/></Target><Target id="@+id/addIncomeButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="164" startOffset="20" endLine="173" endOffset="48"/></Target><Target id="@+id/transactionsCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="179" startOffset="8" endLine="254" endOffset="59"/></Target><Target id="@+id/transactionsRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="201" startOffset="16" endLine="206" endOffset="59"/></Target><Target id="@+id/emptyTransactionsText" view="TextView"><Expressions/><location startLine="208" startOffset="16" endLine="216" endOffset="46"/></Target><Target id="@+id/paginationControls" view="LinearLayout"><Expressions/><location startLine="219" startOffset="16" endLine="252" endOffset="30"/></Target><Target id="@+id/prevPageButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="227" startOffset="20" endLine="234" endOffset="55"/></Target><Target id="@+id/pageIndicator" view="TextView"><Expressions/><location startLine="236" startOffset="20" endLine="242" endOffset="62"/></Target><Target id="@+id/nextPageButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="244" startOffset="20" endLine="251" endOffset="57"/></Target></Targets></Layout>