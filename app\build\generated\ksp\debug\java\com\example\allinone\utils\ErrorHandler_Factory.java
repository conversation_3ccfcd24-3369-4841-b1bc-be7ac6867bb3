package com.example.allinone.utils;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class ErrorHandler_Factory implements Factory<ErrorHandler> {
  private final Provider<Context> contextProvider;

  public ErrorHandler_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public ErrorHandler get() {
    return newInstance(contextProvider.get());
  }

  public static ErrorHandler_Factory create(Provider<Context> contextProvider) {
    return new ErrorHandler_Factory(contextProvider);
  }

  public static ErrorHandler newInstance(Context context) {
    return new ErrorHandler(context);
  }
}
