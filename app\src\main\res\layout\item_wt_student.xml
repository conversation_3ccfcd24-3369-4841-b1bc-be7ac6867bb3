<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/profileImage"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:contentDescription="@string/profile_image"
            android:scaleType="centerCrop"
            android:src="@drawable/default_profile"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearanceOverlay="@style/circleImageView" />

        <TextView
            android:id="@+id/studentName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="2dp"
            android:textAppearance="?attr/textAppearanceSubtitle1"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/statusIndicator"
            app:layout_constraintStart_toEndOf="@id/profileImage"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="John Doe" />

        <TextView
            android:id="@+id/phoneNumber"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="2dp"
            android:textAppearance="?attr/textAppearanceBody2"
            app:layout_constraintEnd_toStartOf="@id/statusIndicator"
            app:layout_constraintStart_toEndOf="@id/profileImage"
            app:layout_constraintTop_toBottomOf="@id/studentName"
            tools:text="+90 ************" />
            
        <TextView
            android:id="@+id/email"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:textAppearance="?attr/textAppearanceCaption"
            android:visibility="gone"
            app:layout_constraintEnd_toStartOf="@id/statusIndicator"
            app:layout_constraintStart_toEndOf="@id/profileImage"
            app:layout_constraintTop_toBottomOf="@id/phoneNumber"
            tools:text="<EMAIL>"
            tools:visibility="visible" />

        <View
            android:id="@+id/statusIndicator"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView> 