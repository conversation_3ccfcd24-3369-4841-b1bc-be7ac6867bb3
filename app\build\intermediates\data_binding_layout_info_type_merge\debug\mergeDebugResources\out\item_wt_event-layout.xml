<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_wt_event" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_wt_event.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_wt_event_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="40" endOffset="35"/></Target><Target id="@+id/eventTitle" view="TextView"><Expressions/><location startLine="16" startOffset="8" endLine="22" endOffset="38"/></Target><Target id="@+id/eventDate" view="TextView"><Expressions/><location startLine="24" startOffset="8" endLine="30" endOffset="38"/></Target><Target id="@+id/eventDescription" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="38" endOffset="57"/></Target></Targets></Layout>