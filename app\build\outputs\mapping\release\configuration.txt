# The proguard configuration file for the following section is C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
-keep class androidx.core.app.CoreComponentFactory { <init>(); }
-keep class androidx.core.content.FileProvider { <init>(); }
-keep class androidx.credentials.playservices.CredentialProviderMetadataHolder { <init>(); }
-keep class androidx.credentials.playservices.HiddenActivity { <init>(); }
-keep class androidx.profileinstaller.ProfileInstallReceiver { <init>(); }
-keep class androidx.room.MultiInstanceInvalidationService { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.RescheduleReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.SystemAlarmService { <init>(); }
-keep class androidx.work.impl.background.systemjob.SystemJobService { <init>(); }
-keep class androidx.work.impl.diagnostics.DiagnosticsReceiver { <init>(); }
-keep class androidx.work.impl.foreground.SystemForegroundService { <init>(); }
-keep class androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver { <init>(); }
-keep class com.example.allinone.AllinOneApplication { <init>(); }
-keep class com.example.allinone.MainActivity { <init>(); }
-keep class com.example.allinone.backup.BackupActivity { <init>(); }
-keep class com.example.allinone.ui.drawing.DrawingActivity { <init>(); }
-keep class com.google.android.datatransport.runtime.backends.TransportBackendDiscovery { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService { <init>(); }
-keep class com.google.android.gms.auth.api.signin.RevocationBoundService { <init>(); }
-keep class com.google.android.gms.auth.api.signin.internal.SignInHubActivity { <init>(); }
-keep class com.google.android.gms.common.api.GoogleApiActivity { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementJobService { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementReceiver { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementService { <init>(); }
-keep class com.google.android.play.core.common.PlayCoreDialogWrapperActivity { <init>(); }
-keep class com.google.firebase.auth.internal.GenericIdpActivity { <init>(); }
-keep class com.google.firebase.auth.internal.RecaptchaActivity { <init>(); }
-keep class com.google.firebase.components.ComponentDiscoveryService { <init>(); }
-keep class com.google.firebase.provider.FirebaseInitProvider { <init>(); }
-keep class com.google.firebase.sessions.SessionLifecycleService { <init>(); }
-keep class android.widget.Space { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.app.AlertController$RecycleListView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ActionMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ExpandedMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ListMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContainer { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContextView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarOverlayLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActivityChooserView$InnerLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.AlertDialogLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ButtonBarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ContentFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.DialogTitle { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsLinearLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.SearchView { <init>(android.content.Context); }

-keep class androidx.appcompat.widget.SearchView$SearchAutoComplete { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.SwitchCompat { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.Toolbar { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ViewStubCompat { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.browser.browseractions.BrowserActionsFallbackMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.cardview.widget.CardView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.constraintlayout.helper.widget.Flow { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.constraintlayout.widget.ConstraintLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.coordinatorlayout.widget.CoordinatorLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.core.widget.NestedScrollView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.fragment.app.FragmentContainerView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.recyclerview.widget.RecyclerView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.swiperefreshlayout.widget.SwipeRefreshLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.viewpager2.widget.ViewPager2 { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.github.mikephil.charting.charts.LineChart { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.github.mikephil.charting.charts.PieChart { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.exoplayer2.ui.AspectRatioFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.exoplayer2.ui.SubtitleView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.exoplayer2.ui.TrackSelectionView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.appbar.MaterialToolbar { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.button.MaterialButton { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.button.MaterialButtonToggleGroup { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.card.MaterialCardView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.checkbox.MaterialCheckBox { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.chip.Chip { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.datepicker.MaterialCalendarGridView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.floatingactionbutton.FloatingActionButton { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.imageview.ShapeableImageView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.BaselineLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.CheckableImageButton { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.ClippableRoundedCornerLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.NavigationMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.NavigationMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.TouchObserverFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.radiobutton.MaterialRadioButton { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.snackbar.Snackbar$SnackbarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.snackbar.SnackbarContentLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.switchmaterial.SwitchMaterial { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.tabs.TabItem { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.tabs.TabLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.textfield.TextInputEditText { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.textfield.TextInputLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.ChipTextInputComboView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.ClockFaceView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.ClockHandView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.TimePickerView { <init>(android.content.Context, android.util.AttributeSet); }


# End of content from C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
# The proguard configuration file for the following section is C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.8.2
# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html
#
# Starting with version 2.2 of the Android plugin for Gradle, this file is distributed together with
# the plugin and unpacked at build-time. The files in $ANDROID_HOME are no longer maintained and
# will be ignored by new version of the Android plugin for Gradle.

# Optimizations: If you don't want to optimize, use the proguard-android.txt configuration file
# instead of this one, which turns off the optimization flags.
-allowaccessmodification

# Preserve some attributes that may be required for reflection.
-keepattributes AnnotationDefault,
                EnclosingMethod,
                InnerClasses,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations,
                Signature

-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService
-keep public class com.google.android.vending.licensing.ILicensingService
-dontnote com.android.vending.licensing.ILicensingService
-dontnote com.google.vending.licensing.ILicensingService
-dontnote com.google.android.vending.licensing.ILicensingService

# For native methods, see https://www.guardsquare.com/manual/configuration/examples#native
-keepclasseswithmembernames,includedescriptorclasses class * {
    native <methods>;
}

# Keep setters in Views so that animations can still work.
-keepclassmembers public class * extends android.view.View {
    void set*(***);
    *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick.
-keepclassmembers class * extends android.app.Activity {
    public void *(android.view.View);
}

# For enumeration classes, see https://www.guardsquare.com/manual/configuration/examples#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

# Preserve annotated Javascript interface methods.
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# The support libraries contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version. We know about them, and they are safe.
-dontnote android.support.**
-dontnote androidx.**
-dontwarn android.support.**
-dontwarn androidx.**

# Understand the @Keep support annotation.
-keep class android.support.annotation.Keep

-keep @android.support.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <init>(...);
}

# These classes are duplicated between android.jar and org.apache.http.legacy.jar.
-dontnote org.apache.http.**
-dontnote android.net.http.**

# These classes are duplicated between android.jar and core-lambda-stubs.jar.
-dontnote java.lang.invoke.**

# End of content from C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.8.2
# The proguard configuration file for the following section is C:\Users\<USER>\Documents\GitHub\AllinOne\app\proguard-rules.pro
# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# Keep all data classes and models
-keep class com.example.allinone.data.** { *; }
-keep class com.example.allinone.feature.**.data.model.** { *; }

# Keep all ViewModels and related classes
-keep class com.example.allinone.viewmodels.** { *; }
-keep class com.example.allinone.feature.**.ui.viewmodel.** { *; }

# Keep all Fragments and Activities
-keep class com.example.allinone.ui.** { *; }
-keep class com.example.allinone.feature.**.ui.** { *; }

# Keep Firebase/Firestore classes
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.firebase.**
-dontwarn com.google.android.gms.**

# Keep all classes with no-arg constructors (for Firebase deserialization)
-keepclassmembers class * {
    <init>();
}

# Keep all classes annotated with @Keep
-keep @androidx.annotation.Keep class *
-keepclassmembers class * {
    @androidx.annotation.Keep *;
}

# Keep all Serializable classes
-keep class * implements java.io.Serializable { *; }

# Keep all Parcelable classes
-keep class * implements android.os.Parcelable { *; }

# General Android rules
-keepattributes Signature
-keepattributes *Annotation*
-keepattributes EnclosingMethod
-keepattributes InnerClasses

# Gson specific rules
-keepattributes Signature
-keepattributes *Annotation*
-dontwarn sun.misc.**
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Gson TypeToken rules
-keep class com.google.gson.reflect.TypeToken
-keep class * extends com.google.gson.reflect.TypeToken
-keep public class * implements java.lang.reflect.Type

# Preserve Gson generic signatures
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.SerializedName <fields>;
}

# Keep all Instagram models (prevent Firebase deserialization issues)
-keep class com.example.allinone.feature.instagram.data.model.** { *; }

# Keep all classes used with reflection
-keep class com.example.allinone.feature.instagram.data.model.InstagramPost { *; }
-keep class com.example.allinone.feature.instagram.data.model.InstagramMetrics { *; }
-keep class com.example.allinone.feature.instagram.data.model.InstagramAccount { *; }
-keep class com.example.allinone.feature.instagram.data.model.InstagramAnalytics { *; }
-keep class com.example.allinone.feature.instagram.data.model.AnalyticsSummary { *; }
-keep class com.example.allinone.feature.instagram.data.model.SyncInfo { *; }
-keep class com.example.allinone.feature.instagram.data.model.ChatMessage { *; }
-keep class com.example.allinone.feature.instagram.data.model.AISource { *; }
-keep class com.example.allinone.feature.instagram.data.model.AIMetadata { *; }
-keep class com.example.allinone.feature.instagram.data.model.AISourceMetadata { *; }

# Keep all Task and TaskGroup classes
-keep class com.example.allinone.data.Task { *; }
-keep class com.example.allinone.data.TaskGroup { *; }

# Keep all Firebase repository classes
-keep class com.example.allinone.firebase.** { *; }

# Keep all adapters (to prevent issues with ViewHolder access)
-keep class com.example.allinone.adapters.** { *; }

# Hilt/Dagger rules
-dontwarn com.google.errorprone.annotations.**
-keep class dagger.hilt.** { *; }
-keep class javax.inject.** { *; }
-keep class * extends dagger.hilt.internal.GeneratedComponent
-keep class **_HiltComponents$* { *; }
-keep class **_*Factory { *; }
-keep class **_*Module { *; }

# SLF4J rules - suppress warnings for missing implementation classes
-dontwarn org.slf4j.**
-dontwarn org.slf4j.impl.**
-keep class org.slf4j.** { *; }

# WebSocket library rules
-dontwarn org.java_websocket.**
-keep class org.java_websocket.** { *; }

# OkHttp and Retrofit rules
-dontwarn okhttp3.**
-dontwarn retrofit2.**
-keep class okhttp3.** { *; }
-keep class retrofit2.** { *; }
# End of content from C:\Users\<USER>\Documents\GitHub\AllinOne\app\proguard-rules.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ab49c54b76bd63e9b51dedbdd7ffaa2\transformed\databinding-runtime-8.8.2\proguard.txt
-dontwarn androidx.databinding.ViewDataBinding
-dontwarn androidx.databinding.ViewDataBinding$LiveDataListener

# instant apps load these via reflection so we need to keep them.
-keep public class * extends androidx.databinding.DataBinderMapper

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ab49c54b76bd63e9b51dedbdd7ffaa2\transformed\databinding-runtime-8.8.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\536a5d77f69c07dc1aedc4b2fccb6e40\transformed\navigation-common-2.8.2\proguard.txt
# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# NavArgsLazy creates NavArgs instances using reflection
-if public class ** implements androidx.navigation.NavArgs
-keepclassmembers public class <1> {
    ** fromBundle(android.os.Bundle);
}

# Retain the @Navigator.Name annotation on each subclass of Navigator.
# R8 full mode only retains annotations on items matched by a -keep rule,
# hence the extra -keep rule for the subclasses of Navigator.
#
# A -keep rule for the Navigator.Name annotation class is not required
# since the annotation is referenced from the code.
-keepattributes RuntimeVisibleAnnotations
-keep,allowobfuscation,allowshrinking class * extends androidx.navigation.Navigator

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\536a5d77f69c07dc1aedc4b2fccb6e40\transformed\navigation-common-2.8.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e82eb3f2167345ace1b8e91ea59beec2\transformed\navigation-ui-2.8.2\proguard.txt
# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# setProgress is called via an ObjectAnimator in AbstractAppBarOnDestinationChangedListener
-keepclassmembers class androidx.appcompat.graphics.drawable.DrawerArrowDrawable {
    void setProgress(float);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e82eb3f2167345ace1b8e91ea59beec2\transformed\navigation-ui-2.8.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75ac7f2a23ffbf48dd6409062de1d131\transformed\material-1.12.0\proguard.txt
# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# CoordinatorLayout resolves the behaviors of its child components with reflection.
-keep public class * extends androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>();
}

# Make sure we keep annotations for CoordinatorLayout's DefaultBehavior
-keepattributes RuntimeVisible*Annotation*

# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# AppCompatViewInflater reads the viewInflaterClass theme attribute which then
# reflectively instantiates MaterialComponentsViewInflater using the no-argument
# constructor. We only need to keep this constructor and the class name if
# AppCompatViewInflater is also being kept.
-if class androidx.appcompat.app.AppCompatViewInflater
-keep class com.google.android.material.theme.MaterialComponentsViewInflater {
    <init>();
}


# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75ac7f2a23ffbf48dd6409062de1d131\transformed\material-1.12.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1375e70be1fe8041f38907ceec5f1093\transformed\appcompat-1.7.0\proguard.txt
# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl* {
  <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1375e70be1fe8041f38907ceec5f1093\transformed\appcompat-1.7.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\proguard.txt
-dontwarn com.google.appengine.api.**
-dontwarn okio.**
-dontwarn org.apache.**
-dontwarn retrofit.android.**
-dontwarn retrofit.appengine.**
-dontwarn retrofit.client.**
-dontwarn rx.**

# This is necessary for keeping SecureTokenHttpApi and IdentityToolkitHttpApi
# Otherwise those classes get stripped out, as they are only being used
# reflectively.

-keepclasseswithmembernames interface * {
    @retrofit.http.* <methods>;
}

# This is necessary for parsing JSON responses, since the JSON converter uses reflection to figure out the class/type of response.
# We mainly need the *Response.classes to not be stripped out. All the firebase-auth classes are proguarded into "com.google.android.gms.internal.firebase-auth-api*".

-keep class com.google.android.gms.internal.** { *; }

# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.firebase-auth-api.zzajy {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\proguard.txt
-if class androidx.credentials.CredentialManager
-keep class androidx.credentials.playservices.** {
  *;
}
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f9ce903e2ff26f2c616d9362ffafb64\transformed\exoplayer-ui-2.19.1\proguard.txt
# Proguard rules specific to the UI module.

# Constructor method accessed via reflection in StyledPlayerView
-dontnote com.google.android.exoplayer2.video.spherical.SphericalGLSurfaceView
-keepclassmembers class com.google.android.exoplayer2.video.spherical.SphericalGLSurfaceView {
  <init>(android.content.Context);
}
-dontnote com.google.android.exoplayer2.video.VideoDecoderGLSurfaceView
-keepclassmembers class com.google.android.exoplayer2.video.VideoDecoderGLSurfaceView {
  <init>(android.content.Context);
}

# Constructor method accessed via reflection in TrackSelectionDialogBuilder
-dontnote androidx.appcompat.app.AlertDialog.Builder
-keepclassmembers class androidx.appcompat.app.AlertDialog$Builder {
  <init>(android.content.Context, int);
  public android.content.Context getContext();
  public androidx.appcompat.app.AlertDialog$Builder setTitle(java.lang.CharSequence);
  public androidx.appcompat.app.AlertDialog$Builder setView(android.view.View);
  public androidx.appcompat.app.AlertDialog$Builder setPositiveButton(int, android.content.DialogInterface$OnClickListener);
  public androidx.appcompat.app.AlertDialog$Builder setNegativeButton(int, android.content.DialogInterface$OnClickListener);
  public androidx.appcompat.app.AlertDialog create();
}
# Equivalent methods needed when the library is de-jetified.
-dontnote android.support.v7.app.AlertDialog.Builder
-keepclassmembers class android.support.v7.app.AlertDialog$Builder {
  <init>(android.content.Context, int);
  public android.content.Context getContext();
  public android.support.v7.app.AlertDialog$Builder setTitle(java.lang.CharSequence);
  public android.support.v7.app.AlertDialog$Builder setView(android.view.View);
  public android.support.v7.app.AlertDialog$Builder setPositiveButton(int, android.content.DialogInterface$OnClickListener);
  public android.support.v7.app.AlertDialog$Builder setNegativeButton(int, android.content.DialogInterface$OnClickListener);
  public android.support.v7.app.AlertDialog create();
}

# Don't warn about checkerframework and Kotlin annotations
-dontwarn org.checkerframework.**
-dontwarn kotlin.annotations.jvm.**
-dontwarn javax.annotation.**

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f9ce903e2ff26f2c616d9362ffafb64\transformed\exoplayer-ui-2.19.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db3070082bb6446577445679923bae22\transformed\recyclerview-1.3.1\proguard.txt
# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# When layoutManager xml attribute is used, RecyclerView inflates
#LayoutManagers' constructors using reflection.
-keep public class * extends androidx.recyclerview.widget.RecyclerView$LayoutManager {
    public <init>(android.content.Context, android.util.AttributeSet, int, int);
    public <init>();
}

-keepclassmembers class androidx.recyclerview.widget.RecyclerView {
    public void suppressLayout(boolean);
    public boolean isLayoutSuppressed();
}
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db3070082bb6446577445679923bae22\transformed\recyclerview-1.3.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f697fc0f692dda17f643d9f51015b66\transformed\hilt-android-2.52\proguard.txt
# Keep for the reflective cast done in EntryPoints.
# See b/183070411#comment4 for more info.
-keep,allowobfuscation,allowshrinking @dagger.hilt.EntryPoint class *# Keep for the reflective cast done in EntryPoints.
# See b/183070411#comment4 for more info.
-keep,allowobfuscation,allowshrinking @dagger.hilt.android.EarlyEntryPoint class *# Keep for the reflective cast done in EntryPoints.
# See b/183070411#comment4 for more info.
-keep,allowobfuscation,allowshrinking @dagger.hilt.internal.ComponentEntryPoint class *
-keep,allowobfuscation,allowshrinking @dagger.hilt.internal.GeneratedEntryPoint class *
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f697fc0f692dda17f643d9f51015b66\transformed\hilt-android-2.52\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\660371d89149d49eb6de9d074ae462ea\transformed\glide-4.16.0\proguard.txt
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep class * extends com.bumptech.glide.module.AppGlideModule {
 <init>(...);
}
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}
-keep class com.bumptech.glide.load.data.ParcelFileDescriptorRewinder$InternalRewinder {
  *** rewind();
}

# Uncomment for DexGuard only
#-keepresourcexmlelements manifest/application/meta-data@value=GlideModule

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\660371d89149d49eb6de9d074ae462ea\transformed\glide-4.16.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\proguard.txt
# Needed for DNS resolution.  Present in OpenJDK, but not Android
-dontwarn javax.naming.**

# Don't warn about checkerframework
#
# Guava uses the checkerframework and the annotations
# can safely be ignored at runtime.
-dontwarn org.checkerframework.**

# Guava warnings:
-dontwarn java.lang.ClassValue
-dontwarn com.google.j2objc.annotations.Weak
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
-dontwarn javax.lang.model.element.Modifier

# Okhttp warnings.
-dontwarn okio.**
-dontwarn com.google.j2objc.annotations.**

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14c2bcefa55c73856487bee5353b1cb1\transformed\play-services-auth-base-18.0.10\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.auth.zzev {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14c2bcefa55c73856487bee5353b1cb1\transformed\play-services-auth-base-18.0.10\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjt {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\proguard.txt
# Can be removed once we pull in a dependency on firebase-common that includes
# https://github.com/firebase/firebase-android-sdk/pull/1472/commits/856f1ca1151cdd88679bbc778892f23dfa34fc06#diff-a2ed34b5a38b4c6c686b09e54865eb48
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjt {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f55701a2806ad5fbdfaf217a3797a4c8\transformed\play-services-measurement-sdk-22.1.2\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjt {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f55701a2806ad5fbdfaf217a3797a4c8\transformed\play-services-measurement-sdk-22.1.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\431ca28934aa0e759e1ba596c21cfdf5\transformed\recaptcha-18.5.1\proguard.txt
# Proguard cannot process META-INF/versions/9.
# See https://discuss.gradle.org/t/meta-inf-version-duplicate-error-when-using-proguard/31380
-dontwarn module-info

# Ignore the warning becuse ClassValueCtorCache is never used on Android.
-dontwarn kotlinx.coroutines.internal.ClassValueCtorCache

# Ignore warning due to the usage from Guava and kotlinx.coroutines.internal.ClassValueCtorCache
-dontwarn java.lang.ClassValue

# Ignore warning to accommodate the missing injar of kotlinx.coroutines.flow for
# androidx.slidingpanelayout.widget.
-dontwarn kotlinx.coroutines.flow.**

# This prevents the SDK to be obfuscated again when building the android app.
-keep class com.google.android.recaptcha.** { *; }

# This is required for recaptcha mobile to function properly.
# See: https://cloud.google.com/recaptcha-enterprise/docs/instrument-android-apps
-keep class com.google.android.play.core.integrity.** { *; }
-keep class com.google.android.gms.tasks.** {*;}

# To keep okhttp3 generated files which are used in our NetworkModule which is
# used widely across the app.
-dontwarn com.squareup.okhttp3.**
-dontwarn okhttp3.**
-keep class com.squareup.okhttp3.* { *;}
-keep class okhttp3.**
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.recaptcha.internal.zzks {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\431ca28934aa0e759e1ba596c21cfdf5\transformed\recaptcha-18.5.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ce1a7dd250c9120ea5bf8555ae67b1a\transformed\firebase-auth-interop-20.0.0\proguard.txt
# Can be removed once we pull in a dependency on firebase-common that includes
# https://github.com/firebase/firebase-android-sdk/pull/1472/commits/856f1ca1151cdd88679bbc778892f23dfa34fc06#diff-a2ed34b5a38b4c6c686b09e54865eb48
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ce1a7dd250c9120ea5bf8555ae67b1a\transformed\firebase-auth-interop-20.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\proguard.txt
-dontwarn com.google.firebase.platforminfo.KotlinDetector
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\506c2d7d88d4acfac211914fc9a152c8\transformed\play-services-measurement-impl-22.1.2\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjt {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\506c2d7d88d4acfac211914fc9a152c8\transformed\play-services-measurement-impl-22.1.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46fa31863f3fabfb17c38d00da312695\transformed\play-services-fido-20.1.0\proguard.txt
# Methods enable and disable in this class are complained as unresolved
# references, but they are system APIs and are not used by Fido client apps.
-dontwarn android.nfc.NfcAdapter

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46fa31863f3fabfb17c38d00da312695\transformed\play-services-fido-20.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7bf19bacce7fd4c141ebe47b25076b8\transformed\ui-release\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# We supply these as stubs and are able to link to them at runtime
# because they are hidden public classes in Android. We don't want
# R8 to complain about them not being there during optimization.
-dontwarn android.view.RenderNode
-dontwarn android.view.DisplayListCanvas
-dontwarn android.view.HardwareCanvas

-keepclassmembers class androidx.compose.ui.platform.ViewLayerContainer {
    protected void dispatchGetDisplayList();
}

-keepclassmembers class androidx.compose.ui.platform.AndroidComposeView {
    android.view.View findViewByAccessibilityIdTraversal(int);
}

# Users can create Modifier.Node instances that implement multiple Modifier.Node interfaces,
# so we cannot tell whether two modifier.node instances are of the same type without using
# reflection to determine the class type. See b/265188224 for more context.
-keep,allowshrinking class * extends androidx.compose.ui.node.ModifierNodeElement

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7bf19bacce7fd4c141ebe47b25076b8\transformed\ui-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63bcf4d35504fd2547407dc074988349\transformed\transition-1.5.0\proguard.txt
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep a field in transition that is used to keep a reference to weakly-referenced object
-keepclassmembers class androidx.transition.ChangeBounds$* extends android.animation.AnimatorListenerAdapter {
  androidx.transition.ChangeBounds$ViewBounds mViewBounds;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63bcf4d35504fd2547407dc074988349\transformed\transition-1.5.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0b251d390f4afb1f9ca91dbc800110d\transformed\coordinatorlayout-1.1.0\proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# CoordinatorLayout resolves the behaviors of its child components with reflection.
-keep public class * extends androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>();
}

# Make sure we keep annotations for CoordinatorLayout's DefaultBehavior and ViewPager's DecorView
-keepattributes *Annotation*

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0b251d390f4afb1f9ca91dbc800110d\transformed\coordinatorlayout-1.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d1983e8f816433307bdd0bbd836e24a\transformed\vectordrawable-animated-1.1.0\proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# keep setters in VectorDrawables so that animations can still work.
-keepclassmembers class androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$* {
   void set*(***);
   *** get*();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d1983e8f816433307bdd0bbd836e24a\transformed\vectordrawable-animated-1.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e87b0a766c58fccb5c3caec65c96631b\transformed\exoplayer-core-2.19.1\proguard.txt
# Proguard rules specific to the core module.

# Constructors accessed via reflection in DefaultRenderersFactory
-dontnote com.google.android.exoplayer2.ext.vp9.LibvpxVideoRenderer
-keepclassmembers class com.google.android.exoplayer2.ext.vp9.LibvpxVideoRenderer {
  <init>(long, android.os.Handler, com.google.android.exoplayer2.video.VideoRendererEventListener, int);
}
-dontnote com.google.android.exoplayer2.ext.av1.Libgav1VideoRenderer
-keepclassmembers class com.google.android.exoplayer2.ext.av1.Libgav1VideoRenderer {
  <init>(long, android.os.Handler, com.google.android.exoplayer2.video.VideoRendererEventListener, int);
}
-dontnote com.google.android.exoplayer2.ext.opus.LibopusAudioRenderer
-keepclassmembers class com.google.android.exoplayer2.ext.opus.LibopusAudioRenderer {
  <init>(android.os.Handler, com.google.android.exoplayer2.audio.AudioRendererEventListener, com.google.android.exoplayer2.audio.AudioSink);
}
-dontnote com.google.android.exoplayer2.ext.flac.LibflacAudioRenderer
-keepclassmembers class com.google.android.exoplayer2.ext.flac.LibflacAudioRenderer {
  <init>(android.os.Handler, com.google.android.exoplayer2.audio.AudioRendererEventListener, com.google.android.exoplayer2.audio.AudioSink);
}
-dontnote com.google.android.exoplayer2.ext.ffmpeg.FfmpegAudioRenderer
-keepclassmembers class com.google.android.exoplayer2.ext.ffmpeg.FfmpegAudioRenderer {
  <init>(android.os.Handler, com.google.android.exoplayer2.audio.AudioRendererEventListener, com.google.android.exoplayer2.audio.AudioSink);
}

# Constructors accessed via reflection in DefaultDownloaderFactory
-dontnote com.google.android.exoplayer2.source.dash.offline.DashDownloader
-keepclassmembers class com.google.android.exoplayer2.source.dash.offline.DashDownloader {
  <init>(com.google.android.exoplayer2.MediaItem, com.google.android.exoplayer2.upstream.cache.CacheDataSource$Factory, java.util.concurrent.Executor);
}
-dontnote com.google.android.exoplayer2.source.hls.offline.HlsDownloader
-keepclassmembers class com.google.android.exoplayer2.source.hls.offline.HlsDownloader {
  <init>(com.google.android.exoplayer2.MediaItem, com.google.android.exoplayer2.upstream.cache.CacheDataSource$Factory, java.util.concurrent.Executor);
}
-dontnote com.google.android.exoplayer2.source.smoothstreaming.offline.SsDownloader
-keepclassmembers class com.google.android.exoplayer2.source.smoothstreaming.offline.SsDownloader {
  <init>(com.google.android.exoplayer2.MediaItem, com.google.android.exoplayer2.upstream.cache.CacheDataSource$Factory, java.util.concurrent.Executor);
}

# Constructors accessed via reflection in DefaultMediaSourceFactory
-dontnote com.google.android.exoplayer2.source.dash.DashMediaSource$Factory
-keepclasseswithmembers class com.google.android.exoplayer2.source.dash.DashMediaSource$Factory {
  <init>(com.google.android.exoplayer2.upstream.DataSource$Factory);
}
-dontnote com.google.android.exoplayer2.source.hls.HlsMediaSource$Factory
-keepclasseswithmembers class com.google.android.exoplayer2.source.hls.HlsMediaSource$Factory {
  <init>(com.google.android.exoplayer2.upstream.DataSource$Factory);
}
-dontnote com.google.android.exoplayer2.source.smoothstreaming.SsMediaSource$Factory
-keepclasseswithmembers class com.google.android.exoplayer2.source.smoothstreaming.SsMediaSource$Factory {
  <init>(com.google.android.exoplayer2.upstream.DataSource$Factory);
}
-dontnote com.google.android.exoplayer2.source.rtsp.RtspMediaSource$Factory
-keepclasseswithmembers class com.google.android.exoplayer2.source.rtsp.RtspMediaSource$Factory {
  <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e87b0a766c58fccb5c3caec65c96631b\transformed\exoplayer-core-2.19.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\proguard.txt
-keep class * extends androidx.work.Worker
-keep class * extends androidx.work.InputMerger
# Keep all constructors on ListenableWorker, Worker
-keep public class * extends androidx.work.ListenableWorker {
    public <init>(...);
}
# We need to keep WorkerParameters for the ListenableWorker constructor
-keep class androidx.work.WorkerParameters

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd1523a13beb55e8e76f6d0d33c82918\transformed\media-1.6.0\proguard.txt
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Prevent Parcelable objects from being removed or renamed.
-keep class android.support.v4.media.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Prevent Parcelable objects from being removed or renamed.
-keep class androidx.media.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd1523a13beb55e8e76f6d0d33c82918\transformed\media-1.6.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# A rule that will keep classes that implement SidecarInterface$SidecarCallback if Sidecar seems
# be used. See b/157286362 and b/165268619 for details.
# TODO(b/208543178) investigate how to pass header jar to R8 so we don't need this rule
-if class androidx.window.layout.SidecarCompat {
  public setExtensionCallback(androidx.window.layout.ExtensionInterfaceCompat$ExtensionCallbackInterface);
}
-keep class androidx.window.layout.SidecarCompat$TranslatingCallback,
 androidx.window.layout.SidecarCompat$DistinctSidecarElementCallback {
  public onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState);
  public onWindowLayoutChanged(android.os.IBinder, androidx.window.sidecar.SidecarWindowLayoutInfo);
}
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd483c27f09d2683183fc5f548eeb5d9\transformed\play-services-base-18.5.0\proguard.txt
# b/35135904 Ensure that proguard will not strip the mResultGuardian.
-keepclassmembers class com.google.android.gms.common.api.internal.BasePendingResult {
  com.google.android.gms.common.api.internal.BasePendingResult$ReleasableResultGuardian mResultGuardian;
}



# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd483c27f09d2683183fc5f548eeb5d9\transformed\play-services-base-18.5.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f8c3e6828abcbffa3c9f5535dfc64a3\transformed\graphics-path-1.0.1\proguard.txt
-keepclasseswithmembers class androidx.graphics.path.** {
    native <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f8c3e6828abcbffa3c9f5535dfc64a3\transformed\graphics-path-1.0.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\proguard.txt
# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.ViewCompat$Api* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.WindowInsetsCompat$*Impl* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.app.NotificationCompat$*$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.os.UserHandleCompat$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.widget.EdgeEffectCompat$Api*Impl {
  <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f316befd3963bb027428722e4bcae7f\transformed\savedstate-1.2.1\proguard.txt
# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

-keepclassmembers,allowobfuscation class * implements androidx.savedstate.SavedStateRegistry$AutoRecreated {
    <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f316befd3963bb027428722e4bcae7f\transformed\savedstate-1.2.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a59630a39ebfd67a70a167ceaa9ba65d\transformed\lifecycle-viewmodel-release\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>();
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a59630a39ebfd67a70a167ceaa9ba65d\transformed\lifecycle-viewmodel-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5fb10182892807adb5a50c0d073a398\transformed\runtime-release\proguard.txt
-assumenosideeffects public class androidx.compose.runtime.ComposerKt {
    void sourceInformation(androidx.compose.runtime.Composer,java.lang.String);
    void sourceInformationMarkerStart(androidx.compose.runtime.Composer,int,java.lang.String);
    void sourceInformationMarkerEnd(androidx.compose.runtime.Composer);
}

# Composer's class initializer doesn't do anything but create an EMPTY object. Marking the
# initializers as having no side effects can help encourage shrinkers to merge/devirtualize Composer
# with ComposerImpl.
-assumenosideeffects public class androidx.compose.runtime.Composer {
    void <clinit>();
}
-assumenosideeffects public class androidx.compose.runtime.ComposerImpl {
    void <clinit>();
}
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5fb10182892807adb5a50c0d073a398\transformed\runtime-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c8d87b123cdc25e1157775c2004abe2\transformed\lifecycle-runtime-release\proguard.txt
-keepattributes AnnotationDefault,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations

-keepclassmembers enum androidx.lifecycle.Lifecycle$Event {
    <fields>;
}

-keep class * implements androidx.lifecycle.GeneratedAdapter {
    <init>(...);
}

-keepclassmembers class ** {
    @androidx.lifecycle.OnLifecycleEvent *;
}

# The deprecated `android.app.Fragment` creates `Fragment` instances using reflection.
# See: b/338958225, b/341537875
-keepclasseswithmembers,allowobfuscation public class androidx.lifecycle.ReportFragment {
    public <init>();
}

# this rule is need to work properly when app is compiled with api 28, see b/142778206
# Also this rule prevents registerIn from being inlined.
-keepclassmembers class androidx.lifecycle.ReportFragment$LifecycleCallbacks { *; }

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c8d87b123cdc25e1157775c2004abe2\transformed\lifecycle-runtime-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\770bfe3740ffe9976cba2d0c51b81483\transformed\lifecycle-viewmodel-savedstate-2.8.6\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>(androidx.lifecycle.SavedStateHandle);
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application,androidx.lifecycle.SavedStateHandle);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\770bfe3740ffe9976cba2d0c51b81483\transformed\lifecycle-viewmodel-savedstate-2.8.6\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aea5573deca53022a416382f3264193f\transformed\lifecycle-process-2.8.6\proguard.txt
# this rule is need to work properly when app is compiled with api 28, see b/142778206
-keepclassmembers class * extends androidx.lifecycle.EmptyActivityLifecycleCallbacks { *; }
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aea5573deca53022a416382f3264193f\transformed\lifecycle-process-2.8.6\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d29603c3210a6d0855335cb17a945164\transformed\lifecycle-runtime-compose-release\proguard.txt
# Workaround for https://issuetracker.google.com/issues/346808608
#
# `androidx.lifecycle.compose.LocalLifecycleOwner` will reflectively lookup for
# `androidx.compose.ui.platform.LocalLifecycleOwner` to ensure backward compatibility
# when using Lifecycle 2.8+ with Compose 1.6.
#
# We need to keep the getter if the code using this is included.
#
# We need to suppress `ShrinkerUnresolvedReference` because the `LocalComposition` is in a
# different module.
#
#noinspection ShrinkerUnresolvedReference
-if public class androidx.compose.ui.platform.AndroidCompositionLocals_androidKt {
    public static *** getLocalLifecycleOwner();
}
-keep public class androidx.compose.ui.platform.AndroidCompositionLocals_androidKt {
    public static *** getLocalLifecycleOwner();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d29603c3210a6d0855335cb17a945164\transformed\lifecycle-runtime-compose-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3bc0680d46b752b3dba03a568ba1a3\transformed\room-runtime-2.6.1\proguard.txt
-keep class * extends androidx.room.RoomDatabase
-dontwarn androidx.room.paging.**
-dontwarn androidx.lifecycle.LiveData

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3bc0680d46b752b3dba03a568ba1a3\transformed\room-runtime-2.6.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\********************************\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# Allow R8 to optimize away the FastServiceLoader.
# Together with ServiceLoader optimization in R8
# this results in direct instantiation when loading Dispatchers.Main
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatcherLoader {
    boolean FAST_SERVICE_LOADER_ENABLED return false;
}

-assumenosideeffects class kotlinx.coroutines.internal.FastServiceLoaderKt {
    boolean ANDROID_DETECTED return true;
}

# Disable support for "Missing Main Dispatcher", since we always have Android main dispatcher
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatchersKt {
    boolean SUPPORT_MISSING return false;
}

# Statically turn off all debugging facilities and assertions
-assumenosideeffects class kotlinx.coroutines.DebugKt {
    boolean getASSERTIONS_ENABLED() return false;
    boolean getDEBUG() return false;
    boolean getRECOVER_STACK_TRACES() return false;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\********************************\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\414e0683e18e9aaca9df3e519609757c\transformed\datastore-preferences-1.0.0\proguard.txt
-keepclassmembers class * extends androidx.datastore.preferences.protobuf.GeneratedMessageLite {
    <fields>;
}
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\414e0683e18e9aaca9df3e519609757c\transformed\datastore-preferences-1.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4d7472c7627ffac9dbf4334eb863e38\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# When editing this file, update the following files as well:
# - META-INF/proguard/coroutines.pro
# - META-INF/com.android.tools/proguard/coroutines.pro

# Most of volatile fields are updated with AFU and should not be mangled
-keepclassmembers class kotlinx.coroutines.** {
    volatile <fields>;
}

# Same story for the standard library's SafeContinuation that also uses AtomicReferenceFieldUpdater
-keepclassmembers class kotlin.coroutines.SafeContinuation {
    volatile <fields>;
}

# These classes are only required by kotlinx.coroutines.debug.AgentPremain, which is only loaded when
# kotlinx-coroutines-core is used as a Java agent, so these are not needed in contexts where ProGuard is used.
-dontwarn java.lang.instrument.ClassFileTransformer
-dontwarn sun.misc.SignalHandler
-dontwarn java.lang.instrument.Instrumentation
-dontwarn sun.misc.Signal

# Only used in `kotlinx.coroutines.internal.ExceptionsConstructor`.
# The case when it is not available is hidden in a `try`-`catch`, as well as a check for Android.
-dontwarn java.lang.ClassValue

# An annotation used for build tooling, won't be directly accessed.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4d7472c7627ffac9dbf4334eb863e38\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9223f0e1271e59953c421b866dd50ca\transformed\play-services-tasks-18.2.0\proguard.txt


# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9223f0e1271e59953c421b866dd50ca\transformed\play-services-tasks-18.2.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1cab4525135b934e909b93124109edf\transformed\play-services-measurement-sdk-api-22.1.2\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjt {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1cab4525135b934e909b93124109edf\transformed\play-services-measurement-sdk-api-22.1.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd31ab42f6df184811fef9a3c088b0f0\transformed\play-services-measurement-base-22.1.2\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjt {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd31ab42f6df184811fef9a3c088b0f0\transformed\play-services-measurement-base-22.1.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98a7034e786415bc9e009aca9ccbea7a\transformed\play-services-basement-18.4.0\proguard.txt
# Needed when building against pre-Marshmallow SDK.
-dontwarn android.security.NetworkSecurityPolicy

# Needed when building against Marshmallow SDK.
-dontwarn android.app.Notification

# Protobuf has references not on the Android boot classpath
-dontwarn sun.misc.Unsafe
-dontwarn libcore.io.Memory

# Annotations used during internal SDK shrinking.
-dontwarn com.google.android.apps.common.proguard.UsedBy*
-dontwarn com.google.android.apps.common.proguard.SideEffectFree

# Annotations referenced by the SDK but whose definitions are contained in
# non-required dependencies.
-dontwarn javax.annotation.**
-dontwarn org.checkerframework.**
-dontwarn com.google.errorprone.annotations.**
-dontwarn org.jspecify.nullness.NullMarked

# Annotations no longer exist. Suppression prevents ProGuard failures in
# SDKs which depend on earlier versions of play-services-basement.
-dontwarn com.google.android.gms.common.util.VisibleForTesting

# Proguard flags for consumers of the Google Play services SDK
# https://developers.google.com/android/guides/setup#add_google_play_services_to_your_project

# Keep SafeParcelable NULL value, needed for reflection by DowngradeableSafeParcel
-keepclassmembers public class com.google.android.gms.common.internal.safeparcel.SafeParcelable {
    public static final *** NULL;
}

# Needed for Parcelable/SafeParcelable classes & their creators to not get renamed, as they are
# found via reflection.
-keep class com.google.android.gms.common.internal.ReflectedParcelable
-keepnames class * implements com.google.android.gms.common.internal.ReflectedParcelable
-keepclassmembers class * implements android.os.Parcelable {
  public static final *** CREATOR;
}

# Keep the classes/members we need for client functionality.
-keep @interface android.support.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep androidX equivalent of above android.support to allow Jetification.
-keep @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep the names of classes/members we need for client functionality.
-keep @interface com.google.android.gms.common.annotation.KeepName
-keepnames @com.google.android.gms.common.annotation.KeepName class *
-keepclassmembernames class * {
  @com.google.android.gms.common.annotation.KeepName *;
}

# Keep Dynamite API entry points
-keep @interface com.google.android.gms.common.util.DynamiteApi
-keep @com.google.android.gms.common.util.DynamiteApi public class * {
  public <fields>;
  public <methods>;
}



# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98a7034e786415bc9e009aca9ccbea7a\transformed\play-services-basement-18.4.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d472de1097adacf621614efcbac1e21\transformed\fragment-1.8.4\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The default FragmentFactory creates Fragment instances using reflection
-if public class ** extends androidx.fragment.app.Fragment
-keepclasseswithmembers,allowobfuscation public class <1> {
    public <init>();
}

# FragmentTransition will reflectively lookup:
# androidx.transition.FragmentTransitionSupport
# We should ensure that we keep the constructor if the code using this is alive
-if class androidx.fragment.app.FragmentTransition {
   private static androidx.fragment.app.FragmentTransitionImpl resolveSupportImpl();
}
-keep class androidx.transition.FragmentTransitionSupport {
    public <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d472de1097adacf621614efcbac1e21\transformed\fragment-1.8.4\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e74249777095f16bf90043ea48d86009\transformed\timber-5.0.1\proguard.txt
-dontwarn org.jetbrains.annotations.**

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e74249777095f16bf90043ea48d86009\transformed\timber-5.0.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0aa8749e7f225f5fe526166a8caa4eb9\transformed\rules\lib\META-INF\proguard\retrofit2.pro
# Retrofit does reflection on generic parameters. InnerClasses is required to use Signature and
# EnclosingMethod is required to use InnerClasses.
-keepattributes Signature, InnerClasses, EnclosingMethod

# Retrofit does reflection on method and parameter annotations.
-keepattributes RuntimeVisibleAnnotations, RuntimeVisibleParameterAnnotations

# Keep annotation default values (e.g., retrofit2.http.Field.encoded).
-keepattributes AnnotationDefault

# Retain service method parameters when optimizing.
-keepclassmembers,allowshrinking,allowobfuscation interface * {
    @retrofit2.http.* <methods>;
}

# Ignore annotation used for build tooling.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement

# Ignore JSR 305 annotations for embedding nullability information.
-dontwarn javax.annotation.**

# Guarded by a NoClassDefFoundError try/catch and only used when on the classpath.
-dontwarn kotlin.Unit

# Top-level functions that can only be used by Kotlin.
-dontwarn retrofit2.KotlinExtensions
-dontwarn retrofit2.KotlinExtensions$*

# With R8 full mode, it sees no subtypes of Retrofit interfaces since they are created with a Proxy
# and replaces all potential values with null. Explicitly keeping the interfaces prevents this.
-if interface * { @retrofit2.http.* <methods>; }
-keep,allowobfuscation interface <1>

# Keep inherited services.
-if interface * { @retrofit2.http.* <methods>; }
-keep,allowobfuscation interface * extends <1>

# With R8 full mode generic signatures are stripped for classes that are not
# kept. Suspend functions are wrapped in continuations where the type argument
# is used.
-keep,allowobfuscation,allowshrinking class kotlin.coroutines.Continuation

# R8 full mode strips generic signatures from return types if not kept.
-if interface * { @retrofit2.http.* public *** *(...); }
-keep,allowoptimization,allowshrinking,allowobfuscation class <3>

# With R8 full mode generic signatures are stripped for classes that are not kept.
-keep,allowobfuscation,allowshrinking class retrofit2.Response

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0aa8749e7f225f5fe526166a8caa4eb9\transformed\rules\lib\META-INF\proguard\retrofit2.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11a41d5f1c360319a3dcad235161600d\transformed\rules\lib\META-INF\proguard\okhttp3.pro
# JSR 305 annotations are for embedding nullability information.
-dontwarn javax.annotation.**

# A resource is loaded with a relative path so the package of this class must be preserved.
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*

# OkHttp platform used only on JVM and when Conscrypt and other security providers are available.
-dontwarn okhttp3.internal.platform.**
-dontwarn org.conscrypt.**
-dontwarn org.bouncycastle.**
-dontwarn org.openjsse.**

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11a41d5f1c360319a3dcad235161600d\transformed\rules\lib\META-INF\proguard\okhttp3.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\373d4135f1586ad81ac2cd37e6ac32ee\transformed\googleid-1.1.0\proguard.txt
# Proguard cannot process META-INF/versions/9.
# See https://discuss.gradle.org/t/meta-inf-version-duplicate-error-when-using-proguard/31380
-dontwarn module-info

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\373d4135f1586ad81ac2cd37e6ac32ee\transformed\googleid-1.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2971a01e425cf143536a38b3589caa3\transformed\rules\lib\META-INF\com.android.tools\r8\kotlinx-serialization-common.pro
# Keep `Companion` object fields of serializable classes.
# This avoids serializer lookup through `getDeclaredClasses` as done for named companion objects.
-if @kotlinx.serialization.Serializable class **
-keepclassmembers class <1> {
    static <1>$Companion Companion;
}

# Keep `serializer()` on companion objects (both default and named) of serializable classes.
-if @kotlinx.serialization.Serializable class ** {
    static **$* *;
}
-keepclassmembers class <2>$<3> {
    kotlinx.serialization.KSerializer serializer(...);
}

# Keep `INSTANCE.serializer()` of serializable objects.
-if @kotlinx.serialization.Serializable class ** {
    public static ** INSTANCE;
}
-keepclassmembers class <1> {
    public static <1> INSTANCE;
    kotlinx.serialization.KSerializer serializer(...);
}

# @Serializable and @Polymorphic are used at runtime for polymorphic serialization.
-keepattributes RuntimeVisibleAnnotations,AnnotationDefault

# Don't print notes about potential mistakes or omissions in the configuration for kotlinx-serialization classes
# See also https://github.com/Kotlin/kotlinx.serialization/issues/1900
-dontnote kotlinx.serialization.**

# Serialization core uses `java.lang.ClassValue` for caching inside these specified classes.
# If there is no `java.lang.ClassValue` (for example, in Android), then R8/ProGuard will print a warning.
# However, since in this case they will not be used, we can disable these warnings
-dontwarn kotlinx.serialization.internal.ClassValueReferences

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2971a01e425cf143536a38b3589caa3\transformed\rules\lib\META-INF\com.android.tools\r8\kotlinx-serialization-common.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2971a01e425cf143536a38b3589caa3\transformed\rules\lib\META-INF\com.android.tools\r8\kotlinx-serialization-r8.pro
# Rule to save runtime annotations on serializable class.
# If the R8 full mode is used, annotations are removed from classes-files.
#
# For the annotation serializer, it is necessary to read the `Serializable` annotation inside the serializer<T>() function - if it is present,
# then `SealedClassSerializer` is used, if absent, then `PolymorphicSerializer'.
#
# When using R8 full mode, all interfaces will be serialized using `PolymorphicSerializer`.
#
# see https://github.com/Kotlin/kotlinx.serialization/issues/2050

 -if @kotlinx.serialization.Serializable class **
 -keep, allowshrinking, allowoptimization, allowobfuscation, allowaccessmodification class <1>

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2971a01e425cf143536a38b3589caa3\transformed\rules\lib\META-INF\com.android.tools\r8\kotlinx-serialization-r8.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a23e484f9ab2c8f2fe1c821821b8dd08\transformed\exoplayer-datasource-2.19.1\proguard.txt
# Proguard rules specific to the DataSource module.

# Constant folding for resource integers may mean that a resource passed to this method appears to be unused. Keep the method to prevent this from happening.
-keepclassmembers class com.google.android.exoplayer2.upstream.RawResourceDataSource {
  public static android.net.Uri buildRawResourceUri(int);
}

# Constructors accessed via reflection in DefaultDataSource
-dontnote com.google.android.exoplayer2.ext.rtmp.RtmpDataSource
-keepclassmembers class com.google.android.exoplayer2.ext.rtmp.RtmpDataSource {
  <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a23e484f9ab2c8f2fe1c821821b8dd08\transformed\exoplayer-datasource-2.19.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952b95c172269eee0bea8252aa77f918\transformed\exoplayer-extractor-2.19.1\proguard.txt
# Proguard rules specific to the extractor module.

# Methods accessed via reflection in DefaultExtractorsFactory
-dontnote com.google.android.exoplayer2.ext.flac.FlacExtractor
-keepclassmembers class com.google.android.exoplayer2.ext.flac.FlacExtractor {
  <init>(int);
}
-dontnote com.google.android.exoplayer2.ext.flac.FlacLibrary
-keepclassmembers class com.google.android.exoplayer2.ext.flac.FlacLibrary {
  public static boolean isAvailable();
}

# Don't warn about checkerframework and Kotlin annotations
-dontwarn org.checkerframework.**
-dontwarn kotlin.annotations.jvm.**
-dontwarn javax.annotation.**

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952b95c172269eee0bea8252aa77f918\transformed\exoplayer-extractor-2.19.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c58cd8b4696f8fa5bb0d8d3c36af5768\transformed\exoplayer-common-2.19.1\proguard.txt
# Proguard rules specific to the common module.

# Don't warn about checkerframework and Kotlin annotations
-dontwarn org.checkerframework.**
-dontwarn kotlin.annotations.jvm.**
-dontwarn javax.annotation.**

# From https://github.com/google/guava/wiki/UsingProGuardWithGuava
-dontwarn java.lang.ClassValue
-dontwarn java.lang.SafeVarargs
-dontwarn javax.lang.model.element.Modifier
-dontwarn sun.misc.Unsafe

# Don't warn about Guava's compile-only dependencies.
# These lines are needed for ProGuard but not R8.
-dontwarn com.google.errorprone.annotations.**
-dontwarn com.google.j2objc.annotations.**
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement

# Workaround for https://issuetracker.google.com/issues/112297269
# This is needed for ProGuard but not R8.
-keepclassmembernames class com.google.common.base.Function { *; }

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c58cd8b4696f8fa5bb0d8d3c36af5768\transformed\exoplayer-common-2.19.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed63fd1d6f0c32c1e430798002671246\transformed\startup-runtime-1.1.1\proguard.txt
# It's important that we preserve initializer names, given they are used in the AndroidManifest.xml.
-keepnames class * extends androidx.startup.Initializer

# These Proguard rules ensures that ComponentInitializers are are neither shrunk nor obfuscated,
# and are a part of the primary dex file. This is because they are discovered and instantiated
# during application startup.
-keep class * extends androidx.startup.Initializer {
    # Keep the public no-argument constructor while allowing other methods to be optimized.
    <init>();
}

-assumenosideeffects class androidx.startup.StartupLogger { public static <methods>; }

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed63fd1d6f0c32c1e430798002671246\transformed\startup-runtime-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b54da9fb788aca02c18c87741f8a5cf\transformed\firebase-components-18.0.0\proguard.txt
-dontwarn com.google.firebase.components.Component$Instantiation
-dontwarn com.google.firebase.components.Component$ComponentType

-keep class * implements com.google.firebase.components.ComponentRegistrar
-keep,allowshrinking interface com.google.firebase.components.ComponentRegistrar

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b54da9fb788aca02c18c87741f8a5cf\transformed\firebase-components-18.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44ad0f2412c7c8a71f80cba0dcd24c9e\transformed\transport-api-3.2.0\proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44ad0f2412c7c8a71f80cba0dcd24c9e\transformed\transport-api-3.2.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9891cc273c69fca165fe759e0b5d848a\transformed\versionedparcelable-1.1.1\proguard.txt
-keep class * implements androidx.versionedparcelable.VersionedParcelable
-keep public class android.support.**Parcelizer { *; }
-keep public class androidx.**Parcelizer { *; }
-keep public class androidx.versionedparcelable.ParcelImpl

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9891cc273c69fca165fe759e0b5d848a\transformed\versionedparcelable-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aaff3ddf01defa86e86a7a861f3d7804\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
-keep,allowobfuscation @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <init>(...);
}

-keepclassmembers,allowobfuscation class * {
  @androidx.annotation.DoNotInline <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aaff3ddf01defa86e86a7a861f3d7804\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\711c15a9b4ce900db66f546d07cac37e\transformed\rules\lib\META-INF\proguard\protobuf.pro
# Recently Protobuf Javalite introduced a change that relies on reflection,
# which doesn't work with Proguard. This rule keeps the reflection usages in
# (shaded) Protobuf classes in Tink as-is.
# The location of this file is determined by
# - https://developer.android.com/studio/build/shrink-code#configuration-files
# - https://docs.bazel.build/versions/master/be/java.html#java_library.resources
# See also:
# - https://github.com/google/tink/issues/361
# - https://github.com/protocolbuffers/protobuf/issues/6463
# WARNING: the shaded package name com.google.crypto.tink.shaded.protobuf must
# be kept in sync with jar_jar_rules.txt.
-keepclassmembers class * extends com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\711c15a9b4ce900db66f546d07cac37e\transformed\rules\lib\META-INF\proguard\protobuf.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cb1633cc58bfea7552b59738c79880\transformed\rules\lib\META-INF\proguard\gson.pro
### Gson ProGuard and R8 rules which are relevant for all users
### This file is automatically recognized by ProGuard and R8, see https://developer.android.com/build/shrink-code#configuration-files
###
### IMPORTANT:
### - These rules are additive; don't include anything here which is not specific to Gson (such as completely
###   disabling obfuscation for all classes); the user would be unable to disable that then
### - These rules are not complete; users will most likely have to add additional rules for their specific
###   classes, for example to disable obfuscation for certain fields or to keep no-args constructors
###

# Keep generic signatures; needed for correct type resolution
-keepattributes Signature

# Keep Gson annotations
# Note: Cannot perform finer selection here to only cover Gson annotations, see also https://stackoverflow.com/q/47515093
-keepattributes RuntimeVisibleAnnotations,AnnotationDefault

### The following rules are needed for R8 in "full mode" which only adheres to `-keepattribtues` if
### the corresponding class or field is matches by a `-keep` rule as well, see
### https://r8.googlesource.com/r8/+/refs/heads/main/compatibility-faq.md#r8-full-mode

# Keep class TypeToken (respectively its generic signature) if present
-if class com.google.gson.reflect.TypeToken
-keep,allowobfuscation class com.google.gson.reflect.TypeToken

# Keep any (anonymous) classes extending TypeToken
-keep,allowobfuscation class * extends com.google.gson.reflect.TypeToken

# Keep classes with @JsonAdapter annotation
-keep,allowobfuscation,allowoptimization @com.google.gson.annotations.JsonAdapter class *

# Keep fields with any other Gson annotation
# Also allow obfuscation, assuming that users will additionally use @SerializedName or
# other means to preserve the field names
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.Expose <fields>;
  @com.google.gson.annotations.JsonAdapter <fields>;
  @com.google.gson.annotations.Since <fields>;
  @com.google.gson.annotations.Until <fields>;
}

# Keep no-args constructor of classes which can be used with @JsonAdapter
# By default their no-args constructor is invoked to create an adapter instance
-keepclassmembers class * extends com.google.gson.TypeAdapter {
  <init>();
}
-keepclassmembers class * implements com.google.gson.TypeAdapterFactory {
  <init>();
}
-keepclassmembers class * implements com.google.gson.JsonSerializer {
  <init>();
}
-keepclassmembers class * implements com.google.gson.JsonDeserializer {
  <init>();
}

# Keep fields annotated with @SerializedName for classes which are referenced.
# If classes with fields annotated with @SerializedName have a no-args
# constructor keep that as well. Based on
# https://issuetracker.google.com/issues/150189783#comment11.
# See also https://github.com/google/gson/pull/2420#discussion_r1241813541
# for a more detailed explanation.
-if class *
-keepclasseswithmembers,allowobfuscation class <1> {
  @com.google.gson.annotations.SerializedName <fields>;
}
-if class * {
  @com.google.gson.annotations.SerializedName <fields>;
}
-keepclassmembers,allowobfuscation,allowoptimization class <1> {
  <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cb1633cc58bfea7552b59738c79880\transformed\rules\lib\META-INF\proguard\gson.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c10136bfd6f8a668c34f448c44ecfdf\transformed\rules\lib\META-INF\com.android.tools\r8\r8.pro
-identifiernamestring @dagger.internal.IdentifierNameString class ** {
    static java.lang.String *;
}
-keepclassmembers,includedescriptorclasses,allowobfuscation,allowshrinking class * {
   @dagger.internal.KeepFieldType <fields>;
}
# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c10136bfd6f8a668c34f448c44ecfdf\transformed\rules\lib\META-INF\com.android.tools\r8\r8.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.10.2\transforms\673ec8ce3066443b41d99eb75dbc1193\transformed\protolite-well-known-types-18.0.0\proguard.txt
# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# protobuf-javalite has a bug that requires this workaround rule
# https://github.com/protocolbuffers/protobuf/issues/6463#issuecomment-553183215
-keepclassmembers class * extends com.google.protobuf.GeneratedMessageLite {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.10.2\transforms\673ec8ce3066443b41d99eb75dbc1193\transformed\protolite-well-known-types-18.0.0\proguard.txt
# The proguard configuration file for the following section is <unknown>

# End of content from <unknown>