// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogEditSeminarBinding implements ViewBinding {
  @NonNull
  private final NestedScrollView rootView;

  @NonNull
  public final TextInputEditText dateInput;

  @NonNull
  public final TextInputLayout dateLayout;

  @NonNull
  public final TextInputEditText descriptionInput;

  @NonNull
  public final TextInputLayout descriptionLayout;

  @NonNull
  public final TextInputEditText endTimeInput;

  @NonNull
  public final TextInputLayout endTimeLayout;

  @NonNull
  public final TextInputEditText nameInput;

  @NonNull
  public final TextInputLayout nameLayout;

  @NonNull
  public final TextInputEditText startTimeInput;

  @NonNull
  public final TextInputLayout startTimeLayout;

  private DialogEditSeminarBinding(@NonNull NestedScrollView rootView,
      @NonNull TextInputEditText dateInput, @NonNull TextInputLayout dateLayout,
      @NonNull TextInputEditText descriptionInput, @NonNull TextInputLayout descriptionLayout,
      @NonNull TextInputEditText endTimeInput, @NonNull TextInputLayout endTimeLayout,
      @NonNull TextInputEditText nameInput, @NonNull TextInputLayout nameLayout,
      @NonNull TextInputEditText startTimeInput, @NonNull TextInputLayout startTimeLayout) {
    this.rootView = rootView;
    this.dateInput = dateInput;
    this.dateLayout = dateLayout;
    this.descriptionInput = descriptionInput;
    this.descriptionLayout = descriptionLayout;
    this.endTimeInput = endTimeInput;
    this.endTimeLayout = endTimeLayout;
    this.nameInput = nameInput;
    this.nameLayout = nameLayout;
    this.startTimeInput = startTimeInput;
    this.startTimeLayout = startTimeLayout;
  }

  @Override
  @NonNull
  public NestedScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogEditSeminarBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogEditSeminarBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_edit_seminar, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogEditSeminarBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.dateInput;
      TextInputEditText dateInput = ViewBindings.findChildViewById(rootView, id);
      if (dateInput == null) {
        break missingId;
      }

      id = R.id.dateLayout;
      TextInputLayout dateLayout = ViewBindings.findChildViewById(rootView, id);
      if (dateLayout == null) {
        break missingId;
      }

      id = R.id.descriptionInput;
      TextInputEditText descriptionInput = ViewBindings.findChildViewById(rootView, id);
      if (descriptionInput == null) {
        break missingId;
      }

      id = R.id.descriptionLayout;
      TextInputLayout descriptionLayout = ViewBindings.findChildViewById(rootView, id);
      if (descriptionLayout == null) {
        break missingId;
      }

      id = R.id.endTimeInput;
      TextInputEditText endTimeInput = ViewBindings.findChildViewById(rootView, id);
      if (endTimeInput == null) {
        break missingId;
      }

      id = R.id.endTimeLayout;
      TextInputLayout endTimeLayout = ViewBindings.findChildViewById(rootView, id);
      if (endTimeLayout == null) {
        break missingId;
      }

      id = R.id.nameInput;
      TextInputEditText nameInput = ViewBindings.findChildViewById(rootView, id);
      if (nameInput == null) {
        break missingId;
      }

      id = R.id.nameLayout;
      TextInputLayout nameLayout = ViewBindings.findChildViewById(rootView, id);
      if (nameLayout == null) {
        break missingId;
      }

      id = R.id.startTimeInput;
      TextInputEditText startTimeInput = ViewBindings.findChildViewById(rootView, id);
      if (startTimeInput == null) {
        break missingId;
      }

      id = R.id.startTimeLayout;
      TextInputLayout startTimeLayout = ViewBindings.findChildViewById(rootView, id);
      if (startTimeLayout == null) {
        break missingId;
      }

      return new DialogEditSeminarBinding((NestedScrollView) rootView, dateInput, dateLayout,
          descriptionInput, descriptionLayout, endTimeInput, endTimeLayout, nameInput, nameLayout,
          startTimeInput, startTimeLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
