<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_log_entry" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_log_entry.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_log_entry_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="39" endOffset="51"/></Target><Target id="@+id/timestampText" view="TextView"><Expressions/><location startLine="15" startOffset="8" endLine="20" endOffset="37"/></Target><Target id="@+id/levelTagText" view="TextView"><Expressions/><location startLine="22" startOffset="8" endLine="28" endOffset="38"/></Target><Target id="@+id/messageText" view="TextView"><Expressions/><location startLine="30" startOffset="8" endLine="35" endOffset="37"/></Target></Targets></Layout>