// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTransactionBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView amountText;

  @NonNull
  public final TextView dateText;

  @NonNull
  public final TextView descriptionText;

  @NonNull
  public final TextView typeText;

  private ItemTransactionBinding(@NonNull MaterialCardView rootView, @NonNull TextView amountText,
      @NonNull TextView dateText, @NonNull TextView descriptionText, @NonNull TextView typeText) {
    this.rootView = rootView;
    this.amountText = amountText;
    this.dateText = dateText;
    this.descriptionText = descriptionText;
    this.typeText = typeText;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTransactionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTransactionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_transaction, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTransactionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.amountText;
      TextView amountText = ViewBindings.findChildViewById(rootView, id);
      if (amountText == null) {
        break missingId;
      }

      id = R.id.dateText;
      TextView dateText = ViewBindings.findChildViewById(rootView, id);
      if (dateText == null) {
        break missingId;
      }

      id = R.id.descriptionText;
      TextView descriptionText = ViewBindings.findChildViewById(rootView, id);
      if (descriptionText == null) {
        break missingId;
      }

      id = R.id.typeText;
      TextView typeText = ViewBindings.findChildViewById(rootView, id);
      if (typeText == null) {
        break missingId;
      }

      return new ItemTransactionBinding((MaterialCardView) rootView, amountText, dateText,
          descriptionText, typeText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
