#Sun Jul 13 09:31:12 TRT 2025
com.example.allinone.app-mergeDebugResources-80\:/layout/activity_main.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_add_event.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_event.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_event.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_event.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_seminar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_seminar.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/activity_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_backup.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_investment.xml.flat
com.example.allinone.app-main-112\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.allinone.app-main-116\:/drawable/ic_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_menu.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/activity_main.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.example.allinone.app-main-83\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.allinone.app-main-113\:/xml/file_paths.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_file_paths.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_instagram_posts.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_instagram_posts.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_backup.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_history.xml.flat
com.example.allinone.app-main-113\:/menu/search_students.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_search_students.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_reports.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_reports.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_transaction.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_transaction.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_category_dropdown.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_category_dropdown.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_chat_source.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_chat_source.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout-night-v8/item_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_item_history.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_students.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_students.xml.flat
com.example.allinone.app-main-83\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.allinone.app-main-113\:/drawable/bg_day_with_registration.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_day_with_registration.xml.flat
com.example.allinone.app-main-116\:/color/chip_background_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_chip_background_selector.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_pin_input.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_pin_input.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_category_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_investment.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_view_list.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_view_list.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_stats.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_stats.xml.flat
com.example.allinone.app-main-83\:/drawable/bg_day_with_events.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_day_with_events.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_add_photo.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add_photo.xml.flat
com.example.allinone.app-main-113\:/menu/menu_group_options.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_menu_group_options.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_log_entry.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_log_entry.xml.flat
com.example.allinone.app-main-83\:/drawable/bg_selected_day.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_selected_day.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_add_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_student.xml.flat
com.example.allinone.app-main-83\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.allinone.app-main-112\:/drawable/simple_text_splash.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_simple_text_splash.xml.flat
com.example.allinone.app-main-83\:/xml/data_extraction_rules.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.example.allinone.app-main-116\:/menu/wt_bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_wt_bottom_nav_menu.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_database.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_database.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_category_all.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_all.xml.flat
com.example.allinone.app-main-116\:/font/opensans_bold.ttf=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_opensans_bold.ttf.flat
com.example.allinone.app-mergeDebugResources-109\:/layout-night-v8/fragment_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_fragment_history.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_tasks.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_tasks.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_image.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_workout_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_workout_program.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_exercise.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_history.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_wt_students.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_wt_students.xml.flat
com.example.allinone.app-main-116\:/drawable-v24/ic_launcher_foreground.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-v24_ic_launcher_foreground.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_investment_selection.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_investment_selection.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/splash_text_layout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_splash_text_layout.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_attach_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_attach_image.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout-night-v8/fragment_wt_registry.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_fragment_wt_registry.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_call.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_call.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_futures.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_futures.xml.flat
com.example.allinone.app-main-116\:/drawable/bg_category_chip.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_category_chip.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_workout_dashboard.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_workout_dashboard.xml.flat
com.example.allinone.app-main-113\:/menu/search_notes.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_search_notes.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_income.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_income.xml.flat
com.example.allinone.app-main-113\:/drawable/error_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_error_image.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_category_spending.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_category_spending.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_edit_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_edit_note.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_empty_state.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_empty_state.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_expand_less.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_expand_less.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_investments.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_investments.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/activity_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_backup.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_menu.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout-night-v8/item_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_item_history.xml.flat
com.example.allinone.app-main-112\:/color-night/text_input_box_stroke.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color-night_text_input_box_stroke.xml.flat
com.example.allinone.app-main-116\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_task_group.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_task_group.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/nav_header.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_nav_header.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_no_registrations.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_no_registrations.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_note.xml.flat
com.example.allinone.app-main-112\:/drawable/rounded_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_rounded_background.xml.flat
com.example.allinone.app-main-83\:/color/dialog_action_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_dialog_action_button_color.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_wt_lessons.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_wt_lessons.xml.flat
com.example.allinone.app-main-112\:/menu/wt_student_context_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_wt_student_context_menu.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_futures_tab.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_futures_tab.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_database_management.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_database_management.xml.flat
com.example.allinone.app-main-116\:/menu/search_register.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_search_register.xml.flat
com.example.allinone.app-main-116\:/menu/search_notes.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_search_notes.xml.flat
com.example.allinone.app-main-112\:/drawable/bg_day_with_events.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_day_with_events.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_wt_registers.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_wt_registers.xml.flat
com.example.allinone.app-main-116\:/color/text_input_box_stroke.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_text_input_box_stroke.xml.flat
com.example.allinone.app-main-112\:/menu/search_students.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_search_students.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_image.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/activity_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_backup.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_category_game.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_game.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_chevron_right.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_chevron_right.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_futures_tab.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_futures_tab.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_student.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_home.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_home.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_wt_registry.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_wt_registry.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_edit_wt_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_wt_student.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_task.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_task.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_registration.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_registration.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_income_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_income_investment.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_task_group.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_task_group.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_instagram_posts.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_instagram_posts.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_save.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_save.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_investment.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_binance_position.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_binance_position.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_instagram_post.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_instagram_post.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_edit.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_edit.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_format_bold.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_bold.xml.flat
com.example.allinone.app-main-113\:/menu/wt_student_context_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_wt_student_context_menu.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/activity_drawing.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_drawing.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_launcher_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout-night-v8/activity_main.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_activity_main.xml.flat
com.example.allinone.app-main-113\:/color/bottom_nav_item_color_light.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_bottom_nav_item_color_light.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_program.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_wt.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_wt.xml.flat
com.example.allinone.app-main-113\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.allinone.app-main-113\:/color-night/dialog_delete_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color-night_dialog_delete_button_color.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_students.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_students.xml.flat
com.example.allinone.app-main-113\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.allinone.app-main-112\:/drawable/ic_clear_data.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_clear_data.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_edit_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_edit_note.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_edit.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_edit.xml.flat
com.example.allinone.app-main-116\:/drawable-night/bg_category_chip.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-night_bg_category_chip.xml.flat
com.example.allinone.app-main-116\:/menu/menu_edit_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_menu_edit_note.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_calendar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_calendar.xml.flat
com.example.allinone.app-main-83\:/drawable/bg_current_day.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_current_day.xml.flat
com.example.allinone.app-main-113\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.allinone.app-main-116\:/color/dialog_action_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_dialog_action_button_color.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_format_list_bulleted.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_list_bulleted.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_play_circle.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_play_circle.xml.flat
com.example.allinone.app-main-113\:/font/opensans.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_opensans.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/splash_text_layout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_splash_text_layout.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_calendar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_calendar.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_wt_registration.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_wt_registration.xml.flat
com.example.allinone.app-main-83\:/drawable/rounded_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_rounded_background.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/dialog_edit_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_investment.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_cleardata.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_cleardata.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_category_sports.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_sports.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_close.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_close.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_instagram_ask_ai.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_instagram_ask_ai.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_folder.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_folder.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_add_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_student.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_futures.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_futures.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_chevron_left.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_chevron_left.xml.flat
com.example.allinone.app-main-116\:/menu/instagram_bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_instagram_bottom_nav_menu.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_investments.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_investments.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_home.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_home.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_stop.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_stop.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_instagram.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_instagram.xml.flat
com.example.allinone.app-main-113\:/menu/search_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_search_history.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_chat_user.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_chat_user.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_instagram_ask_ai.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_instagram_ask_ai.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_share.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_share.xml.flat
com.example.allinone.app-main-83\:/drawable-v24/ic_launcher_foreground.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-v24_ic_launcher_foreground.xml.flat
com.example.allinone.app-main-116\:/drawable/uncompleted_exercise_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_uncompleted_exercise_background.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_format_list_bulleted.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_list_bulleted.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_play_circle.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_play_circle.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_income.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_income.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_edit_seminar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_seminar.xml.flat
com.example.allinone.app-main-116\:/menu/menu_tasks.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_menu_tasks.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_stats.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_stats.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_program.xml.flat
com.example.allinone.app-main-112\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/activity_edit_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_edit_note.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_format_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_text.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_stop.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_stop.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_calendar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_calendar.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_category_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_investment.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_category_all.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_all.xml.flat
com.example.allinone.app-main-116\:/color/wt_bottom_nav_item_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_wt_bottom_nav_item_color.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_category_food.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_food.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_category_dropdown.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_category_dropdown.xml.flat
com.example.allinone.app-main-116\:/navigation/mobile_navigation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\navigation_mobile_navigation.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_notification.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_notification.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_chat_user.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_chat_user.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_database.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_database.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout-night-v8/fragment_wt_registry.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_fragment_wt_registry.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_voice_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_voice_note.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_home.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_home.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_chat_source.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_chat_source.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_graduation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_graduation.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_workout_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_workout_exercise.xml.flat
com.example.allinone.app-main-113\:/drawable/rounded_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_rounded_background.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_whatsapp.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_whatsapp.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_instagram.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_instagram.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_instagram_posts.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_instagram_posts.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_futures.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_futures.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/layout_page_header.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_layout_page_header.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_log_errors.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_log_errors.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_program_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_program_exercise.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/dialog_add_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_student.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_note_video.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_note_video.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_edit_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_program.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_workout_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_workout_program.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_database_management.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_database_management.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_program_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_program_exercise.xml.flat
com.example.allinone.app-main-113\:/color/wt_bottom_nav_item_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_wt_bottom_nav_item_color.xml.flat
com.example.allinone.app-main-112\:/color/chip_background_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_chip_background_selector.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_category_general.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_general.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_post_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_post_details.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_reports.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_reports.xml.flat
com.example.allinone.app-main-113\:/drawable/completed_exercise_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_completed_exercise_background.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_tasks.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_tasks.xml.flat
com.example.allinone.app-main-116\:/menu/drawer_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_drawer_menu.xml.flat
com.example.allinone.app-main-113\:/drawable/bg_day_with_lesson.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_day_with_lesson.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_database_record.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_database_record.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout-night-v8/item_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_item_backup.xml.flat
com.example.allinone.app-main-83\:/menu/menu_task_options.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_menu_task_options.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/nav_header.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_nav_header.xml.flat
com.example.allinone.app-main-113\:/menu/instagram_bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_instagram_bottom_nav_menu.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_note.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_investment_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_investment_image.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_pause.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_pause.xml.flat
com.example.allinone.app-main-112\:/menu/workout_bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_workout_bottom_nav_menu.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_play.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_play.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_investments.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_investments.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_expand_less.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_expand_less.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout-night-v8/dialog_edit_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_dialog_edit_note.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_wt_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_wt_student.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_futures_position.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_futures_position.xml.flat
com.example.allinone.app-main-83\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/pie_chart_tooltip.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_pie_chart_tooltip.xml.flat
com.example.allinone.app-main-112\:/drawable/error_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_error_image.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_tasks.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_tasks.xml.flat
com.example.allinone.app-main-112\:/xml/backup_rules.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_income_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_income_investment.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_save.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_save.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_loading.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_loading.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_format_italic.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_italic.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_category_general.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_general.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_fullscreen_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_fullscreen_image.xml.flat
com.example.allinone.app-main-112\:/color/wt_bottom_nav_item_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_wt_bottom_nav_item_color.xml.flat
com.example.allinone.app-main-113\:/color/chip_background_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_chip_background_selector.xml.flat
com.example.allinone.app-main-113\:/drawable/bg_day_with_events.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_day_with_events.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_add_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_student.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/dialog_edit_wt_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_wt_student.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_lessons.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_lessons.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_program_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_program_exercise.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout-night-v8/activity_edit_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_activity_edit_note.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_fitness.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_fitness.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_chevron_left.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_chevron_left.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_add_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_program.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_edit_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_edit_note.xml.flat
com.example.allinone.app-main-112\:/menu/instagram_bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_instagram_bottom_nav_menu.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_category_summary.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_category_summary.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/nav_header.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_nav_header.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_expand_more.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_expand_more.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_active_workout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_active_workout.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_note.xml.flat
com.example.allinone.app-main-116\:/drawable/circle_shape.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_shape.xml.flat
com.example.allinone.app-main-83\:/drawable/simple_text_splash.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_simple_text_splash.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_futures_tab.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_futures_tab.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/dialog_program_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_program_details.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_transaction.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_transaction.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_transaction_report.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_transaction_report.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_category_shopping.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_shopping.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_log_errors.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_log_errors.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_format_list_numbered.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_list_numbered.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_program.xml.flat
com.example.allinone.app-main-83\:/drawable/border_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_border_background.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_workout_log.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_workout_log.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_transaction_report.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_transaction_report.xml.flat
com.example.allinone.app-main-113\:/drawable/border_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_border_background.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_note.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_wt_students.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_wt_students.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout-night-v8/dialog_student_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_dialog_student_details.xml.flat
com.example.allinone.app-main-116\:/color-night/text_input_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color-night_text_input_text_color.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_play.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_play.xml.flat
com.example.allinone.app-main-83\:/menu/search_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_search_history.xml.flat
com.example.allinone.app-main-112\:/color/dialog_action_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_dialog_action_button_color.xml.flat
com.example.allinone.app-main-83\:/color/wt_bottom_nav_item_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_wt_bottom_nav_item_color.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_format_list_bulleted.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_list_bulleted.xml.flat
com.example.allinone.app-main-112\:/color-night/text_input_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color-night_text_input_text_color.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/dialog_add_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_program.xml.flat
com.example.allinone.app-main-83\:/menu/menu_edit_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_menu_edit_note.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/activity_edit_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_edit_note.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout-night-v8/activity_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_activity_backup.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_add_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_program.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_student.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_edit_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_program.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_investment.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_category_bills.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_bills.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_investment_dropdown.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_investment_dropdown.xml.flat
com.example.allinone.app-main-113\:/mipmap-anydpi-v33/ic_launcher.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v33_ic_launcher.xml.flat
com.example.allinone.app-main-83\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.allinone.app-main-83\:/menu/drawer_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_drawer_menu.xml.flat
com.example.allinone.app-main-113\:/xml/backup_rules.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_notification.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_notification.xml.flat
com.example.allinone.app-main-83\:/menu/wt_bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_wt_bottom_nav_menu.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_instagram_insights.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_instagram_insights.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_format_list_numbered.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_list_numbered.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_category_all.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_all.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_loading.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_loading.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_home.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_home.xml.flat
com.example.allinone.app-main-83\:/drawable/circle_shape.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_shape.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/dialog_expense_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_expense_investment.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_transaction_report.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_transaction_report.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_log.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_log.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_workout_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_workout_exercise.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_wt_lessons.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_wt_lessons.xml.flat
com.example.allinone.app-main-113\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_expense_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_expense_investment.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_lesson.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_lesson.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_investments.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_investments.xml.flat
com.example.allinone.app-main-113\:/color/drawer_item_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_drawer_item_color.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_student_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_student_details.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_no_registrations.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_no_registrations.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_notification.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_notification.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_backup.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_exercise.xml.flat
com.example.allinone.app-main-113\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_seminar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_seminar.xml.flat
com.example.allinone.app-main-112\:/mipmap-anydpi-v33/ic_launcher.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v33_ic_launcher.xml.flat
com.example.allinone.app-main-112\:/drawable/bg_day_with_registration.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_day_with_registration.xml.flat
com.example.allinone.app-main-113\:/color/dialog_action_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_dialog_action_button_color.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_transactions_overview.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_transactions_overview.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_seminar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_seminar.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_fullscreen_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_fullscreen_image.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_add_event.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_event.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_category_transport.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_transport.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout-night-v8/activity_edit_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_activity_edit_note.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_backup.xml.flat
com.example.allinone.app-main-83\:/drawable-night/dialog_rounded_bg.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-night_dialog_rounded_bg.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_pause.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_pause.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_log.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_log.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_share.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_share.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_wt_students.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_wt_students.xml.flat
com.example.allinone.app-main-83\:/xml/network_security_config.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_network_security_config.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_workout_log.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_workout_log.xml.flat
com.example.allinone.app-main-83\:/color/dialog_delete_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_dialog_delete_button_color.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_category_dropdown.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_category_dropdown.xml.flat
com.example.allinone.app-main-116\:/drawable/bg_current_day.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_current_day.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout-night-v8/item_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_item_backup.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_video_error.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_video_error.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_program_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_program_details.xml.flat
com.example.allinone.app-main-112\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_history.xml.flat
com.example.allinone.app-main-112\:/drawable/transparent.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_transparent.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_log_errors.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_log_errors.xml.flat
com.example.allinone.app-main-113\:/drawable/splash_layout_drawable.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_splash_layout_drawable.xml.flat
com.example.allinone.app-main-83\:/menu/search_students.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_search_students.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout-night-v8/item_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_item_backup.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_active_workout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_active_workout.xml.flat
com.example.allinone.app-main-112\:/drawable/bg_tag_blue.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_tag_blue.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/layout_page_header.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_layout_page_header.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_edit_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_investment.xml.flat
com.example.allinone.app-main-83\:/navigation/nav_graph.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\navigation_nav_graph.xml.flat
com.example.allinone.app-main-116\:/menu/search_students.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_search_students.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_chevron_right.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_chevron_right.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_edit_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_note.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/pie_chart_tooltip.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_pie_chart_tooltip.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_pin_input.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_pin_input.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_add_task.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_task.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/dialog_fullscreen_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_fullscreen_image.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_delete.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_delete.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_format_italic.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_italic.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_investments.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_investments.xml.flat
com.example.allinone.app-main-116\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_calendar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_calendar.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_close.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_close.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_error.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_error.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_lesson.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_lesson.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/theme_switch_layout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_theme_switch_layout.xml.flat
com.example.allinone.app-main-83\:/color-night/dialog_action_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color-night_dialog_action_button_color.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_active_workout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_active_workout.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_category_salary.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_salary.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_category_shopping.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_shopping.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_instagram_business.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_instagram_business.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_investment_dropdown.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_investment_dropdown.xml.flat
com.example.allinone.app-main-113\:/color-night/dialog_action_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color-night_dialog_action_button_color.xml.flat
com.example.allinone.app-main-116\:/drawable/bg_selected_day.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_selected_day.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_format_bold.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_bold.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_fitness.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_fitness.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_futures_position.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_futures_position.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_database_management.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_database_management.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_investments_tab.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_investments_tab.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_note_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_note_image.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_fitness.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_fitness.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_attach_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_attach_image.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_share.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_share.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_launcher_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.example.allinone.app-main-116\:/xml/network_security_config.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_network_security_config.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_wt_registry.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_wt_registry.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_format_italic.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_italic.xml.flat
com.example.allinone.app-main-83\:/drawable/dialog_rounded_bg.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dialog_rounded_bg.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout-night-v8/activity_edit_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_activity_edit_note.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_no_students.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_no_students.xml.flat
com.example.allinone.app-main-112\:/navigation/mobile_navigation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\navigation_mobile_navigation.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_program.xml.flat
com.example.allinone.app-main-112\:/font/opensans.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_opensans.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_wt_lessons.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_wt_lessons.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout-night-v8/activity_main.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_activity_main.xml.flat
com.example.allinone.app-main-83\:/drawable/bg_category_chip.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_category_chip.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout-night-v8/item_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_item_history.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dropdown_item.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dropdown_item.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_workout_stats.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_workout_stats.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_add.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add.xml.flat
com.example.allinone.app-main-83\:/menu/menu_group_options.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_menu_group_options.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_backup.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_tasks.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_tasks.xml.flat
com.example.allinone.app-main-112\:/drawable/bg_day_with_lesson.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_day_with_lesson.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_investment_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_investment_image.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_investment.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_transactions.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_transactions.xml.flat
com.example.allinone.app-main-113\:/drawable/circle_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_background.xml.flat
com.example.allinone.app-main-113\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.allinone.app-main-116\:/menu/bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_bottom_nav_menu.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/activity_edit_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_edit_note.xml.flat
com.example.allinone.app-main-113\:/font/opensans_regular.ttf=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_opensans_regular.ttf.flat
com.example.allinone.app-main-112\:/color/bottom_nav_item_color_light.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_bottom_nav_item_color_light.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_futures_tp_sl.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_futures_tp_sl.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_chevron_right.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_chevron_right.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_note_video.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_note_video.xml.flat
com.example.allinone.app-main-116\:/color/bottom_nav_item_color_light.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_bottom_nav_item_color_light.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_chevron_right.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_chevron_right.xml.flat
com.example.allinone.app-main-113\:/xml/data_extraction_rules.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.example.allinone.app-main-83\:/menu/workout_bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_workout_bottom_nav_menu.xml.flat
com.example.allinone.app-main-83\:/drawable/bg_tag_red.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_tag_red.xml.flat
com.example.allinone.app-main-83\:/drawable/bg_day_with_registration.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_day_with_registration.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_video_placeholder.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_video_placeholder.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_play.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_play.xml.flat
com.example.allinone.app-main-113\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.allinone.app-main-113\:/drawable/ic_whatsapp.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_whatsapp.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_workout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_workout.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_fullscreen_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_fullscreen_image.xml.flat
com.example.allinone.app-main-113\:/drawable/bg_day_with_event.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_day_with_event.xml.flat
com.example.allinone.app-main-112\:/menu/search_register.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_search_register.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_note_video.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_note_video.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_color_picker.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_color_picker.xml.flat
com.example.allinone.app-main-112\:/font/opensans_regular.ttf=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_opensans_regular.ttf.flat
com.example.allinone.app-main-116\:/menu/search_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_search_history.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_instagram_post.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_instagram_post.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/pie_chart_tooltip.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_pie_chart_tooltip.xml.flat
com.example.allinone.app-main-116\:/xml/data_extraction_rules.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.example.allinone.app-main-83\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.allinone.app-main-116\:/drawable/ic_view_list.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_view_list.xml.flat
com.example.allinone.app-main-112\:/menu/menu_edit_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_menu_edit_note.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_history.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_add_photo.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add_photo.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_task_group.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_task_group.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_instagram_business.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_instagram_business.xml.flat
com.example.allinone.app-main-113\:/menu/search_register.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_search_register.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_format_list_numbered.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_list_numbered.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_student.xml.flat
com.example.allinone.app-main-112\:/drawable/fully_uncompleted_exercise_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_fully_uncompleted_exercise_background.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout-night-v8/activity_main.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_activity_main.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_error.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_error.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_transaction_report.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_transaction_report.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_call.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_call.xml.flat
com.example.allinone.app-main-112\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_workout_log.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_workout_log.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_instagram_business.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_instagram_business.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_program.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_search_white.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_search_white.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_history.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_transactions_overview.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_transactions_overview.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_back.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_back.xml.flat
com.example.allinone.app-main-112\:/menu/drawer_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_drawer_menu.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_video_error.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_video_error.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_notes.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_notes.xml.flat
com.example.allinone.app-main-112\:/drawable/border_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_border_background.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_instagram_insights.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_instagram_insights.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_voice_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_voice_note.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_category_wing_tzun.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_wing_tzun.xml.flat
com.example.allinone.app-main-113\:/menu/wt_bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_wt_bottom_nav_menu.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_task.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_task.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_add_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_program.xml.flat
com.example.allinone.app-main-116\:/font/opensans_regular.ttf=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_opensans_regular.ttf.flat
com.example.allinone.app-main-113\:/drawable/ic_category_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_investment.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout-night-v8/activity_edit_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_activity_edit_note.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_notes.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_notes.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_instagram_ask_ai.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_instagram_ask_ai.xml.flat
com.example.allinone.app-main-113\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.allinone.app-main-116\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.allinone.app-main-112\:/xml/data_extraction_rules.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_menu.xml.flat
com.example.allinone.app-main-83\:/drawable/placeholder_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_placeholder_image.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_registration.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_registration.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_lesson.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_lesson.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_futures_tp_sl.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_futures_tp_sl.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_workout_stats.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_workout_stats.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_transaction_report.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_transaction_report.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_workout_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_workout_exercise.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_expense.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_expense.xml.flat
com.example.allinone.app-main-83\:/font/opensans_regular.ttf=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_opensans_regular.ttf.flat
com.example.allinone.app-main-116\:/drawable/ic_save.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_save.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_category_wing_tzun.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_wing_tzun.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_home.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_home.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_category_shopping.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_shopping.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_category_sports.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_sports.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_voice_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_voice_note.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_stop.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_stop.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_expense.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_expense.xml.flat
com.example.allinone.app-main-116\:/xml/file_paths.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_file_paths.xml.flat
com.example.allinone.app-main-112\:/drawable-v24/ic_launcher_foreground.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-v24_ic_launcher_foreground.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_fullscreen_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_fullscreen_image.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_wt_event.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_wt_event.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_edit_lesson.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_lesson.xml.flat
com.example.allinone.app-main-113\:/color/bottom_nav_item_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_bottom_nav_item_color.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_note.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_income.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_income.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_format_underline.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_underline.xml.flat
com.example.allinone.app-main-116\:/drawable/bg_tag_green.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_tag_green.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_category_food.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_food.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_category_summary.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_category_summary.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout-night-v8/activity_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_activity_backup.xml.flat
com.example.allinone.app-main-83\:/drawable/circle_background_red.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_background_red.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_workout_dashboard.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_workout_dashboard.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_edit_wt_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_wt_student.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_history.xml.flat
com.example.allinone.app-main-113\:/menu/bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_bottom_nav_menu.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_wt_register.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_wt_register.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_program_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_program_exercise.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_expense_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_expense_investment.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_delete.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_delete.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_workout_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_workout_exercise.xml.flat
com.example.allinone.app-main-116\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.allinone.app-main-113\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_transaction_report.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_transaction_report.xml.flat
com.example.allinone.app-main-83\:/menu/wt_registration_context_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_wt_registration_context_menu.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_color_picker.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_color_picker.xml.flat
com.example.allinone.app-main-112\:/drawable/completed_exercise_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_completed_exercise_background.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_exercise.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_wt_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_wt_student.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_note_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_note_image.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_view_list.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_view_list.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/nav_header.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_nav_header.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_draw.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_draw.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_transaction_report.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_transaction_report.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_students.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_students.xml.flat
com.example.allinone.app-main-112\:/color-night/dialog_delete_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color-night_dialog_delete_button_color.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/splash_text_layout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_splash_text_layout.xml.flat
com.example.allinone.app-main-116\:/drawable/circle_background_red.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_background_red.xml.flat
com.example.allinone.app-main-116\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/dialog_workout_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_workout_details.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_wt_registry.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_wt_registry.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_category_transport.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_transport.xml.flat
com.example.allinone.app-main-112\:/drawable/bg_tag_green.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_tag_green.xml.flat
com.example.allinone.app-main-113\:/drawable/bg_category_chip.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_category_chip.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_investment.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_no_students.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_no_students.xml.flat
com.example.allinone.app-main-83\:/color-night/text_input_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color-night_text_input_text_color.xml.flat
com.example.allinone.app-main-113\:/drawable/fully_uncompleted_exercise_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_fully_uncompleted_exercise_background.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_search_white.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_search_white.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_wt_registration.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_wt_registration.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_expand_more.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_expand_more.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_chat_ai.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_chat_ai.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_tasks.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_tasks.xml.flat
com.example.allinone.app-main-112\:/drawable/bg_category_chip.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_category_chip.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_wt_registration.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_wt_registration.xml.flat
com.example.allinone.app-main-83\:/drawable/bg_tag_green.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_tag_green.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_format_bold.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_bold.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_instagram_insights.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_instagram_insights.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_task_group_header.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_task_group_header.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_wt_seminars.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_wt_seminars.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_workout_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_workout_exercise.xml.flat
com.example.allinone.app-main-83\:/drawable/error_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_error_image.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_graduation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_graduation.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_wt_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_wt_student.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/pie_chart_tooltip.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_pie_chart_tooltip.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_category_bills.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_bills.xml.flat
com.example.allinone.app-main-113\:/menu/workout_bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_workout_bottom_nav_menu.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_notes.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_notes.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_expand_less.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_expand_less.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/layout_page_header.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_layout_page_header.xml.flat
com.example.allinone.app-main-112\:/drawable/bg_tag_red.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_tag_red.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_instagram_business.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_instagram_business.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_back.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_back.xml.flat
com.example.allinone.app-main-113\:/drawable/bg_tag_green.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_tag_green.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_empty_state.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_empty_state.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_category_salary.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_salary.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_instagram_post_new.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_instagram_post_new.xml.flat
com.example.allinone.app-main-113\:/font/opensans_bold.ttf=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_opensans_bold.ttf.flat
com.example.allinone.app-main-112\:/drawable/ic_wt_registers.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_wt_registers.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/dialog_futures_tp_sl.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_futures_tp_sl.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_progress.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_progress.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_log_errors.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_log_errors.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_format_italic.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_italic.xml.flat
com.example.allinone.app-main-113\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.allinone.app-main-112\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_investments.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_investments.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_save.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_save.xml.flat
com.example.allinone.app-main-112\:/xml/file_paths.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_file_paths.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/offline_status_view.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_offline_status_view.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_add.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add.xml.flat
com.example.allinone.app-main-83\:/drawable-night/bg_category_chip.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-night_bg_category_chip.xml.flat
com.example.allinone.app-main-116\:/color-night/dialog_action_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color-night_dialog_action_button_color.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_workout_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_workout_program.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_format_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_text.xml.flat
com.example.allinone.app-main-112\:/color/text_input_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_text_input_text_color.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_event.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_event.xml.flat
com.example.allinone.app-main-116\:/drawable/bg_tag_blue.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_tag_blue.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_edit_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_program.xml.flat
com.example.allinone.app-main-112\:/menu/wt_bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_wt_bottom_nav_menu.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_play_circle.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_play_circle.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_task_group_header.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_task_group_header.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_log_entry.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_log_entry.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_category_spending.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_category_spending.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout-night-v8/dialog_student_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_dialog_student_details.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/offline_status_view.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_offline_status_view.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_instagram_post_new.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_instagram_post_new.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_transactions_overview.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_transactions_overview.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_tasks.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_tasks.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_category_food.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_food.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_delete.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_delete.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_registration.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_registration.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_program_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_program_details.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_database_management.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_database_management.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_image.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_post_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_post_details.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_fullscreen_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_fullscreen_image.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_calendar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_calendar.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/dialog_pin_input.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_pin_input.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_view_list.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_view_list.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_investment_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_investment_image.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_investment_dropdown.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_investment_dropdown.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_instagram_post_new.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_instagram_post_new.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_call.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_call.xml.flat
com.example.allinone.app-main-116\:/drawable/circle_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_background.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_add_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_add_exercise.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_add_photo.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add_photo.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout-night-v8/item_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_item_backup.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_log.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_log.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_remove.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_remove.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_stats.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_stats.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_investment_selection.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_investment_selection.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_edit_seminar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_seminar.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_back.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_back.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_wt_registers.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_wt_registers.xml.flat
com.example.allinone.app-main-83\:/drawable/uncompleted_exercise_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_uncompleted_exercise_background.xml.flat
com.example.allinone.app-main-112\:/menu/wt_registration_context_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_wt_registration_context_menu.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout-night-v8/item_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_item_history.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_history.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_folder.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_folder.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_call.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_call.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_note_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_note_image.xml.flat
com.example.allinone.app-main-116\:/drawable/simple_text_splash.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_simple_text_splash.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_instagram_post_new.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_instagram_post_new.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_code.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_code.xml.flat
com.example.allinone.app-main-83\:/color/drawer_item_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_drawer_item_color.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout-night-v8/dialog_student_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_dialog_student_details.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_menu.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_edit_seminar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_seminar.xml.flat
com.example.allinone.app-main-112\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.allinone.app-main-112\:/menu/bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_bottom_nav_menu.xml.flat
com.example.allinone.app-main-112\:/drawable/uncompleted_exercise_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_uncompleted_exercise_background.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_event.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_event.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout-night-v8/fragment_wt_registry.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_fragment_wt_registry.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_reports.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_reports.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_whatsapp.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_whatsapp.xml.flat
com.example.allinone.app-main-116\:/drawable/dialog_rounded_bg.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dialog_rounded_bg.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_instagram_post.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_instagram_post.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_task.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_task.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/theme_switch_layout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_theme_switch_layout.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_file.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_file.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/dialog_task_group.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_task_group.xml.flat
com.example.allinone.app-main-112\:/drawable/circle_shape.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_shape.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_format_underline.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_underline.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_cleardata.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_cleardata.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_graduation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_graduation.xml.flat
com.example.allinone.app-main-116\:/drawable/rounded_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_rounded_background.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_category_all.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_all.xml.flat
com.example.allinone.app-main-116\:/drawable/border_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_border_background.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_instagram_ask_ai.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_instagram_ask_ai.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_workout_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_workout_details.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_instagram_insights.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_instagram_insights.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_post_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_post_details.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_stats.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_stats.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_edit_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_investment.xml.flat
com.example.allinone.app-main-112\:/drawable-night/bg_category_chip.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-night_bg_category_chip.xml.flat
com.example.allinone.app-main-83\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.allinone.app-main-112\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_workout_dashboard.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_workout_dashboard.xml.flat
com.example.allinone.app-main-112\:/drawable/circle_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_background.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_history.xml.flat
com.example.allinone.app-main-113\:/drawable/bg_tag_red.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_tag_red.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_edit_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_investment.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_lessons.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_lessons.xml.flat
com.example.allinone.app-main-112\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/dropdown_item.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dropdown_item.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_wt_registration.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_wt_registration.xml.flat
com.example.allinone.app-main-113\:/drawable/uncompleted_exercise_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_uncompleted_exercise_background.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_income.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_income.xml.flat
com.example.allinone.app-main-116\:/font/opensans.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_opensans.xml.flat
com.example.allinone.app-main-113\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.allinone.app-main-83\:/font/opensans_bold.ttf=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_opensans_bold.ttf.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/activity_main.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.example.allinone.app-main-112\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.allinone.app-main-112\:/drawable/ic_category_general.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_general.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_notes.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_notes.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_add.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_clear_data.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_clear_data.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_category_wing_tzun.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_wing_tzun.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_empty_state.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_empty_state.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_notes.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_notes.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_transactions.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_transactions.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_launcher_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_instagram.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_instagram.xml.flat
com.example.allinone.app-main-113\:/color/text_input_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_text_input_text_color.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_pause.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_pause.xml.flat
com.example.allinone.app-main-112\:/drawable/rounded_corner_bg.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_rounded_corner_bg.xml.flat
com.example.allinone.app-main-83\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.allinone.app-main-112\:/drawable/dialog_rounded_bg.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dialog_rounded_bg.xml.flat
com.example.allinone.app-main-113\:/drawable/bg_selected_day.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_selected_day.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/activity_drawing.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_drawing.xml.flat
com.example.allinone.app-main-113\:/color/dialog_delete_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_dialog_delete_button_color.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_edit_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_note.xml.flat
com.example.allinone.app-main-112\:/drawable/default_profile.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_default_profile.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_category_food.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_food.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_student_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_student_details.xml.flat
com.example.allinone.app-main-112\:/color/bottom_nav_item_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_bottom_nav_item_color.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_home.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_home.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_workout_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_workout_exercise.xml.flat
com.example.allinone.app-main-112\:/drawable/bg_day_with_event.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_day_with_event.xml.flat
com.example.allinone.app-main-116\:/menu/wt_student_context_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_wt_student_context_menu.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_investments.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_investments.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_program.xml.flat
com.example.allinone.app-main-116\:/drawable/splash_layout_drawable.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_splash_layout_drawable.xml.flat
com.example.allinone.app-main-83\:/drawable/transparent.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_transparent.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_video_error.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_video_error.xml.flat
com.example.allinone.app-main-83\:/drawable/rounded_corner_bg.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_rounded_corner_bg.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_investment.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_instagram_post.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_instagram_post.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_search_white.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_search_white.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_whatsapp.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_whatsapp.xml.flat
com.example.allinone.app-main-112\:/drawable/selected_circle_shape.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_selected_circle_shape.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_edit.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_edit.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_category_spending.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_category_spending.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_attach_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_attach_image.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_category_summary.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_category_summary.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_chat_source.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_chat_source.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_workout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_workout.xml.flat
com.example.allinone.app-main-116\:/drawable/placeholder_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_placeholder_image.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_back.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_back.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_pin_input.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_pin_input.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_tasks.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_tasks.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_empty_state.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_empty_state.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_category_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_investment.xml.flat
com.example.allinone.app-main-112\:/font/opensans_bold.ttf=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_opensans_bold.ttf.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_task_group_header.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_task_group_header.xml.flat
com.example.allinone.app-main-83\:/drawable/selected_circle_shape.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_selected_circle_shape.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_chevron_left.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_chevron_left.xml.flat
com.example.allinone.app-main-113\:/drawable/selected_circle_shape.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_selected_circle_shape.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_wt_event.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_wt_event.xml.flat
com.example.allinone.app-main-116\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.allinone.app-main-113\:/drawable/bg_current_day.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_current_day.xml.flat
com.example.allinone.app-main-113\:/drawable/circle_shape.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_shape.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dropdown_item.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dropdown_item.xml.flat
com.example.allinone.app-main-83\:/color/bottom_nav_item_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_bottom_nav_item_color.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_no_registrations.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_no_registrations.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout-night-v8/activity_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_activity_backup.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_folder.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_folder.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_code.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_code.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_category_sports.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_sports.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_chevron_left.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_chevron_left.xml.flat
com.example.allinone.app-main-113\:/drawable/circle_background_red.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_background_red.xml.flat
com.example.allinone.app-main-83\:/color/text_input_box_stroke.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_text_input_box_stroke.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_chat_user.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_chat_user.xml.flat
com.example.allinone.app-main-116\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_wt_students.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_wt_students.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_workout_log.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_workout_log.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_note_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_note_image.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_profit_loss.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_profit_loss.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_attach_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_attach_image.xml.flat
com.example.allinone.app-main-116\:/drawable/bg_day_with_registration.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_day_with_registration.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_expand_less.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_expand_less.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/dialog_profit_loss.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_profit_loss.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_draw.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_draw.xml.flat
com.example.allinone.app-main-112\:/menu/menu_group_options.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_menu_group_options.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_error.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_error.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_checkbox.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_checkbox.xml.flat
com.example.allinone.app-main-116\:/menu/wt_registration_context_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_wt_registration_context_menu.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_no_students.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_no_students.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_instagram_posts.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_instagram_posts.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_format_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_text.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_database.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_database.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_investment_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_investment_image.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_add_photo.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add_photo.xml.flat
com.example.allinone.app-main-116\:/drawable/bg_day_with_event.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_day_with_event.xml.flat
com.example.allinone.app-main-83\:/color-night/text_input_box_stroke.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color-night_text_input_box_stroke.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_wt_event.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_wt_event.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_draw.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_draw.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_edit.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_edit.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_workout_stats.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_workout_stats.xml.flat
com.example.allinone.app-main-83\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/dialog_loading.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_loading.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_transactions.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_transactions.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_notes.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_notes.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_calendar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_calendar.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_wt.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_wt.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_notification.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_notification.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_workout_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_workout_details.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/dialog_student_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_student_details.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_investment.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_note_video.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_note_video.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_program.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_chat_ai.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_chat_ai.xml.flat
com.example.allinone.app-main-83\:/navigation/mobile_navigation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\navigation_mobile_navigation.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_futures_tab.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_futures_tab.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_category_general.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_general.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_profit_loss.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_profit_loss.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/dialog_progress.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_progress.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_format_underline.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_underline.xml.flat
com.example.allinone.app-main-112\:/color/text_input_box_stroke.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_text_input_box_stroke.xml.flat
com.example.allinone.app-main-116\:/drawable/transparent.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_transparent.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_clear_data.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_clear_data.xml.flat
com.example.allinone.app-main-83\:/menu/wt_student_context_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_wt_student_context_menu.xml.flat
com.example.allinone.app-main-113\:/color-night/text_input_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color-night_text_input_text_color.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_wt_registry.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_wt_registry.xml.flat
com.example.allinone.app-main-116\:/drawable/bg_day_with_events.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_day_with_events.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_transaction.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_transaction.xml.flat
com.example.allinone.app-main-116\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_binance_position.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_binance_position.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout-night-v8/fragment_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_fragment_history.xml.flat
com.example.allinone.app-main-112\:/drawable/placeholder_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_placeholder_image.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_checkbox.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_checkbox.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_database_record.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_database_record.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_no_students.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_no_students.xml.flat
com.example.allinone.app-main-83\:/menu/instagram_bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_instagram_bottom_nav_menu.xml.flat
com.example.allinone.app-main-116\:/drawable/rounded_corner_bg.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_rounded_corner_bg.xml.flat
com.example.allinone.app-main-113\:/menu/menu_tasks.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_menu_tasks.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/offline_status_view.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_offline_status_view.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_home.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_home.xml.flat
com.example.allinone.app-main-83\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/dialog_add_event.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_event.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_image.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_launcher_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout-night-v8/fragment_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_fragment_history.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_task.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_task.xml.flat
com.example.allinone.app-main-116\:/drawable/bg_day_with_lesson.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_day_with_lesson.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_investments_tab.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_investments_tab.xml.flat
com.example.allinone.app-main-83\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_workout_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_workout_exercise.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_video_error.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_video_error.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_category_summary.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_category_summary.xml.flat
com.example.allinone.app-main-83\:/drawable/bg_tag_blue.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_tag_blue.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_lessons.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_lessons.xml.flat
com.example.allinone.app-main-83\:/menu/search_notes.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_search_notes.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_file.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_file.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_chat_ai.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_chat_ai.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_file_structure.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_file_structure.xml.flat
com.example.allinone.app-main-113\:/navigation/nav_graph.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\navigation_nav_graph.xml.flat
com.example.allinone.app-main-113\:/menu/menu_edit_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_menu_edit_note.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_student.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_remove.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_remove.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_file_structure.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_file_structure.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/theme_switch_layout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_theme_switch_layout.xml.flat
com.example.allinone.app-main-112\:/drawable/bg_current_day.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_current_day.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_note.xml.flat
com.example.allinone.app-main-83\:/mipmap-anydpi-v33/ic_launcher.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v33_ic_launcher.xml.flat
com.example.allinone.app-main-113\:/menu/drawer_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_drawer_menu.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_investment_dropdown.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_investment_dropdown.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_transactions.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_transactions.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_close.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_close.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_add_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_add_exercise.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_add.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add.xml.flat
com.example.allinone.app-main-116\:/navigation/nav_graph.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\navigation_nav_graph.xml.flat
com.example.allinone.app-main-112\:/color-night/dialog_action_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color-night_dialog_action_button_color.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_format_text.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_text.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_progress.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_progress.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_error.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_error.xml.flat
com.example.allinone.app-main-113\:/drawable/dialog_rounded_bg.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dialog_rounded_bg.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_folder.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_folder.xml.flat
com.example.allinone.app-main-83\:/font/opensans.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_opensans.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_expense_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_expense_investment.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_note.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_wt_seminars.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_wt_seminars.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_file_structure.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_file_structure.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_active_workout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_active_workout.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/dialog_income_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_income_investment.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_share.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_share.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/dialog_add_task.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_task.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_remove.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_remove.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_category_bills.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_bills.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_history.xml.flat
com.example.allinone.app-main-112\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.allinone.app-main-116\:/drawable/ic_expand_more.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_expand_more.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_instagram_posts.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_instagram_posts.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_log_entry.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_log_entry.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/activity_main.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_expense.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_expense.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_log.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_log.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_category_game.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_game.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_wt.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_wt.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_format_list_numbered.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_list_numbered.xml.flat
com.example.allinone.app-main-116\:/drawable/default_profile.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_default_profile.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_search_white.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_search_white.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_workout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_workout.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_category_transport.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_transport.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_students.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_students.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_profit_loss.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_profit_loss.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_seminar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_seminar.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_video_placeholder.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_video_placeholder.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_investment_selection.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_investment_selection.xml.flat
com.example.allinone.app-main-113\:/drawable/bg_tag_blue.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_tag_blue.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_history.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout-night-v8/fragment_wt_registry.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_fragment_wt_registry.xml.flat
com.example.allinone.app-main-113\:/navigation/mobile_navigation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\navigation_mobile_navigation.xml.flat
com.example.allinone.app-main-83\:/drawable/circle_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_background.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_dashboard.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_dashboard.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_edit_wt_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_wt_student.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_history.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_chat_ai.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_chat_ai.xml.flat
com.example.allinone.app-main-116\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.allinone.app-main-112\:/drawable/ic_expand_more.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_expand_more.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_expense.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_expense.xml.flat
com.example.allinone.app-main-112\:/color/dialog_delete_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_dialog_delete_button_color.xml.flat
com.example.allinone.app-main-83\:/color/chip_background_selector.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_chip_background_selector.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/dialog_post_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_post_details.xml.flat
com.example.allinone.app-main-113\:/drawable-v24/ic_launcher_foreground.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-v24_ic_launcher_foreground.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_format_bold.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_bold.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_chat_user.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_chat_user.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_close.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_close.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_category_game.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_game.xml.flat
com.example.allinone.app-main-113\:/color/text_input_box_stroke.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_text_input_box_stroke.xml.flat
com.example.allinone.app-main-113\:/xml/network_security_config.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_network_security_config.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_database.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_database.xml.flat
com.example.allinone.app-main-113\:/menu/menu_task_options.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_menu_task_options.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_format_list_bulleted.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_list_bulleted.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_wt_register.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_wt_register.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_fullscreen_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_fullscreen_image.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_no_registrations.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_no_registrations.xml.flat
com.example.allinone.app-main-113\:/menu/wt_registration_context_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_wt_registration_context_menu.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_instagram_posts.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_instagram_posts.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_cleardata.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_cleardata.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_category_game.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_game.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_edit_lesson.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_lesson.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_edit_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_note.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_color_picker.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_color_picker.xml.flat
com.example.allinone.app-main-83\:/color/bottom_nav_item_color_light.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_bottom_nav_item_color_light.xml.flat
com.example.allinone.app-main-112\:/drawable/splash_layout_drawable.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_splash_layout_drawable.xml.flat
com.example.allinone.app-main-116\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_database_record.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_database_record.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/fragment_workout_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_workout_program.xml.flat
com.example.allinone.app-main-116\:/drawable/error_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_error_image.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_add_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_add_exercise.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_calendar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_calendar.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_format_underline.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_format_underline.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_task_group_header.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_task_group_header.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/dialog_edit_seminar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_seminar.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/activity_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_backup.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_note.xml.flat
com.example.allinone.app-main-116\:/drawable-night/dialog_rounded_bg.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-night_dialog_rounded_bg.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_workout_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_workout_details.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_backup.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_program.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_instagram_posts.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_instagram_posts.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_wt_seminars.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_wt_seminars.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_fullscreen_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_fullscreen_image.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_category_dropdown.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_category_dropdown.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_category_shopping.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_shopping.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_stop.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_stop.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_remove.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_remove.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_category_salary.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_salary.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_video_placeholder.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_video_placeholder.xml.flat
com.example.allinone.app-main-112\:/menu/menu_task_options.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_menu_task_options.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_lessons.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_lessons.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_video_placeholder.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_video_placeholder.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_dashboard.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_dashboard.xml.flat
com.example.allinone.app-main-83\:/drawable/bg_day_with_event.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_day_with_event.xml.flat
com.example.allinone.app-main-113\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.allinone.app-main-116\:/color/text_input_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_text_input_text_color.xml.flat
com.example.allinone.app-main-113\:/drawable-night/bg_category_chip.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-night_bg_category_chip.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_category_spending.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_category_spending.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_transaction_report.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_transaction_report.xml.flat
com.example.allinone.app-main-116\:/drawable/completed_exercise_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_completed_exercise_background.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout-night-v8/fragment_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_fragment_history.xml.flat
com.example.allinone.app-main-112\:/menu/search_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_search_history.xml.flat
com.example.allinone.app-main-112\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_futures.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_futures.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/dialog_loading.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_loading.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_checkbox.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_checkbox.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_clear_data.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_clear_data.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout-night-v8/dialog_edit_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_dialog_edit_note.xml.flat
com.example.allinone.app-main-112\:/drawable/bg_selected_day.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_selected_day.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dropdown_item.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dropdown_item.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_student_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_student_details.xml.flat
com.example.allinone.app-main-116\:/menu/workout_bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_workout_bottom_nav_menu.xml.flat
com.example.allinone.app-main-83\:/drawable/bg_day_with_lesson.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_day_with_lesson.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_program_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_program_details.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/layout_page_header.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_layout_page_header.xml.flat
com.example.allinone.app-main-116\:/menu/menu_group_options.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_menu_group_options.xml.flat
com.example.allinone.app-main-116\:/color-night/dialog_delete_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color-night_dialog_delete_button_color.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_binance_position.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_binance_position.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_dashboard.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_dashboard.xml.flat
com.example.allinone.app-main-83\:/xml/file_paths.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_file_paths.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_add_event.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_event.xml.flat
com.example.allinone.app-main-116\:/color/drawer_item_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_drawer_item_color.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_investments_tab.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_investments_tab.xml.flat
com.example.allinone.app-main-116\:/menu/menu_task_options.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_menu_task_options.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_futures_position.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_futures_position.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/theme_switch_layout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_theme_switch_layout.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout-night-v8/dialog_edit_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_dialog_edit_note.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_tasks.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_tasks.xml.flat
com.example.allinone.app-main-113\:/drawable/rounded_corner_bg.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_rounded_corner_bg.xml.flat
com.example.allinone.app-main-116\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.allinone.app-main-112\:/drawable/ic_checkbox.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_checkbox.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_workout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_workout.xml.flat
com.example.allinone.app-main-83\:/xml/backup_rules.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.example.allinone.app-main-83\:/menu/search_register.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_search_register.xml.flat
com.example.allinone.app-main-113\:/drawable/transparent.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_transparent.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_code.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_code.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_database_record.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_database_record.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout-night-v8/activity_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_activity_backup.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_cleardata.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_cleardata.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_play.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_play.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_investment.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_workout_dashboard.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_workout_dashboard.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_chat_source.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_chat_source.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_add_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_add_exercise.xml.flat
com.example.allinone.app-main-83\:/drawable/completed_exercise_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_completed_exercise_background.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_wt_register.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_wt_register.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_futures_position.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_futures_position.xml.flat
com.example.allinone.app-main-83\:/menu/menu_tasks.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_menu_tasks.xml.flat
com.example.allinone.app-main-83\:/color/text_input_text_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_text_input_text_color.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_play_circle.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_play_circle.xml.flat
com.example.allinone.app-main-83\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.allinone.app-main-112\:/menu/menu_tasks.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_menu_tasks.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_wt_lessons.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_wt_lessons.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout-night-v8/activity_main.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_activity_main.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_instagram.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_instagram.xml.flat
com.example.allinone.app-main-112\:/color/drawer_item_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_drawer_item_color.xml.flat
com.example.allinone.app-main-113\:/color-night/text_input_box_stroke.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color-night_text_input_box_stroke.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_calendar.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_calendar.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_wt_registers.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_wt_registers.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_fitness.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_fitness.xml.flat
com.example.allinone.app-main-112\:/menu/search_notes.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_search_notes.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_reports.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_reports.xml.flat
com.example.allinone.app-main-112\:/drawable/circle_background_red.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_background_red.xml.flat
com.example.allinone.app-main-116\:/color-night/text_input_box_stroke.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color-night_text_input_box_stroke.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_home.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_home.xml.flat
com.example.allinone.app-main-113\:/drawable/placeholder_image.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_placeholder_image.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/fragment_instagram_posts.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_instagram_posts.xml.flat
com.example.allinone.app-main-116\:/drawable/selected_circle_shape.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_selected_circle_shape.xml.flat
com.example.allinone.app-main-112\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_wt_register.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_wt_register.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_draw.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_draw.xml.flat
com.example.allinone.app-main-112\:/xml/network_security_config.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_network_security_config.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_backup.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/dialog_edit_lesson.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_lesson.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_file.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_file.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_add_task.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_task.xml.flat
com.example.allinone.app-main-116\:/color/dialog_delete_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_dialog_delete_button_color.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_voice_note.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_voice_note.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_notes.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_notes.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_binance_position.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_binance_position.xml.flat
com.example.allinone.app-main-113\:/drawable-night/dialog_rounded_bg.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-night_dialog_rounded_bg.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_backup.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_registration.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_registration.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_add_task.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_task.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_file_structure.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_file_structure.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_exercise.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_delete.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_delete.xml.flat
com.example.allinone.app-main-83\:/drawable/default_profile.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_default_profile.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/fragment_workout_stats.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_workout_stats.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_wt.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_wt.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_dashboard.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_dashboard.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/activity_drawing.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_drawing.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_lesson.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_lesson.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_investments.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_investments.xml.flat
com.example.allinone.app-main-116\:/xml/backup_rules.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.example.allinone.app-main-116\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.allinone.app-main-116\:/mipmap-anydpi-v33/ic_launcher.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v33_ic_launcher.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_transaction.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_transaction.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_pause.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_pause.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_file.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_file.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_workout_exercise.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_workout_exercise.xml.flat
com.example.allinone.app-main-83\:/drawable/splash_layout_drawable.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_splash_layout_drawable.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_category_transport.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_transport.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/item_wt_event.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_wt_event.xml.flat
com.example.allinone.app-main-113\:/drawable/simple_text_splash.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_simple_text_splash.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/offline_status_view.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_offline_status_view.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/splash_text_layout.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_splash_text_layout.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_transactions_overview.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_transactions_overview.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_graduation.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_graduation.xml.flat
com.example.allinone.app-main-116\:/drawable/bg_tag_red.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_tag_red.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_history.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_history.xml.flat
com.example.allinone.app-main-83\:/menu/bottom_nav_menu.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_bottom_nav_menu.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_wt_seminars.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_wt_seminars.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_category_wing_tzun.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_wing_tzun.xml.flat
com.example.allinone.app-main-116\:/color/bottom_nav_item_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_bottom_nav_item_color.xml.flat
com.example.allinone.app-main-83\:/color-night/dialog_delete_button_color.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color-night_dialog_delete_button_color.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout/item_wt_student.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_wt_student.xml.flat
com.example.allinone.app-main-113\:/drawable/ic_category_salary.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_salary.xml.flat
com.example.allinone.app-mergeDebugResources-80\:/layout-night-v8/dialog_student_details.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-night_dialog_student_details.xml.flat
com.example.allinone.app-mergeDebugResources-109\:/layout/dialog_progress.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_progress.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_futures_tp_sl.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_futures_tp_sl.xml.flat
com.example.allinone.app-main-113\:/drawable/default_profile.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_default_profile.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_income_investment.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_income_investment.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/dialog_edit_program.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_program.xml.flat
com.example.allinone.app-main-112\:/drawable-night/dialog_rounded_bg.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-night_dialog_rounded_bg.xml.flat
com.example.allinone.app-main-116\:/drawable/ic_code.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_code.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_log_entry.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_log_entry.xml.flat
com.example.allinone.app-main-116\:/drawable/fully_uncompleted_exercise_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_fully_uncompleted_exercise_background.xml.flat
com.example.allinone.app-main-112\:/drawable/ic_category_sports.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_sports.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/dialog_edit_lesson.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_edit_lesson.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_backup.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_backup.xml.flat
com.example.allinone.app-main-83\:/drawable/fully_uncompleted_exercise_background.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_fully_uncompleted_exercise_background.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/fragment_investments_tab.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_investments_tab.xml.flat
com.example.allinone.app-main-112\:/navigation/nav_graph.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\navigation_nav_graph.xml.flat
com.example.allinone.app-mergeDebugResources-110\:/layout/item_event.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_event.xml.flat
com.example.allinone.app-main-83\:/drawable/ic_category_bills.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category_bills.xml.flat
com.example.allinone.app-mergeDebugResources-113\:/layout/item_investment_selection.xml=C\:\\Users\\goktu\\Documents\\GitHub\\AllinOne\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_investment_selection.xml.flat
