<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:attr/windowBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Balance Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:cardBackgroundColor="?attr/colorSurface">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Current Balance"
                    android:textAppearance="?attr/textAppearanceBody1"
                    android:alpha="0.7" />

                <TextView
                    android:id="@+id/balanceText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:fontFamily="@font/opensans"
                    android:textAppearance="?attr/textAppearanceHeadline4"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/incomeText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:textAppearance="?attr/textAppearanceBody1" />

                <TextView
                    android:id="@+id/expenseText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:textAppearance="?attr/textAppearanceBody1" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Pie Chart Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:cardBackgroundColor="?attr/colorSurface">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Transaction Summary"
                    android:textAppearance="?attr/textAppearanceHeadline6" />

                <com.github.mikephil.charting.charts.PieChart
                    android:id="@+id/pieChart"
                    android:layout_width="match_parent"
                    android:layout_height="250dp"
                    android:layout_marginTop="16dp" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Transaction Inputs -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:cardBackgroundColor="?attr/colorSurface">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/amountLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Amount">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/amountInput"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="numberDecimal" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/typeLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="Category">

                    <AutoCompleteTextView
                        android:id="@+id/typeDropdown"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/descriptionLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="Description (Optional)">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/descriptionInput"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text" />
                </com.google.android.material.textfield.TextInputLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:orientation="horizontal">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/addExpenseButton"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:layout_weight="1"
                        android:backgroundTint="@android:color/holo_red_dark"
                        android:padding="12dp"
                        android:text="Expense"
                        app:cornerRadius="8dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/addIncomeButton"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_weight="1"
                        android:backgroundTint="@android:color/holo_green_dark"
                        android:padding="12dp"
                        android:text="Income"
                        app:cornerRadius="8dp" />
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Transactions Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/transactionsCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:cardBackgroundColor="?attr/colorSurface">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Transactions"
                    android:textAppearance="?attr/textAppearanceHeadline6"
                    android:layout_marginBottom="12dp"/>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/transactionsRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:minHeight="150dp"
                    android:nestedScrollingEnabled="false"/>

                <TextView
                    android:id="@+id/emptyTransactionsText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:padding="16dp"
                    android:text="No transactions found"
                    android:textColor="@color/gray"
                    android:visibility="gone"/>

                <!-- Pagination Controls -->
                <LinearLayout
                    android:id="@+id/paginationControls"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center"
                    android:layout_marginTop="8dp">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/prevPageButton"
                        android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:text="Prev"
                        android:textAllCaps="false"
                        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                        android:layout_marginEnd="8dp"/>

                    <TextView
                        android:id="@+id/pageIndicator"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Page 1"
                        android:textSize="12sp"
                        android:layout_marginHorizontal="8dp"/>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/nextPageButton"
                        android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:text="Next"
                        android:textAllCaps="false"
                        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                        android:layout_marginStart="8dp"/>
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>
</ScrollView>