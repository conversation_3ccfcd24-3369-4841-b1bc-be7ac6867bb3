/ Header Record For PersistentHashMapValueStorage= android.app.Application$androidx.work.Configuration.Provider timber.log.Timber.Tree$ #androidx.activity.ComponentActivity) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback android.widget.ArrayAdapter2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback android.widget.ArrayAdapter) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback1 0androidx.viewpager2.adapter.FragmentStateAdapter) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback# "com.example.allinone.api.ApiResult# "com.example.allinone.api.ApiResult# "com.example.allinone.api.ApiResult) (androidx.appcompat.app.AppCompatActivity) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback5 4com.example.allinone.core.data.datasource.DataSource= <com.example.allinone.core.data.datasource.ReactiveDataSource5 4com.example.allinone.core.data.datasource.DataSource= <com.example.allinone.core.data.datasource.ReactiveDataSource android.os.Parcelable kotlin.Enum android.os.Parcelable android.os.Parcelable android.os.Parcelable android.os.Parcelable android.os.Parcelable android.os.Parcelable android.os.Parcelable androidx.lifecycle.ViewModel) (com.example.allinone.data.common.UiState) (com.example.allinone.data.common.UiState) (com.example.allinone.data.common.UiState) (com.example.allinone.data.common.UiState androidx.room.RoomDatabase kotlin.Enum kotlin.Enum kotlin.EnumB Acom.example.allinone.feature.instagram.data.model.InstagramResultB Acom.example.allinone.feature.instagram.data.model.InstagramResultB Acom.example.allinone.feature.instagram.data.model.InstagramResultM Lcom.example.allinone.feature.instagram.domain.repository.InstagramRepository2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel; :com.example.allinone.core.data.datasource.RemoteDataSourcey >com.example.allinone.core.data.datasource.SearchableDataSource9com.example.allinone.core.data.datasource.LocalDataSourceG Fcom.example.allinone.feature.notes.data.datasource.NoteLocalDataSourceH Gcom.example.allinone.feature.notes.data.datasource.NoteRemoteDataSourceD Ccom.example.allinone.feature.notes.domain.repository.NoteRepository; :com.example.allinone.core.data.datasource.RemoteDataSourcey >com.example.allinone.core.data.datasource.SearchableDataSource9com.example.allinone.core.data.datasource.LocalDataSourceR Qcom.example.allinone.feature.transactions.domain.repository.TransactionRepository; :com.example.allinone.core.data.datasource.RemoteDataSourcey >com.example.allinone.core.data.datasource.SearchableDataSource9com.example.allinone.core.data.datasource.LocalDataSource; :com.example.allinone.core.data.datasource.RemoteDataSourcey >com.example.allinone.core.data.datasource.SearchableDataSource9com.example.allinone.core.data.datasource.LocalDataSourceL Kcom.example.allinone.feature.workout.data.datasource.WorkoutLocalDataSourceM Lcom.example.allinone.feature.workout.data.datasource.WorkoutRemoteDataSourceI Hcom.example.allinone.feature.workout.domain.repository.WorkoutRepositoryL Kcom.example.allinone.firebase.GenericOfflineQueueProcessor.OperationHandlerL Kcom.example.allinone.firebase.GenericOfflineQueueProcessor.OperationHandlerL Kcom.example.allinone.firebase.GenericOfflineQueueProcessor.OperationHandlerL Kcom.example.allinone.firebase.GenericOfflineQueueProcessor.OperationHandlerL Kcom.example.allinone.firebase.GenericOfflineQueueProcessor.OperationHandlerL Kcom.example.allinone.firebase.GenericOfflineQueueProcessor.OperationHandlerL Kcom.example.allinone.firebase.GenericOfflineQueueProcessor.OperationHandlerL Kcom.example.allinone.firebase.GenericOfflineQueueProcessor.OperationHandlerL Kcom.example.allinone.firebase.GenericOfflineQueueProcessor.OperationHandlerL Kcom.example.allinone.firebase.GenericOfflineQueueProcessor.OperationHandlerL Kcom.example.allinone.firebase.GenericOfflineQueueProcessor.OperationHandler kotlin.Enum kotlin.Enum androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment1 0androidx.viewpager2.adapter.FragmentStateAdapter androidx.fragment.app.Fragment androidx.fragment.app.Fragment kotlin.Enum% $com.example.allinone.ui.BaseFragment androidx.fragment.app.Fragment/ .com.example.allinone.ui.components.HtmlElement/ .com.example.allinone.ui.components.HtmlElement/ .com.example.allinone.ui.components.HtmlElement kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum$ #androidx.activity.ComponentActivity+ *com.example.allinone.ui.navigation.NavItem+ *com.example.allinone.ui.navigation.NavItem+ *com.example.allinone.ui.navigation.NavItem+ *com.example.allinone.ui.navigation.NavItem+ *com.example.allinone.ui.navigation.NavItem+ *com.example.allinone.ui.navigation.NavItem+ *com.example.allinone.ui.navigation.NavItem+ *com.example.allinone.ui.navigation.NavItem+ *com.example.allinone.ui.navigation.NavItem+ *com.example.allinone.ui.navigation.NavItem+ *com.example.allinone.ui.navigation.NavItem+ *com.example.allinone.ui.navigation.NavItem+ *com.example.allinone.ui.navigation.NavItem+ *com.example.allinone.ui.navigation.NavItem+ *com.example.allinone.ui.navigation.NavItem+ *com.example.allinone.ui.navigation.NavItem+ *com.example.allinone.ui.navigation.NavItem+ *com.example.allinone.ui.navigation.NavItem+ *com.example.allinone.ui.navigation.NavItem$ #androidx.lifecycle.AndroidViewModel$ #androidx.lifecycle.AndroidViewModel androidx.lifecycle.ViewModel kotlin.Enum$ #androidx.lifecycle.AndroidViewModel androidx.lifecycle.ViewModel$ #androidx.lifecycle.AndroidViewModel? >com.example.allinone.viewmodels.InvestmentsViewModel.AddStatus? >com.example.allinone.viewmodels.InvestmentsViewModel.AddStatus? >com.example.allinone.viewmodels.InvestmentsViewModel.AddStatusB Acom.example.allinone.viewmodels.InvestmentsViewModel.UpdateStatusB Acom.example.allinone.viewmodels.InvestmentsViewModel.UpdateStatusB Acom.example.allinone.viewmodels.InvestmentsViewModel.UpdateStatusB Acom.example.allinone.viewmodels.InvestmentsViewModel.DeleteStatusB Acom.example.allinone.viewmodels.InvestmentsViewModel.DeleteStatusB Acom.example.allinone.viewmodels.InvestmentsViewModel.DeleteStatus$ #androidx.lifecycle.AndroidViewModel$ #androidx.lifecycle.AndroidViewModel androidx.lifecycle.ViewModel2 1com.example.allinone.viewmodels.LessonChangeEvent2 1com.example.allinone.viewmodels.LessonChangeEvent2 1com.example.allinone.viewmodels.LessonChangeEvent2 1com.example.allinone.viewmodels.LessonChangeEvent$ #androidx.lifecycle.AndroidViewModel$ #androidx.lifecycle.AndroidViewModel kotlin.Enum$ #androidx.lifecycle.AndroidViewModel androidx.work.CoroutineWorker androidx.work.CoroutineWorker androidx.work.CoroutineWorker!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding