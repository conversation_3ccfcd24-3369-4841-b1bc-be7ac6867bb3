<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    <color name="gray_dark">#FF444444</color>
    <color name="gray_light">#FFD3D3D3</color>
    <color name="gray">#999999</color>
    <color name="colorPrimary">#2196F3</color>
    <color name="colorPrimaryDark">#1976D2</color>
    <color name="colorPrimaryLight">#BBDEFB</color>
    <color name="colorAccent">#FF4081</color>
    <color name="expiration_color">#FFEBEE</color>
    <color name="start_color">#E8F5E9</color>
    <color name="default_event_color">#FFFFFF</color>
    <color name="lesson_event_color">#E3F2FD</color>

    <!-- Text colors -->
    <color name="textPrimary">#212121</color>

    <!-- History page colors -->
    <color name="red">#F44336</color>
    <color name="dark_gray">#555555</color>
    <color name="medium_gray">#888888</color>

    <!-- Network status color -->
    <color name="colorError">#F44336</color>
    <color name="colorSuccess">#4CAF50</color>
    <color name="colorWarning">#FFC107</color>

    <!-- Dark theme colors - Professional Navy Blue theme -->
    <color name="navy_primary">#003566</color>         <!-- Deep navy blue (primary) -->
    <color name="navy_background">#001D3D</color>      <!-- Midnight blue (background) -->
    <color name="navy_surface">#00264D</color>         <!-- Slightly lighter navy (surface) -->
    <color name="navy_variant">#004E92</color>         <!-- Lighter navy variant -->
    <color name="navy_accent">#4DA8DA</color>          <!-- Blue accent that complements navy -->
    <color name="navy_text_secondary">#A5C5E1</color>  <!-- Light blue for secondary text -->

    <!-- Bright tab color for better visibility in dark theme -->
    <color name="bright_tab_selected">#0466C8</color>  <!-- Bright blue for selected tabs -->
    <color name="bright_tab_unselected">#7FB5DE</color> <!-- Light blue for unselected tabs -->

    <color name="error_light">#FFCDD2</color>
    <color name="warning_light">#FFF9C4</color>

    <!-- Add green color for chips -->
    <color name="green">#4CAF50</color>
    <color name="purple">#9C27B0</color>

    <!-- Registration event colors -->
    <color name="registration_start">#4CAF50</color>  <!-- Green -->
    <color name="registration_end">@color/red</color>  <!-- Red -->

    <!-- Tag colors -->
    <color name="bg_tag_blue">#2196F3</color>  <!-- Blue color for tags and indicators -->

    <!-- Instagram card colors -->
    <color name="feedCard">#FFFFFF</color>
    <color name="reelsCard">#F0F8FF</color>
    <color name="storyCard">#F0FFF0</color>
    <color name="albumCard">#F5F5F5</color>
    <color name="cardStroke">#E0E0E0</color>

    <!-- New color for bold text that adapts to dark/light mode -->
    <color name="boldTextColor">@color/black</color>

    <!-- Instagram Post Type Background Colors -->
    <color name="reels_background">@color/reelsCard</color>
    <color name="video_background">@color/storyCard</color>
    <color name="carousel_background">@color/albumCard</color>

    <!-- Instagram Engagement Colors -->
    <color name="excellent_green">#4CAF50</color>
    <color name="good_orange">#FF9800</color>
    <color name="poor_red">#F44336</color>

    <!-- Instagram Media Type Colors -->
    <color name="reels_purple">#8E24AA</color>
    <color name="video_blue">#2196F3</color>
    <color name="carousel_orange">#FF9800</color>
    <color name="image_green">#4CAF50</color>
    <color name="light_blue">#E3F2FD</color>  <!-- Light blue for image upload cards -->

    <!-- Chat Message Colors -->
    <color name="text_primary">#212121</color>
    <color name="error_dark">#D32F2F</color>

    <!-- Utility Colors -->
    <color name="light_gray">#F0F0F0</color>

    <!-- User Interface Colors -->
    <color name="background_color">#F5F5F5</color>
    <color name="surface_color">#FFFFFF</color>
    <color name="on_surface_color">#212121</color>
    
    <!-- Chat colors -->
    <color name="user_message_bg">#007AFF</color>
    <color name="ai_message_bg">#E8E8E8</color>
    
    <!-- Task colors -->
    <color name="task_checkbox_color">#4CAF50</color>  <!-- Green for task completion -->
    
    <!-- Material Design 500 color variants for task group colors -->
    <color name="blue_500">#2196F3</color>
    <color name="green_500">#4CAF50</color>
    <color name="red_500">#F44336</color>
    <color name="orange_500">#FF9800</color>
    <color name="purple_500">#9C27B0</color>
</resources>