// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentLogErrorsBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final LinearLayout buttonLayout;

  @NonNull
  public final MaterialButton clearButton;

  @NonNull
  public final TextView emptyView;

  @NonNull
  public final RecyclerView recyclerView;

  @NonNull
  public final MaterialButton refreshButton;

  @NonNull
  public final MaterialButton shareButton;

  private FragmentLogErrorsBinding(@NonNull ConstraintLayout rootView,
      @NonNull LinearLayout buttonLayout, @NonNull MaterialButton clearButton,
      @NonNull TextView emptyView, @NonNull RecyclerView recyclerView,
      @NonNull MaterialButton refreshButton, @NonNull MaterialButton shareButton) {
    this.rootView = rootView;
    this.buttonLayout = buttonLayout;
    this.clearButton = clearButton;
    this.emptyView = emptyView;
    this.recyclerView = recyclerView;
    this.refreshButton = refreshButton;
    this.shareButton = shareButton;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentLogErrorsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentLogErrorsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_log_errors, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentLogErrorsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonLayout;
      LinearLayout buttonLayout = ViewBindings.findChildViewById(rootView, id);
      if (buttonLayout == null) {
        break missingId;
      }

      id = R.id.clearButton;
      MaterialButton clearButton = ViewBindings.findChildViewById(rootView, id);
      if (clearButton == null) {
        break missingId;
      }

      id = R.id.emptyView;
      TextView emptyView = ViewBindings.findChildViewById(rootView, id);
      if (emptyView == null) {
        break missingId;
      }

      id = R.id.recyclerView;
      RecyclerView recyclerView = ViewBindings.findChildViewById(rootView, id);
      if (recyclerView == null) {
        break missingId;
      }

      id = R.id.refreshButton;
      MaterialButton refreshButton = ViewBindings.findChildViewById(rootView, id);
      if (refreshButton == null) {
        break missingId;
      }

      id = R.id.shareButton;
      MaterialButton shareButton = ViewBindings.findChildViewById(rootView, id);
      if (shareButton == null) {
        break missingId;
      }

      return new FragmentLogErrorsBinding((ConstraintLayout) rootView, buttonLayout, clearButton,
          emptyView, recyclerView, refreshButton, shareButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
