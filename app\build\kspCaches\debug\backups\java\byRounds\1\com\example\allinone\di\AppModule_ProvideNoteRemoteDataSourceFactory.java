package com.example.allinone.di;

import com.example.allinone.feature.notes.data.datasource.NoteRemoteDataSource;
import com.example.allinone.firebase.FirebaseManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class AppModule_ProvideNoteRemoteDataSourceFactory implements Factory<NoteRemoteDataSource> {
  private final Provider<FirebaseManager> firebaseManagerProvider;

  public AppModule_ProvideNoteRemoteDataSourceFactory(
      Provider<FirebaseManager> firebaseManagerProvider) {
    this.firebaseManagerProvider = firebaseManagerProvider;
  }

  @Override
  public NoteRemoteDataSource get() {
    return provideNoteRemoteDataSource(firebaseManagerProvider.get());
  }

  public static AppModule_ProvideNoteRemoteDataSourceFactory create(
      Provider<FirebaseManager> firebaseManagerProvider) {
    return new AppModule_ProvideNoteRemoteDataSourceFactory(firebaseManagerProvider);
  }

  public static NoteRemoteDataSource provideNoteRemoteDataSource(FirebaseManager firebaseManager) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideNoteRemoteDataSource(firebaseManager));
  }
}
