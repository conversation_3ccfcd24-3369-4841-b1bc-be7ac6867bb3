// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.8.2'
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.25'
        classpath 'com.google.gms:google-services:4.4.2'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.2'
        classpath 'com.google.dagger:hilt-android-gradle-plugin:2.51.1'
        classpath "androidx.navigation:navigation-safe-args-gradle-plugin:2.8.4"
    }
}

plugins {
    id 'com.android.application' version '8.8.2' apply false
    id 'com.android.library' version '8.8.2' apply false
    id 'org.jetbrains.kotlin.android' version '1.9.25' apply false
    id 'com.google.devtools.ksp' version '1.9.25-1.0.20' apply false
    id 'com.google.gms.google-services' version '4.4.2' apply false
    id 'com.diffplug.spotless' version '6.25.0' apply false
}

// Nothing else here - we'll apply Spotless directly in app/build.gradle