<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_tasks" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\fragment_tasks.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_tasks_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="50" endOffset="51"/></Target><Target id="@+id/swipe_refresh_layout" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="7" startOffset="4" endLine="26" endOffset="59"/></Target><Target id="@+id/tasksRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="16" startOffset="8" endLine="24" endOffset="55"/></Target><Target id="@+id/emptyStateText" view="TextView"><Expressions/><location startLine="28" startOffset="4" endLine="38" endOffset="51"/></Target><Target id="@+id/addTaskFab" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="40" startOffset="4" endLine="48" endOffset="51"/></Target></Targets></Layout>