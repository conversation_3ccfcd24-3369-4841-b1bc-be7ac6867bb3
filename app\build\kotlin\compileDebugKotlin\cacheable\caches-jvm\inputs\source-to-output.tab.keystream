Dapp/src/main/java/com/example/allinone/api/BinanceWebSocketClient.kt<app/src/main/java/com/example/allinone/ui/FuturesFragment.kt@app/src/main/java/com/example/allinone/ui/InvestmentsFragment.ktUapp/src/main/java/com/example/allinone/ui/transactions/TransactionsDashboardScreen.kt8app/src/main/java/com/example/allinone/ui/theme/Theme.ktBapp/src/main/java/com/example/allinone/ui/wt/WTRegistryFragment.ktBapp/src/main/java/com/example/allinone/ui/futures/FuturesScreen.kt:app/src/main/java/com/example/allinone/ui/WorkoutScreen.ktEapp/src/main/java/com/example/allinone/ui/components/DrawingCanvas.ktCapp/src/main/java/com/example/allinone/utils/OfflineStatusHelper.ktCapp/src/main/java/com/example/allinone/adapters/NoteImageAdapter.ktHapp/src/main/java/com/example/allinone/viewmodels/WTSeminarsViewModel.kt8app/src/main/java/com/example/allinone/ui/NotesScreen.kt4app/src/main/java/com/example/allinone/data/Event.kt?app/src/main/java/com/example/allinone/adapters/TasksAdapter.ktEapp/src/main/java/com/example/allinone/data/local/RoomCacheManager.kt?app/src/main/java/com/example/allinone/adapters/EventAdapter.kt:app/src/main/java/com/example/allinone/ui/TasksFragment.ktAapp/src/main/java/com/example/allinone/ui/compose/wt/WTDialogs.ktLapp/src/main/java/com/example/allinone/firebase/GenericDataChangeNotifier.ktCapp/src/main/java/com/example/allinone/viewmodels/TasksViewModel.kt7app/src/main/java/com/example/allinone/data/WTLesson.kt>app/src/main/java/com/example/allinone/ui/LogErrorsFragment.ktDapp/src/main/java/com/example/allinone/ui/compose/wt/WTComponents.kt3app/src/main/java/com/example/allinone/data/Task.ktFapp/src/main/java/com/example/allinone/ui/components/RichTextEditor.kt8app/src/main/java/com/example/allinone/data/TaskGroup.ktMapp/src/main/java/com/example/allinone/ui/instagram/InstagramPostsFragment.ktAapp/src/main/java/com/example/allinone/ui/CoinMFuturesFragment.kt@app/src/main/java/com/example/allinone/ui/UsdMFuturesFragment.kt_app/src/main/java/com/example/allinone/feature/workout/data/repository/WorkoutRepositoryImpl.kt6app/src/main/java/com/example/allinone/MainActivity.ktAapp/src/main/java/com/example/allinone/adapters/WTEventAdapter.kt=app/src/main/java/com/example/allinone/data/BinanceBalance.ktEapp/src/main/java/com/example/allinone/ui/components/VoiceRecorder.ktXapp/src/main/java/com/example/allinone/feature/instagram/data/api/InstagramApiService.kt9app/src/main/java/com/example/allinone/data/Investment.ktMapp/src/main/java/com/example/allinone/core/data/repository/BaseRepository.kt[app/src/main/java/com/example/allinone/feature/workout/data/datasource/WorkoutDataSource.kt9app/src/main/java/com/example/allinone/ui/BaseFragment.kt=app/src/main/java/com/example/allinone/utils/ApiKeyManager.ktBapp/src/main/java/com/example/allinone/viewmodels/HomeViewModel.kt<app/src/main/java/com/example/allinone/data/BinanceFuture.kt<app/src/main/java/com/example/allinone/utils/LogcatHelper.ktHapp/src/main/java/com/example/allinone/ui/components/MediaAttachments.ktGapp/src/main/java/com/example/allinone/ui/navigation/NavigationItems.kt[app/src/main/java/com/example/allinone/feature/program/data/datasource/ProgramDataSource.ktEapp/src/main/java/com/example/allinone/firebase/FirebaseRepository.ktFapp/src/main/java/com/example/allinone/ui/InstagramBusinessFragment.ktFapp/src/main/java/com/example/allinone/data/local/dao/CachedNoteDao.ktKapp/src/main/java/com/example/allinone/ui/dialogs/TaskGroupDialogManager.ktDapp/src/main/java/com/example/allinone/ui/workout/WorkoutFragment.ktFapp/src/main/java/com/example/allinone/adapters/GroupedTasksAdapter.ktOapp/src/main/java/com/example/allinone/ui/workout/adapters/WorkoutLogAdapter.kt=app/src/main/java/com/example/allinone/AllinOneApplication.kt<app/src/main/java/com/example/allinone/utils/NetworkUtils.ktZapp/src/main/java/com/example/allinone/feature/notes/data/repository/NoteRepositoryImpl.ktFapp/src/main/java/com/example/allinone/viewmodels/LogErrorViewModel.ktfapp/src/main/java/com/example/allinone/feature/transactions/domain/repository/TransactionRepository.kt>app/src/main/java/com/example/allinone/data/BinancePosition.ktSapp/src/main/java/com/example/allinone/data/local/entities/CachedWTStudentEntity.ktEapp/src/main/java/com/example/allinone/viewmodels/FuturesViewModel.ktIapp/src/main/java/com/example/allinone/adapters/CategorySummaryAdapter.kt=app/src/main/java/com/example/allinone/data/common/UiState.kt>app/src/main/java/com/example/allinone/data/CategorySummary.kt:app/src/main/java/com/example/allinone/data/HistoryItem.ktAapp/src/main/java/com/example/allinone/ui/wt/WTLessonsFragment.kt;app/src/main/java/com/example/allinone/data/BinanceOrder.kt6app/src/main/java/com/example/allinone/data/Program.ktMapp/src/main/java/com/example/allinone/ui/workout/WorkoutDashboardFragment.kt;app/src/main/java/com/example/allinone/ui/EditNoteScreen.kt`app/src/main/java/com/example/allinone/feature/notes/data/datasource/NoteRemoteDataSourceImpl.kt[app/src/main/java/com/example/allinone/feature/instagram/ui/viewmodel/InstagramViewModel.kt<app/src/main/java/com/example/allinone/utils/TradingUtils.kt8app/src/main/java/com/example/allinone/data/VoiceNote.ktCapp/src/main/java/com/example/allinone/data/common/BaseViewModel.ktIapp/src/main/java/com/example/allinone/adapters/InvestmentPagerAdapter.ktMapp/src/main/java/com/example/allinone/data/local/dao/CachedTransactionDao.ktCapp/src/main/java/com/example/allinone/viewmodels/NotesViewModel.ktIapp/src/main/java/com/example/allinone/data/local/dao/CachedProgramDao.ktIapp/src/main/java/com/example/allinone/data/local/dao/CachedWorkoutDao.ktFapp/src/main/java/com/example/allinone/api/ExternalBinanceApiClient.ktGapp/src/main/java/com/example/allinone/viewmodels/WTLessonsViewModel.kt8app/src/main/java/com/example/allinone/data/WTStudent.ktFapp/src/main/java/com/example/allinone/ui/TransactionReportFragment.kt@app/src/main/java/com/example/allinone/data/local/AppDatabase.kt=app/src/main/java/com/example/allinone/config/MuscleGroups.kthapp/src/main/java/com/example/allinone/feature/transactions/data/repository/TransactionRepositoryImpl.kt;app/src/main/java/com/example/allinone/ui/CalendarScreen.ktDapp/src/main/java/com/example/allinone/adapters/InvestmentAdapter.kt<app/src/main/java/com/example/allinone/cache/CacheManager.ktRapp/src/main/java/com/example/allinone/feature/instagram/ui/adapter/ChatAdapter.ktCapp/src/main/java/com/example/allinone/adapters/WTStudentAdapter.ktCapp/src/main/java/com/example/allinone/ui/components/MediaViewer.kt_app/src/main/java/com/example/allinone/feature/notes/data/datasource/NoteLocalDataSourceImpl.ktHapp/src/main/java/com/example/allinone/utils/GooglePlayServicesHelper.kt=app/src/main/java/com/example/allinone/ui/CalendarFragment.ktWapp/src/main/java/com/example/allinone/feature/instagram/data/api/InstagramApiClient.kt?app/src/main/java/com/example/allinone/adapters/NotesAdapter.kt=app/src/main/java/com/example/allinone/data/WTRegistration.ktCapp/src/main/java/com/example/allinone/utils/ConnectivityMonitor.ktOapp/src/main/java/com/example/allinone/firebase/GenericOfflineQueueProcessor.kt7app/src/main/java/com/example/allinone/ui/theme/Type.ktdapp/src/main/java/com/example/allinone/feature/workout/data/datasource/WorkoutLocalDataSourceImpl.ktVapp/src/main/java/com/example/allinone/feature/instagram/data/model/InstagramModels.ktBapp/src/main/java/com/example/allinone/firebase/FirebaseManager.ktHapp/src/main/java/com/example/allinone/viewmodels/WTRegisterViewModel.ktTapp/src/main/java/com/example/allinone/ui/workout/adapters/ProgramExerciseAdapter.ktBapp/src/main/java/com/example/allinone/adapters/LogEntryAdapter.kt>app/src/main/java/com/example/allinone/ui/wt/WTPagerAdapter.ktTapp/src/main/java/com/example/allinone/data/local/entities/CachedInvestmentEntity.ktCapp/src/main/java/com/example/allinone/ui/InvestmentsTabFragment.ktLapp/src/main/java/com/example/allinone/adapters/InvestmentDropdownAdapter.kt>app/src/main/java/com/example/allinone/workers/BackupWorker.ktEapp/src/main/java/com/example/allinone/viewmodels/HistoryViewModel.ktMapp/src/main/java/com/example/allinone/ui/instagram/InstagramAskAIFragment.ktMapp/src/main/java/com/example/allinone/utils/security/SecureStorageManager.kt:app/src/main/java/com/example/allinone/data/Transaction.kt8app/src/main/java/com/example/allinone/ui/theme/Color.kt<app/src/main/java/com/example/allinone/utils/ErrorHandler.kt<app/src/main/java/com/example/allinone/ui/HistoryFragment.ktSapp/src/main/java/com/example/allinone/ui/transactions/TransactionOverviewScreen.kt?app/src/main/java/com/example/allinone/api/ExternalApiModels.kt8app/src/main/java/com/example/allinone/ui/TaskDialogs.ktEapp/src/main/java/com/example/allinone/firebase/DataChangeNotifier.kt8app/src/main/java/com/example/allinone/ui/TasksScreen.ktAapp/src/main/java/com/example/allinone/adapters/HistoryAdapter.ktcapp/src/main/java/com/example/allinone/feature/instagram/data/repository/InstagramRepositoryImpl.ktIapp/src/main/java/com/example/allinone/adapters/InvestmentImageAdapter.ktVapp/src/main/java/com/example/allinone/feature/notes/data/datasource/NoteDataSource.ktKapp/src/main/java/com/example/allinone/adapters/TransactionReportAdapter.ktIapp/src/main/java/com/example/allinone/ui/wt/WTRegisterContentFragment.ktJapp/src/main/java/com/example/allinone/ui/workout/ActiveWorkoutFragment.kt3app/src/main/java/com/example/allinone/data/Note.ktNapp/src/main/java/com/example/allinone/workers/ExpirationNotificationWorker.kt^app/src/main/java/com/example/allinone/feature/wingtzun/data/datasource/WTStudentDataSource.kt?app/src/main/java/com/example/allinone/firebase/OfflineQueue.kt\app/src/main/java/com/example/allinone/feature/instagram/domain/usecase/InstagramUseCases.ktAapp/src/main/java/com/example/allinone/adapters/SeminarAdapter.ktHapp/src/main/java/com/example/allinone/adapters/BinanceFuturesAdapter.ktLapp/src/main/java/com/example/allinone/ui/workout/adapters/ProgramAdapter.ktKapp/src/main/java/com/example/allinone/ui/components/InteractiveHtmlText.kt]app/src/main/java/com/example/allinone/feature/instagram/ui/viewmodel/InstagramAIViewModel.ktIapp/src/main/java/com/example/allinone/ui/investments/InvestmentScreen.kt>app/src/main/java/com/example/allinone/utils/TextStyleUtils.ktFapp/src/main/java/com/example/allinone/firebase/FirebaseStorageUtil.kt]app/src/main/java/com/example/allinone/feature/workout/domain/repository/WorkoutRepository.ktTapp/src/main/java/com/example/allinone/ui/workout/adapters/WorkoutExerciseAdapter.ktEapp/src/main/java/com/example/allinone/ui/workout/WorkoutViewModel.kt<app/src/main/java/com/example/allinone/ui/CalendarDialogs.kt6app/src/main/java/com/example/allinone/data/Workout.ktDapp/src/main/java/com/example/allinone/ui/ExternalFuturesFragment.ktIapp/src/main/java/com/example/allinone/core/data/datasource/DataSource.ktaapp/src/main/java/com/example/allinone/feature/instagram/domain/repository/InstagramRepository.ktIapp/src/main/java/com/example/allinone/adapters/BinancePositionAdapter.ktLapp/src/main/java/com/example/allinone/ui/workout/WorkoutExerciseFragment.ktKapp/src/main/java/com/example/allinone/data/local/dao/CachedWTStudentDao.kt7app/src/main/java/com/example/allinone/data/Exercise.kt=app/src/main/java/com/example/allinone/data/BinanceFutures.ktCapp/src/main/java/com/example/allinone/adapters/NoteVideoAdapter.ktHapp/src/main/java/com/example/allinone/adapters/WTRegistrationAdapter.ktQapp/src/main/java/com/example/allinone/ui/transactions/TransactionReportScreen.ktGapp/src/main/java/com/example/allinone/ui/DatabaseManagementFragment.ktBapp/src/main/java/com/example/allinone/ui/wt/WTRegisterFragment.ktPapp/src/main/java/com/example/allinone/ui/instagram/InstagramInsightsFragment.ktEapp/src/main/java/com/example/allinone/workers/LogcatCaptureWorker.ktIapp/src/main/java/com/example/allinone/viewmodels/InvestmentsViewModel.ktQapp/src/main/java/com/example/allinone/data/local/entities/CachedProgramEntity.ktXapp/src/main/java/com/example/allinone/feature/notes/domain/repository/NoteRepository.ktJapp/src/main/java/com/example/allinone/adapters/CategorySpendingAdapter.ktSapp/src/main/java/com/example/allinone/feature/instagram/ui/adapter/PostsAdapter.kt8app/src/main/java/com/example/allinone/data/WTSeminar.kt<app/src/main/java/com/example/allinone/utils/BackupHelper.ktKapp/src/main/java/com/example/allinone/ui/workout/WorkoutProgramFragment.ktNapp/src/main/java/com/example/allinone/data/local/entities/CachedNoteEntity.ktAapp/src/main/java/com/example/allinone/utils/NumberFormatUtils.ktBapp/src/main/java/com/example/allinone/ui/wt/WTSeminarsFragment.ktQapp/src/main/java/com/example/allinone/data/local/entities/CachedWorkoutEntity.ktMapp/src/main/java/com/example/allinone/adapters/InvestmentSelectionAdapter.ktUapp/src/main/java/com/example/allinone/data/local/entities/CachedTransactionEntity.kt>app/src/main/java/com/example/allinone/backup/BackupAdapter.kteapp/src/main/java/com/example/allinone/feature/workout/data/datasource/WorkoutRemoteDataSourceImpl.ktDapp/src/main/java/com/example/allinone/ui/drawing/DrawingActivity.ktHapp/src/main/java/com/example/allinone/ui/compose/wt/WTRegistryScreen.ktFapp/src/main/java/com/example/allinone/config/TransactionCategories.ktFapp/src/main/java/com/example/allinone/viewmodels/CalendarViewModel.ktEapp/src/main/java/com/example/allinone/adapters/TransactionAdapter.ktIapp/src/main/java/com/example/allinone/ui/workout/WorkoutStatsFragment.ktBapp/src/main/java/com/example/allinone/ui/wt/WTStudentsFragment.ktNapp/src/main/java/com/example/allinone/feature/instagram/di/InstagramModule.kt6app/src/main/java/com/example/allinone/di/AppModule.ktCapp/src/main/java/com/example/allinone/adapters/VoiceNoteAdapter.ktJapp/src/main/java/com/example/allinone/adapters/CategoryDropdownAdapter.ktDapp/src/main/java/com/example/allinone/firebase/FirebaseIdManager.ktIapp/src/main/java/com/example/allinone/adapters/FullscreenImageAdapter.kt?app/src/main/java/com/example/allinone/backup/BackupActivity.ktDapp/src/main/java/com/example/allinone/api/BinanceExternalService.ktGapp/src/main/java/com/example/allinone/api/ExternalBinanceRepository.kt<app/src/main/java/com/example/allinone/ui/InstagramScreen.kt:app/src/main/java/com/example/allinone/ui/HistoryScreen.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               