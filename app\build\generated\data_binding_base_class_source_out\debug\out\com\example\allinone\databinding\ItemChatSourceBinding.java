// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemChatSourceBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView textSourceEmoji;

  @NonNull
  public final TextView textSourceMetrics;

  @NonNull
  public final TextView textSourcePost;

  @NonNull
  public final TextView textSourceScore;

  private ItemChatSourceBinding(@NonNull MaterialCardView rootView,
      @NonNull TextView textSourceEmoji, @NonNull TextView textSourceMetrics,
      @NonNull TextView textSourcePost, @NonNull TextView textSourceScore) {
    this.rootView = rootView;
    this.textSourceEmoji = textSourceEmoji;
    this.textSourceMetrics = textSourceMetrics;
    this.textSourcePost = textSourcePost;
    this.textSourceScore = textSourceScore;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemChatSourceBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemChatSourceBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_chat_source, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemChatSourceBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.textSourceEmoji;
      TextView textSourceEmoji = ViewBindings.findChildViewById(rootView, id);
      if (textSourceEmoji == null) {
        break missingId;
      }

      id = R.id.textSourceMetrics;
      TextView textSourceMetrics = ViewBindings.findChildViewById(rootView, id);
      if (textSourceMetrics == null) {
        break missingId;
      }

      id = R.id.textSourcePost;
      TextView textSourcePost = ViewBindings.findChildViewById(rootView, id);
      if (textSourcePost == null) {
        break missingId;
      }

      id = R.id.textSourceScore;
      TextView textSourceScore = ViewBindings.findChildViewById(rootView, id);
      if (textSourceScore == null) {
        break missingId;
      }

      return new ItemChatSourceBinding((MaterialCardView) rootView, textSourceEmoji,
          textSourceMetrics, textSourcePost, textSourceScore);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
