<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_pin_input" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\dialog_pin_input.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_pin_input_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="39" endOffset="14"/></Target><Target id="@+id/pinEditText" view="EditText"><Expressions/><location startLine="30" startOffset="4" endLine="37" endOffset="33"/></Target></Targets></Layout>