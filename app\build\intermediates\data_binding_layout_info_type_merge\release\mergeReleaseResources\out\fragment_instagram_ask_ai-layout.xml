<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_instagram_ask_ai" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\fragment_instagram_ask_ai.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_instagram_ask_ai_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="567" endOffset="51"/></Target><Target id="@+id/textAITitle" view="TextView"><Expressions/><location startLine="9" startOffset="4" endLine="19" endOffset="51"/></Target><Target id="@+id/recyclerChatMessages" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="22" startOffset="4" endLine="34" endOffset="47"/></Target><Target id="@+id/emptyState" view="LinearLayout"><Expressions/><location startLine="37" startOffset="4" endLine="73" endOffset="18"/></Target><Target id="@+id/cardSuggestedQuestions" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="76" startOffset="4" endLine="146" endOffset="55"/></Target><Target id="@+id/chipGroupSuggestions" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="101" startOffset="12" endLine="144" endOffset="56"/></Target><Target id="@+id/chipBestPosts" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="108" startOffset="16" endLine="115" endOffset="47"/></Target><Target id="@+id/chipHashtags" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="117" startOffset="16" endLine="124" endOffset="47"/></Target><Target id="@+id/chipImprove" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="126" startOffset="16" endLine="133" endOffset="47"/></Target><Target id="@+id/chipContent" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="135" startOffset="16" endLine="142" endOffset="47"/></Target><Target id="@+id/inputCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="149" startOffset="4" endLine="314" endOffset="55"/></Target><Target id="@+id/layoutAttachmentPreview" view="LinearLayout"><Expressions/><location startLine="160" startOffset="8" endLine="196" endOffset="22"/></Target><Target id="@+id/imgAttachmentIcon" view="ImageView"><Expressions/><location startLine="170" startOffset="12" endLine="175" endOffset="48"/></Target><Target id="@+id/textAttachmentName" view="TextView"><Expressions/><location startLine="177" startOffset="12" endLine="186" endOffset="41"/></Target><Target id="@+id/btnRemoveAttachment" view="ImageButton"><Expressions/><location startLine="188" startOffset="12" endLine="194" endOffset="64"/></Target><Target id="@+id/layoutAudioRecording" view="LinearLayout"><Expressions/><location startLine="199" startOffset="8" endLine="257" endOffset="22"/></Target><Target id="@+id/textRecordingDuration" view="TextView"><Expressions/><location startLine="229" startOffset="16" endLine="235" endOffset="52"/></Target><Target id="@+id/btnStopRecording" view="ImageButton"><Expressions/><location startLine="237" startOffset="16" endLine="243" endOffset="65"/></Target><Target id="@+id/progressAudioWave" view="ProgressBar"><Expressions/><location startLine="248" startOffset="12" endLine="255" endOffset="39"/></Target><Target id="@+id/layoutMainInput" view="LinearLayout"><Expressions/><location startLine="260" startOffset="8" endLine="313" endOffset="22"/></Target><Target id="@+id/btnAttachments" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="269" startOffset="12" endLine="284" endOffset="62"/></Target><Target id="@+id/editTextQuestion" view="EditText"><Expressions/><location startLine="286" startOffset="12" endLine="297" endOffset="41"/></Target><Target id="@+id/btnSendQuestion" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="299" startOffset="12" endLine="312" endOffset="41"/></Target><Target id="@+id/cardAttachmentOptions" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="317" startOffset="4" endLine="533" endOffset="55"/></Target><Target id="@+id/cardImageUpload" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="352" startOffset="16" endLine="383" endOffset="67"/></Target><Target id="@+id/cardVoiceRecord" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="386" startOffset="16" endLine="417" endOffset="67"/></Target><Target id="@+id/cardAudioUpload" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="420" startOffset="16" endLine="451" endOffset="67"/></Target><Target id="@+id/cardPDFUpload" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="454" startOffset="16" endLine="485" endOffset="67"/></Target><Target id="@+id/cardURLAnalysis" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="490" startOffset="12" endLine="530" endOffset="63"/></Target><Target id="@+id/loadingOverlay" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="536" startOffset="4" endLine="565" endOffset="55"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="547" startOffset="8" endLine="554" endOffset="55"/></Target></Targets></Layout>