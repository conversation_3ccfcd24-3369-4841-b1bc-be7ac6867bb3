package com.example.allinone.data.local;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class RoomCacheManager_Factory implements Factory<RoomCacheManager> {
  private final Provider<Context> contextProvider;

  public RoomCacheManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public RoomCacheManager get() {
    return newInstance(contextProvider.get());
  }

  public static RoomCacheManager_Factory create(Provider<Context> contextProvider) {
    return new RoomCacheManager_Factory(contextProvider);
  }

  public static RoomCacheManager newInstance(Context context) {
    return new RoomCacheManager(context);
  }
}
