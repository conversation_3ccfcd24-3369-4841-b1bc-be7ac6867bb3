package com.example.allinone.di;

import android.content.Context;
import com.example.allinone.firebase.FirebaseManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class AppModule_ProvideFirebaseManagerFactory implements Factory<FirebaseManager> {
  private final Provider<Context> contextProvider;

  public AppModule_ProvideFirebaseManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public FirebaseManager get() {
    return provideFirebaseManager(contextProvider.get());
  }

  public static AppModule_ProvideFirebaseManagerFactory create(Provider<Context> contextProvider) {
    return new AppModule_ProvideFirebaseManagerFactory(contextProvider);
  }

  public static FirebaseManager provideFirebaseManager(Context context) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideFirebaseManager(context));
  }
}
