<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            android:elevation="4dp"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:weightSum="10"
            android:padding="16dp">

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Title"
                android:background="@android:color/white"
                app:boxBackgroundColor="@android:color/white">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editNoteTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1"
                    android:background="@android:color/white" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- Rich Text Editor Toolbar -->
            <HorizontalScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageButton
                        android:id="@+id/boldButton"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="Bold"
                        android:src="@drawable/ic_format_bold" />

                    <ImageButton
                        android:id="@+id/italicButton"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="Italic"
                        android:src="@drawable/ic_format_italic" />

                    <ImageButton
                        android:id="@+id/underlineButton"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="Underline"
                        android:src="@drawable/ic_format_underline" />

                    <View
                        android:layout_width="1dp"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="8dp"
                        android:background="?android:attr/listDivider" />

                    <ImageButton
                        android:id="@+id/bulletListButton"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="Bullet List"
                        android:src="@drawable/ic_format_list_bulleted" />

                    <ImageButton
                        android:id="@+id/checkboxListButton"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="Checkbox List"
                        android:src="@drawable/ic_checkbox" />

                    <View
                        android:layout_width="1dp"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="8dp"
                        android:background="?android:attr/listDivider" />

                    <ImageButton
                        android:id="@+id/drawingButton"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="@string/create_drawing"
                        android:src="@drawable/ic_draw" />

                    <View
                        android:layout_width="1dp"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="8dp"
                        android:background="?android:attr/listDivider" />

                    <ImageButton
                        android:id="@+id/addImageButton"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="Add Image"
                        android:src="@drawable/ic_image" />
                </LinearLayout>
            </HorizontalScrollView>

            <!-- Rich Text Editor -->
            <jp.wasabeef.richeditor.RichEditor
                android:id="@+id/editNoteContent"
                android:layout_width="match_parent"
                android:layout_height="300dp"
                android:layout_marginTop="8dp"
                android:background="@android:color/transparent"
                android:padding="8dp" />

            <!-- Spacer to push content down
            <Space
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:minHeight="120dp" /> -->

            <!-- Image Attachments -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:orientation="vertical">
                
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Attached Images"
                    android:textStyle="bold"
                    android:textSize="16sp"
                    android:textColor="?attr/colorPrimary"
                    android:textAppearance="?attr/textAppearanceSubtitle1"
                    android:visibility="visible" />
    
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/imagesRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:minHeight="120dp"
                    android:paddingBottom="8dp"
                    android:clipToPadding="false"
                    android:orientation="horizontal"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
            </LinearLayout>

            <!-- Video Attachments -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:orientation="vertical">
                
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Attached Videos"
                    android:textStyle="bold"
                    android:textSize="16sp"
                    android:textColor="?attr/colorPrimary"
                    android:textAppearance="?attr/textAppearanceSubtitle1"
                    android:visibility="visible" />
    
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/videosRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:minHeight="120dp"
                    android:paddingBottom="8dp"
                    android:clipToPadding="false"
                    android:orientation="horizontal"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
            </LinearLayout>
            
            <Button
                android:id="@+id/addAttachmentButton"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:layout_marginBottom="16dp"
                android:layout_gravity="center_horizontal"
                android:text="Add Attachment"
                android:drawableStart="@drawable/ic_add_photo"
                android:drawablePadding="8dp" />
                
            <!-- Voice Notes Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:orientation="vertical">
                
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/voice_notes"
                    android:textStyle="bold"
                    android:textSize="16sp"
                    android:textColor="?attr/colorPrimary"
                    android:textAppearance="?attr/textAppearanceSubtitle1"
                    android:visibility="visible" />
                
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/voiceNotesRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:minHeight="80dp"
                    android:paddingBottom="8dp"
                    android:clipToPadding="false"
                    android:orientation="vertical"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
                
                <!-- Voice Recording Controls -->
                <LinearLayout
                    android:id="@+id/recordingControlsLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center"
                    android:layout_marginTop="8dp">
                    
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/recordButton"
                        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/record"
                        app:icon="@android:drawable/ic_btn_speak_now"
                        android:drawablePadding="8dp" />
                        
                    <TextView
                        android:id="@+id/recordingTimeText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="00:00"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:textSize="18sp"
                        android:visibility="gone" />
                        
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/stopRecordingButton"
                        style="@style/Widget.MaterialComponents.Button"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/stop"
                        app:backgroundTint="@android:color/holo_red_light"
                        android:visibility="gone" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/saveFab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:contentDescription="Save Note"
        android:src="@drawable/ic_save"
        app:backgroundTint="@color/bright_tab_selected" />
</androidx.coordinatorlayout.widget.CoordinatorLayout> 