// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAddProgramBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final MaterialButton addExerciseButton;

  @NonNull
  public final LinearLayout exercisesContainer;

  @NonNull
  public final TextInputEditText programDescriptionInput;

  @NonNull
  public final TextInputEditText programNameInput;

  private DialogAddProgramBinding(@NonNull ScrollView rootView,
      @NonNull MaterialButton addExerciseButton, @NonNull LinearLayout exercisesContainer,
      @NonNull TextInputEditText programDescriptionInput,
      @NonNull TextInputEditText programNameInput) {
    this.rootView = rootView;
    this.addExerciseButton = addExerciseButton;
    this.exercisesContainer = exercisesContainer;
    this.programDescriptionInput = programDescriptionInput;
    this.programNameInput = programNameInput;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAddProgramBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAddProgramBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_add_program, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAddProgramBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.add_exercise_button;
      MaterialButton addExerciseButton = ViewBindings.findChildViewById(rootView, id);
      if (addExerciseButton == null) {
        break missingId;
      }

      id = R.id.exercises_container;
      LinearLayout exercisesContainer = ViewBindings.findChildViewById(rootView, id);
      if (exercisesContainer == null) {
        break missingId;
      }

      id = R.id.program_description_input;
      TextInputEditText programDescriptionInput = ViewBindings.findChildViewById(rootView, id);
      if (programDescriptionInput == null) {
        break missingId;
      }

      id = R.id.program_name_input;
      TextInputEditText programNameInput = ViewBindings.findChildViewById(rootView, id);
      if (programNameInput == null) {
        break missingId;
      }

      return new DialogAddProgramBinding((ScrollView) rootView, addExerciseButton,
          exercisesContainer, programDescriptionInput, programNameInput);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
