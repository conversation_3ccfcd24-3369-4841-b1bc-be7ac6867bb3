package com.example.allinone.feature.transactions.data.repository;

import android.content.Context;
import com.example.allinone.data.local.RoomCacheManager;
import com.example.allinone.firebase.FirebaseManager;
import com.example.allinone.firebase.OfflineQueue;
import com.example.allinone.utils.NetworkUtils;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class TransactionRepositoryImpl_Factory implements Factory<TransactionRepositoryImpl> {
  private final Provider<Context> contextProvider;

  private final Provider<FirebaseManager> firebaseManagerProvider;

  private final Provider<NetworkUtils> networkUtilsProvider;

  private final Provider<RoomCacheManager> roomCacheManagerProvider;

  private final Provider<OfflineQueue> offlineQueueProvider;

  public TransactionRepositoryImpl_Factory(Provider<Context> contextProvider,
      Provider<FirebaseManager> firebaseManagerProvider,
      Provider<NetworkUtils> networkUtilsProvider,
      Provider<RoomCacheManager> roomCacheManagerProvider,
      Provider<OfflineQueue> offlineQueueProvider) {
    this.contextProvider = contextProvider;
    this.firebaseManagerProvider = firebaseManagerProvider;
    this.networkUtilsProvider = networkUtilsProvider;
    this.roomCacheManagerProvider = roomCacheManagerProvider;
    this.offlineQueueProvider = offlineQueueProvider;
  }

  @Override
  public TransactionRepositoryImpl get() {
    return newInstance(contextProvider.get(), firebaseManagerProvider.get(), networkUtilsProvider.get(), roomCacheManagerProvider.get(), offlineQueueProvider.get());
  }

  public static TransactionRepositoryImpl_Factory create(Provider<Context> contextProvider,
      Provider<FirebaseManager> firebaseManagerProvider,
      Provider<NetworkUtils> networkUtilsProvider,
      Provider<RoomCacheManager> roomCacheManagerProvider,
      Provider<OfflineQueue> offlineQueueProvider) {
    return new TransactionRepositoryImpl_Factory(contextProvider, firebaseManagerProvider, networkUtilsProvider, roomCacheManagerProvider, offlineQueueProvider);
  }

  public static TransactionRepositoryImpl newInstance(Context context,
      FirebaseManager firebaseManager, NetworkUtils networkUtils, RoomCacheManager roomCacheManager,
      OfflineQueue offlineQueue) {
    return new TransactionRepositoryImpl(context, firebaseManager, networkUtils, roomCacheManager, offlineQueue);
  }
}
