package com.example.allinone.feature.instagram.domain.usecase;

import com.example.allinone.feature.instagram.domain.repository.InstagramRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class GetInstagramPostsUseCase_Factory implements Factory<GetInstagramPostsUseCase> {
  private final Provider<InstagramRepository> repositoryProvider;

  public GetInstagramPostsUseCase_Factory(Provider<InstagramRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public GetInstagramPostsUseCase get() {
    return newInstance(repositoryProvider.get());
  }

  public static GetInstagramPostsUseCase_Factory create(
      Provider<InstagramRepository> repositoryProvider) {
    return new GetInstagramPostsUseCase_Factory(repositoryProvider);
  }

  public static GetInstagramPostsUseCase newInstance(InstagramRepository repository) {
    return new GetInstagramPostsUseCase(repository);
  }
}
