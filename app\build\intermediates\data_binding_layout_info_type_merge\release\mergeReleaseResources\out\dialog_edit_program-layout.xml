<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_edit_program" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\dialog_edit_program.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/dialog_edit_program_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="66" endOffset="12"/></Target><Target id="@+id/program_name_input" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="19" startOffset="12" endLine="24" endOffset="59"/></Target><Target id="@+id/program_description_input" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="34" startOffset="12" endLine="40" endOffset="59"/></Target><Target id="@+id/exercises_container" view="LinearLayout"><Expressions/><location startLine="50" startOffset="8" endLine="54" endOffset="44"/></Target><Target id="@+id/add_exercise_button" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="56" startOffset="8" endLine="63" endOffset="41"/></Target></Targets></Layout>