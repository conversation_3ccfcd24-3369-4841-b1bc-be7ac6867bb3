<lint-module
    format="1"
    dir="C:\Users\<USER>\Documents\GitHub\AllinOne\app"
    name=":app"
    type="APP"
    maven="AllinOne:app:unspecified"
    agpVersion="8.8.2"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-34\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\35.0.0\core-lambda-stubs.jar"
    javaSourceLevel="17"
    compileTarget="android-34">
  <lintOptions
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
