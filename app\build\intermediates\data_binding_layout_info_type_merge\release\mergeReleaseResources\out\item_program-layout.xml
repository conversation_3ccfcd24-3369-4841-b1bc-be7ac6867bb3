<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_program" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_program.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_program_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="43" endOffset="51"/></Target><Target id="@+id/program_name" view="TextView"><Expressions/><location startLine="16" startOffset="8" endLine="21" endOffset="45"/></Target><Target id="@+id/program_exercise_count" view="TextView"><Expressions/><location startLine="23" startOffset="8" endLine="29" endOffset="38"/></Target><Target id="@+id/program_description" view="TextView"><Expressions/><location startLine="31" startOffset="8" endLine="39" endOffset="40"/></Target></Targets></Layout>