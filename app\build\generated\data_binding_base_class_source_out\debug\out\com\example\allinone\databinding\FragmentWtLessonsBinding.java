// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentWtLessonsBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton addLessonButton;

  @NonNull
  public final ChipGroup daysChipGroup;

  @NonNull
  public final EditText endTimeField;

  @NonNull
  public final Chip fridayChip;

  @NonNull
  public final TextView lessonDaysLabel;

  @NonNull
  public final TextView lessonTimeLabel;

  @NonNull
  public final LinearLayout lessonsContainer;

  @NonNull
  public final TextView lessonsLabel;

  @NonNull
  public final Chip mondayChip;

  @NonNull
  public final TextView noLessonsText;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final Chip saturdayChip;

  @NonNull
  public final EditText startTimeField;

  @NonNull
  public final Chip sundayChip;

  @NonNull
  public final Chip thursdayChip;

  @NonNull
  public final Chip tuesdayChip;

  @NonNull
  public final Chip wednesdayChip;

  private FragmentWtLessonsBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton addLessonButton, @NonNull ChipGroup daysChipGroup,
      @NonNull EditText endTimeField, @NonNull Chip fridayChip, @NonNull TextView lessonDaysLabel,
      @NonNull TextView lessonTimeLabel, @NonNull LinearLayout lessonsContainer,
      @NonNull TextView lessonsLabel, @NonNull Chip mondayChip, @NonNull TextView noLessonsText,
      @NonNull ProgressBar progressBar, @NonNull Chip saturdayChip,
      @NonNull EditText startTimeField, @NonNull Chip sundayChip, @NonNull Chip thursdayChip,
      @NonNull Chip tuesdayChip, @NonNull Chip wednesdayChip) {
    this.rootView = rootView;
    this.addLessonButton = addLessonButton;
    this.daysChipGroup = daysChipGroup;
    this.endTimeField = endTimeField;
    this.fridayChip = fridayChip;
    this.lessonDaysLabel = lessonDaysLabel;
    this.lessonTimeLabel = lessonTimeLabel;
    this.lessonsContainer = lessonsContainer;
    this.lessonsLabel = lessonsLabel;
    this.mondayChip = mondayChip;
    this.noLessonsText = noLessonsText;
    this.progressBar = progressBar;
    this.saturdayChip = saturdayChip;
    this.startTimeField = startTimeField;
    this.sundayChip = sundayChip;
    this.thursdayChip = thursdayChip;
    this.tuesdayChip = tuesdayChip;
    this.wednesdayChip = wednesdayChip;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentWtLessonsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentWtLessonsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_wt_lessons, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentWtLessonsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addLessonButton;
      MaterialButton addLessonButton = ViewBindings.findChildViewById(rootView, id);
      if (addLessonButton == null) {
        break missingId;
      }

      id = R.id.daysChipGroup;
      ChipGroup daysChipGroup = ViewBindings.findChildViewById(rootView, id);
      if (daysChipGroup == null) {
        break missingId;
      }

      id = R.id.endTimeField;
      EditText endTimeField = ViewBindings.findChildViewById(rootView, id);
      if (endTimeField == null) {
        break missingId;
      }

      id = R.id.fridayChip;
      Chip fridayChip = ViewBindings.findChildViewById(rootView, id);
      if (fridayChip == null) {
        break missingId;
      }

      id = R.id.lessonDaysLabel;
      TextView lessonDaysLabel = ViewBindings.findChildViewById(rootView, id);
      if (lessonDaysLabel == null) {
        break missingId;
      }

      id = R.id.lessonTimeLabel;
      TextView lessonTimeLabel = ViewBindings.findChildViewById(rootView, id);
      if (lessonTimeLabel == null) {
        break missingId;
      }

      id = R.id.lessonsContainer;
      LinearLayout lessonsContainer = ViewBindings.findChildViewById(rootView, id);
      if (lessonsContainer == null) {
        break missingId;
      }

      id = R.id.lessonsLabel;
      TextView lessonsLabel = ViewBindings.findChildViewById(rootView, id);
      if (lessonsLabel == null) {
        break missingId;
      }

      id = R.id.mondayChip;
      Chip mondayChip = ViewBindings.findChildViewById(rootView, id);
      if (mondayChip == null) {
        break missingId;
      }

      id = R.id.noLessonsText;
      TextView noLessonsText = ViewBindings.findChildViewById(rootView, id);
      if (noLessonsText == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.saturdayChip;
      Chip saturdayChip = ViewBindings.findChildViewById(rootView, id);
      if (saturdayChip == null) {
        break missingId;
      }

      id = R.id.startTimeField;
      EditText startTimeField = ViewBindings.findChildViewById(rootView, id);
      if (startTimeField == null) {
        break missingId;
      }

      id = R.id.sundayChip;
      Chip sundayChip = ViewBindings.findChildViewById(rootView, id);
      if (sundayChip == null) {
        break missingId;
      }

      id = R.id.thursdayChip;
      Chip thursdayChip = ViewBindings.findChildViewById(rootView, id);
      if (thursdayChip == null) {
        break missingId;
      }

      id = R.id.tuesdayChip;
      Chip tuesdayChip = ViewBindings.findChildViewById(rootView, id);
      if (tuesdayChip == null) {
        break missingId;
      }

      id = R.id.wednesdayChip;
      Chip wednesdayChip = ViewBindings.findChildViewById(rootView, id);
      if (wednesdayChip == null) {
        break missingId;
      }

      return new FragmentWtLessonsBinding((ConstraintLayout) rootView, addLessonButton,
          daysChipGroup, endTimeField, fridayChip, lessonDaysLabel, lessonTimeLabel,
          lessonsContainer, lessonsLabel, mondayChip, noLessonsText, progressBar, saturdayChip,
          startTimeField, sundayChip, thursdayChip, tuesdayChip, wednesdayChip);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
