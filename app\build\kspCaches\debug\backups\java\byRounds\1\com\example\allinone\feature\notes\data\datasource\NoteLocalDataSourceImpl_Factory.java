package com.example.allinone.feature.notes.data.datasource;

import com.example.allinone.data.local.dao.CachedNoteDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NoteLocalDataSourceImpl_Factory implements Factory<NoteLocalDataSourceImpl> {
  private final Provider<CachedNoteDao> noteDaoProvider;

  public NoteLocalDataSourceImpl_Factory(Provider<CachedNoteDao> noteDaoProvider) {
    this.noteDaoProvider = noteDaoProvider;
  }

  @Override
  public NoteLocalDataSourceImpl get() {
    return newInstance(noteDaoProvider.get());
  }

  public static NoteLocalDataSourceImpl_Factory create(Provider<CachedNoteDao> noteDaoProvider) {
    return new NoteLocalDataSourceImpl_Factory(noteDaoProvider);
  }

  public static NoteLocalDataSourceImpl newInstance(CachedNoteDao noteDao) {
    return new NoteLocalDataSourceImpl(noteDao);
  }
}
