<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_wt_registration" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_wt_registration.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_wt_registration_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="101" endOffset="51"/></Target><Target id="@+id/studentPhoto" view="com.google.android.material.imageview.ShapeableImageView"><Expressions/><location startLine="15" startOffset="8" endLine="28" endOffset="65"/></Target><Target id="@+id/studentName" view="TextView"><Expressions/><location startLine="30" startOffset="8" endLine="40" endOffset="39"/></Target><Target id="@+id/shareButton" view="ImageButton"><Expressions/><location startLine="42" startOffset="8" endLine="51" endOffset="55"/></Target><Target id="@+id/startDate" view="TextView"><Expressions/><location startLine="53" startOffset="8" endLine="64" endOffset="45"/></Target><Target id="@+id/endDate" view="TextView"><Expressions/><location startLine="66" startOffset="8" endLine="74" endOffset="43"/></Target><Target id="@+id/amount" view="TextView"><Expressions/><location startLine="76" startOffset="8" endLine="86" endOffset="35"/></Target><Target id="@+id/paymentStatusChip" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="88" startOffset="8" endLine="97" endOffset="31"/></Target></Targets></Layout>