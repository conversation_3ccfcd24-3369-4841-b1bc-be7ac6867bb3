<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_task_group_header" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_task_group_header.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_task_group_header_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="79" endOffset="14"/></Target><Target id="@+id/colorIndicator" view="View"><Expressions/><location startLine="13" startOffset="4" endLine="18" endOffset="46"/></Target><Target id="@+id/groupTitle" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="42" endOffset="46"/></Target><Target id="@+id/taskCount" view="TextView"><Expressions/><location startLine="44" startOffset="12" endLine="51" endOffset="44"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="56" startOffset="8" endLine="65" endOffset="33"/></Target><Target id="@+id/expandIcon" view="ImageView"><Expressions/><location startLine="70" startOffset="4" endLine="77" endOffset="48"/></Target></Targets></Layout>