// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogPostDetailsBinding implements ViewBinding {
  @NonNull
  private final NestedScrollView rootView;

  @NonNull
  public final Button closeButton;

  @NonNull
  public final View divider;

  @NonNull
  public final TextView insightsLabel;

  @NonNull
  public final TextView postCaption;

  @NonNull
  public final TextView postCaptionLabel;

  @NonNull
  public final TextView postDate;

  @NonNull
  public final TextView postDateLabel;

  @NonNull
  public final ImageView postDetailThumbnail;

  @NonNull
  public final TextView postInsights;

  @NonNull
  public final TextView postLink;

  @NonNull
  public final TextView postLinkLabel;

  @NonNull
  public final TextView postType;

  @NonNull
  public final TextView postTypeLabel;

  private DialogPostDetailsBinding(@NonNull NestedScrollView rootView, @NonNull Button closeButton,
      @NonNull View divider, @NonNull TextView insightsLabel, @NonNull TextView postCaption,
      @NonNull TextView postCaptionLabel, @NonNull TextView postDate,
      @NonNull TextView postDateLabel, @NonNull ImageView postDetailThumbnail,
      @NonNull TextView postInsights, @NonNull TextView postLink, @NonNull TextView postLinkLabel,
      @NonNull TextView postType, @NonNull TextView postTypeLabel) {
    this.rootView = rootView;
    this.closeButton = closeButton;
    this.divider = divider;
    this.insightsLabel = insightsLabel;
    this.postCaption = postCaption;
    this.postCaptionLabel = postCaptionLabel;
    this.postDate = postDate;
    this.postDateLabel = postDateLabel;
    this.postDetailThumbnail = postDetailThumbnail;
    this.postInsights = postInsights;
    this.postLink = postLink;
    this.postLinkLabel = postLinkLabel;
    this.postType = postType;
    this.postTypeLabel = postTypeLabel;
  }

  @Override
  @NonNull
  public NestedScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogPostDetailsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogPostDetailsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_post_details, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogPostDetailsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.closeButton;
      Button closeButton = ViewBindings.findChildViewById(rootView, id);
      if (closeButton == null) {
        break missingId;
      }

      id = R.id.divider;
      View divider = ViewBindings.findChildViewById(rootView, id);
      if (divider == null) {
        break missingId;
      }

      id = R.id.insightsLabel;
      TextView insightsLabel = ViewBindings.findChildViewById(rootView, id);
      if (insightsLabel == null) {
        break missingId;
      }

      id = R.id.postCaption;
      TextView postCaption = ViewBindings.findChildViewById(rootView, id);
      if (postCaption == null) {
        break missingId;
      }

      id = R.id.postCaptionLabel;
      TextView postCaptionLabel = ViewBindings.findChildViewById(rootView, id);
      if (postCaptionLabel == null) {
        break missingId;
      }

      id = R.id.postDate;
      TextView postDate = ViewBindings.findChildViewById(rootView, id);
      if (postDate == null) {
        break missingId;
      }

      id = R.id.postDateLabel;
      TextView postDateLabel = ViewBindings.findChildViewById(rootView, id);
      if (postDateLabel == null) {
        break missingId;
      }

      id = R.id.postDetailThumbnail;
      ImageView postDetailThumbnail = ViewBindings.findChildViewById(rootView, id);
      if (postDetailThumbnail == null) {
        break missingId;
      }

      id = R.id.postInsights;
      TextView postInsights = ViewBindings.findChildViewById(rootView, id);
      if (postInsights == null) {
        break missingId;
      }

      id = R.id.postLink;
      TextView postLink = ViewBindings.findChildViewById(rootView, id);
      if (postLink == null) {
        break missingId;
      }

      id = R.id.postLinkLabel;
      TextView postLinkLabel = ViewBindings.findChildViewById(rootView, id);
      if (postLinkLabel == null) {
        break missingId;
      }

      id = R.id.postType;
      TextView postType = ViewBindings.findChildViewById(rootView, id);
      if (postType == null) {
        break missingId;
      }

      id = R.id.postTypeLabel;
      TextView postTypeLabel = ViewBindings.findChildViewById(rootView, id);
      if (postTypeLabel == null) {
        break missingId;
      }

      return new DialogPostDetailsBinding((NestedScrollView) rootView, closeButton, divider,
          insightsLabel, postCaption, postCaptionLabel, postDate, postDateLabel,
          postDetailThumbnail, postInsights, postLink, postLinkLabel, postType, postTypeLabel);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
