// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAddTaskBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton clearDueDateButton;

  @NonNull
  public final TextView dueDateLabel;

  @NonNull
  public final TextView dueDateText;

  @NonNull
  public final Spinner groupSpinner;

  @NonNull
  public final MaterialButton pickDueDateButton;

  @NonNull
  public final TextInputEditText taskDescriptionInput;

  @NonNull
  public final TextInputEditText taskNameInput;

  private DialogAddTaskBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton clearDueDateButton, @NonNull TextView dueDateLabel,
      @NonNull TextView dueDateText, @NonNull Spinner groupSpinner,
      @NonNull MaterialButton pickDueDateButton, @NonNull TextInputEditText taskDescriptionInput,
      @NonNull TextInputEditText taskNameInput) {
    this.rootView = rootView;
    this.clearDueDateButton = clearDueDateButton;
    this.dueDateLabel = dueDateLabel;
    this.dueDateText = dueDateText;
    this.groupSpinner = groupSpinner;
    this.pickDueDateButton = pickDueDateButton;
    this.taskDescriptionInput = taskDescriptionInput;
    this.taskNameInput = taskNameInput;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAddTaskBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAddTaskBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_add_task, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAddTaskBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.clearDueDateButton;
      MaterialButton clearDueDateButton = ViewBindings.findChildViewById(rootView, id);
      if (clearDueDateButton == null) {
        break missingId;
      }

      id = R.id.dueDateLabel;
      TextView dueDateLabel = ViewBindings.findChildViewById(rootView, id);
      if (dueDateLabel == null) {
        break missingId;
      }

      id = R.id.dueDateText;
      TextView dueDateText = ViewBindings.findChildViewById(rootView, id);
      if (dueDateText == null) {
        break missingId;
      }

      id = R.id.groupSpinner;
      Spinner groupSpinner = ViewBindings.findChildViewById(rootView, id);
      if (groupSpinner == null) {
        break missingId;
      }

      id = R.id.pickDueDateButton;
      MaterialButton pickDueDateButton = ViewBindings.findChildViewById(rootView, id);
      if (pickDueDateButton == null) {
        break missingId;
      }

      id = R.id.taskDescriptionInput;
      TextInputEditText taskDescriptionInput = ViewBindings.findChildViewById(rootView, id);
      if (taskDescriptionInput == null) {
        break missingId;
      }

      id = R.id.taskNameInput;
      TextInputEditText taskNameInput = ViewBindings.findChildViewById(rootView, id);
      if (taskNameInput == null) {
        break missingId;
      }

      return new DialogAddTaskBinding((LinearLayout) rootView, clearDueDateButton, dueDateLabel,
          dueDateText, groupSpinner, pickDueDateButton, taskDescriptionInput, taskNameInput);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
