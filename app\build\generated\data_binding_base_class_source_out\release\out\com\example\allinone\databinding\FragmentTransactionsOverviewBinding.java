// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.github.mikephil.charting.charts.PieChart;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentTransactionsOverviewBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final MaterialButton addExpenseButton;

  @NonNull
  public final MaterialButton addIncomeButton;

  @NonNull
  public final TextInputEditText amountInput;

  @NonNull
  public final TextInputLayout amountLayout;

  @NonNull
  public final TextView balanceText;

  @NonNull
  public final TextInputEditText descriptionInput;

  @NonNull
  public final TextInputLayout descriptionLayout;

  @NonNull
  public final TextView emptyTransactionsText;

  @NonNull
  public final TextView expenseText;

  @NonNull
  public final TextView incomeText;

  @NonNull
  public final MaterialButton nextPageButton;

  @NonNull
  public final TextView pageIndicator;

  @NonNull
  public final LinearLayout paginationControls;

  @NonNull
  public final PieChart pieChart;

  @NonNull
  public final MaterialButton prevPageButton;

  @NonNull
  public final MaterialCardView transactionsCard;

  @NonNull
  public final RecyclerView transactionsRecyclerView;

  @NonNull
  public final AutoCompleteTextView typeDropdown;

  @NonNull
  public final TextInputLayout typeLayout;

  private FragmentTransactionsOverviewBinding(@NonNull ScrollView rootView,
      @NonNull MaterialButton addExpenseButton, @NonNull MaterialButton addIncomeButton,
      @NonNull TextInputEditText amountInput, @NonNull TextInputLayout amountLayout,
      @NonNull TextView balanceText, @NonNull TextInputEditText descriptionInput,
      @NonNull TextInputLayout descriptionLayout, @NonNull TextView emptyTransactionsText,
      @NonNull TextView expenseText, @NonNull TextView incomeText,
      @NonNull MaterialButton nextPageButton, @NonNull TextView pageIndicator,
      @NonNull LinearLayout paginationControls, @NonNull PieChart pieChart,
      @NonNull MaterialButton prevPageButton, @NonNull MaterialCardView transactionsCard,
      @NonNull RecyclerView transactionsRecyclerView, @NonNull AutoCompleteTextView typeDropdown,
      @NonNull TextInputLayout typeLayout) {
    this.rootView = rootView;
    this.addExpenseButton = addExpenseButton;
    this.addIncomeButton = addIncomeButton;
    this.amountInput = amountInput;
    this.amountLayout = amountLayout;
    this.balanceText = balanceText;
    this.descriptionInput = descriptionInput;
    this.descriptionLayout = descriptionLayout;
    this.emptyTransactionsText = emptyTransactionsText;
    this.expenseText = expenseText;
    this.incomeText = incomeText;
    this.nextPageButton = nextPageButton;
    this.pageIndicator = pageIndicator;
    this.paginationControls = paginationControls;
    this.pieChart = pieChart;
    this.prevPageButton = prevPageButton;
    this.transactionsCard = transactionsCard;
    this.transactionsRecyclerView = transactionsRecyclerView;
    this.typeDropdown = typeDropdown;
    this.typeLayout = typeLayout;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentTransactionsOverviewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentTransactionsOverviewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_transactions_overview, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentTransactionsOverviewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addExpenseButton;
      MaterialButton addExpenseButton = ViewBindings.findChildViewById(rootView, id);
      if (addExpenseButton == null) {
        break missingId;
      }

      id = R.id.addIncomeButton;
      MaterialButton addIncomeButton = ViewBindings.findChildViewById(rootView, id);
      if (addIncomeButton == null) {
        break missingId;
      }

      id = R.id.amountInput;
      TextInputEditText amountInput = ViewBindings.findChildViewById(rootView, id);
      if (amountInput == null) {
        break missingId;
      }

      id = R.id.amountLayout;
      TextInputLayout amountLayout = ViewBindings.findChildViewById(rootView, id);
      if (amountLayout == null) {
        break missingId;
      }

      id = R.id.balanceText;
      TextView balanceText = ViewBindings.findChildViewById(rootView, id);
      if (balanceText == null) {
        break missingId;
      }

      id = R.id.descriptionInput;
      TextInputEditText descriptionInput = ViewBindings.findChildViewById(rootView, id);
      if (descriptionInput == null) {
        break missingId;
      }

      id = R.id.descriptionLayout;
      TextInputLayout descriptionLayout = ViewBindings.findChildViewById(rootView, id);
      if (descriptionLayout == null) {
        break missingId;
      }

      id = R.id.emptyTransactionsText;
      TextView emptyTransactionsText = ViewBindings.findChildViewById(rootView, id);
      if (emptyTransactionsText == null) {
        break missingId;
      }

      id = R.id.expenseText;
      TextView expenseText = ViewBindings.findChildViewById(rootView, id);
      if (expenseText == null) {
        break missingId;
      }

      id = R.id.incomeText;
      TextView incomeText = ViewBindings.findChildViewById(rootView, id);
      if (incomeText == null) {
        break missingId;
      }

      id = R.id.nextPageButton;
      MaterialButton nextPageButton = ViewBindings.findChildViewById(rootView, id);
      if (nextPageButton == null) {
        break missingId;
      }

      id = R.id.pageIndicator;
      TextView pageIndicator = ViewBindings.findChildViewById(rootView, id);
      if (pageIndicator == null) {
        break missingId;
      }

      id = R.id.paginationControls;
      LinearLayout paginationControls = ViewBindings.findChildViewById(rootView, id);
      if (paginationControls == null) {
        break missingId;
      }

      id = R.id.pieChart;
      PieChart pieChart = ViewBindings.findChildViewById(rootView, id);
      if (pieChart == null) {
        break missingId;
      }

      id = R.id.prevPageButton;
      MaterialButton prevPageButton = ViewBindings.findChildViewById(rootView, id);
      if (prevPageButton == null) {
        break missingId;
      }

      id = R.id.transactionsCard;
      MaterialCardView transactionsCard = ViewBindings.findChildViewById(rootView, id);
      if (transactionsCard == null) {
        break missingId;
      }

      id = R.id.transactionsRecyclerView;
      RecyclerView transactionsRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (transactionsRecyclerView == null) {
        break missingId;
      }

      id = R.id.typeDropdown;
      AutoCompleteTextView typeDropdown = ViewBindings.findChildViewById(rootView, id);
      if (typeDropdown == null) {
        break missingId;
      }

      id = R.id.typeLayout;
      TextInputLayout typeLayout = ViewBindings.findChildViewById(rootView, id);
      if (typeLayout == null) {
        break missingId;
      }

      return new FragmentTransactionsOverviewBinding((ScrollView) rootView, addExpenseButton,
          addIncomeButton, amountInput, amountLayout, balanceText, descriptionInput,
          descriptionLayout, emptyTransactionsText, expenseText, incomeText, nextPageButton,
          pageIndicator, paginationControls, pieChart, prevPageButton, transactionsCard,
          transactionsRecyclerView, typeDropdown, typeLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
