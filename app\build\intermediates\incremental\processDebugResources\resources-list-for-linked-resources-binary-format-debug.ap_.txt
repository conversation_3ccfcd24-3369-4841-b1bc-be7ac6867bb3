C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\color-night_dialog_action_button_color.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\color-night_dialog_delete_button_color.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\color-night_text_input_box_stroke.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\color-night_text_input_text_color.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\color_bottom_nav_item_color.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\color_bottom_nav_item_color_light.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\color_chip_background_selector.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\color_dialog_action_button_color.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\color_dialog_delete_button_color.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\color_drawer_item_color.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\color_text_input_box_stroke.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\color_text_input_text_color.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\color_wt_bottom_nav_item_color.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable-night_bg_category_chip.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable-night_dialog_rounded_bg.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable-v24_ic_launcher_foreground.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_bg_category_chip.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_bg_current_day.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_bg_day_with_event.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_bg_day_with_events.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_bg_day_with_lesson.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_bg_day_with_registration.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_bg_selected_day.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_bg_tag_blue.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_bg_tag_green.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_bg_tag_red.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_border_background.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_circle_background.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_circle_background_red.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_circle_shape.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_completed_exercise_background.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_default_profile.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_dialog_rounded_bg.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_error_image.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_fully_uncompleted_exercise_background.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_add.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_add_photo.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_attach_image.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_back.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_backup.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_calendar.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_call.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_category_all.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_category_bills.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_category_food.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_category_game.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_category_general.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_category_investment.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_category_salary.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_category_shopping.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_category_sports.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_category_transport.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_category_wing_tzun.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_checkbox.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_chevron_left.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_chevron_right.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_clear_data.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_cleardata.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_close.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_code.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_dashboard.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_database.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_delete.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_draw.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_edit.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_empty_state.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_error.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_exercise.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_expand_less.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_expand_more.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_expense.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_file.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_fitness.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_folder.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_format_bold.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_format_italic.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_format_list_bulleted.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_format_list_numbered.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_format_text.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_format_underline.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_graduation.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_history.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_home.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_image.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_income.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_instagram.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_instagram_posts.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_investment.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_investments.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_launcher_background.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_lessons.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_log.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_menu.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_no_registrations.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_no_students.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_note.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_notes.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_notification.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_pause.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_play.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_play_circle.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_program.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_registration.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_remove.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_reports.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_save.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_search_white.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_share.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_stats.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_stop.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_student.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_students.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_tasks.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_transactions.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_video_error.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_video_placeholder.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_view_list.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_whatsapp.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_wt.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_wt_registers.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_placeholder_image.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_rounded_background.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_rounded_corner_bg.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_selected_circle_shape.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_simple_text_splash.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_splash_layout_drawable.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_transparent.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_uncompleted_exercise_background.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\font_opensans.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\font_opensans_bold.ttf.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\font_opensans_regular.ttf.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout-night-v8_activity_backup.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout-night-v8_activity_edit_note.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout-night-v8_activity_main.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout-night-v8_dialog_student_details.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout-night-v8_fragment_history.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout-night-v8_fragment_wt_registry.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout-night-v8_item_backup.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout-night-v8_item_history.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_backup.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_main.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_add_event.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_add_program.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_add_student.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_add_task.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_edit_investment.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_edit_lesson.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_edit_program.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_edit_seminar.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_edit_wt_student.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_expense_investment.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_fullscreen_image.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_futures_tp_sl.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_income_investment.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_loading.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_pin_input.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_post_details.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_profit_loss.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_program_details.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_progress.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_student_details.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_task_group.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_workout_details.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dropdown_item.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_futures.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_futures_tab.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_home.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_investments.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_investments_tab.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_transaction_report.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_transactions_overview.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_wt_lessons.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_wt_register.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_wt_registry.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_wt_seminars.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_wt_students.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_add_exercise.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_backup.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_binance_position.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_category_dropdown.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_category_spending.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_category_summary.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_chat_ai.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_chat_source.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_chat_user.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_file_structure.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_fullscreen_image.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_futures_position.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_investment.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_investment_dropdown.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_investment_image.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_investment_selection.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_lesson.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_note.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_note_image.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_note_video.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_seminar.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_transaction.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_transaction_report.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_voice_note.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_wt_event.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_wt_registration.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_wt_student.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_layout_page_header.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_nav_header.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_offline_status_view.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_pie_chart_tooltip.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_splash_text_layout.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_theme_switch_layout.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_bottom_nav_menu.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_drawer_menu.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_instagram_bottom_nav_menu.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_menu_edit_note.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_menu_group_options.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_menu_task_options.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_menu_tasks.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_search_history.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_search_notes.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_search_register.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_search_students.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_workout_bottom_nav_menu.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_wt_bottom_nav_menu.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_wt_registration_context_menu.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_wt_student_context_menu.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-anydpi-v26_ic_launcher.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-anydpi-v26_ic_launcher_round.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-anydpi-v33_ic_launcher.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-hdpi_ic_launcher.webp.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-hdpi_ic_launcher_round.webp.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-mdpi_ic_launcher.webp.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-mdpi_ic_launcher_round.webp.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-xhdpi_ic_launcher.webp.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-xhdpi_ic_launcher_round.webp.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-xxhdpi_ic_launcher.webp.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-xxhdpi_ic_launcher_round.webp.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-xxxhdpi_ic_launcher.webp.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-xxxhdpi_ic_launcher_round.webp.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-af_values-af.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-am_values-am.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ar_values-ar.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-as_values-as.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-az_values-az.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-b+es+419_values-b+es+419.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-b+sr+Latn_values-b+sr+Latn.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-be_values-be.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-bg_values-bg.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-bn_values-bn.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-bs_values-bs.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ca_values-ca.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-cs_values-cs.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-da_values-da.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-de_values-de.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-el_values-el.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-en-rAU_values-en-rAU.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-en-rCA_values-en-rCA.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-en-rGB_values-en-rGB.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-en-rIN_values-en-rIN.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-en-rXC_values-en-rXC.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-es-rUS_values-es-rUS.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-es_values-es.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-et_values-et.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-eu_values-eu.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-fa_values-fa.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-fi_values-fi.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-fr-rCA_values-fr-rCA.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-fr_values-fr.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-gl_values-gl.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-gu_values-gu.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-h320dp-port-v13_values-h320dp-port-v13.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-h360dp-land-v13_values-h360dp-land-v13.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-h480dp-land-v13_values-h480dp-land-v13.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-h550dp-port-v13_values-h550dp-port-v13.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-h720dp-v13_values-h720dp-v13.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-hdpi-v4_values-hdpi-v4.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-hi_values-hi.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-hr_values-hr.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-hu_values-hu.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-hy_values-hy.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-in_values-in.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-is_values-is.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-it_values-it.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-iw_values-iw.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ja_values-ja.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ka_values-ka.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-kk_values-kk.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-km_values-km.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-kn_values-kn.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ko_values-ko.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ky_values-ky.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-land_values-land.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-large-v4_values-large-v4.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ldltr-v21_values-ldltr-v21.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ldrtl-v17_values-ldrtl-v17.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-lo_values-lo.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-lt_values-lt.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-lv_values-lv.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-mk_values-mk.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ml_values-ml.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-mn_values-mn.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-mr_values-mr.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ms_values-ms.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-my_values-my.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-nb_values-nb.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ne_values-ne.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-night-v8_values-night-v8.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-nl_values-nl.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-or_values-or.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-pa_values-pa.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-pl_values-pl.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-port_values-port.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-pt-rBR_values-pt-rBR.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-pt-rPT_values-pt-rPT.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-pt_values-pt.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ro_values-ro.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ru_values-ru.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-si_values-si.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sk_values-sk.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sl_values-sl.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-small-v4_values-small-v4.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sq_values-sq.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sr_values-sr.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sv_values-sv.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sw600dp-v13_values-sw600dp-v13.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sw_values-sw.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ta_values-ta.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-te_values-te.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-th_values-th.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-tl_values-tl.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-tr_values-tr.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-uk_values-uk.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ur_values-ur.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-uz_values-uz.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v16_values-v16.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v17_values-v17.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v18_values-v18.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v21_values-v21.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v22_values-v22.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v23_values-v23.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v24_values-v24.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v25_values-v25.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v26_values-v26.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v27_values-v27.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v28_values-v28.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v29_values-v29.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v31_values-v31.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v34_values-v34.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-vi_values-vi.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-w320dp-land-v13_values-w320dp-land-v13.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-w360dp-port-v13_values-w360dp-port-v13.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-w400dp-port-v13_values-w400dp-port-v13.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-w600dp-land-v13_values-w600dp-land-v13.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-watch-v20_values-watch-v20.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-watch-v21_values-watch-v21.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-xlarge-v4_values-xlarge-v4.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-zh-rCN_values-zh-rCN.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-zh-rHK_values-zh-rHK.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-zh-rTW_values-zh-rTW.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values-zu_values-zu.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\values_values.arsc.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\xml_backup_rules.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\xml_data_extraction_rules.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\xml_file_paths.xml.flat C:\Users\<USER>\Documents\GitHub\AllinOne\app\build\intermediates\merged_res\debug\mergeDebugResources\xml_network_security_config.xml.flat 