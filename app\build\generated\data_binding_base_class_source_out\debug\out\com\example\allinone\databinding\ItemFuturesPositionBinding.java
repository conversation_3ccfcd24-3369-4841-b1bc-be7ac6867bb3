// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemFuturesPositionBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final TextView entryPriceLabelText;

  @NonNull
  public final TextView entryPriceText;

  @NonNull
  public final TextView leverageLabelText;

  @NonNull
  public final TextView leverageText;

  @NonNull
  public final TextView liquidationPriceLabelText;

  @NonNull
  public final TextView liquidationPriceText;

  @NonNull
  public final TextView marginTypeLabelText;

  @NonNull
  public final TextView marginTypeText;

  @NonNull
  public final TextView markPriceLabelText;

  @NonNull
  public final TextView markPriceText;

  @NonNull
  public final TextView pnlText;

  @NonNull
  public final TextView positionAmtLabelText;

  @NonNull
  public final TextView positionAmtText;

  @NonNull
  public final TextView positionSideText;

  @NonNull
  public final TextView symbolText;

  @NonNull
  public final TextView tpSlText;

  private ItemFuturesPositionBinding(@NonNull CardView rootView,
      @NonNull TextView entryPriceLabelText, @NonNull TextView entryPriceText,
      @NonNull TextView leverageLabelText, @NonNull TextView leverageText,
      @NonNull TextView liquidationPriceLabelText, @NonNull TextView liquidationPriceText,
      @NonNull TextView marginTypeLabelText, @NonNull TextView marginTypeText,
      @NonNull TextView markPriceLabelText, @NonNull TextView markPriceText,
      @NonNull TextView pnlText, @NonNull TextView positionAmtLabelText,
      @NonNull TextView positionAmtText, @NonNull TextView positionSideText,
      @NonNull TextView symbolText, @NonNull TextView tpSlText) {
    this.rootView = rootView;
    this.entryPriceLabelText = entryPriceLabelText;
    this.entryPriceText = entryPriceText;
    this.leverageLabelText = leverageLabelText;
    this.leverageText = leverageText;
    this.liquidationPriceLabelText = liquidationPriceLabelText;
    this.liquidationPriceText = liquidationPriceText;
    this.marginTypeLabelText = marginTypeLabelText;
    this.marginTypeText = marginTypeText;
    this.markPriceLabelText = markPriceLabelText;
    this.markPriceText = markPriceText;
    this.pnlText = pnlText;
    this.positionAmtLabelText = positionAmtLabelText;
    this.positionAmtText = positionAmtText;
    this.positionSideText = positionSideText;
    this.symbolText = symbolText;
    this.tpSlText = tpSlText;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemFuturesPositionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemFuturesPositionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_futures_position, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemFuturesPositionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.entryPriceLabelText;
      TextView entryPriceLabelText = ViewBindings.findChildViewById(rootView, id);
      if (entryPriceLabelText == null) {
        break missingId;
      }

      id = R.id.entryPriceText;
      TextView entryPriceText = ViewBindings.findChildViewById(rootView, id);
      if (entryPriceText == null) {
        break missingId;
      }

      id = R.id.leverageLabelText;
      TextView leverageLabelText = ViewBindings.findChildViewById(rootView, id);
      if (leverageLabelText == null) {
        break missingId;
      }

      id = R.id.leverageText;
      TextView leverageText = ViewBindings.findChildViewById(rootView, id);
      if (leverageText == null) {
        break missingId;
      }

      id = R.id.liquidationPriceLabelText;
      TextView liquidationPriceLabelText = ViewBindings.findChildViewById(rootView, id);
      if (liquidationPriceLabelText == null) {
        break missingId;
      }

      id = R.id.liquidationPriceText;
      TextView liquidationPriceText = ViewBindings.findChildViewById(rootView, id);
      if (liquidationPriceText == null) {
        break missingId;
      }

      id = R.id.marginTypeLabelText;
      TextView marginTypeLabelText = ViewBindings.findChildViewById(rootView, id);
      if (marginTypeLabelText == null) {
        break missingId;
      }

      id = R.id.marginTypeText;
      TextView marginTypeText = ViewBindings.findChildViewById(rootView, id);
      if (marginTypeText == null) {
        break missingId;
      }

      id = R.id.markPriceLabelText;
      TextView markPriceLabelText = ViewBindings.findChildViewById(rootView, id);
      if (markPriceLabelText == null) {
        break missingId;
      }

      id = R.id.markPriceText;
      TextView markPriceText = ViewBindings.findChildViewById(rootView, id);
      if (markPriceText == null) {
        break missingId;
      }

      id = R.id.pnlText;
      TextView pnlText = ViewBindings.findChildViewById(rootView, id);
      if (pnlText == null) {
        break missingId;
      }

      id = R.id.positionAmtLabelText;
      TextView positionAmtLabelText = ViewBindings.findChildViewById(rootView, id);
      if (positionAmtLabelText == null) {
        break missingId;
      }

      id = R.id.positionAmtText;
      TextView positionAmtText = ViewBindings.findChildViewById(rootView, id);
      if (positionAmtText == null) {
        break missingId;
      }

      id = R.id.positionSideText;
      TextView positionSideText = ViewBindings.findChildViewById(rootView, id);
      if (positionSideText == null) {
        break missingId;
      }

      id = R.id.symbolText;
      TextView symbolText = ViewBindings.findChildViewById(rootView, id);
      if (symbolText == null) {
        break missingId;
      }

      id = R.id.tpSlText;
      TextView tpSlText = ViewBindings.findChildViewById(rootView, id);
      if (tpSlText == null) {
        break missingId;
      }

      return new ItemFuturesPositionBinding((CardView) rootView, entryPriceLabelText,
          entryPriceText, leverageLabelText, leverageText, liquidationPriceLabelText,
          liquidationPriceText, marginTypeLabelText, marginTypeText, markPriceLabelText,
          markPriceText, pnlText, positionAmtLabelText, positionAmtText, positionSideText,
          symbolText, tpSlText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
