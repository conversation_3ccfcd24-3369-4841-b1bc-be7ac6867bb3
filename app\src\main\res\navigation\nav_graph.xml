<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/homeFragment">

    <fragment
        android:id="@+id/homeFragment"
        android:name="com.example.allinone.ui.HomeFragment"
        android:label="Home" />

    <fragment
        android:id="@+id/nav_investments"
        android:name="com.example.allinone.ui.InvestmentsFragment"
        android:label="Investments" />

    <fragment
        android:id="@+id/nav_wt_registry"
        android:name="com.example.allinone.ui.wt.WTRegistryFragment"
        android:label="WT Registry" />

    <fragment
        android:id="@+id/nav_notes"
        android:name="com.example.allinone.ui.NotesFragment"
        android:label="Notes"
        tools:layout="@layout/fragment_notes" />

    <fragment
        android:id="@+id/nav_history"
        android:name="com.example.allinone.ui.HistoryFragment"
        android:label="History"
        tools:layout="@layout/fragment_history" />

    <fragment
        android:id="@+id/nav_calendar"
        android:name="com.example.allinone.ui.CalendarFragment"
        android:label="Calendar"
        tools:layout="@layout/fragment_calendar" />

    <fragment
        android:id="@+id/nav_transaction_report"
        android:name="com.example.allinone.ui.TransactionReportFragment"
        android:label="Financial Report"
        tools:layout="@layout/fragment_transaction_report" />

    <fragment
        android:id="@+id/nav_workout"
        android:name="com.example.allinone.ui.WorkoutFragment"
        android:label="Workout"
        tools:layout="@layout/fragment_workout" />

    <fragment
        android:id="@+id/nav_instagram_business"
        android:name="com.example.allinone.ui.InstagramBusinessFragment"
        android:label="Instagram Business"
        tools:layout="@layout/fragment_instagram_business" />

    <fragment
        android:id="@+id/nav_error_logs"
        android:name="com.example.allinone.ui.LogErrorsFragment"
        android:label="Error Logs"
        tools:layout="@layout/fragment_log_errors" />

    <fragment
        android:id="@+id/nav_database_management"
        android:name="com.example.allinone.ui.DatabaseManagementFragment"
        android:label="Database Management"
        tools:layout="@layout/fragment_database_management" />

</navigation> 