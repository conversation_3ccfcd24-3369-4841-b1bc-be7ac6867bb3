// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogWorkoutDetailsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final RecyclerView exercisesRecyclerView;

  @NonNull
  public final TextView workoutDateText;

  @NonNull
  public final TextView workoutDurationText;

  @NonNull
  public final TextView workoutNameText;

  private DialogWorkoutDetailsBinding(@NonNull LinearLayout rootView,
      @NonNull RecyclerView exercisesRecyclerView, @NonNull TextView workoutDateText,
      @NonNull TextView workoutDurationText, @NonNull TextView workoutNameText) {
    this.rootView = rootView;
    this.exercisesRecyclerView = exercisesRecyclerView;
    this.workoutDateText = workoutDateText;
    this.workoutDurationText = workoutDurationText;
    this.workoutNameText = workoutNameText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogWorkoutDetailsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogWorkoutDetailsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_workout_details, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogWorkoutDetailsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.exercises_recycler_view;
      RecyclerView exercisesRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (exercisesRecyclerView == null) {
        break missingId;
      }

      id = R.id.workout_date_text;
      TextView workoutDateText = ViewBindings.findChildViewById(rootView, id);
      if (workoutDateText == null) {
        break missingId;
      }

      id = R.id.workout_duration_text;
      TextView workoutDurationText = ViewBindings.findChildViewById(rootView, id);
      if (workoutDurationText == null) {
        break missingId;
      }

      id = R.id.workout_name_text;
      TextView workoutNameText = ViewBindings.findChildViewById(rootView, id);
      if (workoutNameText == null) {
        break missingId;
      }

      return new DialogWorkoutDetailsBinding((LinearLayout) rootView, exercisesRecyclerView,
          workoutDateText, workoutDurationText, workoutNameText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
