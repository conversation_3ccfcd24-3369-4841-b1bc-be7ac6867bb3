<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_futures" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\fragment_futures.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_futures_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="38" endOffset="51"/></Target><Target id="@+id/futuresTabLayout" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="7" startOffset="4" endLine="27" endOffset="48"/></Target><Target id="@+id/futuresViewPager" view="androidx.viewpager2.widget.ViewPager2"><Expressions/><location startLine="29" startOffset="4" endLine="36" endOffset="69"/></Target></Targets></Layout>