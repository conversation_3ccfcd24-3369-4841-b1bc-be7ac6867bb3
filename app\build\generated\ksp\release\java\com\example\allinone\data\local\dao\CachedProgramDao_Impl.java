package com.example.allinone.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.allinone.data.local.entities.CachedProgramEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class CachedProgramDao_Impl implements CachedProgramDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<CachedProgramEntity> __insertionAdapterOfCachedProgramEntity;

  private final EntityDeletionOrUpdateAdapter<CachedProgramEntity> __deletionAdapterOfCachedProgramEntity;

  private final EntityDeletionOrUpdateAdapter<CachedProgramEntity> __updateAdapterOfCachedProgramEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteProgramById;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllPrograms;

  private final SharedSQLiteStatement __preparedStmtOfDeleteExpiredPrograms;

  public CachedProgramDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfCachedProgramEntity = new EntityInsertionAdapter<CachedProgramEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `cached_programs` (`id`,`name`,`description`,`exercisesJson`,`createdDate`,`lastModifiedDate`,`cachedAt`) VALUES (?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CachedProgramEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindString(2, entity.getName());
        if (entity.getDescription() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getDescription());
        }
        statement.bindString(4, entity.getExercisesJson());
        statement.bindLong(5, entity.getCreatedDate());
        statement.bindLong(6, entity.getLastModifiedDate());
        statement.bindLong(7, entity.getCachedAt());
      }
    };
    this.__deletionAdapterOfCachedProgramEntity = new EntityDeletionOrUpdateAdapter<CachedProgramEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `cached_programs` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CachedProgramEntity entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfCachedProgramEntity = new EntityDeletionOrUpdateAdapter<CachedProgramEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `cached_programs` SET `id` = ?,`name` = ?,`description` = ?,`exercisesJson` = ?,`createdDate` = ?,`lastModifiedDate` = ?,`cachedAt` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CachedProgramEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindString(2, entity.getName());
        if (entity.getDescription() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getDescription());
        }
        statement.bindString(4, entity.getExercisesJson());
        statement.bindLong(5, entity.getCreatedDate());
        statement.bindLong(6, entity.getLastModifiedDate());
        statement.bindLong(7, entity.getCachedAt());
        statement.bindLong(8, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteProgramById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM cached_programs WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllPrograms = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM cached_programs";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteExpiredPrograms = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM cached_programs WHERE cachedAt < ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertProgram(final CachedProgramEntity program,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfCachedProgramEntity.insert(program);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertAllPrograms(final List<CachedProgramEntity> programs,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfCachedProgramEntity.insert(programs);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteProgram(final CachedProgramEntity program,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfCachedProgramEntity.handle(program);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateProgram(final CachedProgramEntity program,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfCachedProgramEntity.handle(program);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteProgramById(final long id, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteProgramById.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteProgramById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllPrograms(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllPrograms.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllPrograms.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteExpiredPrograms(final long expiredTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteExpiredPrograms.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, expiredTime);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteExpiredPrograms.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<CachedProgramEntity>> getAllPrograms() {
    final String _sql = "SELECT `cached_programs`.`id` AS `id`, `cached_programs`.`name` AS `name`, `cached_programs`.`description` AS `description`, `cached_programs`.`exercisesJson` AS `exercisesJson`, `cached_programs`.`createdDate` AS `createdDate`, `cached_programs`.`lastModifiedDate` AS `lastModifiedDate`, `cached_programs`.`cachedAt` AS `cachedAt` FROM cached_programs ORDER BY name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cached_programs"}, new Callable<List<CachedProgramEntity>>() {
      @Override
      @NonNull
      public List<CachedProgramEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfName = 1;
          final int _cursorIndexOfDescription = 2;
          final int _cursorIndexOfExercisesJson = 3;
          final int _cursorIndexOfCreatedDate = 4;
          final int _cursorIndexOfLastModifiedDate = 5;
          final int _cursorIndexOfCachedAt = 6;
          final List<CachedProgramEntity> _result = new ArrayList<CachedProgramEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CachedProgramEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpExercisesJson;
            _tmpExercisesJson = _cursor.getString(_cursorIndexOfExercisesJson);
            final long _tmpCreatedDate;
            _tmpCreatedDate = _cursor.getLong(_cursorIndexOfCreatedDate);
            final long _tmpLastModifiedDate;
            _tmpLastModifiedDate = _cursor.getLong(_cursorIndexOfLastModifiedDate);
            final long _tmpCachedAt;
            _tmpCachedAt = _cursor.getLong(_cursorIndexOfCachedAt);
            _item = new CachedProgramEntity(_tmpId,_tmpName,_tmpDescription,_tmpExercisesJson,_tmpCreatedDate,_tmpLastModifiedDate,_tmpCachedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getProgramById(final long id,
      final Continuation<? super CachedProgramEntity> $completion) {
    final String _sql = "SELECT * FROM cached_programs WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<CachedProgramEntity>() {
      @Override
      @Nullable
      public CachedProgramEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExercisesJson = CursorUtil.getColumnIndexOrThrow(_cursor, "exercisesJson");
          final int _cursorIndexOfCreatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "createdDate");
          final int _cursorIndexOfLastModifiedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastModifiedDate");
          final int _cursorIndexOfCachedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "cachedAt");
          final CachedProgramEntity _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpExercisesJson;
            _tmpExercisesJson = _cursor.getString(_cursorIndexOfExercisesJson);
            final long _tmpCreatedDate;
            _tmpCreatedDate = _cursor.getLong(_cursorIndexOfCreatedDate);
            final long _tmpLastModifiedDate;
            _tmpLastModifiedDate = _cursor.getLong(_cursorIndexOfLastModifiedDate);
            final long _tmpCachedAt;
            _tmpCachedAt = _cursor.getLong(_cursorIndexOfCachedAt);
            _result = new CachedProgramEntity(_tmpId,_tmpName,_tmpDescription,_tmpExercisesJson,_tmpCreatedDate,_tmpLastModifiedDate,_tmpCachedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<CachedProgramEntity>> searchPrograms(final String searchQuery) {
    final String _sql = "SELECT * FROM cached_programs WHERE name LIKE '%' || ? || '%' OR description LIKE '%' || ? || '%' ORDER BY name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, searchQuery);
    _argIndex = 2;
    _statement.bindString(_argIndex, searchQuery);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cached_programs"}, new Callable<List<CachedProgramEntity>>() {
      @Override
      @NonNull
      public List<CachedProgramEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfExercisesJson = CursorUtil.getColumnIndexOrThrow(_cursor, "exercisesJson");
          final int _cursorIndexOfCreatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "createdDate");
          final int _cursorIndexOfLastModifiedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastModifiedDate");
          final int _cursorIndexOfCachedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "cachedAt");
          final List<CachedProgramEntity> _result = new ArrayList<CachedProgramEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CachedProgramEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpExercisesJson;
            _tmpExercisesJson = _cursor.getString(_cursorIndexOfExercisesJson);
            final long _tmpCreatedDate;
            _tmpCreatedDate = _cursor.getLong(_cursorIndexOfCreatedDate);
            final long _tmpLastModifiedDate;
            _tmpLastModifiedDate = _cursor.getLong(_cursorIndexOfLastModifiedDate);
            final long _tmpCachedAt;
            _tmpCachedAt = _cursor.getLong(_cursorIndexOfCachedAt);
            _item = new CachedProgramEntity(_tmpId,_tmpName,_tmpDescription,_tmpExercisesJson,_tmpCreatedDate,_tmpLastModifiedDate,_tmpCachedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CachedProgramEntity>> getProgramsByLastModified() {
    final String _sql = "SELECT `cached_programs`.`id` AS `id`, `cached_programs`.`name` AS `name`, `cached_programs`.`description` AS `description`, `cached_programs`.`exercisesJson` AS `exercisesJson`, `cached_programs`.`createdDate` AS `createdDate`, `cached_programs`.`lastModifiedDate` AS `lastModifiedDate`, `cached_programs`.`cachedAt` AS `cachedAt` FROM cached_programs ORDER BY lastModifiedDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cached_programs"}, new Callable<List<CachedProgramEntity>>() {
      @Override
      @NonNull
      public List<CachedProgramEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfName = 1;
          final int _cursorIndexOfDescription = 2;
          final int _cursorIndexOfExercisesJson = 3;
          final int _cursorIndexOfCreatedDate = 4;
          final int _cursorIndexOfLastModifiedDate = 5;
          final int _cursorIndexOfCachedAt = 6;
          final List<CachedProgramEntity> _result = new ArrayList<CachedProgramEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CachedProgramEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpExercisesJson;
            _tmpExercisesJson = _cursor.getString(_cursorIndexOfExercisesJson);
            final long _tmpCreatedDate;
            _tmpCreatedDate = _cursor.getLong(_cursorIndexOfCreatedDate);
            final long _tmpLastModifiedDate;
            _tmpLastModifiedDate = _cursor.getLong(_cursorIndexOfLastModifiedDate);
            final long _tmpCachedAt;
            _tmpCachedAt = _cursor.getLong(_cursorIndexOfCachedAt);
            _item = new CachedProgramEntity(_tmpId,_tmpName,_tmpDescription,_tmpExercisesJson,_tmpCreatedDate,_tmpLastModifiedDate,_tmpCachedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CachedProgramEntity>> getProgramsByCreated() {
    final String _sql = "SELECT `cached_programs`.`id` AS `id`, `cached_programs`.`name` AS `name`, `cached_programs`.`description` AS `description`, `cached_programs`.`exercisesJson` AS `exercisesJson`, `cached_programs`.`createdDate` AS `createdDate`, `cached_programs`.`lastModifiedDate` AS `lastModifiedDate`, `cached_programs`.`cachedAt` AS `cachedAt` FROM cached_programs ORDER BY createdDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cached_programs"}, new Callable<List<CachedProgramEntity>>() {
      @Override
      @NonNull
      public List<CachedProgramEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfName = 1;
          final int _cursorIndexOfDescription = 2;
          final int _cursorIndexOfExercisesJson = 3;
          final int _cursorIndexOfCreatedDate = 4;
          final int _cursorIndexOfLastModifiedDate = 5;
          final int _cursorIndexOfCachedAt = 6;
          final List<CachedProgramEntity> _result = new ArrayList<CachedProgramEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CachedProgramEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpExercisesJson;
            _tmpExercisesJson = _cursor.getString(_cursorIndexOfExercisesJson);
            final long _tmpCreatedDate;
            _tmpCreatedDate = _cursor.getLong(_cursorIndexOfCreatedDate);
            final long _tmpLastModifiedDate;
            _tmpLastModifiedDate = _cursor.getLong(_cursorIndexOfLastModifiedDate);
            final long _tmpCachedAt;
            _tmpCachedAt = _cursor.getLong(_cursorIndexOfCachedAt);
            _item = new CachedProgramEntity(_tmpId,_tmpName,_tmpDescription,_tmpExercisesJson,_tmpCreatedDate,_tmpLastModifiedDate,_tmpCachedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getProgramCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM cached_programs";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
