<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_add_task" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\dialog_add_task.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_add_task_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="126" endOffset="14"/></Target><Target id="@+id/taskNameInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="13" startOffset="8" endLine="19" endOffset="55"/></Target><Target id="@+id/taskDescriptionInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="27" startOffset="8" endLine="33" endOffset="55"/></Target><Target id="@+id/groupSpinner" view="Spinner"><Expressions/><location startLine="53" startOffset="8" endLine="59" endOffset="38"/></Target><Target id="@+id/dueDateLabel" view="TextView"><Expressions/><location startLine="72" startOffset="8" endLine="79" endOffset="46"/></Target><Target id="@+id/dueDateText" view="TextView"><Expressions/><location startLine="95" startOffset="12" endLine="103" endOffset="50"/></Target><Target id="@+id/pickDueDateButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="105" startOffset="12" endLine="113" endOffset="47"/></Target><Target id="@+id/clearDueDateButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="115" startOffset="12" endLine="122" endOffset="75"/></Target></Targets></Layout>