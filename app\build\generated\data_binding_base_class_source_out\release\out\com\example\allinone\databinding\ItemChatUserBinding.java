// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemChatUserBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialCardView cardUserMessage;

  @NonNull
  public final TextView textUserMessage;

  @NonNull
  public final TextView textUserTimestamp;

  private ItemChatUserBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialCardView cardUserMessage, @NonNull TextView textUserMessage,
      @NonNull TextView textUserTimestamp) {
    this.rootView = rootView;
    this.cardUserMessage = cardUserMessage;
    this.textUserMessage = textUserMessage;
    this.textUserTimestamp = textUserTimestamp;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemChatUserBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemChatUserBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_chat_user, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemChatUserBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cardUserMessage;
      MaterialCardView cardUserMessage = ViewBindings.findChildViewById(rootView, id);
      if (cardUserMessage == null) {
        break missingId;
      }

      id = R.id.textUserMessage;
      TextView textUserMessage = ViewBindings.findChildViewById(rootView, id);
      if (textUserMessage == null) {
        break missingId;
      }

      id = R.id.textUserTimestamp;
      TextView textUserTimestamp = ViewBindings.findChildViewById(rootView, id);
      if (textUserTimestamp == null) {
        break missingId;
      }

      return new ItemChatUserBinding((ConstraintLayout) rootView, cardUserMessage, textUserMessage,
          textUserTimestamp);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
