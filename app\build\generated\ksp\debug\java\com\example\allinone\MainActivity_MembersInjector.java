package com.example.allinone;

import com.example.allinone.firebase.FirebaseManager;
import com.example.allinone.firebase.FirebaseRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class MainActivity_MembersInjector implements MembersInjector<MainActivity> {
  private final Provider<FirebaseManager> firebaseManagerProvider;

  private final Provider<FirebaseRepository> firebaseRepositoryProvider;

  public MainActivity_MembersInjector(Provider<FirebaseManager> firebaseManagerProvider,
      Provider<FirebaseRepository> firebaseRepositoryProvider) {
    this.firebaseManagerProvider = firebaseManagerProvider;
    this.firebaseRepositoryProvider = firebaseRepositoryProvider;
  }

  public static MembersInjector<MainActivity> create(
      Provider<FirebaseManager> firebaseManagerProvider,
      Provider<FirebaseRepository> firebaseRepositoryProvider) {
    return new MainActivity_MembersInjector(firebaseManagerProvider, firebaseRepositoryProvider);
  }

  @Override
  public void injectMembers(MainActivity instance) {
    injectFirebaseManager(instance, firebaseManagerProvider.get());
    injectFirebaseRepository(instance, firebaseRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.example.allinone.MainActivity.firebaseManager")
  public static void injectFirebaseManager(MainActivity instance, FirebaseManager firebaseManager) {
    instance.firebaseManager = firebaseManager;
  }

  @InjectedFieldSignature("com.example.allinone.MainActivity.firebaseRepository")
  public static void injectFirebaseRepository(MainActivity instance,
      FirebaseRepository firebaseRepository) {
    instance.firebaseRepository = firebaseRepository;
  }
}
