package com.example.allinone.feature.instagram.di;

import com.example.allinone.feature.instagram.domain.repository.InstagramRepository;
import com.example.allinone.feature.instagram.domain.usecase.UploadFileForAnalysisUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class InstagramModule_Companion_ProvideUploadFileForAnalysisUseCaseFactory implements Factory<UploadFileForAnalysisUseCase> {
  private final Provider<InstagramRepository> repositoryProvider;

  public InstagramModule_Companion_ProvideUploadFileForAnalysisUseCaseFactory(
      Provider<InstagramRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public UploadFileForAnalysisUseCase get() {
    return provideUploadFileForAnalysisUseCase(repositoryProvider.get());
  }

  public static InstagramModule_Companion_ProvideUploadFileForAnalysisUseCaseFactory create(
      Provider<InstagramRepository> repositoryProvider) {
    return new InstagramModule_Companion_ProvideUploadFileForAnalysisUseCaseFactory(repositoryProvider);
  }

  public static UploadFileForAnalysisUseCase provideUploadFileForAnalysisUseCase(
      InstagramRepository repository) {
    return Preconditions.checkNotNullFromProvides(InstagramModule.Companion.provideUploadFileForAnalysisUseCase(repository));
  }
}
