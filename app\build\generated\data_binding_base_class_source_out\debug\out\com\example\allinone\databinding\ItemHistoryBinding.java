// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemHistoryBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView amountText;

  @NonNull
  public final TextView dateText;

  @NonNull
  public final ImageButton deleteButton;

  @NonNull
  public final TextView descriptionText;

  @NonNull
  public final TextView titleText;

  @NonNull
  public final ImageView typeIcon;

  private ItemHistoryBinding(@NonNull MaterialCardView rootView, @NonNull TextView amountText,
      @NonNull TextView dateText, @NonNull ImageButton deleteButton,
      @NonNull TextView descriptionText, @NonNull TextView titleText, @NonNull ImageView typeIcon) {
    this.rootView = rootView;
    this.amountText = amountText;
    this.dateText = dateText;
    this.deleteButton = deleteButton;
    this.descriptionText = descriptionText;
    this.titleText = titleText;
    this.typeIcon = typeIcon;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemHistoryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemHistoryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_history, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemHistoryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.amount_text;
      TextView amountText = ViewBindings.findChildViewById(rootView, id);
      if (amountText == null) {
        break missingId;
      }

      id = R.id.date_text;
      TextView dateText = ViewBindings.findChildViewById(rootView, id);
      if (dateText == null) {
        break missingId;
      }

      id = R.id.delete_button;
      ImageButton deleteButton = ViewBindings.findChildViewById(rootView, id);
      if (deleteButton == null) {
        break missingId;
      }

      id = R.id.description_text;
      TextView descriptionText = ViewBindings.findChildViewById(rootView, id);
      if (descriptionText == null) {
        break missingId;
      }

      id = R.id.title_text;
      TextView titleText = ViewBindings.findChildViewById(rootView, id);
      if (titleText == null) {
        break missingId;
      }

      id = R.id.type_icon;
      ImageView typeIcon = ViewBindings.findChildViewById(rootView, id);
      if (typeIcon == null) {
        break missingId;
      }

      return new ItemHistoryBinding((MaterialCardView) rootView, amountText, dateText, deleteButton,
          descriptionText, titleText, typeIcon);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
