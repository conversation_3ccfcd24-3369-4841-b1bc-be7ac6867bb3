<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_note_image" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_note_image.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_note_image_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="30" endOffset="51"/></Target><Target id="@+id/imageView" view="ImageView"><Expressions/><location startLine="14" startOffset="8" endLine="18" endOffset="43"/></Target><Target id="@+id/deleteButton" view="ImageButton"><Expressions/><location startLine="20" startOffset="8" endLine="28" endOffset="36"/></Target></Targets></Layout>