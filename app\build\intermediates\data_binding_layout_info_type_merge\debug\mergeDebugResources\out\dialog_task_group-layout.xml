<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_task_group" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\dialog_task_group.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_task_group_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="165" endOffset="14"/></Target><Target id="@+id/dialog_title" view="TextView"><Expressions/><location startLine="9" startOffset="4" endLine="18" endOffset="34"/></Target><Target id="@+id/group_title_layout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="20" startOffset="4" endLine="37" endOffset="59"/></Target><Target id="@+id/group_title_edit" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="29" startOffset="8" endLine="36" endOffset="62"/></Target><Target id="@+id/group_description_layout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="39" startOffset="4" endLine="56" endOffset="59"/></Target><Target id="@+id/group_description_edit" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="48" startOffset="8" endLine="55" endOffset="62"/></Target><Target id="@+id/color_blue" view="View"><Expressions/><location startLine="74" startOffset="8" endLine="83" endOffset="75"/></Target><Target id="@+id/color_green" view="View"><Expressions/><location startLine="85" startOffset="8" endLine="94" endOffset="75"/></Target><Target id="@+id/color_red" view="View"><Expressions/><location startLine="96" startOffset="8" endLine="105" endOffset="75"/></Target><Target id="@+id/color_orange" view="View"><Expressions/><location startLine="107" startOffset="8" endLine="116" endOffset="75"/></Target><Target id="@+id/color_purple" view="View"><Expressions/><location startLine="118" startOffset="8" endLine="127" endOffset="75"/></Target><Target id="@+id/btn_cancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="137" startOffset="8" endLine="144" endOffset="63"/></Target><Target id="@+id/btn_delete" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="146" startOffset="8" endLine="154" endOffset="63"/></Target><Target id="@+id/btn_save" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="156" startOffset="8" endLine="161" endOffset="52"/></Target></Targets></Layout>