// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.github.mikephil.charting.charts.LineChart;
import com.github.mikephil.charting.charts.PieChart;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentTransactionReportBinding implements ViewBinding {
  @NonNull
  private final NestedScrollView rootView;

  @NonNull
  public final Button applyFiltersButton;

  @NonNull
  public final TextView averageTransactionText;

  @NonNull
  public final TextView balanceText;

  @NonNull
  public final TextView cashFlowTitle;

  @NonNull
  public final AutoCompleteTextView categoryAutoComplete;

  @NonNull
  public final TextInputLayout categoryLayout;

  @NonNull
  public final PieChart categoryPieChart;

  @NonNull
  public final MaterialCardView categorySpendingCard;

  @NonNull
  public final MaterialCardView chartCard;

  @NonNull
  public final AutoCompleteTextView dateRangeAutoComplete;

  @NonNull
  public final TextInputLayout dateRangeLayout;

  @NonNull
  public final MaterialCardView filterCard;

  @NonNull
  public final MaterialCardView insightsCard;

  @NonNull
  public final TextView largestExpenseText;

  @NonNull
  public final LineChart lineChart;

  @NonNull
  public final TextView mostSpentCategoryText;

  @NonNull
  public final MaterialCardView summaryCard;

  @NonNull
  public final RecyclerView topCategoriesRecyclerView;

  @NonNull
  public final TextView totalExpenseText;

  @NonNull
  public final TextView totalIncomeText;

  @NonNull
  public final TextView transactionCountText;

  private FragmentTransactionReportBinding(@NonNull NestedScrollView rootView,
      @NonNull Button applyFiltersButton, @NonNull TextView averageTransactionText,
      @NonNull TextView balanceText, @NonNull TextView cashFlowTitle,
      @NonNull AutoCompleteTextView categoryAutoComplete, @NonNull TextInputLayout categoryLayout,
      @NonNull PieChart categoryPieChart, @NonNull MaterialCardView categorySpendingCard,
      @NonNull MaterialCardView chartCard, @NonNull AutoCompleteTextView dateRangeAutoComplete,
      @NonNull TextInputLayout dateRangeLayout, @NonNull MaterialCardView filterCard,
      @NonNull MaterialCardView insightsCard, @NonNull TextView largestExpenseText,
      @NonNull LineChart lineChart, @NonNull TextView mostSpentCategoryText,
      @NonNull MaterialCardView summaryCard, @NonNull RecyclerView topCategoriesRecyclerView,
      @NonNull TextView totalExpenseText, @NonNull TextView totalIncomeText,
      @NonNull TextView transactionCountText) {
    this.rootView = rootView;
    this.applyFiltersButton = applyFiltersButton;
    this.averageTransactionText = averageTransactionText;
    this.balanceText = balanceText;
    this.cashFlowTitle = cashFlowTitle;
    this.categoryAutoComplete = categoryAutoComplete;
    this.categoryLayout = categoryLayout;
    this.categoryPieChart = categoryPieChart;
    this.categorySpendingCard = categorySpendingCard;
    this.chartCard = chartCard;
    this.dateRangeAutoComplete = dateRangeAutoComplete;
    this.dateRangeLayout = dateRangeLayout;
    this.filterCard = filterCard;
    this.insightsCard = insightsCard;
    this.largestExpenseText = largestExpenseText;
    this.lineChart = lineChart;
    this.mostSpentCategoryText = mostSpentCategoryText;
    this.summaryCard = summaryCard;
    this.topCategoriesRecyclerView = topCategoriesRecyclerView;
    this.totalExpenseText = totalExpenseText;
    this.totalIncomeText = totalIncomeText;
    this.transactionCountText = transactionCountText;
  }

  @Override
  @NonNull
  public NestedScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentTransactionReportBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentTransactionReportBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_transaction_report, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentTransactionReportBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.applyFiltersButton;
      Button applyFiltersButton = ViewBindings.findChildViewById(rootView, id);
      if (applyFiltersButton == null) {
        break missingId;
      }

      id = R.id.averageTransactionText;
      TextView averageTransactionText = ViewBindings.findChildViewById(rootView, id);
      if (averageTransactionText == null) {
        break missingId;
      }

      id = R.id.balanceText;
      TextView balanceText = ViewBindings.findChildViewById(rootView, id);
      if (balanceText == null) {
        break missingId;
      }

      id = R.id.cashFlowTitle;
      TextView cashFlowTitle = ViewBindings.findChildViewById(rootView, id);
      if (cashFlowTitle == null) {
        break missingId;
      }

      id = R.id.categoryAutoComplete;
      AutoCompleteTextView categoryAutoComplete = ViewBindings.findChildViewById(rootView, id);
      if (categoryAutoComplete == null) {
        break missingId;
      }

      id = R.id.categoryLayout;
      TextInputLayout categoryLayout = ViewBindings.findChildViewById(rootView, id);
      if (categoryLayout == null) {
        break missingId;
      }

      id = R.id.categoryPieChart;
      PieChart categoryPieChart = ViewBindings.findChildViewById(rootView, id);
      if (categoryPieChart == null) {
        break missingId;
      }

      id = R.id.categorySpendingCard;
      MaterialCardView categorySpendingCard = ViewBindings.findChildViewById(rootView, id);
      if (categorySpendingCard == null) {
        break missingId;
      }

      id = R.id.chartCard;
      MaterialCardView chartCard = ViewBindings.findChildViewById(rootView, id);
      if (chartCard == null) {
        break missingId;
      }

      id = R.id.dateRangeAutoComplete;
      AutoCompleteTextView dateRangeAutoComplete = ViewBindings.findChildViewById(rootView, id);
      if (dateRangeAutoComplete == null) {
        break missingId;
      }

      id = R.id.dateRangeLayout;
      TextInputLayout dateRangeLayout = ViewBindings.findChildViewById(rootView, id);
      if (dateRangeLayout == null) {
        break missingId;
      }

      id = R.id.filterCard;
      MaterialCardView filterCard = ViewBindings.findChildViewById(rootView, id);
      if (filterCard == null) {
        break missingId;
      }

      id = R.id.insightsCard;
      MaterialCardView insightsCard = ViewBindings.findChildViewById(rootView, id);
      if (insightsCard == null) {
        break missingId;
      }

      id = R.id.largestExpenseText;
      TextView largestExpenseText = ViewBindings.findChildViewById(rootView, id);
      if (largestExpenseText == null) {
        break missingId;
      }

      id = R.id.lineChart;
      LineChart lineChart = ViewBindings.findChildViewById(rootView, id);
      if (lineChart == null) {
        break missingId;
      }

      id = R.id.mostSpentCategoryText;
      TextView mostSpentCategoryText = ViewBindings.findChildViewById(rootView, id);
      if (mostSpentCategoryText == null) {
        break missingId;
      }

      id = R.id.summaryCard;
      MaterialCardView summaryCard = ViewBindings.findChildViewById(rootView, id);
      if (summaryCard == null) {
        break missingId;
      }

      id = R.id.topCategoriesRecyclerView;
      RecyclerView topCategoriesRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (topCategoriesRecyclerView == null) {
        break missingId;
      }

      id = R.id.totalExpenseText;
      TextView totalExpenseText = ViewBindings.findChildViewById(rootView, id);
      if (totalExpenseText == null) {
        break missingId;
      }

      id = R.id.totalIncomeText;
      TextView totalIncomeText = ViewBindings.findChildViewById(rootView, id);
      if (totalIncomeText == null) {
        break missingId;
      }

      id = R.id.transactionCountText;
      TextView transactionCountText = ViewBindings.findChildViewById(rootView, id);
      if (transactionCountText == null) {
        break missingId;
      }

      return new FragmentTransactionReportBinding((NestedScrollView) rootView, applyFiltersButton,
          averageTransactionText, balanceText, cashFlowTitle, categoryAutoComplete, categoryLayout,
          categoryPieChart, categorySpendingCard, chartCard, dateRangeAutoComplete, dateRangeLayout,
          filterCard, insightsCard, largestExpenseText, lineChart, mostSpentCategoryText,
          summaryCard, topCategoriesRecyclerView, totalExpenseText, totalIncomeText,
          transactionCountText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
