<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_futures_tab" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\fragment_futures_tab.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.swiperefreshlayout.widget.SwipeRefreshLayout" rootNodeViewId="@+id/futuresSwipeRefreshLayout"><Targets><Target id="@+id/futuresSwipeRefreshLayout" tag="layout/fragment_futures_tab_0" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="1" startOffset="0" endLine="148" endOffset="55"/></Target><Target id="@+id/summaryCard" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="15" startOffset="8" endLine="99" endOffset="43"/></Target><Target id="@+id/balanceTitleText" view="TextView"><Expressions/><location startLine="31" startOffset="16" endLine="38" endOffset="63"/></Target><Target id="@+id/balanceValueText" view="TextView"><Expressions/><location startLine="40" startOffset="16" endLine="48" endOffset="80"/></Target><Target id="@+id/pnlTitleText" view="TextView"><Expressions/><location startLine="50" startOffset="16" endLine="57" endOffset="63"/></Target><Target id="@+id/pnlValueText" view="TextView"><Expressions/><location startLine="59" startOffset="16" endLine="67" endOffset="76"/></Target><Target id="@+id/divider" view="View"><Expressions/><location startLine="69" startOffset="16" endLine="75" endOffset="80"/></Target><Target id="@+id/marginBalanceTitleText" view="TextView"><Expressions/><location startLine="79" startOffset="16" endLine="87" endOffset="71"/></Target><Target id="@+id/marginBalanceValueText" view="TextView"><Expressions/><location startLine="89" startOffset="16" endLine="96" endOffset="83"/></Target><Target id="@+id/positionsTitle" view="TextView"><Expressions/><location startLine="102" startOffset="8" endLine="110" endOffset="67"/></Target><Target id="@+id/positionsRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="113" startOffset="8" endLine="121" endOffset="55"/></Target><Target id="@+id/emptyStateText" view="TextView"><Expressions/><location startLine="124" startOffset="8" endLine="134" endOffset="70"/></Target><Target id="@+id/loadingProgress" view="ProgressBar"><Expressions/><location startLine="137" startOffset="8" endLine="145" endOffset="55"/></Target></Targets></Layout>