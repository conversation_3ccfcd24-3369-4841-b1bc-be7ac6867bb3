<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_database_record" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_database_record.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_database_record_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="118" endOffset="51"/></Target><Target id="@+id/record_id" view="TextView"><Expressions/><location startLine="21" startOffset="12" endLine="27" endOffset="37"/></Target><Target id="@+id/record_date" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="39" endOffset="41"/></Target><Target id="@+id/record_title" view="TextView"><Expressions/><location startLine="42" startOffset="8" endLine="49" endOffset="37"/></Target><Target id="@+id/record_subtitle" view="TextView"><Expressions/><location startLine="51" startOffset="8" endLine="57" endOffset="66"/></Target><Target id="@+id/record_details" view="TextView"><Expressions/><location startLine="59" startOffset="8" endLine="65" endOffset="112"/></Target><Target id="@+id/record_tags" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="67" startOffset="8" endLine="89" endOffset="52"/></Target><Target id="@+id/details_button" view="Button"><Expressions/><location startLine="96" startOffset="12" endLine="104" endOffset="59"/></Target><Target id="@+id/delete_button" view="Button"><Expressions/><location startLine="106" startOffset="12" endLine="115" endOffset="59"/></Target></Targets></Layout>