// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityBackupBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final CardView backupCard;

  @NonNull
  public final CardView backupsCard;

  @NonNull
  public final RecyclerView backupsRecyclerView;

  @NonNull
  public final Button createBackupButton;

  @NonNull
  public final TextView noBackupsText;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final Toolbar toolbar;

  private ActivityBackupBinding(@NonNull ConstraintLayout rootView, @NonNull CardView backupCard,
      @NonNull CardView backupsCard, @NonNull RecyclerView backupsRecyclerView,
      @NonNull Button createBackupButton, @NonNull TextView noBackupsText,
      @NonNull ProgressBar progressBar, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.backupCard = backupCard;
    this.backupsCard = backupsCard;
    this.backupsRecyclerView = backupsRecyclerView;
    this.createBackupButton = createBackupButton;
    this.noBackupsText = noBackupsText;
    this.progressBar = progressBar;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityBackupBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityBackupBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_backup, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityBackupBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.backup_card;
      CardView backupCard = ViewBindings.findChildViewById(rootView, id);
      if (backupCard == null) {
        break missingId;
      }

      id = R.id.backups_card;
      CardView backupsCard = ViewBindings.findChildViewById(rootView, id);
      if (backupsCard == null) {
        break missingId;
      }

      id = R.id.backups_recycler_view;
      RecyclerView backupsRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (backupsRecyclerView == null) {
        break missingId;
      }

      id = R.id.create_backup_button;
      Button createBackupButton = ViewBindings.findChildViewById(rootView, id);
      if (createBackupButton == null) {
        break missingId;
      }

      id = R.id.no_backups_text;
      TextView noBackupsText = ViewBindings.findChildViewById(rootView, id);
      if (noBackupsText == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityBackupBinding((ConstraintLayout) rootView, backupCard, backupsCard,
          backupsRecyclerView, createBackupButton, noBackupsText, progressBar, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
