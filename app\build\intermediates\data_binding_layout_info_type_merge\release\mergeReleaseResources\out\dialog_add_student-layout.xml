<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_add_student" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\dialog_add_student.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView"><Targets><Target tag="layout/dialog_add_student_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="128" endOffset="39"/></Target><Target id="@+id/profileImageView" view="com.google.android.material.imageview.ShapeableImageView"><Expressions/><location startLine="19" startOffset="12" endLine="30" endOffset="69"/></Target><Target id="@+id/nameInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="34" startOffset="8" endLine="47" endOffset="63"/></Target><Target id="@+id/nameEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="42" startOffset="12" endLine="46" endOffset="52"/></Target><Target id="@+id/phoneInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="49" startOffset="8" endLine="62" endOffset="63"/></Target><Target id="@+id/phoneEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="57" startOffset="12" endLine="61" endOffset="43"/></Target><Target id="@+id/emailInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="64" startOffset="8" endLine="77" endOffset="63"/></Target><Target id="@+id/emailEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="72" startOffset="12" endLine="76" endOffset="54"/></Target><Target id="@+id/instagramInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="79" startOffset="8" endLine="92" endOffset="63"/></Target><Target id="@+id/instagramEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="87" startOffset="12" endLine="91" endOffset="42"/></Target><Target id="@+id/activeSwitch" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="94" startOffset="8" endLine="100" endOffset="50"/></Target><Target id="@+id/cancelButton" view="Button"><Expressions/><location startLine="109" startOffset="12" endLine="115" endOffset="47"/></Target><Target id="@+id/saveButton" view="Button"><Expressions/><location startLine="117" startOffset="12" endLine="123" endOffset="45"/></Target></Targets></Layout>