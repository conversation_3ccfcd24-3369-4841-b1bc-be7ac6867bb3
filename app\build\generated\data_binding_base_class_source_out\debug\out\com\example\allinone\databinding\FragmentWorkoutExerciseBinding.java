// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.ScrollView;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentWorkoutExerciseBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final MaterialButton applyFiltersButton;

  @NonNull
  public final MaterialButton createWorkoutButton;

  @NonNull
  public final TextView emptyLogText;

  @NonNull
  public final AutoCompleteTextView filterMuscleGroup;

  @NonNull
  public final MaterialCardView programSelectionCard;

  @NonNull
  public final Spinner programSpinner;

  @NonNull
  public final AutoCompleteTextView sortOption;

  @NonNull
  public final SwipeRefreshLayout swipeRefreshLayout;

  @NonNull
  public final MaterialCardView workoutHistoryCard;

  @NonNull
  public final RecyclerView workoutLogRecyclerView;

  private FragmentWorkoutExerciseBinding(@NonNull ScrollView rootView,
      @NonNull MaterialButton applyFiltersButton, @NonNull MaterialButton createWorkoutButton,
      @NonNull TextView emptyLogText, @NonNull AutoCompleteTextView filterMuscleGroup,
      @NonNull MaterialCardView programSelectionCard, @NonNull Spinner programSpinner,
      @NonNull AutoCompleteTextView sortOption, @NonNull SwipeRefreshLayout swipeRefreshLayout,
      @NonNull MaterialCardView workoutHistoryCard, @NonNull RecyclerView workoutLogRecyclerView) {
    this.rootView = rootView;
    this.applyFiltersButton = applyFiltersButton;
    this.createWorkoutButton = createWorkoutButton;
    this.emptyLogText = emptyLogText;
    this.filterMuscleGroup = filterMuscleGroup;
    this.programSelectionCard = programSelectionCard;
    this.programSpinner = programSpinner;
    this.sortOption = sortOption;
    this.swipeRefreshLayout = swipeRefreshLayout;
    this.workoutHistoryCard = workoutHistoryCard;
    this.workoutLogRecyclerView = workoutLogRecyclerView;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentWorkoutExerciseBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentWorkoutExerciseBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_workout_exercise, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentWorkoutExerciseBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.apply_filters_button;
      MaterialButton applyFiltersButton = ViewBindings.findChildViewById(rootView, id);
      if (applyFiltersButton == null) {
        break missingId;
      }

      id = R.id.create_workout_button;
      MaterialButton createWorkoutButton = ViewBindings.findChildViewById(rootView, id);
      if (createWorkoutButton == null) {
        break missingId;
      }

      id = R.id.empty_log_text;
      TextView emptyLogText = ViewBindings.findChildViewById(rootView, id);
      if (emptyLogText == null) {
        break missingId;
      }

      id = R.id.filter_muscle_group;
      AutoCompleteTextView filterMuscleGroup = ViewBindings.findChildViewById(rootView, id);
      if (filterMuscleGroup == null) {
        break missingId;
      }

      id = R.id.program_selection_card;
      MaterialCardView programSelectionCard = ViewBindings.findChildViewById(rootView, id);
      if (programSelectionCard == null) {
        break missingId;
      }

      id = R.id.program_spinner;
      Spinner programSpinner = ViewBindings.findChildViewById(rootView, id);
      if (programSpinner == null) {
        break missingId;
      }

      id = R.id.sort_option;
      AutoCompleteTextView sortOption = ViewBindings.findChildViewById(rootView, id);
      if (sortOption == null) {
        break missingId;
      }

      id = R.id.swipe_refresh_layout;
      SwipeRefreshLayout swipeRefreshLayout = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefreshLayout == null) {
        break missingId;
      }

      id = R.id.workout_history_card;
      MaterialCardView workoutHistoryCard = ViewBindings.findChildViewById(rootView, id);
      if (workoutHistoryCard == null) {
        break missingId;
      }

      id = R.id.workout_log_recycler_view;
      RecyclerView workoutLogRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (workoutLogRecyclerView == null) {
        break missingId;
      }

      return new FragmentWorkoutExerciseBinding((ScrollView) rootView, applyFiltersButton,
          createWorkoutButton, emptyLogText, filterMuscleGroup, programSelectionCard,
          programSpinner, sortOption, swipeRefreshLayout, workoutHistoryCard,
          workoutLogRecyclerView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
