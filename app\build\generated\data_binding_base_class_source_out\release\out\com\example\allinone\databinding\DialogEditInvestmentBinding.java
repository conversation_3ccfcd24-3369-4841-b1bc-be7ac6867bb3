// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.Button;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.checkbox.MaterialCheckBox;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogEditInvestmentBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button addImageButton;

  @NonNull
  public final TextInputEditText amountInput;

  @NonNull
  public final TextInputEditText descriptionInput;

  @NonNull
  public final RecyclerView imagesRecyclerView;

  @NonNull
  public final MaterialCheckBox isPastInvestmentCheckbox;

  @NonNull
  public final TextInputEditText nameInput;

  @NonNull
  public final AutoCompleteTextView typeInput;

  private DialogEditInvestmentBinding(@NonNull LinearLayout rootView,
      @NonNull Button addImageButton, @NonNull TextInputEditText amountInput,
      @NonNull TextInputEditText descriptionInput, @NonNull RecyclerView imagesRecyclerView,
      @NonNull MaterialCheckBox isPastInvestmentCheckbox, @NonNull TextInputEditText nameInput,
      @NonNull AutoCompleteTextView typeInput) {
    this.rootView = rootView;
    this.addImageButton = addImageButton;
    this.amountInput = amountInput;
    this.descriptionInput = descriptionInput;
    this.imagesRecyclerView = imagesRecyclerView;
    this.isPastInvestmentCheckbox = isPastInvestmentCheckbox;
    this.nameInput = nameInput;
    this.typeInput = typeInput;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogEditInvestmentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogEditInvestmentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_edit_investment, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogEditInvestmentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addImageButton;
      Button addImageButton = ViewBindings.findChildViewById(rootView, id);
      if (addImageButton == null) {
        break missingId;
      }

      id = R.id.amountInput;
      TextInputEditText amountInput = ViewBindings.findChildViewById(rootView, id);
      if (amountInput == null) {
        break missingId;
      }

      id = R.id.descriptionInput;
      TextInputEditText descriptionInput = ViewBindings.findChildViewById(rootView, id);
      if (descriptionInput == null) {
        break missingId;
      }

      id = R.id.imagesRecyclerView;
      RecyclerView imagesRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (imagesRecyclerView == null) {
        break missingId;
      }

      id = R.id.isPastInvestmentCheckbox;
      MaterialCheckBox isPastInvestmentCheckbox = ViewBindings.findChildViewById(rootView, id);
      if (isPastInvestmentCheckbox == null) {
        break missingId;
      }

      id = R.id.nameInput;
      TextInputEditText nameInput = ViewBindings.findChildViewById(rootView, id);
      if (nameInput == null) {
        break missingId;
      }

      id = R.id.typeInput;
      AutoCompleteTextView typeInput = ViewBindings.findChildViewById(rootView, id);
      if (typeInput == null) {
        break missingId;
      }

      return new DialogEditInvestmentBinding((LinearLayout) rootView, addImageButton, amountInput,
          descriptionInput, imagesRecyclerView, isPastInvestmentCheckbox, nameInput, typeInput);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
