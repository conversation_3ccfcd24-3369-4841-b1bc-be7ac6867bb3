package com.example.allinone.di;

import com.example.allinone.data.local.AppDatabase;
import com.example.allinone.data.local.dao.CachedWorkoutDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class AppModule_ProvideCachedWorkoutDaoFactory implements Factory<CachedWorkoutDao> {
  private final Provider<AppDatabase> databaseProvider;

  public AppModule_ProvideCachedWorkoutDaoFactory(Provider<AppDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public CachedWorkoutDao get() {
    return provideCachedWorkoutDao(databaseProvider.get());
  }

  public static AppModule_ProvideCachedWorkoutDaoFactory create(
      Provider<AppDatabase> databaseProvider) {
    return new AppModule_ProvideCachedWorkoutDaoFactory(databaseProvider);
  }

  public static CachedWorkoutDao provideCachedWorkoutDao(AppDatabase database) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideCachedWorkoutDao(database));
  }
}
