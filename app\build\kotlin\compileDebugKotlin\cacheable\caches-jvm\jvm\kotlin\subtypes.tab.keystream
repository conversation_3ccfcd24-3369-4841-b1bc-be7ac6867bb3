=com.example.allinone.adapters.GroupedTasksAdapter.GroupedItemandroid.widget.ArrayAdapter"com.example.allinone.api.ApiResult(androidx.recyclerview.widget.ListAdapter2androidx.recyclerview.widget.DiffUtil.ItemCallback4com.example.allinone.core.data.datasource.DataSource<com.example.allinone.core.data.datasource.ReactiveDataSourceandroid.os.Parcelable(com.example.allinone.data.common.UiStateAcom.example.allinone.feature.instagram.data.model.InstagramResult:com.example.allinone.core.data.datasource.RemoteDataSource>com.example.allinone.core.data.datasource.SearchableDataSource9com.example.allinone.core.data.datasource.LocalDataSourceKcom.example.allinone.firebase.GenericOfflineQueueProcessor.OperationHandler.com.example.allinone.ui.components.HtmlElement#androidx.activity.ComponentActivity*com.example.allinone.ui.navigation.NavItem$com.example.allinone.ui.BaseFragment1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolder0androidx.viewpager2.adapter.FragmentStateAdapterandroidx.fragment.app.Fragment>com.example.allinone.viewmodels.InvestmentsViewModel.AddStatusAcom.example.allinone.viewmodels.InvestmentsViewModel.UpdateStatusAcom.example.allinone.viewmodels.InvestmentsViewModel.DeleteStatusandroidx.lifecycle.ViewModel1com.example.allinone.viewmodels.LessonChangeEventkotlin.Enum#androidx.lifecycle.AndroidViewModelandroidx.work.CoroutineWorker androidx.viewbinding.ViewBindingandroid.app.Application$androidx.work.Configuration.Providertimber.log.Timber.Tree(androidx.appcompat.app.AppCompatActivityandroidx.room.RoomDatabaseLcom.example.allinone.feature.instagram.domain.repository.InstagramRepositoryFcom.example.allinone.feature.notes.data.datasource.NoteLocalDataSourceGcom.example.allinone.feature.notes.data.datasource.NoteRemoteDataSourceCcom.example.allinone.feature.notes.domain.repository.NoteRepositoryQcom.example.allinone.feature.transactions.domain.repository.TransactionRepositoryKcom.example.allinone.feature.workout.data.datasource.WorkoutLocalDataSourceLcom.example.allinone.feature.workout.data.datasource.WorkoutRemoteDataSourceHcom.example.allinone.feature.workout.domain.repository.WorkoutRepository                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             