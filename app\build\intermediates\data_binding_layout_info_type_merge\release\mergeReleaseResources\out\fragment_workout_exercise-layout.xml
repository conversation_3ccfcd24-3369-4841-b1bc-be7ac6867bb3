<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_workout_exercise" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\fragment_workout_exercise.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_workout_exercise_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="149" endOffset="12"/></Target><Target id="@+id/program_selection_card" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="13" startOffset="8" endLine="50" endOffset="59"/></Target><Target id="@+id/program_spinner" view="Spinner"><Expressions/><location startLine="35" startOffset="16" endLine="40" endOffset="46"/></Target><Target id="@+id/create_workout_button" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="42" startOffset="16" endLine="47" endOffset="50"/></Target><Target id="@+id/workout_history_card" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="52" startOffset="8" endLine="146" endOffset="59"/></Target><Target id="@+id/filter_muscle_group" view="AutoCompleteTextView"><Expressions/><location startLine="88" startOffset="24" endLine="94" endOffset="54"/></Target><Target id="@+id/sort_option" view="AutoCompleteTextView"><Expressions/><location startLine="103" startOffset="24" endLine="109" endOffset="54"/></Target><Target id="@+id/apply_filters_button" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="113" startOffset="16" endLine="119" endOffset="42"/></Target><Target id="@+id/swipe_refresh_layout" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="121" startOffset="16" endLine="133" endOffset="71"/></Target><Target id="@+id/workout_log_recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="127" startOffset="20" endLine="131" endOffset="67"/></Target><Target id="@+id/empty_log_text" view="TextView"><Expressions/><location startLine="135" startOffset="16" endLine="143" endOffset="44"/></Target></Targets></Layout>