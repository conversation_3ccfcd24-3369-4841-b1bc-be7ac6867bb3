<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_history" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_history.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_history_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="92" endOffset="51"/></Target><Target id="@+id/type_icon" view="ImageView"><Expressions/><location startLine="15" startOffset="8" endLine="22" endOffset="43"/></Target><Target id="@+id/title_text" view="TextView"><Expressions/><location startLine="24" startOffset="8" endLine="38" endOffset="37"/></Target><Target id="@+id/description_text" view="TextView"><Expressions/><location startLine="40" startOffset="8" endLine="54" endOffset="112"/></Target><Target id="@+id/date_text" view="TextView"><Expressions/><location startLine="56" startOffset="8" endLine="66" endOffset="39"/></Target><Target id="@+id/amount_text" view="TextView"><Expressions/><location startLine="68" startOffset="8" endLine="77" endOffset="34"/></Target><Target id="@+id/delete_button" view="ImageButton"><Expressions/><location startLine="79" startOffset="8" endLine="88" endOffset="35"/></Target></Targets></Layout>