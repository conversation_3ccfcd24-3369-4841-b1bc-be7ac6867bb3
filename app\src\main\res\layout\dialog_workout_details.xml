<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:id="@+id/workout_name_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textAppearance="?attr/textAppearanceHeadline6"
        tools:text="Upper Body Workout" />

    <TextView
        android:id="@+id/workout_date_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textAppearance="?attr/textAppearanceBody2"
        tools:text="May 15, 2023 - 10:30 AM" />

    <TextView
        android:id="@+id/workout_duration_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textAppearance="?attr/textAppearanceBody2"
        tools:text="Duration: 45 min" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="8dp"
        android:text="Exercises"
        android:textAppearance="?attr/textAppearanceSubtitle1"
        android:textStyle="bold" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/exercises_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxHeight="300dp"
        tools:itemCount="3"
        tools:listitem="@layout/item_workout_exercise" />

</LinearLayout>
