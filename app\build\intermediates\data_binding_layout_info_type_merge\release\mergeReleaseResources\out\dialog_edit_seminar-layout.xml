<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_edit_seminar" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\dialog_edit_seminar.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView"><Targets><Target tag="layout/dialog_edit_seminar_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="126" endOffset="39"/></Target><Target id="@+id/nameLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="12" startOffset="8" endLine="29" endOffset="63"/></Target><Target id="@+id/nameInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="22" startOffset="12" endLine="27" endOffset="38"/></Target><Target id="@+id/dateLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="32" startOffset="8" endLine="52" endOffset="63"/></Target><Target id="@+id/dateInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="45" startOffset="12" endLine="50" endOffset="42"/></Target><Target id="@+id/startTimeLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="55" startOffset="8" endLine="76" endOffset="63"/></Target><Target id="@+id/startTimeInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="69" startOffset="12" endLine="74" endOffset="42"/></Target><Target id="@+id/endTimeLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="79" startOffset="8" endLine="100" endOffset="63"/></Target><Target id="@+id/endTimeInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="93" startOffset="12" endLine="98" endOffset="42"/></Target><Target id="@+id/descriptionLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="103" startOffset="8" endLine="122" endOffset="63"/></Target><Target id="@+id/descriptionInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="114" startOffset="12" endLine="120" endOffset="38"/></Target></Targets></Layout>