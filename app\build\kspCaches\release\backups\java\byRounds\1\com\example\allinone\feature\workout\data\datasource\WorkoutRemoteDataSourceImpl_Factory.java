package com.example.allinone.feature.workout.data.datasource;

import com.example.allinone.firebase.FirebaseManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class WorkoutRemoteDataSourceImpl_Factory implements Factory<WorkoutRemoteDataSourceImpl> {
  private final Provider<FirebaseManager> firebaseManagerProvider;

  public WorkoutRemoteDataSourceImpl_Factory(Provider<FirebaseManager> firebaseManagerProvider) {
    this.firebaseManagerProvider = firebaseManagerProvider;
  }

  @Override
  public WorkoutRemoteDataSourceImpl get() {
    return newInstance(firebaseManagerProvider.get());
  }

  public static WorkoutRemoteDataSourceImpl_Factory create(
      Provider<FirebaseManager> firebaseManagerProvider) {
    return new WorkoutRemoteDataSourceImpl_Factory(firebaseManagerProvider);
  }

  public static WorkoutRemoteDataSourceImpl newInstance(FirebaseManager firebaseManager) {
    return new WorkoutRemoteDataSourceImpl(firebaseManager);
  }
}
