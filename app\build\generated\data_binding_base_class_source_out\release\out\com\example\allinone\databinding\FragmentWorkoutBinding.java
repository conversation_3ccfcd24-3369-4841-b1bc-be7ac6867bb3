// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentWorkoutBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final BottomNavigationView workoutBottomNavigation;

  @NonNull
  public final FrameLayout workoutFragmentContainer;

  private FragmentWorkoutBinding(@NonNull ConstraintLayout rootView,
      @NonNull BottomNavigationView workoutBottomNavigation,
      @NonNull FrameLayout workoutFragmentContainer) {
    this.rootView = rootView;
    this.workoutBottomNavigation = workoutBottomNavigation;
    this.workoutFragmentContainer = workoutFragmentContainer;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentWorkoutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentWorkoutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_workout, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentWorkoutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.workout_bottom_navigation;
      BottomNavigationView workoutBottomNavigation = ViewBindings.findChildViewById(rootView, id);
      if (workoutBottomNavigation == null) {
        break missingId;
      }

      id = R.id.workout_fragment_container;
      FrameLayout workoutFragmentContainer = ViewBindings.findChildViewById(rootView, id);
      if (workoutFragmentContainer == null) {
        break missingId;
      }

      return new FragmentWorkoutBinding((ConstraintLayout) rootView, workoutBottomNavigation,
          workoutFragmentContainer);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
