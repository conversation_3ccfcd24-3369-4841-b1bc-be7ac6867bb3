<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_chat_ai" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_chat_ai.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/item_chat_ai_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="144" endOffset="51"/></Target><Target id="@+id/avatarAI" view="TextView"><Expressions/><location startLine="9" startOffset="4" endLine="19" endOffset="51"/></Target><Target id="@+id/cardAIMessage" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="21" startOffset="4" endLine="115" endOffset="55"/></Target><Target id="@+id/textAIMessage" view="TextView"><Expressions/><location startLine="39" startOffset="12" endLine="46" endOffset="127"/></Target><Target id="@+id/layoutConfidence" view="LinearLayout"><Expressions/><location startLine="49" startOffset="12" endLine="73" endOffset="26"/></Target><Target id="@+id/textConfidence" view="TextView"><Expressions/><location startLine="65" startOffset="16" endLine="72" endOffset="38"/></Target><Target id="@+id/layoutSources" view="LinearLayout"><Expressions/><location startLine="76" startOffset="12" endLine="101" endOffset="26"/></Target><Target id="@+id/recyclerSources" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="93" startOffset="16" endLine="100" endOffset="63"/></Target><Target id="@+id/textAITimestamp" view="TextView"><Expressions/><location startLine="104" startOffset="12" endLine="113" endOffset="38"/></Target><Target id="@+id/layoutTyping" view="LinearLayout"><Expressions/><location startLine="118" startOffset="4" endLine="142" endOffset="18"/></Target></Targets></Layout>