package com.example.allinone.di;

import com.example.allinone.data.local.dao.CachedWorkoutDao;
import com.example.allinone.feature.workout.data.datasource.WorkoutLocalDataSource;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class AppModule_ProvideWorkoutLocalDataSourceFactory implements Factory<WorkoutLocalDataSource> {
  private final Provider<CachedWorkoutDao> workoutDaoProvider;

  public AppModule_ProvideWorkoutLocalDataSourceFactory(
      Provider<CachedWorkoutDao> workoutDaoProvider) {
    this.workoutDaoProvider = workoutDaoProvider;
  }

  @Override
  public WorkoutLocalDataSource get() {
    return provideWorkoutLocalDataSource(workoutDaoProvider.get());
  }

  public static AppModule_ProvideWorkoutLocalDataSourceFactory create(
      Provider<CachedWorkoutDao> workoutDaoProvider) {
    return new AppModule_ProvideWorkoutLocalDataSourceFactory(workoutDaoProvider);
  }

  public static WorkoutLocalDataSource provideWorkoutLocalDataSource(CachedWorkoutDao workoutDao) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideWorkoutLocalDataSource(workoutDao));
  }
}
