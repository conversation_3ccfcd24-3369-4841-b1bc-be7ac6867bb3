// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentActiveWorkoutBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final RecyclerView exercisesRecyclerView;

  @NonNull
  public final TextView exercisesTitle;

  @NonNull
  public final MaterialButton playPauseButton;

  @NonNull
  public final MaterialButton stopButton;

  @NonNull
  public final MaterialCardView timerCard;

  @NonNull
  public final TextView timerMsText;

  @NonNull
  public final TextView timerText;

  @NonNull
  public final TextView workoutNameTitle;

  private FragmentActiveWorkoutBinding(@NonNull ConstraintLayout rootView,
      @NonNull RecyclerView exercisesRecyclerView, @NonNull TextView exercisesTitle,
      @NonNull MaterialButton playPauseButton, @NonNull MaterialButton stopButton,
      @NonNull MaterialCardView timerCard, @NonNull TextView timerMsText,
      @NonNull TextView timerText, @NonNull TextView workoutNameTitle) {
    this.rootView = rootView;
    this.exercisesRecyclerView = exercisesRecyclerView;
    this.exercisesTitle = exercisesTitle;
    this.playPauseButton = playPauseButton;
    this.stopButton = stopButton;
    this.timerCard = timerCard;
    this.timerMsText = timerMsText;
    this.timerText = timerText;
    this.workoutNameTitle = workoutNameTitle;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentActiveWorkoutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentActiveWorkoutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_active_workout, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentActiveWorkoutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.exercises_recycler_view;
      RecyclerView exercisesRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (exercisesRecyclerView == null) {
        break missingId;
      }

      id = R.id.exercises_title;
      TextView exercisesTitle = ViewBindings.findChildViewById(rootView, id);
      if (exercisesTitle == null) {
        break missingId;
      }

      id = R.id.play_pause_button;
      MaterialButton playPauseButton = ViewBindings.findChildViewById(rootView, id);
      if (playPauseButton == null) {
        break missingId;
      }

      id = R.id.stop_button;
      MaterialButton stopButton = ViewBindings.findChildViewById(rootView, id);
      if (stopButton == null) {
        break missingId;
      }

      id = R.id.timer_card;
      MaterialCardView timerCard = ViewBindings.findChildViewById(rootView, id);
      if (timerCard == null) {
        break missingId;
      }

      id = R.id.timer_ms_text;
      TextView timerMsText = ViewBindings.findChildViewById(rootView, id);
      if (timerMsText == null) {
        break missingId;
      }

      id = R.id.timer_text;
      TextView timerText = ViewBindings.findChildViewById(rootView, id);
      if (timerText == null) {
        break missingId;
      }

      id = R.id.workout_name_title;
      TextView workoutNameTitle = ViewBindings.findChildViewById(rootView, id);
      if (workoutNameTitle == null) {
        break missingId;
      }

      return new FragmentActiveWorkoutBinding((ConstraintLayout) rootView, exercisesRecyclerView,
          exercisesTitle, playPauseButton, stopButton, timerCard, timerMsText, timerText,
          workoutNameTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
