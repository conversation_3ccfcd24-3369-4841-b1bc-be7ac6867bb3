<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_wt_student" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_wt_student.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_wt_student_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="77" endOffset="51"/></Target><Target id="@+id/profileImage" view="com.google.android.material.imageview.ShapeableImageView"><Expressions/><location startLine="15" startOffset="8" endLine="25" endOffset="65"/></Target><Target id="@+id/studentName" view="TextView"><Expressions/><location startLine="27" startOffset="8" endLine="38" endOffset="35"/></Target><Target id="@+id/phoneNumber" view="TextView"><Expressions/><location startLine="40" startOffset="8" endLine="50" endOffset="43"/></Target><Target id="@+id/email" view="TextView"><Expressions/><location startLine="52" startOffset="8" endLine="63" endOffset="40"/></Target><Target id="@+id/statusIndicator" view="View"><Expressions/><location startLine="65" startOffset="8" endLine="73" endOffset="61"/></Target></Targets></Layout>