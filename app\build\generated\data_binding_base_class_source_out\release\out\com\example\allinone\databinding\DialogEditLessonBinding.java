// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogEditLessonBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView dayText;

  @NonNull
  public final EditText endTimeField;

  @NonNull
  public final EditText startTimeField;

  private DialogEditLessonBinding(@NonNull LinearLayout rootView, @NonNull TextView dayText,
      @NonNull EditText endTimeField, @NonNull EditText startTimeField) {
    this.rootView = rootView;
    this.dayText = dayText;
    this.endTimeField = endTimeField;
    this.startTimeField = startTimeField;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogEditLessonBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogEditLessonBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_edit_lesson, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogEditLessonBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.dayText;
      TextView dayText = ViewBindings.findChildViewById(rootView, id);
      if (dayText == null) {
        break missingId;
      }

      id = R.id.endTimeField;
      EditText endTimeField = ViewBindings.findChildViewById(rootView, id);
      if (endTimeField == null) {
        break missingId;
      }

      id = R.id.startTimeField;
      EditText startTimeField = ViewBindings.findChildViewById(rootView, id);
      if (startTimeField == null) {
        break missingId;
      }

      return new DialogEditLessonBinding((LinearLayout) rootView, dayText, endTimeField,
          startTimeField);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
