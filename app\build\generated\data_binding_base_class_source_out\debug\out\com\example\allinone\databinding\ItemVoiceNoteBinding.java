// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemVoiceNoteBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageButton deleteVoiceNoteButton;

  @NonNull
  public final ImageButton playPauseButton;

  @NonNull
  public final TextView timeSlash;

  @NonNull
  public final TextView voiceNoteCurrentTime;

  @NonNull
  public final TextView voiceNoteDuration;

  @NonNull
  public final TextView voiceNoteTitle;

  private ItemVoiceNoteBinding(@NonNull MaterialCardView rootView,
      @NonNull ImageButton deleteVoiceNoteButton, @NonNull ImageButton playPauseButton,
      @NonNull TextView timeSlash, @NonNull TextView voiceNoteCurrentTime,
      @NonNull TextView voiceNoteDuration, @NonNull TextView voiceNoteTitle) {
    this.rootView = rootView;
    this.deleteVoiceNoteButton = deleteVoiceNoteButton;
    this.playPauseButton = playPauseButton;
    this.timeSlash = timeSlash;
    this.voiceNoteCurrentTime = voiceNoteCurrentTime;
    this.voiceNoteDuration = voiceNoteDuration;
    this.voiceNoteTitle = voiceNoteTitle;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemVoiceNoteBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemVoiceNoteBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_voice_note, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemVoiceNoteBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.deleteVoiceNoteButton;
      ImageButton deleteVoiceNoteButton = ViewBindings.findChildViewById(rootView, id);
      if (deleteVoiceNoteButton == null) {
        break missingId;
      }

      id = R.id.playPauseButton;
      ImageButton playPauseButton = ViewBindings.findChildViewById(rootView, id);
      if (playPauseButton == null) {
        break missingId;
      }

      id = R.id.timeSlash;
      TextView timeSlash = ViewBindings.findChildViewById(rootView, id);
      if (timeSlash == null) {
        break missingId;
      }

      id = R.id.voiceNoteCurrentTime;
      TextView voiceNoteCurrentTime = ViewBindings.findChildViewById(rootView, id);
      if (voiceNoteCurrentTime == null) {
        break missingId;
      }

      id = R.id.voiceNoteDuration;
      TextView voiceNoteDuration = ViewBindings.findChildViewById(rootView, id);
      if (voiceNoteDuration == null) {
        break missingId;
      }

      id = R.id.voiceNoteTitle;
      TextView voiceNoteTitle = ViewBindings.findChildViewById(rootView, id);
      if (voiceNoteTitle == null) {
        break missingId;
      }

      return new ItemVoiceNoteBinding((MaterialCardView) rootView, deleteVoiceNoteButton,
          playPauseButton, timeSlash, voiceNoteCurrentTime, voiceNoteDuration, voiceNoteTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
