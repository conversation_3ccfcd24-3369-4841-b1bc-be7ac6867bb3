// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemBackupBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final TextView backupName;

  @NonNull
  public final TextView backupSize;

  @NonNull
  public final Button deleteButton;

  @NonNull
  public final Button restoreButton;

  @NonNull
  public final Button shareButton;

  private ItemBackupBinding(@NonNull CardView rootView, @NonNull TextView backupName,
      @NonNull TextView backupSize, @NonNull Button deleteButton, @NonNull Button restoreButton,
      @NonNull Button shareButton) {
    this.rootView = rootView;
    this.backupName = backupName;
    this.backupSize = backupSize;
    this.deleteButton = deleteButton;
    this.restoreButton = restoreButton;
    this.shareButton = shareButton;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemBackupBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemBackupBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_backup, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemBackupBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.backup_name;
      TextView backupName = ViewBindings.findChildViewById(rootView, id);
      if (backupName == null) {
        break missingId;
      }

      id = R.id.backup_size;
      TextView backupSize = ViewBindings.findChildViewById(rootView, id);
      if (backupSize == null) {
        break missingId;
      }

      id = R.id.delete_button;
      Button deleteButton = ViewBindings.findChildViewById(rootView, id);
      if (deleteButton == null) {
        break missingId;
      }

      id = R.id.restore_button;
      Button restoreButton = ViewBindings.findChildViewById(rootView, id);
      if (restoreButton == null) {
        break missingId;
      }

      id = R.id.share_button;
      Button shareButton = ViewBindings.findChildViewById(rootView, id);
      if (shareButton == null) {
        break missingId;
      }

      return new ItemBackupBinding((CardView) rootView, backupName, backupSize, deleteButton,
          restoreButton, shareButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
