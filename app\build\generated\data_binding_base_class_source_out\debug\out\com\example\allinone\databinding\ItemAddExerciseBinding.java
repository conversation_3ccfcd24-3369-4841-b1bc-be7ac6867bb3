// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemAddExerciseBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextInputEditText exerciseNameInput;

  @NonNull
  public final TextInputEditText exerciseNotesInput;

  @NonNull
  public final TextInputEditText exerciseRepsInput;

  @NonNull
  public final TextInputEditText exerciseSetsInput;

  @NonNull
  public final TextInputEditText exerciseWeightInput;

  @NonNull
  public final AutoCompleteTextView muscleGroupDropdown;

  @NonNull
  public final ImageButton removeExerciseButton;

  private ItemAddExerciseBinding(@NonNull LinearLayout rootView,
      @NonNull TextInputEditText exerciseNameInput, @NonNull TextInputEditText exerciseNotesInput,
      @NonNull TextInputEditText exerciseRepsInput, @NonNull TextInputEditText exerciseSetsInput,
      @NonNull TextInputEditText exerciseWeightInput,
      @NonNull AutoCompleteTextView muscleGroupDropdown,
      @NonNull ImageButton removeExerciseButton) {
    this.rootView = rootView;
    this.exerciseNameInput = exerciseNameInput;
    this.exerciseNotesInput = exerciseNotesInput;
    this.exerciseRepsInput = exerciseRepsInput;
    this.exerciseSetsInput = exerciseSetsInput;
    this.exerciseWeightInput = exerciseWeightInput;
    this.muscleGroupDropdown = muscleGroupDropdown;
    this.removeExerciseButton = removeExerciseButton;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemAddExerciseBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemAddExerciseBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_add_exercise, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemAddExerciseBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.exercise_name_input;
      TextInputEditText exerciseNameInput = ViewBindings.findChildViewById(rootView, id);
      if (exerciseNameInput == null) {
        break missingId;
      }

      id = R.id.exercise_notes_input;
      TextInputEditText exerciseNotesInput = ViewBindings.findChildViewById(rootView, id);
      if (exerciseNotesInput == null) {
        break missingId;
      }

      id = R.id.exercise_reps_input;
      TextInputEditText exerciseRepsInput = ViewBindings.findChildViewById(rootView, id);
      if (exerciseRepsInput == null) {
        break missingId;
      }

      id = R.id.exercise_sets_input;
      TextInputEditText exerciseSetsInput = ViewBindings.findChildViewById(rootView, id);
      if (exerciseSetsInput == null) {
        break missingId;
      }

      id = R.id.exercise_weight_input;
      TextInputEditText exerciseWeightInput = ViewBindings.findChildViewById(rootView, id);
      if (exerciseWeightInput == null) {
        break missingId;
      }

      id = R.id.muscle_group_dropdown;
      AutoCompleteTextView muscleGroupDropdown = ViewBindings.findChildViewById(rootView, id);
      if (muscleGroupDropdown == null) {
        break missingId;
      }

      id = R.id.remove_exercise_button;
      ImageButton removeExerciseButton = ViewBindings.findChildViewById(rootView, id);
      if (removeExerciseButton == null) {
        break missingId;
      }

      return new ItemAddExerciseBinding((LinearLayout) rootView, exerciseNameInput,
          exerciseNotesInput, exerciseRepsInput, exerciseSetsInput, exerciseWeightInput,
          muscleGroupDropdown, removeExerciseButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
