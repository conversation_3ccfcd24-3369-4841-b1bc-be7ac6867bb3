<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Loading Indicator -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone" />

        <!-- Header -->
        <TextView
            android:id="@+id/textInsightsTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="📊 Instagram Analytics"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:layout_marginBottom="16dp" />

        <!-- Overview Cards Section -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="📈 Overview"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:layout_marginBottom="8dp" />

        <GridLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:columnCount="2"
            android:rowCount="2"
            android:layout_marginBottom="24dp">

            <!-- Total Posts Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_columnWeight="1"
                android:layout_margin="4dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/textTotalPosts"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/excellent_green" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Total Posts"
                        android:textSize="12sp"
                        android:alpha="0.7" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Total Engagement Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_columnWeight="1"
                android:layout_margin="4dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/textTotalEngagement"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/video_blue" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Total Engagement"
                        android:textSize="12sp"
                        android:alpha="0.7" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Avg Engagement Rate Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_columnWeight="1"
                android:layout_margin="4dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/textAvgEngagementRate"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0.0%"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/good_orange" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Avg Engagement Rate"
                        android:textSize="12sp"
                        android:alpha="0.7" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Total Reach Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_columnWeight="1"
                android:layout_margin="4dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/textTotalReach"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/reels_purple" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Total Reach"
                        android:textSize="12sp"
                        android:alpha="0.7" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
        </GridLayout>

        <!-- Performance Indicators Section -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🎯 Performance Indicators"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:layout_marginBottom="8dp" />

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Engagement Trend -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="📈 Engagement Trend"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/textEngagementTrend"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0.0%"
                        android:textStyle="bold"
                        tools:text="+20.16%" />
                </LinearLayout>

                <!-- Reach Trend -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="👥 Reach Trend"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/textReachTrend"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0.0%"
                        android:textStyle="bold"
                        tools:text="-3.09%" />
                </LinearLayout>

                <!-- Consistency Score -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="🎯 Consistency Score"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/textConsistencyScore"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0.0%"
                        android:textStyle="bold"
                        tools:text="83.2%" />
                </LinearLayout>

                <!-- Growth Potential -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="🚀 Growth Potential"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/textGrowthPotential"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0.0%"
                        android:textStyle="bold"
                        tools:text="44.9%" />
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Top Performing Post Section -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🏆 Top Performing Post"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:layout_marginBottom="8dp" />

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardTopPost"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:id="@+id/textTopPostCaption"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="No top post available"
                    android:textStyle="bold"
                    android:maxLines="3"
                    android:ellipsize="end" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="8dp">

                    <TextView
                        android:id="@+id/textTopPostEngagement"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="📈 0.0%"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/textTopPostInteractions"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="📊 0"
                        android:textSize="12sp" />
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Content Analysis Section -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="📱 Content Analysis"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:layout_marginBottom="8dp" />

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Videos vs Images -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Content Type Breakdown:"
                    android:textStyle="bold"
                    android:textColor="@android:color/black"
                    android:layout_marginBottom="8dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="4dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="🎬 Videos"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/textVideoCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 (0%)"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/textVideoEngagement"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0.0%"
                        android:textSize="12sp"
                        android:layout_marginStart="8dp"
                        android:alpha="0.7" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="📷 Images"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/textImageCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 (0%)"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/textImageEngagement"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0.0%"
                        android:textSize="12sp"
                        android:layout_marginStart="8dp"
                        android:alpha="0.7" />
                </LinearLayout>

                <!-- Posting Frequency -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Posting Frequency:"
                    android:textStyle="bold"
                    android:textColor="@android:color/black"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/textPostingFrequency"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="• Posts per week: 0\n• Posts per month: 0\n• Avg days between posts: 0"
                    android:textSize="14sp" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Error/Empty State -->
        <TextView
            android:id="@+id/textInsightsMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Loading Instagram insights..."
            android:textSize="16sp"
            android:gravity="center"
            android:padding="16dp"
            android:visibility="gone" />

    </LinearLayout>
</ScrollView>