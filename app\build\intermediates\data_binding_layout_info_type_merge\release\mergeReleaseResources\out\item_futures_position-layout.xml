<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_futures_position" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_futures_position.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_futures_position_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="185" endOffset="35"/></Target><Target id="@+id/symbolText" view="TextView"><Expressions/><location startLine="16" startOffset="8" endLine="24" endOffset="34"/></Target><Target id="@+id/positionSideText" view="TextView"><Expressions/><location startLine="26" startOffset="8" endLine="41" endOffset="31"/></Target><Target id="@+id/pnlText" view="TextView"><Expressions/><location startLine="43" startOffset="8" endLine="51" endOffset="35"/></Target><Target id="@+id/positionAmtLabelText" view="TextView"><Expressions/><location startLine="53" startOffset="8" endLine="61" endOffset="66"/></Target><Target id="@+id/positionAmtText" view="TextView"><Expressions/><location startLine="63" startOffset="8" endLine="71" endOffset="35"/></Target><Target id="@+id/leverageLabelText" view="TextView"><Expressions/><location startLine="73" startOffset="8" endLine="81" endOffset="73"/></Target><Target id="@+id/leverageText" view="TextView"><Expressions/><location startLine="83" startOffset="8" endLine="91" endOffset="30"/></Target><Target id="@+id/entryPriceLabelText" view="TextView"><Expressions/><location startLine="93" startOffset="8" endLine="101" endOffset="76"/></Target><Target id="@+id/entryPriceText" view="TextView"><Expressions/><location startLine="103" startOffset="8" endLine="111" endOffset="37"/></Target><Target id="@+id/markPriceLabelText" view="TextView"><Expressions/><location startLine="113" startOffset="8" endLine="121" endOffset="75"/></Target><Target id="@+id/markPriceText" view="TextView"><Expressions/><location startLine="123" startOffset="8" endLine="131" endOffset="39"/></Target><Target id="@+id/liquidationPriceLabelText" view="TextView"><Expressions/><location startLine="133" startOffset="8" endLine="141" endOffset="74"/></Target><Target id="@+id/liquidationPriceText" view="TextView"><Expressions/><location startLine="143" startOffset="8" endLine="151" endOffset="37"/></Target><Target id="@+id/marginTypeLabelText" view="TextView"><Expressions/><location startLine="153" startOffset="8" endLine="161" endOffset="78"/></Target><Target id="@+id/marginTypeText" view="TextView"><Expressions/><location startLine="163" startOffset="8" endLine="171" endOffset="35"/></Target><Target id="@+id/tpSlText" view="TextView"><Expressions/><location startLine="173" startOffset="8" endLine="182" endOffset="48"/></Target></Targets></Layout>