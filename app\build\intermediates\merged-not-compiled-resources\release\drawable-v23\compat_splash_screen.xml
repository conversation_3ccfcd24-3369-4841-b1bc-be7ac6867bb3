<?xml version="1.0" encoding="utf-8"?><!--
  Copyright 2021 The Android Open Source Project

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->

<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:gravity="fill">
        <color android:color="?attr/windowSplashScreenBackground" />
    </item>
    <item
        android:gravity="center"
        android:width="@dimen/splashscreen_icon_size_with_background"
        android:height="@dimen/splashscreen_icon_size_with_background"
        android:drawable="@drawable/icon_background">
    </item>
    <item
        android:drawable="?windowSplashScreenAnimatedIcon"
        android:gravity="center"
        android:width="@dimen/splashscreen_icon_size_with_background"
        android:height="@dimen/splashscreen_icon_size_with_background" />

    <!-- We mask the outer bounds of the icon like we do on Android 12 -->
    <item
        android:gravity="center"
        android:width="@dimen/splashscreen_icon_mask_size_with_background"
        android:height="@dimen/splashscreen_icon_mask_size_with_background">
        <shape android:shape="oval">
            <solid android:color="@android:color/transparent" />
            <stroke
                android:width="@dimen/splashscreen_icon_mask_stroke_with_background"
                android:color="?windowSplashScreenBackground" />
        </shape>
    </item>
</layer-list>