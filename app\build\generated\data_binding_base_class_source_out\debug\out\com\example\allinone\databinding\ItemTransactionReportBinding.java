// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTransactionReportBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView categoryChip;

  @NonNull
  public final View categoryColorIndicator;

  @NonNull
  public final TextView transactionAmount;

  @NonNull
  public final TextView transactionDate;

  @NonNull
  public final TextView transactionDescription;

  @NonNull
  public final TextView transactionTitle;

  private ItemTransactionReportBinding(@NonNull MaterialCardView rootView,
      @NonNull TextView categoryChip, @NonNull View categoryColorIndicator,
      @NonNull TextView transactionAmount, @NonNull TextView transactionDate,
      @NonNull TextView transactionDescription, @NonNull TextView transactionTitle) {
    this.rootView = rootView;
    this.categoryChip = categoryChip;
    this.categoryColorIndicator = categoryColorIndicator;
    this.transactionAmount = transactionAmount;
    this.transactionDate = transactionDate;
    this.transactionDescription = transactionDescription;
    this.transactionTitle = transactionTitle;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTransactionReportBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTransactionReportBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_transaction_report, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTransactionReportBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.categoryChip;
      TextView categoryChip = ViewBindings.findChildViewById(rootView, id);
      if (categoryChip == null) {
        break missingId;
      }

      id = R.id.categoryColorIndicator;
      View categoryColorIndicator = ViewBindings.findChildViewById(rootView, id);
      if (categoryColorIndicator == null) {
        break missingId;
      }

      id = R.id.transactionAmount;
      TextView transactionAmount = ViewBindings.findChildViewById(rootView, id);
      if (transactionAmount == null) {
        break missingId;
      }

      id = R.id.transactionDate;
      TextView transactionDate = ViewBindings.findChildViewById(rootView, id);
      if (transactionDate == null) {
        break missingId;
      }

      id = R.id.transactionDescription;
      TextView transactionDescription = ViewBindings.findChildViewById(rootView, id);
      if (transactionDescription == null) {
        break missingId;
      }

      id = R.id.transactionTitle;
      TextView transactionTitle = ViewBindings.findChildViewById(rootView, id);
      if (transactionTitle == null) {
        break missingId;
      }

      return new ItemTransactionReportBinding((MaterialCardView) rootView, categoryChip,
          categoryColorIndicator, transactionAmount, transactionDate, transactionDescription,
          transactionTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
