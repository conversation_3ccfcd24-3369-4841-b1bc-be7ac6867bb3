<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_lesson" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_lesson.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_lesson_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="59" endOffset="35"/></Target><Target id="@+id/dayText" view="TextView"><Expressions/><location startLine="22" startOffset="12" endLine="28" endOffset="39"/></Target><Target id="@+id/timeText" view="TextView"><Expressions/><location startLine="30" startOffset="12" endLine="35" endOffset="41"/></Target><Target id="@+id/editButton" view="ImageButton"><Expressions/><location startLine="38" startOffset="8" endLine="45" endOffset="58"/></Target><Target id="@+id/deleteButton" view="ImageButton"><Expressions/><location startLine="47" startOffset="8" endLine="56" endOffset="32"/></Target></Targets></Layout>