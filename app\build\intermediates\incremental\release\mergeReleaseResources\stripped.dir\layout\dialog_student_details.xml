<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/profileImageView"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="8dp"
        android:contentDescription="@string/profile_image"
        android:scaleType="centerCrop"
        android:src="@drawable/default_profile"
        app:shapeAppearanceOverlay="@style/circleImageView" />

    <TextView
        android:id="@+id/nameTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        android:textSize="20sp"
        style="@style/BoldText"
        android:textColor="@android:color/black" />

    <TextView
        android:id="@+id/detailsTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="16sp"
        android:lineSpacingExtra="4dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:orientation="horizontal">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/callButton"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginEnd="16dp"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:insetLeft="0dp"
            android:insetTop="0dp"
            android:insetRight="0dp"
            android:insetBottom="0dp"
            android:padding="0dp"
            app:icon="@drawable/ic_call"
            app:iconGravity="textStart"
            app:iconPadding="0dp"
            app:iconSize="36dp"
            app:iconTint="?attr/colorPrimary" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/whatsappButton"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginEnd="16dp"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:insetLeft="0dp"
            android:insetTop="0dp"
            android:insetRight="0dp"
            android:insetBottom="0dp"
            android:padding="0dp"
            app:icon="@drawable/ic_whatsapp"
            app:iconGravity="textStart"
            app:iconPadding="0dp"
            app:iconSize="40dp"
            app:iconTint="@null" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/instagramButton"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:insetLeft="0dp"
            android:insetTop="0dp"
            android:insetRight="0dp"
            android:insetBottom="0dp"
            android:padding="0dp"
            android:visibility="gone"
            app:icon="@drawable/ic_instagram"
            app:iconGravity="textStart"
            app:iconPadding="0dp"
            app:iconSize="40dp"
            app:iconTint="@null" />

    </LinearLayout>
</LinearLayout> 