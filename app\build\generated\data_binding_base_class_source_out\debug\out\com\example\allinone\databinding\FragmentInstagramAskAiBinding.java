// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentInstagramAskAiBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnAttachments;

  @NonNull
  public final ImageButton btnRemoveAttachment;

  @NonNull
  public final MaterialButton btnSendQuestion;

  @NonNull
  public final ImageButton btnStopRecording;

  @NonNull
  public final MaterialCardView cardAttachmentOptions;

  @NonNull
  public final MaterialCardView cardAudioUpload;

  @NonNull
  public final MaterialCardView cardImageUpload;

  @NonNull
  public final MaterialCardView cardPDFUpload;

  @NonNull
  public final MaterialCardView cardSuggestedQuestions;

  @NonNull
  public final MaterialCardView cardURLAnalysis;

  @NonNull
  public final MaterialCardView cardVoiceRecord;

  @NonNull
  public final Chip chipBestPosts;

  @NonNull
  public final Chip chipContent;

  @NonNull
  public final ChipGroup chipGroupSuggestions;

  @NonNull
  public final Chip chipHashtags;

  @NonNull
  public final Chip chipImprove;

  @NonNull
  public final EditText editTextQuestion;

  @NonNull
  public final LinearLayout emptyState;

  @NonNull
  public final ImageView imgAttachmentIcon;

  @NonNull
  public final MaterialCardView inputCard;

  @NonNull
  public final LinearLayout layoutAttachmentPreview;

  @NonNull
  public final LinearLayout layoutAudioRecording;

  @NonNull
  public final LinearLayout layoutMainInput;

  @NonNull
  public final ConstraintLayout loadingOverlay;

  @NonNull
  public final ProgressBar progressAudioWave;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerChatMessages;

  @NonNull
  public final TextView textAITitle;

  @NonNull
  public final TextView textAttachmentName;

  @NonNull
  public final TextView textRecordingDuration;

  private FragmentInstagramAskAiBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnAttachments, @NonNull ImageButton btnRemoveAttachment,
      @NonNull MaterialButton btnSendQuestion, @NonNull ImageButton btnStopRecording,
      @NonNull MaterialCardView cardAttachmentOptions, @NonNull MaterialCardView cardAudioUpload,
      @NonNull MaterialCardView cardImageUpload, @NonNull MaterialCardView cardPDFUpload,
      @NonNull MaterialCardView cardSuggestedQuestions, @NonNull MaterialCardView cardURLAnalysis,
      @NonNull MaterialCardView cardVoiceRecord, @NonNull Chip chipBestPosts,
      @NonNull Chip chipContent, @NonNull ChipGroup chipGroupSuggestions,
      @NonNull Chip chipHashtags, @NonNull Chip chipImprove, @NonNull EditText editTextQuestion,
      @NonNull LinearLayout emptyState, @NonNull ImageView imgAttachmentIcon,
      @NonNull MaterialCardView inputCard, @NonNull LinearLayout layoutAttachmentPreview,
      @NonNull LinearLayout layoutAudioRecording, @NonNull LinearLayout layoutMainInput,
      @NonNull ConstraintLayout loadingOverlay, @NonNull ProgressBar progressAudioWave,
      @NonNull ProgressBar progressBar, @NonNull RecyclerView recyclerChatMessages,
      @NonNull TextView textAITitle, @NonNull TextView textAttachmentName,
      @NonNull TextView textRecordingDuration) {
    this.rootView = rootView;
    this.btnAttachments = btnAttachments;
    this.btnRemoveAttachment = btnRemoveAttachment;
    this.btnSendQuestion = btnSendQuestion;
    this.btnStopRecording = btnStopRecording;
    this.cardAttachmentOptions = cardAttachmentOptions;
    this.cardAudioUpload = cardAudioUpload;
    this.cardImageUpload = cardImageUpload;
    this.cardPDFUpload = cardPDFUpload;
    this.cardSuggestedQuestions = cardSuggestedQuestions;
    this.cardURLAnalysis = cardURLAnalysis;
    this.cardVoiceRecord = cardVoiceRecord;
    this.chipBestPosts = chipBestPosts;
    this.chipContent = chipContent;
    this.chipGroupSuggestions = chipGroupSuggestions;
    this.chipHashtags = chipHashtags;
    this.chipImprove = chipImprove;
    this.editTextQuestion = editTextQuestion;
    this.emptyState = emptyState;
    this.imgAttachmentIcon = imgAttachmentIcon;
    this.inputCard = inputCard;
    this.layoutAttachmentPreview = layoutAttachmentPreview;
    this.layoutAudioRecording = layoutAudioRecording;
    this.layoutMainInput = layoutMainInput;
    this.loadingOverlay = loadingOverlay;
    this.progressAudioWave = progressAudioWave;
    this.progressBar = progressBar;
    this.recyclerChatMessages = recyclerChatMessages;
    this.textAITitle = textAITitle;
    this.textAttachmentName = textAttachmentName;
    this.textRecordingDuration = textRecordingDuration;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentInstagramAskAiBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentInstagramAskAiBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_instagram_ask_ai, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentInstagramAskAiBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnAttachments;
      MaterialButton btnAttachments = ViewBindings.findChildViewById(rootView, id);
      if (btnAttachments == null) {
        break missingId;
      }

      id = R.id.btnRemoveAttachment;
      ImageButton btnRemoveAttachment = ViewBindings.findChildViewById(rootView, id);
      if (btnRemoveAttachment == null) {
        break missingId;
      }

      id = R.id.btnSendQuestion;
      MaterialButton btnSendQuestion = ViewBindings.findChildViewById(rootView, id);
      if (btnSendQuestion == null) {
        break missingId;
      }

      id = R.id.btnStopRecording;
      ImageButton btnStopRecording = ViewBindings.findChildViewById(rootView, id);
      if (btnStopRecording == null) {
        break missingId;
      }

      id = R.id.cardAttachmentOptions;
      MaterialCardView cardAttachmentOptions = ViewBindings.findChildViewById(rootView, id);
      if (cardAttachmentOptions == null) {
        break missingId;
      }

      id = R.id.cardAudioUpload;
      MaterialCardView cardAudioUpload = ViewBindings.findChildViewById(rootView, id);
      if (cardAudioUpload == null) {
        break missingId;
      }

      id = R.id.cardImageUpload;
      MaterialCardView cardImageUpload = ViewBindings.findChildViewById(rootView, id);
      if (cardImageUpload == null) {
        break missingId;
      }

      id = R.id.cardPDFUpload;
      MaterialCardView cardPDFUpload = ViewBindings.findChildViewById(rootView, id);
      if (cardPDFUpload == null) {
        break missingId;
      }

      id = R.id.cardSuggestedQuestions;
      MaterialCardView cardSuggestedQuestions = ViewBindings.findChildViewById(rootView, id);
      if (cardSuggestedQuestions == null) {
        break missingId;
      }

      id = R.id.cardURLAnalysis;
      MaterialCardView cardURLAnalysis = ViewBindings.findChildViewById(rootView, id);
      if (cardURLAnalysis == null) {
        break missingId;
      }

      id = R.id.cardVoiceRecord;
      MaterialCardView cardVoiceRecord = ViewBindings.findChildViewById(rootView, id);
      if (cardVoiceRecord == null) {
        break missingId;
      }

      id = R.id.chipBestPosts;
      Chip chipBestPosts = ViewBindings.findChildViewById(rootView, id);
      if (chipBestPosts == null) {
        break missingId;
      }

      id = R.id.chipContent;
      Chip chipContent = ViewBindings.findChildViewById(rootView, id);
      if (chipContent == null) {
        break missingId;
      }

      id = R.id.chipGroupSuggestions;
      ChipGroup chipGroupSuggestions = ViewBindings.findChildViewById(rootView, id);
      if (chipGroupSuggestions == null) {
        break missingId;
      }

      id = R.id.chipHashtags;
      Chip chipHashtags = ViewBindings.findChildViewById(rootView, id);
      if (chipHashtags == null) {
        break missingId;
      }

      id = R.id.chipImprove;
      Chip chipImprove = ViewBindings.findChildViewById(rootView, id);
      if (chipImprove == null) {
        break missingId;
      }

      id = R.id.editTextQuestion;
      EditText editTextQuestion = ViewBindings.findChildViewById(rootView, id);
      if (editTextQuestion == null) {
        break missingId;
      }

      id = R.id.emptyState;
      LinearLayout emptyState = ViewBindings.findChildViewById(rootView, id);
      if (emptyState == null) {
        break missingId;
      }

      id = R.id.imgAttachmentIcon;
      ImageView imgAttachmentIcon = ViewBindings.findChildViewById(rootView, id);
      if (imgAttachmentIcon == null) {
        break missingId;
      }

      id = R.id.inputCard;
      MaterialCardView inputCard = ViewBindings.findChildViewById(rootView, id);
      if (inputCard == null) {
        break missingId;
      }

      id = R.id.layoutAttachmentPreview;
      LinearLayout layoutAttachmentPreview = ViewBindings.findChildViewById(rootView, id);
      if (layoutAttachmentPreview == null) {
        break missingId;
      }

      id = R.id.layoutAudioRecording;
      LinearLayout layoutAudioRecording = ViewBindings.findChildViewById(rootView, id);
      if (layoutAudioRecording == null) {
        break missingId;
      }

      id = R.id.layoutMainInput;
      LinearLayout layoutMainInput = ViewBindings.findChildViewById(rootView, id);
      if (layoutMainInput == null) {
        break missingId;
      }

      id = R.id.loadingOverlay;
      ConstraintLayout loadingOverlay = ViewBindings.findChildViewById(rootView, id);
      if (loadingOverlay == null) {
        break missingId;
      }

      id = R.id.progressAudioWave;
      ProgressBar progressAudioWave = ViewBindings.findChildViewById(rootView, id);
      if (progressAudioWave == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recyclerChatMessages;
      RecyclerView recyclerChatMessages = ViewBindings.findChildViewById(rootView, id);
      if (recyclerChatMessages == null) {
        break missingId;
      }

      id = R.id.textAITitle;
      TextView textAITitle = ViewBindings.findChildViewById(rootView, id);
      if (textAITitle == null) {
        break missingId;
      }

      id = R.id.textAttachmentName;
      TextView textAttachmentName = ViewBindings.findChildViewById(rootView, id);
      if (textAttachmentName == null) {
        break missingId;
      }

      id = R.id.textRecordingDuration;
      TextView textRecordingDuration = ViewBindings.findChildViewById(rootView, id);
      if (textRecordingDuration == null) {
        break missingId;
      }

      return new FragmentInstagramAskAiBinding((ConstraintLayout) rootView, btnAttachments,
          btnRemoveAttachment, btnSendQuestion, btnStopRecording, cardAttachmentOptions,
          cardAudioUpload, cardImageUpload, cardPDFUpload, cardSuggestedQuestions, cardURLAnalysis,
          cardVoiceRecord, chipBestPosts, chipContent, chipGroupSuggestions, chipHashtags,
          chipImprove, editTextQuestion, emptyState, imgAttachmentIcon, inputCard,
          layoutAttachmentPreview, layoutAudioRecording, layoutMainInput, loadingOverlay,
          progressAudioWave, progressBar, recyclerChatMessages, textAITitle, textAttachmentName,
          textRecordingDuration);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
