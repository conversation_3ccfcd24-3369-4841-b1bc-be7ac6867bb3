package com.example.allinone.di;

import com.example.allinone.data.local.dao.CachedNoteDao;
import com.example.allinone.feature.notes.data.datasource.NoteLocalDataSource;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class AppModule_ProvideNoteLocalDataSourceFactory implements Factory<NoteLocalDataSource> {
  private final Provider<CachedNoteDao> noteDaoProvider;

  public AppModule_ProvideNoteLocalDataSourceFactory(Provider<CachedNoteDao> noteDaoProvider) {
    this.noteDaoProvider = noteDaoProvider;
  }

  @Override
  public NoteLocalDataSource get() {
    return provideNoteLocalDataSource(noteDaoProvider.get());
  }

  public static AppModule_ProvideNoteLocalDataSourceFactory create(
      Provider<CachedNoteDao> noteDaoProvider) {
    return new AppModule_ProvideNoteLocalDataSourceFactory(noteDaoProvider);
  }

  public static NoteLocalDataSource provideNoteLocalDataSource(CachedNoteDao noteDao) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideNoteLocalDataSource(noteDao));
  }
}
