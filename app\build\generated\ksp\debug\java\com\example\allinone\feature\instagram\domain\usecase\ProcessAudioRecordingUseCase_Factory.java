package com.example.allinone.feature.instagram.domain.usecase;

import com.example.allinone.feature.instagram.domain.repository.InstagramRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class ProcessAudioRecordingUseCase_Factory implements Factory<ProcessAudioRecordingUseCase> {
  private final Provider<InstagramRepository> repositoryProvider;

  public ProcessAudioRecordingUseCase_Factory(Provider<InstagramRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public ProcessAudioRecordingUseCase get() {
    return newInstance(repositoryProvider.get());
  }

  public static ProcessAudioRecordingUseCase_Factory create(
      Provider<InstagramRepository> repositoryProvider) {
    return new ProcessAudioRecordingUseCase_Factory(repositoryProvider);
  }

  public static ProcessAudioRecordingUseCase newInstance(InstagramRepository repository) {
    return new ProcessAudioRecordingUseCase(repository);
  }
}
