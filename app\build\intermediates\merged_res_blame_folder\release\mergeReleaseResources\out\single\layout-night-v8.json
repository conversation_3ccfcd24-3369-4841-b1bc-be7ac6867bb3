[{"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout-night-v8/activity_main.xml", "source": "com.example.allinone.app-main-112:/layout-night/activity_main.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout-night-v8/fragment_history.xml", "source": "com.example.allinone.app-main-112:/layout-night/fragment_history.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout-night-v8/fragment_wt_registry.xml", "source": "com.example.allinone.app-main-112:/layout-night/fragment_wt_registry.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout-night-v8/dialog_student_details.xml", "source": "com.example.allinone.app-main-112:/layout-night/dialog_student_details.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout-night-v8/item_history.xml", "source": "com.example.allinone.app-main-112:/layout-night/item_history.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout-night-v8/item_backup.xml", "source": "com.example.allinone.app-main-112:/layout-night/item_backup.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout-night-v8/activity_backup.xml", "source": "com.example.allinone.app-main-112:/layout-night/activity_backup.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout-night-v8/activity_edit_note.xml", "source": "com.example.allinone.app-main-112:/layout-night/activity_edit_note.xml"}]