// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAddEventBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextInputEditText eventDescriptionInput;

  @NonNull
  public final TextInputEditText eventEndTimeInput;

  @NonNull
  public final TextInputEditText eventTimeInput;

  @NonNull
  public final TextInputEditText eventTitleInput;

  private DialogAddEventBinding(@NonNull LinearLayout rootView,
      @NonNull TextInputEditText eventDescriptionInput,
      @NonNull TextInputEditText eventEndTimeInput, @NonNull TextInputEditText eventTimeInput,
      @NonNull TextInputEditText eventTitleInput) {
    this.rootView = rootView;
    this.eventDescriptionInput = eventDescriptionInput;
    this.eventEndTimeInput = eventEndTimeInput;
    this.eventTimeInput = eventTimeInput;
    this.eventTitleInput = eventTitleInput;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAddEventBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAddEventBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_add_event, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAddEventBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.eventDescriptionInput;
      TextInputEditText eventDescriptionInput = ViewBindings.findChildViewById(rootView, id);
      if (eventDescriptionInput == null) {
        break missingId;
      }

      id = R.id.eventEndTimeInput;
      TextInputEditText eventEndTimeInput = ViewBindings.findChildViewById(rootView, id);
      if (eventEndTimeInput == null) {
        break missingId;
      }

      id = R.id.eventTimeInput;
      TextInputEditText eventTimeInput = ViewBindings.findChildViewById(rootView, id);
      if (eventTimeInput == null) {
        break missingId;
      }

      id = R.id.eventTitleInput;
      TextInputEditText eventTitleInput = ViewBindings.findChildViewById(rootView, id);
      if (eventTitleInput == null) {
        break missingId;
      }

      return new DialogAddEventBinding((LinearLayout) rootView, eventDescriptionInput,
          eventEndTimeInput, eventTimeInput, eventTitleInput);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
