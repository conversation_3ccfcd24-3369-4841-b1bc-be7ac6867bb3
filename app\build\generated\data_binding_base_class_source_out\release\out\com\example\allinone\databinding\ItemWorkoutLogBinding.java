// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemWorkoutLogBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView workoutDate;

  @NonNull
  public final TextView workoutDuration;

  @NonNull
  public final TextView workoutExerciseCount;

  @NonNull
  public final TextView workoutProgramName;

  private ItemWorkoutLogBinding(@NonNull MaterialCardView rootView, @NonNull TextView workoutDate,
      @NonNull TextView workoutDuration, @NonNull TextView workoutExerciseCount,
      @NonNull TextView workoutProgramName) {
    this.rootView = rootView;
    this.workoutDate = workoutDate;
    this.workoutDuration = workoutDuration;
    this.workoutExerciseCount = workoutExerciseCount;
    this.workoutProgramName = workoutProgramName;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemWorkoutLogBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemWorkoutLogBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_workout_log, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemWorkoutLogBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.workout_date;
      TextView workoutDate = ViewBindings.findChildViewById(rootView, id);
      if (workoutDate == null) {
        break missingId;
      }

      id = R.id.workout_duration;
      TextView workoutDuration = ViewBindings.findChildViewById(rootView, id);
      if (workoutDuration == null) {
        break missingId;
      }

      id = R.id.workout_exercise_count;
      TextView workoutExerciseCount = ViewBindings.findChildViewById(rootView, id);
      if (workoutExerciseCount == null) {
        break missingId;
      }

      id = R.id.workout_program_name;
      TextView workoutProgramName = ViewBindings.findChildViewById(rootView, id);
      if (workoutProgramName == null) {
        break missingId;
      }

      return new ItemWorkoutLogBinding((MaterialCardView) rootView, workoutDate, workoutDuration,
          workoutExerciseCount, workoutProgramName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
