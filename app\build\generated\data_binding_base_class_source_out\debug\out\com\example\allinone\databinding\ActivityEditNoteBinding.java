// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import jp.wasabeef.richeditor.RichEditor;

public final class ActivityEditNoteBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final Button addAttachmentButton;

  @NonNull
  public final ImageButton addImageButton;

  @NonNull
  public final ImageButton boldButton;

  @NonNull
  public final ImageButton bulletListButton;

  @NonNull
  public final ImageButton checkboxListButton;

  @NonNull
  public final RichEditor editNoteContent;

  @NonNull
  public final TextInputEditText editNoteTitle;

  @NonNull
  public final RecyclerView imagesRecyclerView;

  @NonNull
  public final ImageButton italicButton;

  @NonNull
  public final FloatingActionButton saveFab;

  @NonNull
  public final Button shareNoteButton;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final ImageButton underlineButton;

  private ActivityEditNoteBinding(@NonNull CoordinatorLayout rootView,
      @NonNull Button addAttachmentButton, @NonNull ImageButton addImageButton,
      @NonNull ImageButton boldButton, @NonNull ImageButton bulletListButton,
      @NonNull ImageButton checkboxListButton, @NonNull RichEditor editNoteContent,
      @NonNull TextInputEditText editNoteTitle, @NonNull RecyclerView imagesRecyclerView,
      @NonNull ImageButton italicButton, @NonNull FloatingActionButton saveFab,
      @NonNull Button shareNoteButton, @NonNull Toolbar toolbar,
      @NonNull ImageButton underlineButton) {
    this.rootView = rootView;
    this.addAttachmentButton = addAttachmentButton;
    this.addImageButton = addImageButton;
    this.boldButton = boldButton;
    this.bulletListButton = bulletListButton;
    this.checkboxListButton = checkboxListButton;
    this.editNoteContent = editNoteContent;
    this.editNoteTitle = editNoteTitle;
    this.imagesRecyclerView = imagesRecyclerView;
    this.italicButton = italicButton;
    this.saveFab = saveFab;
    this.shareNoteButton = shareNoteButton;
    this.toolbar = toolbar;
    this.underlineButton = underlineButton;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityEditNoteBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityEditNoteBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_edit_note, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityEditNoteBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addAttachmentButton;
      Button addAttachmentButton = ViewBindings.findChildViewById(rootView, id);
      if (addAttachmentButton == null) {
        break missingId;
      }

      id = R.id.addImageButton;
      ImageButton addImageButton = ViewBindings.findChildViewById(rootView, id);
      if (addImageButton == null) {
        break missingId;
      }

      id = R.id.boldButton;
      ImageButton boldButton = ViewBindings.findChildViewById(rootView, id);
      if (boldButton == null) {
        break missingId;
      }

      id = R.id.bulletListButton;
      ImageButton bulletListButton = ViewBindings.findChildViewById(rootView, id);
      if (bulletListButton == null) {
        break missingId;
      }

      id = R.id.checkboxListButton;
      ImageButton checkboxListButton = ViewBindings.findChildViewById(rootView, id);
      if (checkboxListButton == null) {
        break missingId;
      }

      id = R.id.editNoteContent;
      RichEditor editNoteContent = ViewBindings.findChildViewById(rootView, id);
      if (editNoteContent == null) {
        break missingId;
      }

      id = R.id.editNoteTitle;
      TextInputEditText editNoteTitle = ViewBindings.findChildViewById(rootView, id);
      if (editNoteTitle == null) {
        break missingId;
      }

      id = R.id.imagesRecyclerView;
      RecyclerView imagesRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (imagesRecyclerView == null) {
        break missingId;
      }

      id = R.id.italicButton;
      ImageButton italicButton = ViewBindings.findChildViewById(rootView, id);
      if (italicButton == null) {
        break missingId;
      }

      id = R.id.saveFab;
      FloatingActionButton saveFab = ViewBindings.findChildViewById(rootView, id);
      if (saveFab == null) {
        break missingId;
      }

      id = R.id.shareNoteButton;
      Button shareNoteButton = ViewBindings.findChildViewById(rootView, id);
      if (shareNoteButton == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.underlineButton;
      ImageButton underlineButton = ViewBindings.findChildViewById(rootView, id);
      if (underlineButton == null) {
        break missingId;
      }

      return new ActivityEditNoteBinding((CoordinatorLayout) rootView, addAttachmentButton,
          addImageButton, boldButton, bulletListButton, checkboxListButton, editNoteContent,
          editNoteTitle, imagesRecyclerView, italicButton, saveFab, shareNoteButton, toolbar,
          underlineButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
