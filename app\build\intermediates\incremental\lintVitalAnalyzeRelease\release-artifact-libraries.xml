<libraries>
  <library
      name="androidx.databinding:databinding-runtime:8.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ab49c54b76bd63e9b51dedbdd7ffaa2\transformed\databinding-runtime-8.8.2\jars\classes.jar"
      resolved="androidx.databinding:databinding-runtime:8.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ab49c54b76bd63e9b51dedbdd7ffaa2\transformed\databinding-runtime-8.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.databinding:viewbinding:8.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69523a2d8e64b595678f05bbea41d824\transformed\viewbinding-8.8.2\jars\classes.jar"
      resolved="androidx.databinding:viewbinding:8.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69523a2d8e64b595678f05bbea41d824\transformed\viewbinding-8.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.databinding:databinding-adapters:8.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e1bad23bdb5e6cc441d58fefb38d94e\transformed\databinding-adapters-8.8.2\jars\classes.jar"
      resolved="androidx.databinding:databinding-adapters:8.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e1bad23bdb5e6cc441d58fefb38d94e\transformed\databinding-adapters-8.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.databinding:databinding-common:8.8.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.databinding\databinding-common\8.8.2\2be3530b78477835e69dce953b17adc1dbc5c6fa\databinding-common-8.8.2.jar"
      resolved="androidx.databinding:databinding-common:8.8.2"/>
  <library
      name="androidx.databinding:databinding-ktx:8.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa4ef47006e4b8b8308327162806eebe\transformed\databinding-ktx-8.8.2\jars\classes.jar"
      resolved="androidx.databinding:databinding-ktx:8.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa4ef47006e4b8b8308327162806eebe\transformed\databinding-ktx-8.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.hilt:hilt-navigation-compose:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f06757f7768abe1e62751d0ace0f97ff\transformed\hilt-navigation-compose-1.1.0\jars\classes.jar"
      resolved="androidx.hilt:hilt-navigation-compose:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f06757f7768abe1e62751d0ace0f97ff\transformed\hilt-navigation-compose-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.hilt:hilt-navigation:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\305bd98951c960dec40e72aa6b9162c1\transformed\hilt-navigation-1.1.0\jars\classes.jar"
      resolved="androidx.hilt:hilt-navigation:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\305bd98951c960dec40e72aa6b9162c1\transformed\hilt-navigation-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-common:2.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\536a5d77f69c07dc1aedc4b2fccb6e40\transformed\navigation-common-2.8.2\jars\classes.jar"
      resolved="androidx.navigation:navigation-common:2.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\536a5d77f69c07dc1aedc4b2fccb6e40\transformed\navigation-common-2.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime:2.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25819f6a6edef9ba0843eed17bb2da45\transformed\navigation-runtime-2.8.2\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime:2.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25819f6a6edef9ba0843eed17bb2da45\transformed\navigation-runtime-2.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-common-ktx:2.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\38dc6b0e8abdbadfbe760b3216d7d12d\transformed\navigation-common-ktx-2.8.2\jars\classes.jar"
      resolved="androidx.navigation:navigation-common-ktx:2.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\38dc6b0e8abdbadfbe760b3216d7d12d\transformed\navigation-common-ktx-2.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-ui:2.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e82eb3f2167345ace1b8e91ea59beec2\transformed\navigation-ui-2.8.2\jars\classes.jar"
      resolved="androidx.navigation:navigation-ui:2.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e82eb3f2167345ace1b8e91ea59beec2\transformed\navigation-ui-2.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-fragment:2.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa24054c4015fd46b5a3fc653cb09666\transformed\navigation-fragment-2.8.2\jars\classes.jar"
      resolved="androidx.navigation:navigation-fragment:2.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa24054c4015fd46b5a3fc653cb09666\transformed\navigation-fragment-2.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime-ktx:2.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72e9453ef828cff9a49b70f5928fd438\transformed\navigation-runtime-ktx-2.8.2\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime-ktx:2.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72e9453ef828cff9a49b70f5928fd438\transformed\navigation-runtime-ktx-2.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-ui-ktx:2.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28ec13728b3851c8f2cd1f3f23f56d9f\transformed\navigation-ui-ktx-2.8.2\jars\classes.jar"
      resolved="androidx.navigation:navigation-ui-ktx:2.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28ec13728b3851c8f2cd1f3f23f56d9f\transformed\navigation-ui-ktx-2.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-fragment-ktx:2.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60461ea28113d9f3dde31fd2e6c2a0de\transformed\navigation-fragment-ktx-2.8.2\jars\classes.jar"
      resolved="androidx.navigation:navigation-fragment-ktx:2.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60461ea28113d9f3dde31fd2e6c2a0de\transformed\navigation-fragment-ktx-2.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-compose:2.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\505bfc2505d423b2b5359ed947920737\transformed\navigation-compose-2.8.2\jars\classes.jar"
      resolved="androidx.navigation:navigation-compose:2.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\505bfc2505d423b2b5359ed947920737\transformed\navigation-compose-2.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.12.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75ac7f2a23ffbf48dd6409062de1d131\transformed\material-1.12.0\jars\classes.jar"
      resolved="com.google.android.material:material:1.12.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75ac7f2a23ffbf48dd6409062de1d131\transformed\material-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de5534398a635c9be4065ca9ebc0cd2b\transformed\appcompat-resources-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de5534398a635c9be4065ca9ebc0cd2b\transformed\appcompat-resources-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1375e70be1fe8041f38907ceec5f1093\transformed\appcompat-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1375e70be1fe8041f38907ceec5f1093\transformed\appcompat-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.dagger:hilt-android:2.52@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f697fc0f692dda17f643d9f51015b66\transformed\hilt-android-2.52\jars\classes.jar"
      resolved="com.google.dagger:hilt-android:2.52"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f697fc0f692dda17f643d9f51015b66\transformed\hilt-android-2.52"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.8.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c44a06b68f01c286a9a7efde56763d5b\transformed\fragment-ktx-1.8.4\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.8.4"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c44a06b68f01c286a9a7efde56763d5b\transformed\fragment-ktx-1.8.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth:21.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth:21.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c632fa3fbae39c32b45daeb4bc38e2\transformed\play-services-auth-21.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-auth:23.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-auth:23.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca439d0d644cd3877d936e65bad4fc4f\transformed\firebase-auth-23.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03a3627e1824ee70cf489e592292748f\transformed\viewpager2-1.1.0\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03a3627e1824ee70cf489e592292748f\transformed\viewpager2-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:glide:4.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\660371d89149d49eb6de9d074ae462ea\transformed\glide-4.16.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:glide:4.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\660371d89149d49eb6de9d074ae462ea\transformed\glide-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6659db04af5d97d80d3064c8475b99cd\transformed\play-services-auth-api-phone-18.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-api-phone:18.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6659db04af5d97d80d3064c8475b99cd\transformed\play-services-auth-api-phone-18.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14c2bcefa55c73856487bee5353b1cb1\transformed\play-services-auth-base-18.0.10\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-base:18.0.10"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14c2bcefa55c73856487bee5353b1cb1\transformed\play-services-auth-base-18.0.10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-fido:20.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46fa31863f3fabfb17c38d00da312695\transformed\play-services-fido-20.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-fido:20.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46fa31863f3fabfb17c38d00da312695\transformed\play-services-fido-20.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-firestore:25.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-firestore:25.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d02bface101eba4da1d440353225b18\transformed\firebase-firestore-25.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-storage:21.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-storage:21.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0272a2401555f1ca7eb84a405384e6d\transformed\firebase-storage-21.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-appcheck:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-appcheck:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a955e921bbb5db716b8125c52f5338c\transformed\firebase-appcheck-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-appcheck-interop:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867cfca9bc377d60b7e8af899bf51df1\transformed\firebase-appcheck-interop-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-appcheck-interop:17.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\867cfca9bc377d60b7e8af899bf51df1\transformed\firebase-appcheck-interop-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-database-collection:18.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44ff130bb3a3d63f1a426dff612a1f40\transformed\firebase-database-collection-18.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-database-collection:18.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44ff130bb3a3d63f1a426dff612a1f40\transformed\firebase-database-collection-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-analytics:22.1.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f1617ad11298e9459bdbc3d95c5abe\transformed\firebase-analytics-22.1.2\jars\classes.jar"
      resolved="com.google.firebase:firebase-analytics:22.1.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f1617ad11298e9459bdbc3d95c5abe\transformed\firebase-analytics-22.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement:22.1.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement:22.1.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000c3c63bfe0a034143ea7f4936a8261\transformed\play-services-measurement-22.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk:22.1.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f55701a2806ad5fbdfaf217a3797a4c8\transformed\play-services-measurement-sdk-22.1.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk:22.1.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f55701a2806ad5fbdfaf217a3797a4c8\transformed\play-services-measurement-sdk-22.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-impl:22.1.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\506c2d7d88d4acfac211914fc9a152c8\transformed\play-services-measurement-impl-22.1.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-impl:22.1.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\506c2d7d88d4acfac211914fc9a152c8\transformed\play-services-measurement-impl-22.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd483c27f09d2683183fc5f548eeb5d9\transformed\play-services-base-18.5.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd483c27f09d2683183fc5f548eeb5d9\transformed\play-services-base-18.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-crashlytics:19.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-crashlytics:19.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e01d4927659209b4c4b2ebb18622d\transformed\firebase-crashlytics-19.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-api:22.1.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-api:22.1.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f240d90d0d3be304aa2cfbfd10d37f90\transformed\play-services-measurement-api-22.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.recaptcha:recaptcha:18.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\431ca28934aa0e759e1ba596c21cfdf5\transformed\recaptcha-18.5.1\jars\classes.jar"
      resolved="com.google.android.recaptcha:recaptcha:18.5.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\431ca28934aa0e759e1ba596c21cfdf5\transformed\recaptcha-18.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.play:integrity:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d05001fc8405447d73c08ee91a97e6b\transformed\integrity-1.3.0\jars\classes.jar"
      resolved="com.google.android.play:integrity:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d05001fc8405447d73c08ee91a97e6b\transformed\integrity-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-auth-interop:20.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ce1a7dd250c9120ea5bf8555ae67b1a\transformed\firebase-auth-interop-20.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-auth-interop:20.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ce1a7dd250c9120ea5bf8555ae67b1a\transformed\firebase-auth-interop-20.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a19a63b47c4d23783465fa0e70b382af\transformed\firebase-installations-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-sessions:2.0.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6\jars\classes.jar"
      resolved="com.google.firebase:firebase-sessions:2.0.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ead604f28db74b616d137a4a17b1771c\transformed\firebase-sessions-2.0.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations-interop:17.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f35fa3093855a6c92d644585e0d68472\transformed\firebase-installations-interop-17.2.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations-interop:17.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f35fa3093855a6c92d644585e0d68472\transformed\firebase-installations-interop-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d29eeb71e4b590606a79f795e337f4\transformed\firebase-common-ktx-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common-ktx:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d29eeb71e4b590606a79f795e337f4\transformed\firebase-common-ktx-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa087eee8a59903a2ce68dab71cdc156\transformed\firebase-common-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb516a4a0a9943a281ce6f19605ad596\transformed\swiperefreshlayout-1.1.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb516a4a0a9943a281ce6f19605ad596\transformed\swiperefreshlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1469d513bd6443e66703587865ac5ade\transformed\drawerlayout-1.1.1\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1469d513bd6443e66703587865ac5ade\transformed\drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0b251d390f4afb1f9ca91dbc800110d\transformed\coordinatorlayout-1.1.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0b251d390f4afb1f9ca91dbc800110d\transformed\coordinatorlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e33bfbc537352276299afc3330dd456\transformed\dynamicanimation-1.0.0\jars\classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e33bfbc537352276299afc3330dd456\transformed\dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.paging:paging-common-android:3.3.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05628feb40637c25ba1c41c2d30ca1bc\transformed\paging-common-release\jars\classes.jar"
      resolved="androidx.paging:paging-common-android:3.3.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05628feb40637c25ba1c41c2d30ca1bc\transformed\paging-common-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.paging:paging-common-ktx:3.3.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.paging\paging-common-ktx\3.3.2\2ad14aed781c4a73ed4dbb421966d408a0a06686\paging-common-ktx-3.3.2.jar"
      resolved="androidx.paging:paging-common-ktx:3.3.2"/>
  <library
      name="androidx.paging:paging-runtime-ktx:3.3.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d25a4a00a25ea047bc8f6558ed670e6\transformed\paging-runtime-ktx-3.3.2\jars\classes.jar"
      resolved="androidx.paging:paging-runtime-ktx:3.3.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d25a4a00a25ea047bc8f6558ed670e6\transformed\paging-runtime-ktx-3.3.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.paging:paging-runtime:3.3.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76ef5c50f8c23f7c27c8571c06f9514a\transformed\paging-runtime-3.3.2\jars\classes.jar"
      resolved="androidx.paging:paging-runtime:3.3.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76ef5c50f8c23f7c27c8571c06f9514a\transformed\paging-runtime-3.3.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db3070082bb6446577445679923bae22\transformed\recyclerview-1.3.1\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db3070082bb6446577445679923bae22\transformed\recyclerview-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63bcf4d35504fd2547407dc074988349\transformed\transition-1.5.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63bcf4d35504fd2547407dc074988349\transformed\transition-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d1983e8f816433307bdd0bbd836e24a\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d1983e8f816433307bdd0bbd836e24a\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed65d3d7a605f20c30bf5c652dd965e5\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed65d3d7a605f20c30bf5c652dd965e5\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-stats:17.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\748db3a4e46b24efea46d5e564085cf2\transformed\play-services-stats-17.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-stats:17.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\748db3a4e46b24efea46d5e564085cf2\transformed\play-services-stats-17.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fcfe4ac222496a2880ccbd7dce119031\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fcfe4ac222496a2880ccbd7dce119031\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\549d3916b6809f2845fb5e42208cb6be\transformed\loader-1.1.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\549d3916b6809f2845fb5e42208cb6be\transformed\loader-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eec5b5722953297dab7b2da4ce527235\transformed\browser-1.4.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eec5b5722953297dab7b2da4ce527235\transformed\browser-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a591881580f7036f2574d26b337679f8\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a591881580f7036f2574d26b337679f8\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f4b32b2d4266b520bc3135d7b2885dc\transformed\slidingpanelayout-1.2.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f4b32b2d4266b520bc3135d7b2885dc\transformed\slidingpanelayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\feb00527f72d755a5627314bd3f96d19\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\feb00527f72d755a5627314bd3f96d19\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer:2.19.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63063255e3aad00729fed87c165307c6\transformed\exoplayer-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer:2.19.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63063255e3aad00729fed87c165307c6\transformed\exoplayer-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-ui:2.19.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f9ce903e2ff26f2c616d9362ffafb64\transformed\exoplayer-ui-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-ui:2.19.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f9ce903e2ff26f2c616d9362ffafb64\transformed\exoplayer-ui-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd1523a13beb55e8e76f6d0d33c82918\transformed\media-1.6.0\jars\classes.jar"
      resolved="androidx.media:media:1.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd1523a13beb55e8e76f6d0d33c82918\transformed\media-1.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1\jars\classes.jar"
      resolved="androidx.core:core:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e199df52e4d067d471a8ec3433b6506\transformed\core-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.8.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.8.6\7174a594afb73a9ad9ac9074ce78b94af3cc52a7\lifecycle-common-jvm-2.8.6.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.8.6"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.8.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\572be8aa37747fccc1b25ccc0ad34731\transformed\lifecycle-viewmodel-2.8.6\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.8.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\572be8aa37747fccc1b25ccc0ad34731\transformed\lifecycle-viewmodel-2.8.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a59630a39ebfd67a70a167ceaa9ba65d\transformed\lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.8.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a59630a39ebfd67a70a167ceaa9ba65d\transformed\lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.8.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a317f68135dc86eb1a707ff08cbd84a8\transformed\lifecycle-livedata-core-2.8.6\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.8.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a317f68135dc86eb1a707ff08cbd84a8\transformed\lifecycle-livedata-core-2.8.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.coil-kt:coil-compose:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a44c52eb3e2d2d4c9df76b5d9814581\transformed\coil-compose-2.5.0\jars\classes.jar"
      resolved="io.coil-kt:coil-compose:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a44c52eb3e2d2d4c9df76b5d9814581\transformed\coil-compose-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.coil-kt:coil-compose-base:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a54cd9ff86e142881ef03f497a78d3f0\transformed\coil-compose-base-2.5.0\jars\classes.jar"
      resolved="io.coil-kt:coil-compose-base:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a54cd9ff86e142881ef03f497a78d3f0\transformed\coil-compose-base-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.coil-kt:coil:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46e0541dd417aaeb2f970b774c9b8599\transformed\coil-2.5.0\jars\classes.jar"
      resolved="io.coil-kt:coil:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46e0541dd417aaeb2f970b774c9b8599\transformed\coil-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.coil-kt:coil-base:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a385e492662b576a6d8d8112efe855\transformed\coil-base-2.5.0\jars\classes.jar"
      resolved="io.coil-kt:coil-base:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a385e492662b576a6d8d8112efe855\transformed\coil-base-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.8.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c8d87b123cdc25e1157775c2004abe2\transformed\lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.8.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c8d87b123cdc25e1157775c2004abe2\transformed\lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\770bfe3740ffe9976cba2d0c51b81483\transformed\lifecycle-viewmodel-savedstate-2.8.6\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\770bfe3740ffe9976cba2d0c51b81483\transformed\lifecycle-viewmodel-savedstate-2.8.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30b5cfcb00be2cdd572d6c04b1241c09\transformed\lifecycle-runtime-ktx-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30b5cfcb00be2cdd572d6c04b1241c09\transformed\lifecycle-runtime-ktx-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca32da0058e2cfd9c70ee1e7c1adb262\transformed\lifecycle-livedata-core-ktx-2.8.6\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca32da0058e2cfd9c70ee1e7c1adb262\transformed\lifecycle-livedata-core-ktx-2.8.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d29603c3210a6d0855335cb17a945164\transformed\lifecycle-runtime-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d29603c3210a6d0855335cb17a945164\transformed\lifecycle-runtime-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.8.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ccabb738105bd78fcd4c70adbea2096\transformed\lifecycle-service-2.8.6\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.8.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ccabb738105bd78fcd4c70adbea2096\transformed\lifecycle-service-2.8.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.8.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aea5573deca53022a416382f3264193f\transformed\lifecycle-process-2.8.6\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.8.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aea5573deca53022a416382f3264193f\transformed\lifecycle-process-2.8.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0b737481420e6e821cc1bf6564e28e6\transformed\lifecycle-viewmodel-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0b737481420e6e821cc1bf6564e28e6\transformed\lifecycle-viewmodel-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material3:material3-android:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff95595b5660c73a0c7669f7cd696712\transformed\material3-release\jars\classes.jar"
      resolved="androidx.compose.material3:material3-android:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff95595b5660c73a0c7669f7cd696712\transformed\material3-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-layout-android:1.7.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62a1b30182b029e62d22b2dd2c857809\transformed\foundation-layout-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-layout-android:1.7.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62a1b30182b029e62d22b2dd2c857809\transformed\foundation-layout-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-core-android:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba9ea08de9541b65dd4ab1b547e9b60d\transformed\material-icons-core-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-core-android:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba9ea08de9541b65dd4ab1b547e9b60d\transformed\material-icons-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-extended-android:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f998e930f7cae181b32542910621c7\transformed\material-icons-extended-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-extended-android:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f998e930f7cae181b32542910621c7\transformed\material-icons-extended-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-ripple-android:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\759b6acd1f3bc3a615e39717df962334\transformed\material-ripple-release\jars\classes.jar"
      resolved="androidx.compose.material:material-ripple-android:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\759b6acd1f3bc3a615e39717df962334\transformed\material-ripple-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-android:1.7.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37fc1bdcb99e9ca53adfd9dc62ba1cb8\transformed\foundation-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-android:1.7.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37fc1bdcb99e9ca53adfd9dc62ba1cb8\transformed\foundation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-core-android:1.7.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ac8b48e17b53bc98e07f9162f8a892b\transformed\animation-core-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-core-android:1.7.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ac8b48e17b53bc98e07f9162f8a892b\transformed\animation-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-android:1.7.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1635c0b3dfe1dbc32ae4074e1749255b\transformed\animation-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-android:1.7.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1635c0b3dfe1dbc32ae4074e1749255b\transformed\animation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-util-android:1.7.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1525187ec8133cbbd491972c29238be8\transformed\ui-util-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-util-android:1.7.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1525187ec8133cbbd491972c29238be8\transformed\ui-util-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit-android:1.7.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44ba11e360ef7ca5413456b5c51a2c4f\transformed\ui-unit-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-unit-android:1.7.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44ba11e360ef7ca5413456b5c51a2c4f\transformed\ui-unit-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-text-android:1.7.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1364c55b39eaf8182e00cae12bebcc59\transformed\ui-text-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-text-android:1.7.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1364c55b39eaf8182e00cae12bebcc59\transformed\ui-text-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics-android:1.7.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7378be35798f99346c38131207a4d92\transformed\ui-graphics-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-graphics-android:1.7.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7378be35798f99346c38131207a4d92\transformed\ui-graphics-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry-android:1.7.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05b18cf898fc46bb81d6dc6a059bbb55\transformed\ui-geometry-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-geometry-android:1.7.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05b18cf898fc46bb81d6dc6a059bbb55\transformed\ui-geometry-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-android:1.7.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7bf19bacce7fd4c141ebe47b25076b8\transformed\ui-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-android:1.7.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7bf19bacce7fd4c141ebe47b25076b8\transformed\ui-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-preview-android:1.7.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebbec17bf6c104ade9cbd4874189386e\transformed\ui-tooling-preview-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-preview-android:1.7.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebbec17bf6c104ade9cbd4874189386e\transformed\ui-tooling-preview-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-saveable-android:1.7.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb84a08ced79840842eaf49383a58010\transformed\runtime-saveable-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-saveable-android:1.7.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb84a08ced79840842eaf49383a58010\transformed\runtime-saveable-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-android:1.7.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5fb10182892807adb5a50c0d073a398\transformed\runtime-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-android:1.7.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5fb10182892807adb5a50c0d073a398\transformed\runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-livedata:1.7.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a0de01b529fb2bb0e648b53a3676679\transformed\runtime-livedata-1.7.2\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-livedata:1.7.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a0de01b529fb2bb0e648b53a3676679\transformed\runtime-livedata-1.7.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime-ktx:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1783a6756ba8fb878c7385cec384cd49\transformed\work-runtime-ktx-2.9.1\jars\classes.jar"
      resolved="androidx.work:work-runtime-ktx:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1783a6756ba8fb878c7385cec384cd49\transformed\work-runtime-ktx-2.9.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime:2.9.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1\jars\classes.jar"
      resolved="androidx.work:work-runtime:2.9.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dcba117bb1e37b5a31c4649150d30ee\transformed\work-runtime-2.9.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.8.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6b877db508dcfb3e7ad410982fcb18a\transformed\lifecycle-livedata-2.8.6\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.8.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6b877db508dcfb3e7ad410982fcb18a\transformed\lifecycle-livedata-2.8.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-ktx:2.8.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2a03bf9a2c91a7cce1ae06827c08ffc\transformed\lifecycle-livedata-ktx-2.8.6\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-ktx:2.8.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2a03bf9a2c91a7cce1ae06827c08ffc\transformed\lifecycle-livedata-ktx-2.8.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4a1b7671c535604a38c60aa72e81002c\transformed\lifecycle-viewmodel-ktx-2.8.6\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4a1b7671c535604a38c60aa72e81002c\transformed\lifecycle-viewmodel-ktx-2.8.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-common:2.6.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.room\room-common\2.6.1\ff1b9580850a9b7eef56554e356628d225785265\room-common-2.6.1.jar"
      resolved="androidx.room:room-common:2.6.1"/>
  <library
      name="androidx.room:room-runtime:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3bc0680d46b752b3dba03a568ba1a3\transformed\room-runtime-2.6.1\jars\classes.jar"
      resolved="androidx.room:room-runtime:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3bc0680d46b752b3dba03a568ba1a3\transformed\room-runtime-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-ktx:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d87d94b5c13a001fa84294ac4c30cec6\transformed\room-ktx-2.6.1\jars\classes.jar"
      resolved="androidx.room:room-ktx:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d87d94b5c13a001fa84294ac4c30cec6\transformed\room-ktx-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.8.1\73e2acdd18df99dd4849d99f188dff529fc0afe0\kotlinx-coroutines-android-1.8.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\473ea2509b1f540a006e124452427516\transformed\ads-adservices-java-1.0.0-beta05\jars\classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\473ea2509b1f540a006e124452427516\transformed\ads-adservices-java-1.0.0-beta05"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c656b25fa2dc95e881345daed065935\transformed\ads-adservices-1.0.0-beta05\jars\classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c656b25fa2dc95e881345daed065935\transformed\ads-adservices-1.0.0-beta05"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.8.1\bb0e192bd7c2b6b8217440d36e9758e377e450\kotlinx-coroutines-core-jvm-1.8.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-play-services\1.8.1\71eb0cace3aaa93591a613a32c92853c464d2a53\kotlinx-coroutines-play-services-1.8.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.8.1"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9223f0e1271e59953c421b866dd50ca\transformed\play-services-tasks-18.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9223f0e1271e59953c421b866dd50ca\transformed\play-services-tasks-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-measurement-connector:20.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e0672aec5af16978afd7bbd8e460675\transformed\firebase-measurement-connector-20.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-measurement-connector:20.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e0672aec5af16978afd7bbd8e460675\transformed\firebase-measurement-connector-20.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd946245e66d6cf7b58e6eb126b81219\transformed\play-services-ads-identifier-18.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads-identifier:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd946245e66d6cf7b58e6eb126b81219\transformed\play-services-ads-identifier-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk-api:22.1.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1cab4525135b934e909b93124109edf\transformed\play-services-measurement-sdk-api-22.1.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk-api:22.1.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1cab4525135b934e909b93124109edf\transformed\play-services-measurement-sdk-api-22.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-base:22.1.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd31ab42f6df184811fef9a3c088b0f0\transformed\play-services-measurement-base-22.1.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-base:22.1.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd31ab42f6df184811fef9a3c088b0f0\transformed\play-services-measurement-base-22.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98a7034e786415bc9e009aca9ccbea7a\transformed\play-services-basement-18.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98a7034e786415bc9e009aca9ccbea7a\transformed\play-services-basement-18.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.8.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d472de1097adacf621614efcbac1e21\transformed\fragment-1.8.4\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.8.4"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d472de1097adacf621614efcbac1e21\transformed\fragment-1.8.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.9.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e448ed96b6af2b004b3c7ca7af320d3d\transformed\activity-1.9.2\jars\classes.jar"
      resolved="androidx.activity:activity:1.9.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e448ed96b6af2b004b3c7ca7af320d3d\transformed\activity-1.9.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-compose:1.9.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f07e0e8f60f626ea9f27bd033ae12b\transformed\activity-compose-1.9.2\jars\classes.jar"
      resolved="androidx.activity:activity-compose:1.9.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f07e0e8f60f626ea9f27bd033ae12b\transformed\activity-compose-1.9.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.9.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8dccd7121ce23f325500264b1927919\transformed\activity-ktx-1.9.2\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.9.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8dccd7121ce23f325500264b1927919\transformed\activity-ktx-1.9.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff2de5d72fce1ffa11fecc0c6983bab7\transformed\core-ktx-1.13.1\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff2de5d72fce1ffa11fecc0c6983bab7\transformed\core-ktx-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-splashscreen:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d3c9c4ac15ccaa3d4f8bb593bf66c\transformed\core-splashscreen-1.0.1\jars\classes.jar"
      resolved="androidx.core:core-splashscreen:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d3c9c4ac15ccaa3d4f8bb593bf66c\transformed\core-splashscreen-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.jakewharton.timber:timber:5.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e74249777095f16bf90043ea48d86009\transformed\timber-5.0.1\jars\classes.jar"
      resolved="com.jakewharton.timber:timber:5.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e74249777095f16bf90043ea48d86009\transformed\timber-5.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.25@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-parcelize-runtime\1.9.25\5ddaf36e1f9708ffd4019de9757ba813bd0a1421\kotlin-parcelize-runtime-1.9.25.jar"
      resolved="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.25"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6638fe5cf8f78eefbcd16cdca2d9e5c\transformed\savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6638fe5cf8f78eefbcd16cdca2d9e5c\transformed\savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f316befd3963bb027428722e4bcae7f\transformed\savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f316befd3963bb027428722e4bcae7f\transformed\savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\81e1762d7a12f7128da367a6a2a55145\transformed\annotation-experimental-1.4.1\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\81e1762d7a12f7128da367a6a2a55145\transformed\annotation-experimental-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.credentials:credentials-play-services-auth:1.2.0-rc01@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01\jars\classes.jar"
      resolved="androidx.credentials:credentials-play-services-auth:1.2.0-rc01"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c316a98929039c3d3d85886e56683384\transformed\credentials-play-services-auth-1.2.0-rc01"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.credentials:credentials:1.2.0-rc01@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc3c3a73d52d53a742bae480359958fe\transformed\credentials-1.2.0-rc01\jars\classes.jar"
      resolved="androidx.credentials:credentials:1.2.0-rc01"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc3c3a73d52d53a742bae480359958fe\transformed\credentials-1.2.0-rc01"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="me.saket.swipe:swipe:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d46e4b7ad062911a593f57ff2088e553\transformed\swipe-1.2.0\jars\classes.jar"
      resolved="me.saket.swipe:swipe:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d46e4b7ad062911a593f57ff2088e553\transformed\swipe-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\logging-interceptor\4.12.0\e922c1f14d365c0f2bed140cc0825e18462c2778\logging-interceptor-4.12.0.jar"
      resolved="com.squareup.okhttp3:logging-interceptor:4.12.0"/>
  <library
      name="com.squareup.retrofit2:converter-gson:2.11.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\converter-gson\2.11.0\9d1fe9d1662de0548e08e293041140a8e4026f81\converter-gson-2.11.0.jar"
      resolved="com.squareup.retrofit2:converter-gson:2.11.0"/>
  <library
      name="com.squareup.retrofit2:retrofit:2.11.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\retrofit\2.11.0\6ca8c6caf842271f3232e075519fe04081ef7069\retrofit-2.11.0.jar"
      resolved="com.squareup.retrofit2:retrofit:2.11.0"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.12.0\2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd\okhttp-4.12.0.jar"
      resolved="com.squareup.okhttp3:okhttp:4.12.0"/>
  <library
      name="com.google.firebase:firebase-config-interop:16.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c2ac540a0dbb432b7e96e8026104ab5\transformed\firebase-config-interop-16.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-config-interop:16.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c2ac540a0dbb432b7e96e8026104ab5\transformed\firebase-config-interop-16.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-json:18.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a5ff77ed6e8796335744dd9c5829aa6\transformed\firebase-encoders-json-18.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-encoders-json:18.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a5ff77ed6e8796335744dd9c5829aa6\transformed\firebase-encoders-json-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okio:okio-jvm:3.6.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.6.0\5600569133b7bdefe1daf9ec7f4abeb6d13e1786\okio-jvm-3.6.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.6.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\2.0.21\72f75cc23f0756dec50b2dcbeb72561501fb8af9\kotlin-stdlib-jdk8-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21"/>
  <library
      name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.25@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-android-extensions-runtime\1.9.25\3c69ebd730fc998c00b5d6d69f637cd12231028\kotlin-android-extensions-runtime-1.9.25.jar"
      resolved="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.25"/>
  <library
      name="androidx.security:security-crypto:1.1.0-alpha06@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5438095388770c65ceef79c1ed6cdce\transformed\security-crypto-1.1.0-alpha06\jars\classes.jar"
      resolved="androidx.security:security-crypto:1.1.0-alpha06"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5438095388770c65ceef79c1ed6cdce\transformed\security-crypto-1.1.0-alpha06"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.documentfile:documentfile:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d96accd3e95a849b411f6d40626c55c1\transformed\documentfile-1.0.1\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d96accd3e95a849b411f6d40626c55c1\transformed\documentfile-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee551ec3fd119864ca11d7dccd96f4e7\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee551ec3fd119864ca11d7dccd96f4e7\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\376a5cf04f9e52a43728211b447c8cba\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\376a5cf04f9e52a43728211b447c8cba\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f43bef631a3a38a9d08e025479944c3e\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f43bef631a3a38a9d08e025479944c3e\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63d14b553fca4381dcc9cb52bec5050b\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63d14b553fca4381dcc9cb52bec5050b\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders:17.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders\17.0.0\26f52dc549c42575b155f8c720e84059ee600a85\firebase-encoders-17.0.0.jar"
      resolved="com.google.firebase:firebase-encoders:17.0.0"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b7572c291721d887a8a69c42c05f8fb\transformed\sqlite-framework-2.4.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b7572c291721d887a8a69c42c05f8fb\transformed\sqlite-framework-2.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite:2.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89a4d9fea819de7bc2d872220c0a8f0f\transformed\sqlite-2.4.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite:2.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89a4d9fea819de7bc2d872220c0a8f0f\transformed\sqlite-2.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d20d3d5f1f5a05dec5f94ea4a31a1428\transformed\gifdecoder-4.16.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:gifdecoder:4.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d20d3d5f1f5a05dec5f94ea4a31a1428\transformed\gifdecoder-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7201e597345aa82cd022c99533bbf50\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7201e597345aa82cd022c99533bbf50\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9891cc273c69fca165fe759e0b5d848a\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9891cc273c69fca165fe759e0b5d848a\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.4.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.4.4\2ad14aed781c4a73ed4dbb421966d408a0a06686\collection-ktx-1.4.4.jar"
      resolved="androidx.collection:collection-ktx:1.4.4"/>
  <library
      name="androidx.collection:collection-jvm:1.4.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.4.4\da13a7e557c430276b8cb490420effebc1398c0d\collection-jvm-1.4.4.jar"
      resolved="androidx.collection:collection-jvm:1.4.4"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc83f3f0f89460506f12f9fa8d48059e\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc83f3f0f89460506f12f9fa8d48059e\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.8.1\b8a16fe526014b7941c1debaccaf9c5153692dbb\annotation-jvm-1.8.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.8.1"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\2.0.21\222b2be42672d47c002c1b22ac9f030d781fc5db\kotlin-stdlib-jdk7-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.0.21\618b539767b4899b4660a83006e052b63f1db551\kotlin-stdlib-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.0.21"/>
  <library
      name="com.github.PhilJay:MPAndroidChart:v3.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5798195514dcf9f54facb6622514625d\transformed\MPAndroidChart-v3.1.0\jars\classes.jar"
      resolved="com.github.PhilJay:MPAndroidChart:v3.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5798195514dcf9f54facb6622514625d\transformed\MPAndroidChart-v3.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc472afffedb0f448961e1c05cc4dbbf\transformed\constraintlayout-2.2.0\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc472afffedb0f448961e1c05cc4dbbf\transformed\constraintlayout-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.code.gson:gson:2.11.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.11.0\527175ca6d81050b53bdd4c457a6d6e017626b0e\gson-2.11.0.jar"
      resolved="com.google.code.gson:gson:2.11.0"/>
  <library
      name="com.github.chrisbanes:PhotoView:2.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe15545a327dd3e628a07d26bf9a38c8\transformed\PhotoView-2.3.0\jars\classes.jar"
      resolved="com.github.chrisbanes:PhotoView:2.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe15545a327dd3e628a07d26bf9a38c8\transformed\PhotoView-2.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="jp.wasabeef:richeditor-android:2.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5b4b351cbe3a8b1d05e0f2d04d73525\transformed\richeditor-android-2.0.0\jars\classes.jar"
      resolved="jp.wasabeef:richeditor-android:2.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5b4b351cbe3a8b1d05e0f2d04d73525\transformed\richeditor-android-2.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.android.exoplayer:exoplayer-core:2.19.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e87b0a766c58fccb5c3caec65c96631b\transformed\exoplayer-core-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-core:2.19.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e87b0a766c58fccb5c3caec65c96631b\transformed\exoplayer-core-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-common:2.19.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c58cd8b4696f8fa5bb0d8d3c36af5768\transformed\exoplayer-common-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-common:2.19.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c58cd8b4696f8fa5bb0d8d3c36af5768\transformed\exoplayer-common-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:guava:32.1.3-android@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\32.1.3-android\ea090dd85ca2fa12d42d054369df888665230dd7\guava-32.1.3-android.jar"
      resolved="com.google.guava:guava:32.1.3-android"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed63fd1d6f0c32c1e430798002671246\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed63fd1d6f0c32c1e430798002671246\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\383f67f12f6be5aabe17a1a6b60ff97c\transformed\tracing-1.0.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\383f67f12f6be5aabe17a1a6b60ff97c\transformed\tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.27.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.27.0\91b2c29d8a6148b5e2e4930f070d4840e2e48e34\error_prone_annotations-2.27.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.27.0"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="com.google.dagger:hilt-core:2.52@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.dagger\hilt-core\2.52\d8e3f33770f8b5d9e2e29a303278c3e41faa9980\hilt-core-2.52.jar"
      resolved="com.google.dagger:hilt-core:2.52"/>
  <library
      name="com.google.dagger:dagger:2.52@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.dagger\dagger\2.52\c385ea51a9873b238d183faa22d54c00d65195ac\dagger-2.52.jar"
      resolved="com.google.dagger:dagger:2.52"/>
  <library
      name="jakarta.inject:jakarta.inject-api:2.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\jakarta.inject\jakarta.inject-api\2.0.1\4c28afe1991a941d7702fe1362c365f0a8641d1e\jakarta.inject-api-2.0.1.jar"
      resolved="jakarta.inject:jakarta.inject-api:2.0.1"/>
  <library
      name="com.google.firebase:firebase-components:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b54da9fb788aca02c18c87741f8a5cf\transformed\firebase-components-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-components:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b54da9fb788aca02c18c87741f8a5cf\transformed\firebase-components-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-annotations:16.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-annotations\16.2.0\ba0806703ca285d03fa9c888b5868f101134a501\firebase-annotations-16.2.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.2.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="com.google.dagger:dagger-lint-aar:2.52@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32aace43ec0d198bec8cbb1cbaf9874c\transformed\dagger-lint-aar-2.52\jars\classes.jar"
      resolved="com.google.dagger:dagger-lint-aar:2.52"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32aace43ec0d198bec8cbb1cbaf9874c\transformed\dagger-lint-aar-2.52"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="com.google.guava:failureaccess:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.1\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\failureaccess-1.0.1.jar"
      resolved="com.google.guava:failureaccess:1.0.1"/>
  <library
      name="org.checkerframework:checker-qual:3.37.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.checkerframework\checker-qual\3.37.0\ba74746d38026581c12166e164bb3c15e90cc4ea\checker-qual-3.37.0.jar"
      resolved="org.checkerframework:checker-qual:3.37.0"/>
  <library
      name="com.google.android.play:core-common:2.0.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed98f692eef56b9ec9255f707f2cecc8\transformed\core-common-2.0.3\jars\classes.jar"
      resolved="com.google.android.play:core-common:2.0.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed98f692eef56b9ec9255f707f2cecc8\transformed\core-common-2.0.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:protolite-well-known-types:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\673ec8ce3066443b41d99eb75dbc1193\transformed\protolite-well-known-types-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:protolite-well-known-types:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\673ec8ce3066443b41d99eb75dbc1193\transformed\protolite-well-known-types-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.protobuf:protobuf-javalite:3.25.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.protobuf\protobuf-javalite\3.25.1\3dee29d9cbdb25f9363847054ed68d34b9f9c8b0\protobuf-javalite-3.25.1.jar"
      resolved="com.google.protobuf:protobuf-javalite:3.25.1"/>
  <library
      name="com.google.android.exoplayer:exoplayer-database:2.19.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d06e38b9254253d89d40561949adceca\transformed\exoplayer-database-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-database:2.19.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d06e38b9254253d89d40561949adceca\transformed\exoplayer-database-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-datasource:2.19.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a23e484f9ab2c8f2fe1c821821b8dd08\transformed\exoplayer-datasource-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-datasource:2.19.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a23e484f9ab2c8f2fe1c821821b8dd08\transformed\exoplayer-datasource-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-decoder:2.19.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f84f39bce60e40271bbffac62df7748d\transformed\exoplayer-decoder-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-decoder:2.19.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f84f39bce60e40271bbffac62df7748d\transformed\exoplayer-decoder-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-extractor:2.19.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952b95c172269eee0bea8252aa77f918\transformed\exoplayer-extractor-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-extractor:2.19.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\952b95c172269eee0bea8252aa77f918\transformed\exoplayer-extractor-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-container:2.19.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26aba51db4309e78491b8b75b9ce4e8c\transformed\exoplayer-container-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-container:2.19.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26aba51db4309e78491b8b75b9ce4e8c\transformed\exoplayer-container-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-dash:2.19.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f03f196a6ab2e86824c49d07db5ba34\transformed\exoplayer-dash-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-dash:2.19.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f03f196a6ab2e86824c49d07db5ba34\transformed\exoplayer-dash-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-hls:2.19.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6896777e83f77c6a440bcb66c31cbaf\transformed\exoplayer-hls-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-hls:2.19.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6896777e83f77c6a440bcb66c31cbaf\transformed\exoplayer-hls-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-rtsp:2.19.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e272b663b90dafb232bdf1f1361c364\transformed\exoplayer-rtsp-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-rtsp:2.19.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e272b663b90dafb232bdf1f1361c364\transformed\exoplayer-rtsp-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d89ef1c21515073ae40a0ac1b12cafa4\transformed\exoplayer-smoothstreaming-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d89ef1c21515073ae40a0ac1b12cafa4\transformed\exoplayer-smoothstreaming-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\disklrucache\4.16.0\411aa175d50d10b37c7a1a04d21a4e7145249557\disklrucache-4.16.0.jar"
      resolved="com.github.bumptech.glide:disklrucache:4.16.0"/>
  <library
      name="com.github.bumptech.glide:annotations:4.16.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\annotations\4.16.0\90730f6498299d207aa0878124ab7585969808f0\annotations-4.16.0.jar"
      resolved="com.github.bumptech.glide:annotations:4.16.0"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea3d85634f8413586751d19babfb5cfb\transformed\exifinterface-1.3.6\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea3d85634f8413586751d19babfb5cfb\transformed\exifinterface-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.j2objc:j2objc-annotations:2.8@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.j2objc\j2objc-annotations\2.8\c85270e307e7b822f1086b93689124b89768e273\j2objc-annotations-2.8.jar"
      resolved="com.google.j2objc:j2objc-annotations:2.8"
      provided="true"/>
  <library
      name="com.google.accompanist:accompanist-drawablepainter:0.32.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc269eb2a18ceb548d2a5ea95adc8c96\transformed\accompanist-drawablepainter-0.32.0\jars\classes.jar"
      resolved="com.google.accompanist:accompanist-drawablepainter:0.32.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc269eb2a18ceb548d2a5ea95adc8c96\transformed\accompanist-drawablepainter-0.32.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99bc7333b09f49c249631ba3577589ba\transformed\emoji2-views-helper-1.3.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99bc7333b09f49c249631ba3577589ba\transformed\emoji2-views-helper-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5071997fcfc97d02bafc8941f239eee\transformed\emoji2-1.3.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5071997fcfc97d02bafc8941f239eee\transformed\emoji2-1.3.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5071997fcfc97d02bafc8941f239eee\transformed\emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6fb973f260208f7e6314e3dee7fad99\transformed\customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6fb973f260208f7e6314e3dee7fad99\transformed\customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5670563a54c53bc97f5592427a4620d3\transformed\autofill-1.0.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5670563a54c53bc97f5592427a4620d3\transformed\autofill-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0\jars\classes.jar"
      resolved="androidx.window:window:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c934922658499219f940071b059c508\transformed\window-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.graphics:graphics-path:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f8c3e6828abcbffa3c9f5535dfc64a3\transformed\graphics-path-1.0.1\jars\classes.jar"
      resolved="androidx.graphics:graphics-path:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f8c3e6828abcbffa3c9f5535dfc64a3\transformed\graphics-path-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.8.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.8.6\2ad14aed781c4a73ed4dbb421966d408a0a06686\lifecycle-common-java8-2.8.6.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.8.6"/>
  <library
      name="androidx.datastore:datastore-preferences:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\414e0683e18e9aaca9df3e519609757c\transformed\datastore-preferences-1.0.0\jars\classes.jar"
      resolved="androidx.datastore:datastore-preferences:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\414e0683e18e9aaca9df3e519609757c\transformed\datastore-preferences-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fefa0f32ba14f41623918fd0f72addf5\transformed\datastore-1.0.0\jars\classes.jar"
      resolved="androidx.datastore:datastore:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fefa0f32ba14f41623918fd0f72addf5\transformed\datastore-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-preferences-core:1.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-core\1.0.0\403f64499b9a8994f5f7010329ddd1ee5c919ed5\datastore-preferences-core-1.0.0.jar"
      resolved="androidx.datastore:datastore-preferences-core:1.0.0"/>
  <library
      name="androidx.datastore:datastore-core:1.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-core\1.0.0\91b04fb657294e2906d95dce6a9e5a851f6125c1\datastore-core-1.0.0.jar"
      resolved="androidx.datastore:datastore-core:1.0.0"/>
  <library
      name="com.google.firebase:firebase-datatransport:19.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e2967fd5b276966c87bd4e88ea401c8\transformed\firebase-datatransport-19.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-datatransport:19.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e2967fd5b276966c87bd4e88ea401c8\transformed\firebase-datatransport-19.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-backend-cct:3.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-backend-cct:3.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\529cf4b585069faf1ac5f087123a0625\transformed\transport-backend-cct-3.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.grpc:grpc-okhttp:1.62.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-okhttp\1.62.2\2d8b802e7fe17c1577527195fd53a2e7355b3541\grpc-okhttp-1.62.2.jar"
      resolved="io.grpc:grpc-okhttp:1.62.2"/>
  <library
      name="com.google.android.libraries.identity.googleid:googleid:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\373d4135f1586ad81ac2cd37e6ac32ee\transformed\googleid-1.1.0\jars\classes.jar"
      resolved="com.google.android.libraries.identity.googleid:googleid:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\373d4135f1586ad81ac2cd37e6ac32ee\transformed\googleid-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.6.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-serialization-core-jvm\1.6.3\6b6c17d0312ba7192893adea9d52959941d0119b\kotlinx-serialization-core-jvm-1.6.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.6.3"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\429adfaae5b69da3f3eb7cfbefda646a\transformed\profileinstaller-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout-core:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.constraintlayout\constraintlayout-core\1.1.0\e678372aacb0e91f0ed44854a8a413fc93283050\constraintlayout-core-1.1.0.jar"
      resolved="androidx.constraintlayout:constraintlayout-core:1.1.0"/>
  <library
      name="com.google.android.datatransport:transport-runtime:3.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-runtime:3.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\baebbdf8a667ba63dc61b6c7b3a257f9\transformed\transport-runtime-3.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-api:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44ad0f2412c7c8a71f80cba0dcd24c9e\transformed\transport-api-3.2.0\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-api:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44ad0f2412c7c8a71f80cba0dcd24c9e\transformed\transport-api-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders-proto\16.0.0\a42d5fd83b96ae7b73a8617d29c94703e18c9992\firebase-encoders-proto-16.0.0.jar"
      resolved="com.google.firebase:firebase-encoders-proto:16.0.0"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="com.google.crypto.tink:tink-android:1.8.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.crypto.tink\tink-android\1.8.0\bda82b49568d444a3b54773ca5aa487816473295\tink-android-1.8.0.jar"
      resolved="com.google.crypto.tink:tink-android:1.8.0"/>
  <library
      name="io.grpc:grpc-android:1.62.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d441db952f6ad88c2cd980962bd4c407\transformed\grpc-android-1.62.2\jars\classes.jar"
      resolved="io.grpc:grpc-android:1.62.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d441db952f6ad88c2cd980962bd4c407\transformed\grpc-android-1.62.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.grpc:grpc-util:1.62.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-util\1.62.2\98c4138f09fb57c3ad6cbeffb31ed73e302038f7\grpc-util-1.62.2.jar"
      resolved="io.grpc:grpc-util:1.62.2"/>
  <library
      name="io.grpc:grpc-core:1.62.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-core\1.62.2\5808049a5e33eba6f248a68d58e75399a68f2784\grpc-core-1.62.2.jar"
      resolved="io.grpc:grpc-core:1.62.2"/>
  <library
      name="io.grpc:grpc-stub:1.62.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-stub\1.62.2\fc1e85697502d96d6c912e8dd2a56f46f1aba050\grpc-stub-1.62.2.jar"
      resolved="io.grpc:grpc-stub:1.62.2"/>
  <library
      name="io.grpc:grpc-protobuf-lite:1.62.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-protobuf-lite\1.62.2\9d807d2a0e34bd7284a5336186f57cf241090920\grpc-protobuf-lite-1.62.2.jar"
      resolved="io.grpc:grpc-protobuf-lite:1.62.2"/>
  <library
      name="io.grpc:grpc-context:1.62.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-context\1.62.2\69e86c35140b3b1718d65635bb54ccecc4c12f14\grpc-context-1.62.2.jar"
      resolved="io.grpc:grpc-context:1.62.2"/>
  <library
      name="io.grpc:grpc-api:1.62.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-api\1.62.2\a93b6ee3761d48edd9a9279f20a58be1a245ad01\grpc-api-1.62.2.jar"
      resolved="io.grpc:grpc-api:1.62.2"/>
  <library
      name="io.perfmark:perfmark-api:0.26.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.perfmark\perfmark-api\0.26.0\ef65452adaf20bf7d12ef55913aba24037b82738\perfmark-api-0.26.0.jar"
      resolved="io.perfmark:perfmark-api:0.26.0"/>
  <library
      name="com.google.android:annotations:4.1.1.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.android\annotations\4.1.1.4\a1678ba907bf92691d879fef34e1a187038f9259\annotations-4.1.1.4.jar"
      resolved="com.google.android:annotations:4.1.1.4"/>
  <library
      name="org.codehaus.mojo:animal-sniffer-annotations:1.23@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.codehaus.mojo\animal-sniffer-annotations\1.23\3c0daebd5f0e1ce72cc50c818321ac957aeb5d70\animal-sniffer-annotations-1.23.jar"
      resolved="org.codehaus.mojo:animal-sniffer-annotations:1.23"/>
</libraries>
