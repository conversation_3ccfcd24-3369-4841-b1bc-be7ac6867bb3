package com.example.allinone.viewmodels;

import com.example.allinone.firebase.FirebaseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class TasksViewModel_Factory implements Factory<TasksViewModel> {
  private final Provider<FirebaseRepository> repositoryProvider;

  public TasksViewModel_Factory(Provider<FirebaseRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public TasksViewModel get() {
    return newInstance(repositoryProvider.get());
  }

  public static TasksViewModel_Factory create(Provider<FirebaseRepository> repositoryProvider) {
    return new TasksViewModel_Factory(repositoryProvider);
  }

  public static TasksViewModel newInstance(FirebaseRepository repository) {
    return new TasksViewModel(repository);
  }
}
