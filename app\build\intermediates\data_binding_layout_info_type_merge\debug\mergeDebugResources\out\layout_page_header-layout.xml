<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="layout_page_header" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\layout_page_header.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/layout_page_header_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="36" endOffset="51"/></Target><Target id="@+id/drawer_button" view="ImageButton"><Expressions/><location startLine="9" startOffset="4" endLine="20" endOffset="41"/></Target><Target id="@+id/page_title" view="TextView"><Expressions/><location startLine="22" startOffset="4" endLine="34" endOffset="51"/></Target></Targets></Layout>