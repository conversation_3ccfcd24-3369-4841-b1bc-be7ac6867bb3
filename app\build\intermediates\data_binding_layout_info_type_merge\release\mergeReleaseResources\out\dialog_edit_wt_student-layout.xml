<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_edit_wt_student" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\dialog_edit_wt_student.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView"><Targets><Target tag="layout/dialog_edit_wt_student_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="133" endOffset="39"/></Target><Target id="@+id/studentDropdown" view="AutoCompleteTextView"><Expressions/><location startLine="18" startOffset="12" endLine="22" endOffset="42"/></Target><Target id="@+id/startDateInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="32" startOffset="12" endLine="37" endOffset="42"/></Target><Target id="@+id/endDateInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="47" startOffset="12" endLine="52" endOffset="42"/></Target><Target id="@+id/amountLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="55" startOffset="8" endLine="68" endOffset="63"/></Target><Target id="@+id/amountInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="63" startOffset="12" endLine="67" endOffset="51"/></Target><Target id="@+id/notesEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="77" startOffset="12" endLine="82" endOffset="38"/></Target><Target id="@+id/paidSwitch" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="85" startOffset="8" endLine="90" endOffset="53"/></Target><Target id="@+id/attachmentNameText" view="TextView"><Expressions/><location startLine="107" startOffset="12" endLine="114" endOffset="43"/></Target><Target id="@+id/addAttachmentButton" view="Button"><Expressions/><location startLine="116" startOffset="12" endLine="121" endOffset="50"/></Target><Target id="@+id/attachmentPreview" view="ImageView"><Expressions/><location startLine="124" startOffset="8" endLine="129" endOffset="39"/></Target></Targets></Layout>