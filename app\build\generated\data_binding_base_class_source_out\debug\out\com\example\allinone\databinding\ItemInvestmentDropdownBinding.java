// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemInvestmentDropdownBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView investmentAmount;

  @NonNull
  public final TextView investmentName;

  @NonNull
  public final TextView investmentType;

  private ItemInvestmentDropdownBinding(@NonNull LinearLayout rootView,
      @NonNull TextView investmentAmount, @NonNull TextView investmentName,
      @NonNull TextView investmentType) {
    this.rootView = rootView;
    this.investmentAmount = investmentAmount;
    this.investmentName = investmentName;
    this.investmentType = investmentType;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemInvestmentDropdownBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemInvestmentDropdownBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_investment_dropdown, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemInvestmentDropdownBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.investmentAmount;
      TextView investmentAmount = ViewBindings.findChildViewById(rootView, id);
      if (investmentAmount == null) {
        break missingId;
      }

      id = R.id.investmentName;
      TextView investmentName = ViewBindings.findChildViewById(rootView, id);
      if (investmentName == null) {
        break missingId;
      }

      id = R.id.investmentType;
      TextView investmentType = ViewBindings.findChildViewById(rootView, id);
      if (investmentType == null) {
        break missingId;
      }

      return new ItemInvestmentDropdownBinding((LinearLayout) rootView, investmentAmount,
          investmentName, investmentType);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
