// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogProgramDetailsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final RecyclerView exercisesRecyclerView;

  @NonNull
  public final TextView programDescriptionText;

  @NonNull
  public final TextView programNameText;

  private DialogProgramDetailsBinding(@NonNull LinearLayout rootView,
      @NonNull RecyclerView exercisesRecyclerView, @NonNull TextView programDescriptionText,
      @NonNull TextView programNameText) {
    this.rootView = rootView;
    this.exercisesRecyclerView = exercisesRecyclerView;
    this.programDescriptionText = programDescriptionText;
    this.programNameText = programNameText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogProgramDetailsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogProgramDetailsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_program_details, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogProgramDetailsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.exercises_recycler_view;
      RecyclerView exercisesRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (exercisesRecyclerView == null) {
        break missingId;
      }

      id = R.id.program_description_text;
      TextView programDescriptionText = ViewBindings.findChildViewById(rootView, id);
      if (programDescriptionText == null) {
        break missingId;
      }

      id = R.id.program_name_text;
      TextView programNameText = ViewBindings.findChildViewById(rootView, id);
      if (programNameText == null) {
        break missingId;
      }

      return new DialogProgramDetailsBinding((LinearLayout) rootView, exercisesRecyclerView,
          programDescriptionText, programNameText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
