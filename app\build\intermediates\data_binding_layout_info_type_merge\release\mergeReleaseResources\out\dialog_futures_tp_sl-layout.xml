<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_futures_tp_sl" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\dialog_futures_tp_sl.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_futures_tp_sl_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="80" endOffset="14"/></Target><Target id="@+id/positionSymbolText" view="TextView"><Expressions/><location startLine="9" startOffset="4" endLine="16" endOffset="30"/></Target><Target id="@+id/positionDetailsText" view="TextView"><Expressions/><location startLine="18" startOffset="4" endLine="24" endOffset="57"/></Target><Target id="@+id/takeProfitLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="26" startOffset="4" endLine="40" endOffset="59"/></Target><Target id="@+id/takeProfitInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="34" startOffset="8" endLine="38" endOffset="47"/></Target><Target id="@+id/stopLossLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="42" startOffset="4" endLine="56" endOffset="59"/></Target><Target id="@+id/stopLossInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="50" startOffset="8" endLine="54" endOffset="47"/></Target><Target id="@+id/confirmButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="58" startOffset="4" endLine="63" endOffset="43"/></Target><Target id="@+id/closePositionButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="65" startOffset="4" endLine="71" endOffset="43"/></Target><Target id="@+id/cancelButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="73" startOffset="4" endLine="78" endOffset="31"/></Target></Targets></Layout>