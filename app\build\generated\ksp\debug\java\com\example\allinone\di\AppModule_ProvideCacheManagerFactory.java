package com.example.allinone.di;

import android.content.Context;
import com.example.allinone.cache.CacheManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class AppModule_ProvideCacheManagerFactory implements Factory<CacheManager> {
  private final Provider<Context> contextProvider;

  public AppModule_ProvideCacheManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public CacheManager get() {
    return provideCacheManager(contextProvider.get());
  }

  public static AppModule_ProvideCacheManagerFactory create(Provider<Context> contextProvider) {
    return new AppModule_ProvideCacheManagerFactory(contextProvider);
  }

  public static CacheManager provideCacheManager(Context context) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideCacheManager(context));
  }
}
