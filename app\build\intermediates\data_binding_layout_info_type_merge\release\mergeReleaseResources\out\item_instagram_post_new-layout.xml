<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_instagram_post_new" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_instagram_post_new.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_instagram_post_new_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="186" endOffset="51"/></Target><Target id="@+id/textMediaType" view="TextView"><Expressions/><location startLine="20" startOffset="8" endLine="34" endOffset="32"/></Target><Target id="@+id/imagePost" view="ImageView"><Expressions/><location startLine="37" startOffset="8" endLine="45" endOffset="70"/></Target><Target id="@+id/textCaption" view="TextView"><Expressions/><location startLine="48" startOffset="8" endLine="62" endOffset="94"/></Target><Target id="@+id/textDate" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="75" endOffset="39"/></Target><Target id="@+id/metricsRow1" view="LinearLayout"><Expressions/><location startLine="78" startOffset="8" endLine="112" endOffset="22"/></Target><Target id="@+id/textLikes" view="TextView"><Expressions/><location startLine="88" startOffset="12" endLine="94" endOffset="41"/></Target><Target id="@+id/textComments" view="TextView"><Expressions/><location startLine="96" startOffset="12" endLine="102" endOffset="41"/></Target><Target id="@+id/textReach" view="TextView"><Expressions/><location startLine="104" startOffset="12" endLine="110" endOffset="41"/></Target><Target id="@+id/textEngagement" view="TextView"><Expressions/><location startLine="115" startOffset="8" endLine="124" endOffset="68"/></Target><Target id="@+id/optionalMetrics" view="LinearLayout"><Expressions/><location startLine="127" startOffset="8" endLine="169" endOffset="22"/></Target><Target id="@+id/labelImpressions" view="TextView"><Expressions/><location startLine="137" startOffset="12" endLine="142" endOffset="41"/></Target><Target id="@+id/textImpressions" view="TextView"><Expressions/><location startLine="144" startOffset="12" endLine="151" endOffset="41"/></Target><Target id="@+id/labelSaves" view="TextView"><Expressions/><location startLine="153" startOffset="12" endLine="158" endOffset="41"/></Target><Target id="@+id/textSaves" view="TextView"><Expressions/><location startLine="160" startOffset="12" endLine="167" endOffset="41"/></Target><Target id="@+id/textHashtags" view="TextView"><Expressions/><location startLine="172" startOffset="8" endLine="183" endOffset="56"/></Target></Targets></Layout>