// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentInstagramInsightsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final MaterialCardView cardTopPost;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView textAvgEngagementRate;

  @NonNull
  public final TextView textConsistencyScore;

  @NonNull
  public final TextView textEngagementTrend;

  @NonNull
  public final TextView textGrowthPotential;

  @NonNull
  public final TextView textImageCount;

  @NonNull
  public final TextView textImageEngagement;

  @NonNull
  public final TextView textInsightsMessage;

  @NonNull
  public final TextView textInsightsTitle;

  @NonNull
  public final TextView textPostingFrequency;

  @NonNull
  public final TextView textReachTrend;

  @NonNull
  public final TextView textTopPostCaption;

  @NonNull
  public final TextView textTopPostEngagement;

  @NonNull
  public final TextView textTopPostInteractions;

  @NonNull
  public final TextView textTotalEngagement;

  @NonNull
  public final TextView textTotalPosts;

  @NonNull
  public final TextView textTotalReach;

  @NonNull
  public final TextView textVideoCount;

  @NonNull
  public final TextView textVideoEngagement;

  private FragmentInstagramInsightsBinding(@NonNull ScrollView rootView,
      @NonNull MaterialCardView cardTopPost, @NonNull ProgressBar progressBar,
      @NonNull TextView textAvgEngagementRate, @NonNull TextView textConsistencyScore,
      @NonNull TextView textEngagementTrend, @NonNull TextView textGrowthPotential,
      @NonNull TextView textImageCount, @NonNull TextView textImageEngagement,
      @NonNull TextView textInsightsMessage, @NonNull TextView textInsightsTitle,
      @NonNull TextView textPostingFrequency, @NonNull TextView textReachTrend,
      @NonNull TextView textTopPostCaption, @NonNull TextView textTopPostEngagement,
      @NonNull TextView textTopPostInteractions, @NonNull TextView textTotalEngagement,
      @NonNull TextView textTotalPosts, @NonNull TextView textTotalReach,
      @NonNull TextView textVideoCount, @NonNull TextView textVideoEngagement) {
    this.rootView = rootView;
    this.cardTopPost = cardTopPost;
    this.progressBar = progressBar;
    this.textAvgEngagementRate = textAvgEngagementRate;
    this.textConsistencyScore = textConsistencyScore;
    this.textEngagementTrend = textEngagementTrend;
    this.textGrowthPotential = textGrowthPotential;
    this.textImageCount = textImageCount;
    this.textImageEngagement = textImageEngagement;
    this.textInsightsMessage = textInsightsMessage;
    this.textInsightsTitle = textInsightsTitle;
    this.textPostingFrequency = textPostingFrequency;
    this.textReachTrend = textReachTrend;
    this.textTopPostCaption = textTopPostCaption;
    this.textTopPostEngagement = textTopPostEngagement;
    this.textTopPostInteractions = textTopPostInteractions;
    this.textTotalEngagement = textTotalEngagement;
    this.textTotalPosts = textTotalPosts;
    this.textTotalReach = textTotalReach;
    this.textVideoCount = textVideoCount;
    this.textVideoEngagement = textVideoEngagement;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentInstagramInsightsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentInstagramInsightsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_instagram_insights, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentInstagramInsightsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cardTopPost;
      MaterialCardView cardTopPost = ViewBindings.findChildViewById(rootView, id);
      if (cardTopPost == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.textAvgEngagementRate;
      TextView textAvgEngagementRate = ViewBindings.findChildViewById(rootView, id);
      if (textAvgEngagementRate == null) {
        break missingId;
      }

      id = R.id.textConsistencyScore;
      TextView textConsistencyScore = ViewBindings.findChildViewById(rootView, id);
      if (textConsistencyScore == null) {
        break missingId;
      }

      id = R.id.textEngagementTrend;
      TextView textEngagementTrend = ViewBindings.findChildViewById(rootView, id);
      if (textEngagementTrend == null) {
        break missingId;
      }

      id = R.id.textGrowthPotential;
      TextView textGrowthPotential = ViewBindings.findChildViewById(rootView, id);
      if (textGrowthPotential == null) {
        break missingId;
      }

      id = R.id.textImageCount;
      TextView textImageCount = ViewBindings.findChildViewById(rootView, id);
      if (textImageCount == null) {
        break missingId;
      }

      id = R.id.textImageEngagement;
      TextView textImageEngagement = ViewBindings.findChildViewById(rootView, id);
      if (textImageEngagement == null) {
        break missingId;
      }

      id = R.id.textInsightsMessage;
      TextView textInsightsMessage = ViewBindings.findChildViewById(rootView, id);
      if (textInsightsMessage == null) {
        break missingId;
      }

      id = R.id.textInsightsTitle;
      TextView textInsightsTitle = ViewBindings.findChildViewById(rootView, id);
      if (textInsightsTitle == null) {
        break missingId;
      }

      id = R.id.textPostingFrequency;
      TextView textPostingFrequency = ViewBindings.findChildViewById(rootView, id);
      if (textPostingFrequency == null) {
        break missingId;
      }

      id = R.id.textReachTrend;
      TextView textReachTrend = ViewBindings.findChildViewById(rootView, id);
      if (textReachTrend == null) {
        break missingId;
      }

      id = R.id.textTopPostCaption;
      TextView textTopPostCaption = ViewBindings.findChildViewById(rootView, id);
      if (textTopPostCaption == null) {
        break missingId;
      }

      id = R.id.textTopPostEngagement;
      TextView textTopPostEngagement = ViewBindings.findChildViewById(rootView, id);
      if (textTopPostEngagement == null) {
        break missingId;
      }

      id = R.id.textTopPostInteractions;
      TextView textTopPostInteractions = ViewBindings.findChildViewById(rootView, id);
      if (textTopPostInteractions == null) {
        break missingId;
      }

      id = R.id.textTotalEngagement;
      TextView textTotalEngagement = ViewBindings.findChildViewById(rootView, id);
      if (textTotalEngagement == null) {
        break missingId;
      }

      id = R.id.textTotalPosts;
      TextView textTotalPosts = ViewBindings.findChildViewById(rootView, id);
      if (textTotalPosts == null) {
        break missingId;
      }

      id = R.id.textTotalReach;
      TextView textTotalReach = ViewBindings.findChildViewById(rootView, id);
      if (textTotalReach == null) {
        break missingId;
      }

      id = R.id.textVideoCount;
      TextView textVideoCount = ViewBindings.findChildViewById(rootView, id);
      if (textVideoCount == null) {
        break missingId;
      }

      id = R.id.textVideoEngagement;
      TextView textVideoEngagement = ViewBindings.findChildViewById(rootView, id);
      if (textVideoEngagement == null) {
        break missingId;
      }

      return new FragmentInstagramInsightsBinding((ScrollView) rootView, cardTopPost, progressBar,
          textAvgEngagementRate, textConsistencyScore, textEngagementTrend, textGrowthPotential,
          textImageCount, textImageEngagement, textInsightsMessage, textInsightsTitle,
          textPostingFrequency, textReachTrend, textTopPostCaption, textTopPostEngagement,
          textTopPostInteractions, textTotalEngagement, textTotalPosts, textTotalReach,
          textVideoCount, textVideoEngagement);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
