package com.example.allinone.di;

import com.example.allinone.data.local.AppDatabase;
import com.example.allinone.data.local.dao.CachedWTStudentDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class AppModule_ProvideCachedWTStudentDaoFactory implements Factory<CachedWTStudentDao> {
  private final Provider<AppDatabase> databaseProvider;

  public AppModule_ProvideCachedWTStudentDaoFactory(Provider<AppDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public CachedWTStudentDao get() {
    return provideCachedWTStudentDao(databaseProvider.get());
  }

  public static AppModule_ProvideCachedWTStudentDaoFactory create(
      Provider<AppDatabase> databaseProvider) {
    return new AppModule_ProvideCachedWTStudentDaoFactory(databaseProvider);
  }

  public static CachedWTStudentDao provideCachedWTStudentDao(AppDatabase database) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideCachedWTStudentDao(database));
  }
}
