package com.example.allinone.di;

import android.content.Context;
import com.example.allinone.utils.LogcatHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class AppModule_ProvideLogcatHelperFactory implements Factory<LogcatHelper> {
  private final Provider<Context> contextProvider;

  public AppModule_ProvideLogcatHelperFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public LogcatHelper get() {
    return provideLogcatHelper(contextProvider.get());
  }

  public static AppModule_ProvideLogcatHelperFactory create(Provider<Context> contextProvider) {
    return new AppModule_ProvideLogcatHelperFactory(contextProvider);
  }

  public static LogcatHelper provideLogcatHelper(Context context) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideLogcatHelper(context));
  }
}
