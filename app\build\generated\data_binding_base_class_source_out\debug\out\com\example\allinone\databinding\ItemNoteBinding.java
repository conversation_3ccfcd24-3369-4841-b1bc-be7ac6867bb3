// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemNoteBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final LinearLayout attachmentsSection;

  @NonNull
  public final LinearLayout imageContainer;

  @NonNull
  public final TextView noteContent;

  @NonNull
  public final TextView noteDate;

  @NonNull
  public final TextView noteTitle;

  @NonNull
  public final ImageButton shareButton;

  @NonNull
  public final TextView voiceNoteCountText;

  @NonNull
  public final LinearLayout voiceNoteIndicator;

  private ItemNoteBinding(@NonNull MaterialCardView rootView,
      @NonNull LinearLayout attachmentsSection, @NonNull LinearLayout imageContainer,
      @NonNull TextView noteContent, @NonNull TextView noteDate, @NonNull TextView noteTitle,
      @NonNull ImageButton shareButton, @NonNull TextView voiceNoteCountText,
      @NonNull LinearLayout voiceNoteIndicator) {
    this.rootView = rootView;
    this.attachmentsSection = attachmentsSection;
    this.imageContainer = imageContainer;
    this.noteContent = noteContent;
    this.noteDate = noteDate;
    this.noteTitle = noteTitle;
    this.shareButton = shareButton;
    this.voiceNoteCountText = voiceNoteCountText;
    this.voiceNoteIndicator = voiceNoteIndicator;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemNoteBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemNoteBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_note, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemNoteBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.attachmentsSection;
      LinearLayout attachmentsSection = ViewBindings.findChildViewById(rootView, id);
      if (attachmentsSection == null) {
        break missingId;
      }

      id = R.id.imageContainer;
      LinearLayout imageContainer = ViewBindings.findChildViewById(rootView, id);
      if (imageContainer == null) {
        break missingId;
      }

      id = R.id.noteContent;
      TextView noteContent = ViewBindings.findChildViewById(rootView, id);
      if (noteContent == null) {
        break missingId;
      }

      id = R.id.noteDate;
      TextView noteDate = ViewBindings.findChildViewById(rootView, id);
      if (noteDate == null) {
        break missingId;
      }

      id = R.id.noteTitle;
      TextView noteTitle = ViewBindings.findChildViewById(rootView, id);
      if (noteTitle == null) {
        break missingId;
      }

      id = R.id.shareButton;
      ImageButton shareButton = ViewBindings.findChildViewById(rootView, id);
      if (shareButton == null) {
        break missingId;
      }

      id = R.id.voiceNoteCountText;
      TextView voiceNoteCountText = ViewBindings.findChildViewById(rootView, id);
      if (voiceNoteCountText == null) {
        break missingId;
      }

      id = R.id.voiceNoteIndicator;
      LinearLayout voiceNoteIndicator = ViewBindings.findChildViewById(rootView, id);
      if (voiceNoteIndicator == null) {
        break missingId;
      }

      return new ItemNoteBinding((MaterialCardView) rootView, attachmentsSection, imageContainer,
          noteContent, noteDate, noteTitle, shareButton, voiceNoteCountText, voiceNoteIndicator);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
