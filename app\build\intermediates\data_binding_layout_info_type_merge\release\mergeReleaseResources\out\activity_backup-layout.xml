<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_backup" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\activity_backup.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_backup_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="112" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="8" startOffset="4" endLine="19" endOffset="40"/></Target><Target id="@+id/backup_card" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="21" startOffset="4" endLine="59" endOffset="39"/></Target><Target id="@+id/create_backup_button" view="Button"><Expressions/><location startLine="51" startOffset="12" endLine="57" endOffset="46"/></Target><Target id="@+id/backups_card" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="61" startOffset="4" endLine="100" endOffset="39"/></Target><Target id="@+id/no_backups_text" view="TextView"><Expressions/><location startLine="85" startOffset="12" endLine="92" endOffset="43"/></Target><Target id="@+id/backups_recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="94" startOffset="12" endLine="98" endOffset="48"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="102" startOffset="4" endLine="110" endOffset="51"/></Target></Targets></Layout>