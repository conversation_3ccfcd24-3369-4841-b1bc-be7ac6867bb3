<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.8.2" type="partial_results">
    <map id="NotificationPermission">
        <location id="class"
            file="$GRADLE_USER_HOME/caches/8.10.2/transforms/c58cd8b4696f8fa5bb0d8d3c36af5768/transformed/exoplayer-common-2.19.1/jars/classes.jar"/>
        <entry
            name="className"
            string="com/google/android/exoplayer2/util/NotificationUtil"/>
        <entry
            name="source"
            boolean="true"/>
    </map>
    <map id="UnsafeImplicitIntentLaunch">
            <map id="actionsSent">
                    <map id="android.intent.action.DIAL (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/allinone/ui/compose/wt/WTComponents.kt"
                            line="977"
                            column="54"
                            startOffset="40492"
                            endLine="977"
                            endColumn="105"
                            endOffset="40543"/>
                    </map>
                    <map id="android.intent.action.SENDTO (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/allinone/adapters/NotesAdapter.kt"
                            line="607"
                            column="30"
                            startOffset="29858"
                            endLine="607"
                            endColumn="58"
                            endOffset="29886"/>
                        <location id="1"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/allinone/ui/compose/wt/WTComponents.kt"
                            line="1036"
                            column="54"
                            startOffset="43687"
                            endLine="1036"
                            endColumn="110"
                            endOffset="43743"/>
                    </map>
                    <map id="android.intent.action.VIEW (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/allinone/adapters/NotesAdapter.kt"
                            line="404"
                            column="30"
                            startOffset="20504"
                            endLine="404"
                            endColumn="56"
                            endOffset="20530"/>
                        <location id="1"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/allinone/adapters/NotesAdapter.kt"
                            line="593"
                            column="30"
                            startOffset="29168"
                            endLine="593"
                            endColumn="81"
                            endOffset="29219"/>
                        <location id="2"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/allinone/ui/components/InteractiveHtmlText.kt"
                            line="217"
                            column="38"
                            startOffset="7427"
                            endLine="217"
                            endColumn="92"
                            endOffset="7481"/>
                        <location id="3"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/allinone/ui/components/InteractiveHtmlText.kt"
                            line="317"
                            column="42"
                            startOffset="10986"
                            endLine="317"
                            endColumn="96"
                            endOffset="11040"/>
                        <location id="4"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/allinone/ui/components/InteractiveHtmlText.kt"
                            line="402"
                            column="42"
                            startOffset="13878"
                            endLine="402"
                            endColumn="96"
                            endOffset="13932"/>
                        <location id="5"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/allinone/ui/compose/wt/WTComponents.kt"
                            line="1007"
                            column="54"
                            startOffset="42119"
                            endLine="1007"
                            endColumn="120"
                            endOffset="42185"/>
                        <location id="6"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/allinone/ui/compose/wt/WTComponents.kt"
                            line="1065"
                            column="54"
                            startOffset="45233"
                            endLine="1065"
                            endColumn="131"
                            endOffset="45310"/>
                        <location id="7"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/allinone/ui/compose/wt/WTDialogs.kt"
                            line="483"
                            column="63"
                            startOffset="20596"
                            endLine="483"
                            endColumn="94"
                            endOffset="20627"/>
                    </map>
            </map>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.example.allinone.MainActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.ai_message_bg"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="97"
            column="12"
            startOffset="4143"
            endLine="97"
            endColumn="32"
            endOffset="4163"/>
        <location id="R.color.albumCard"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="60"
            column="12"
            startOffset="2694"
            endLine="60"
            endColumn="28"
            endOffset="2710"/>
        <location id="R.color.background_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="91"
            column="12"
            startOffset="3907"
            endLine="91"
            endColumn="35"
            endOffset="3930"/>
        <location id="R.color.bg_tag_blue"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="54"
            column="12"
            startOffset="2430"
            endLine="54"
            endColumn="30"
            endOffset="2448"/>
        <location id="R.color.boldTextColor"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="64"
            column="12"
            startOffset="2856"
            endLine="64"
            endColumn="32"
            endOffset="2876"/>
        <location id="R.color.bottom_nav_item_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/color/bottom_nav_item_color.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="5"
            endColumn="12"
            endOffset="251"/>
        <location id="R.color.bottom_nav_item_color_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/color/bottom_nav_item_color_light.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="5"
            endColumn="12"
            endOffset="241"/>
        <location id="R.color.bright_tab_unselected"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="40"
            column="12"
            startOffset="1902"
            endLine="40"
            endColumn="40"
            endOffset="1930"/>
        <location id="R.color.cardStroke"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="61"
            column="12"
            startOffset="2739"
            endLine="61"
            endColumn="29"
            endOffset="2756"/>
        <location id="R.color.carousel_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="69"
            column="12"
            startOffset="3086"
            endLine="69"
            endColumn="38"
            endOffset="3112"/>
        <location id="R.color.carousel_orange"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="79"
            column="12"
            startOffset="3474"
            endLine="79"
            endColumn="34"
            endOffset="3496"/>
        <location id="R.color.chip_background_selector"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/color/chip_background_selector.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="5"
            endColumn="12"
            endOffset="269"/>
        <location id="R.color.colorError"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="26"
            column="12"
            startOffset="972"
            endLine="26"
            endColumn="29"
            endOffset="989"/>
        <location id="R.color.dark_gray"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="22"
            column="12"
            startOffset="843"
            endLine="22"
            endColumn="28"
            endOffset="859"/>
        <location id="R.color.dialog_action_button_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/color/dialog_action_button_color.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="6"
            endColumn="12"
            endOffset="330"/>
        <location id="R.color.dialog_delete_button_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/color/dialog_delete_button_color.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="6"
            endColumn="12"
            endOffset="303"/>
        <location id="R.color.error_dark"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="85"
            column="12"
            startOffset="3746"
            endLine="85"
            endColumn="29"
            endOffset="3763"/>
        <location id="R.color.error_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="42"
            column="12"
            startOffset="2001"
            endLine="42"
            endColumn="30"
            endOffset="2019"/>
        <location id="R.color.feedCard"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="57"
            column="12"
            startOffset="2560"
            endLine="57"
            endColumn="27"
            endOffset="2575"/>
        <location id="R.color.good_orange"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="73"
            column="12"
            startOffset="3245"
            endLine="73"
            endColumn="30"
            endOffset="3263"/>
        <location id="R.color.gray_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="6"
            column="12"
            startOffset="197"
            endLine="6"
            endColumn="29"
            endOffset="214"/>
        <location id="R.color.image_green"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="80"
            column="12"
            startOffset="3525"
            endLine="80"
            endColumn="30"
            endOffset="3543"/>
        <location id="R.color.lesson_event_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="15"
            column="12"
            startOffset="639"
            endLine="15"
            endColumn="37"
            endOffset="664"/>
        <location id="R.color.light_blue"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="81"
            column="12"
            startOffset="3572"
            endLine="81"
            endColumn="29"
            endOffset="3589"/>
        <location id="R.color.medium_gray"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="23"
            column="12"
            startOffset="888"
            endLine="23"
            endColumn="30"
            endOffset="906"/>
        <location id="R.color.mtrl_textinput_default_box_stroke_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="4"
            column="12"
            startOffset="173"
            endLine="4"
            endColumn="58"
            endOffset="219"/>
        <location id="R.color.navy_text_secondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="36"
            column="12"
            startOffset="1643"
            endLine="36"
            endColumn="38"
            endOffset="1669"/>
        <location id="R.color.on_surface_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="93"
            column="12"
            startOffset="4008"
            endLine="93"
            endColumn="35"
            endOffset="4031"/>
        <location id="R.color.poor_red"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="74"
            column="12"
            startOffset="3292"
            endLine="74"
            endColumn="27"
            endOffset="3307"/>
        <location id="R.color.purple"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="47"
            column="12"
            startOffset="2180"
            endLine="47"
            endColumn="25"
            endOffset="2193"/>
        <location id="R.color.reelsCard"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="58"
            column="12"
            startOffset="2604"
            endLine="58"
            endColumn="28"
            endOffset="2620"/>
        <location id="R.color.reels_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="67"
            column="12"
            startOffset="2964"
            endLine="67"
            endColumn="35"
            endOffset="2987"/>
        <location id="R.color.reels_purple"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="77"
            column="12"
            startOffset="3380"
            endLine="77"
            endColumn="31"
            endOffset="3399"/>
        <location id="R.color.registration_end"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="51"
            column="12"
            startOffset="2334"
            endLine="51"
            endColumn="35"
            endOffset="2357"/>
        <location id="R.color.registration_start"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="50"
            column="12"
            startOffset="2264"
            endLine="50"
            endColumn="37"
            endOffset="2289"/>
        <location id="R.color.storyCard"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="59"
            column="12"
            startOffset="2649"
            endLine="59"
            endColumn="28"
            endOffset="2665"/>
        <location id="R.color.surface_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="92"
            column="12"
            startOffset="3959"
            endLine="92"
            endColumn="32"
            endOffset="3979"/>
        <location id="R.color.task_checkbox_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="100"
            column="12"
            startOffset="4224"
            endLine="100"
            endColumn="38"
            endOffset="4250"/>
        <location id="R.color.textPrimary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="18"
            column="12"
            startOffset="721"
            endLine="18"
            endColumn="30"
            endOffset="739"/>
        <location id="R.color.text_primary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="84"
            column="12"
            startOffset="3698"
            endLine="84"
            endColumn="31"
            endOffset="3717"/>
        <location id="R.color.user_message_bg"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="96"
            column="12"
            startOffset="4092"
            endLine="96"
            endColumn="34"
            endOffset="4114"/>
        <location id="R.color.video_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="68"
            column="12"
            startOffset="3025"
            endLine="68"
            endColumn="35"
            endOffset="3048"/>
        <location id="R.color.video_blue"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="78"
            column="12"
            startOffset="3428"
            endLine="78"
            endColumn="29"
            endOffset="3445"/>
        <location id="R.color.warning_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="43"
            column="12"
            startOffset="2048"
            endLine="43"
            endColumn="32"
            endOffset="2068"/>
        <location id="R.color.wt_bottom_nav_item_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/color/wt_bottom_nav_item_color.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="5"
            endColumn="12"
            endOffset="251"/>
        <location id="R.dimen.task_indent_margin"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="4"
            column="12"
            startOffset="120"
            endLine="4"
            endColumn="37"
            endOffset="145"/>
        <location id="R.drawable.bg_category_chip"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/bg_category_chip.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="13"
            endColumn="9"
            endOffset="333"/>
        <location id="R.drawable.bg_current_day"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/bg_current_day.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="26"
            endColumn="12"
            endOffset="813"/>
        <location id="R.drawable.bg_day_with_event"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/bg_day_with_event.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="17"
            endColumn="14"
            endOffset="627"/>
        <location id="R.drawable.bg_day_with_events"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/bg_day_with_events.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="31"
            endColumn="12"
            endOffset="1004"/>
        <location id="R.drawable.bg_day_with_lesson"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/bg_day_with_lesson.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="17"
            endColumn="14"
            endOffset="633"/>
        <location id="R.drawable.bg_day_with_registration"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/bg_day_with_registration.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="17"
            endColumn="14"
            endOffset="625"/>
        <location id="R.drawable.bg_selected_day"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/bg_selected_day.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="26"
            endColumn="12"
            endOffset="813"/>
        <location id="R.drawable.bg_tag_blue"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/bg_tag_blue.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="6"
            endColumn="9"
            endOffset="224"/>
        <location id="R.drawable.bg_tag_green"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/bg_tag_green.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="6"
            endColumn="9"
            endOffset="224"/>
        <location id="R.drawable.bg_tag_red"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/bg_tag_red.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="6"
            endColumn="9"
            endOffset="243"/>
        <location id="R.drawable.border_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/border_background.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="13"
            endColumn="9"
            endOffset="394"/>
        <location id="R.drawable.completed_exercise_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/completed_exercise_background.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="7"
            endColumn="9"
            endOffset="328"/>
        <location id="R.drawable.fully_uncompleted_exercise_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/fully_uncompleted_exercise_background.xml"
            line="3"
            column="1"
            startOffset="119"
            endLine="8"
            endColumn="9"
            endOffset="406"/>
        <location id="R.drawable.ic_add"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_add.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="350"/>
        <location id="R.drawable.ic_add_photo"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_add_photo.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="10"
            endOffset="649"/>
        <location id="R.drawable.ic_attach_image"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_attach_image.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="457"/>
        <location id="R.drawable.ic_back"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_back.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="392"/>
        <location id="R.drawable.ic_backup"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_backup.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="504"/>
        <location id="R.drawable.ic_calendar"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_calendar.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="10"
            endOffset="623"/>
        <location id="R.drawable.ic_call"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_call.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="10"
            endOffset="728"/>
        <location id="R.drawable.ic_checkbox"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_checkbox.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="10"
            endOffset="517"/>
        <location id="R.drawable.ic_chevron_left"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_chevron_left.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="10"
            endOffset="419"/>
        <location id="R.drawable.ic_chevron_right"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_chevron_right.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="10"
            endOffset="420"/>
        <location id="R.drawable.ic_clear_data"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_clear_data.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="399"/>
        <location id="R.drawable.ic_cleardata"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_cleardata.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="398"/>
        <location id="R.drawable.ic_code"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_code.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="10"
            endOffset="475"/>
        <location id="R.drawable.ic_dashboard"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_dashboard.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="392"/>
        <location id="R.drawable.ic_database"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_database.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="16"
            endColumn="10"
            endOffset="722"/>
        <location id="R.drawable.ic_delete"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_delete.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="399"/>
        <location id="R.drawable.ic_draw"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_draw.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="557"/>
        <location id="R.drawable.ic_edit"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_edit.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="498"/>
        <location id="R.drawable.ic_empty_state"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_empty_state.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="587"/>
        <location id="R.drawable.ic_error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_error.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="431"/>
        <location id="R.drawable.ic_exercise"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_exercise.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="608"/>
        <location id="R.drawable.ic_expand_less"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_expand_less.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="10"
            endOffset="415"/>
        <location id="R.drawable.ic_expand_more"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_expand_more.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="10"
            endOffset="414"/>
        <location id="R.drawable.ic_expense"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_expense.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="382"/>
        <location id="R.drawable.ic_file"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_file.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="416"/>
        <location id="R.drawable.ic_fitness"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_fitness.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="592"/>
        <location id="R.drawable.ic_folder"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_folder.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="417"/>
        <location id="R.drawable.ic_format_bold"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_format_bold.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="9"
            endColumn="10"
            endOffset="556"/>
        <location id="R.drawable.ic_format_italic"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_format_italic.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="9"
            endColumn="10"
            endOffset="337"/>
        <location id="R.drawable.ic_format_list_bulleted"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_format_list_bulleted.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="9"
            endColumn="10"
            endOffset="608"/>
        <location id="R.drawable.ic_format_list_numbered"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_format_list_numbered.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="9"
            endColumn="10"
            endOffset="464"/>
        <location id="R.drawable.ic_format_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_format_text.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="440"/>
        <location id="R.drawable.ic_format_underline"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_format_underline.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="9"
            endColumn="10"
            endOffset="407"/>
        <location id="R.drawable.ic_graduation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_graduation.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="393"/>
        <location id="R.drawable.ic_history"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_history.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="578"/>
        <location id="R.drawable.ic_home"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_home.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="350"/>
        <location id="R.drawable.ic_income"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_income.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="384"/>
        <location id="R.drawable.ic_instagram"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_instagram.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="1552"/>
        <location id="R.drawable.ic_instagram_posts"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_instagram_posts.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="442"/>
        <location id="R.drawable.ic_investment"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_investment.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="384"/>
        <location id="R.drawable.ic_investments"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_investments.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="378"/>
        <location id="R.drawable.ic_lessons"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_lessons.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="522"/>
        <location id="R.drawable.ic_log"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_log.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="584"/>
        <location id="R.drawable.ic_menu"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_menu.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="372"/>
        <location id="R.drawable.ic_no_registrations"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_no_registrations.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="10"
            endOffset="715"/>
        <location id="R.drawable.ic_no_students"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_no_students.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="10"
            endOffset="899"/>
        <location id="R.drawable.ic_note"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_note.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="474"/>
        <location id="R.drawable.ic_notes"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notes.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="373"/>
        <location id="R.drawable.ic_pause"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_pause.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="354"/>
        <location id="R.drawable.ic_play"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_play.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="329"/>
        <location id="R.drawable.ic_program"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_program.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="586"/>
        <location id="R.drawable.ic_registration"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_registration.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="583"/>
        <location id="R.drawable.ic_remove"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_remove.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="332"/>
        <location id="R.drawable.ic_reports"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_reports.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="5"
            endColumn="10"
            endOffset="437"/>
        <location id="R.drawable.ic_save"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_save.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="531"/>
        <location id="R.drawable.ic_search_white"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_search_white.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="10"
            endOffset="596"/>
        <location id="R.drawable.ic_stats"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_stats.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="356"/>
        <location id="R.drawable.ic_stop"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_stop.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="332"/>
        <location id="R.drawable.ic_student"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_student.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="394"/>
        <location id="R.drawable.ic_students"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_students.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="10"
            endOffset="699"/>
        <location id="R.drawable.ic_view_list"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_view_list.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="10"
            endOffset="479"/>
        <location id="R.drawable.ic_whatsapp"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_whatsapp.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="13"
            endColumn="10"
            endOffset="745"/>
        <location id="R.drawable.ic_wt"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_wt.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="434"/>
        <location id="R.drawable.ic_wt_registers"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_wt_registers.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="485"/>
        <location id="R.drawable.rounded_corner_bg"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_corner_bg.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="5"
            endColumn="9"
            endOffset="193"/>
        <location id="R.drawable.simple_text_splash"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/simple_text_splash.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="105"
            endColumn="10"
            endOffset="3421"/>
        <location id="R.drawable.splash_layout_drawable"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/splash_layout_drawable.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="6"
            endColumn="14"
            endOffset="199"/>
        <location id="R.drawable.uncompleted_exercise_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/uncompleted_exercise_background.xml"
            line="3"
            column="1"
            startOffset="124"
            endLine="8"
            endColumn="9"
            endOffset="408"/>
        <location id="R.layout.dialog_add_event"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_event.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="90"
            endColumn="16"
            endOffset="3656"/>
        <location id="R.layout.dialog_add_program"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_program.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="67"
            endColumn="14"
            endOffset="2859"/>
        <location id="R.layout.dialog_add_student"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_student.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="129"
            endColumn="41"
            endOffset="5716"/>
        <location id="R.layout.dialog_add_task"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_task.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="127"
            endColumn="16"
            endOffset="5112"/>
        <location id="R.layout.dialog_edit_lesson"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_edit_lesson.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="74"
            endColumn="16"
            endOffset="2490"/>
        <location id="R.layout.dialog_edit_program"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_edit_program.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="67"
            endColumn="14"
            endOffset="2859"/>
        <location id="R.layout.dialog_edit_seminar"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_edit_seminar.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="127"
            endColumn="41"
            endOffset="5885"/>
        <location id="R.layout.dialog_edit_wt_student"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_edit_wt_student.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="134"
            endColumn="41"
            endOffset="5890"/>
        <location id="R.layout.dialog_expense_investment"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_expense_investment.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="76"
            endColumn="53"
            endOffset="3244"/>
        <location id="R.layout.dialog_income_investment"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_income_investment.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="61"
            endColumn="16"
            endOffset="2405"/>
        <location id="R.layout.dialog_pin_input"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_pin_input.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="40"
            endColumn="16"
            endOffset="1482"/>
        <location id="R.layout.dialog_post_details"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_post_details.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="165"
            endColumn="41"
            endOffset="7133"/>
        <location id="R.layout.dialog_program_details"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_program_details.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="44"
            endColumn="16"
            endOffset="1685"/>
        <location id="R.layout.dialog_progress"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_progress.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="23"
            endColumn="16"
            endOffset="816"/>
        <location id="R.layout.dialog_student_details"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_student_details.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="102"
            endColumn="16"
            endOffset="3976"/>
        <location id="R.layout.dialog_workout_details"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_workout_details.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="50"
            endColumn="16"
            endOffset="1892"/>
        <location id="R.layout.fragment_home"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="7"
            endColumn="59"
            endOffset="353"/>
        <location id="R.layout.fragment_transactions_overview"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_transactions_overview.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="257"
            endColumn="14"
            endOffset="12039"/>
        <location id="R.layout.item_add_exercise"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_add_exercise.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="130"
            endColumn="16"
            endOffset="5606"/>
        <location id="R.layout.item_file_structure"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_file_structure.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="23"
            endColumn="16"
            endOffset="775"/>
        <location id="R.layout.item_lesson"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_lesson.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="60"
            endColumn="37"
            endOffset="2286"/>
        <location id="R.layout.layout_page_header"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_page_header.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="37"
            endColumn="53"
            endOffset="1545"/>
        <location id="R.layout.nav_header"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/nav_header.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="15"
            endColumn="16"
            endOffset="543"/>
        <location id="R.layout.pie_chart_tooltip"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/pie_chart_tooltip.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="28"
            endColumn="37"
            endOffset="979"/>
        <location id="R.layout.splash_text_layout"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/splash_text_layout.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="22"
            endColumn="53"
            endOffset="936"/>
        <location id="R.layout.theme_switch_layout"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/theme_switch_layout.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="23"
            endColumn="16"
            endOffset="767"/>
        <location id="R.menu.bottom_nav_menu"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/bottom_nav_menu.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="15"
            endColumn="8"
            endOffset="525"/>
        <location id="R.menu.drawer_menu"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/drawer_menu.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="60"
            endColumn="8"
            endOffset="2262"/>
        <location id="R.menu.instagram_bottom_nav_menu"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/instagram_bottom_nav_menu.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="15"
            endColumn="8"
            endOffset="508"/>
        <location id="R.menu.menu_edit_note"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/menu_edit_note.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="9"
            endColumn="8"
            endOffset="337"/>
        <location id="R.menu.menu_group_options"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/menu_group_options.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="14"
            endColumn="8"
            endOffset="404"/>
        <location id="R.menu.menu_task_options"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/menu_task_options.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="8"
            endOffset="386"/>
        <location id="R.menu.menu_tasks"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/menu_tasks.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="19"
            endColumn="8"
            endOffset="629"/>
        <location id="R.menu.search_history"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/search_history.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="12"
            endColumn="8"
            endOffset="450"/>
        <location id="R.menu.search_notes"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/search_notes.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="12"
            endColumn="8"
            endOffset="450"/>
        <location id="R.menu.search_register"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/search_register.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="12"
            endColumn="8"
            endOffset="450"/>
        <location id="R.menu.search_students"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/search_students.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="12"
            endColumn="8"
            endOffset="450"/>
        <location id="R.menu.workout_bottom_nav_menu"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/workout_bottom_nav_menu.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="19"
            endColumn="8"
            endOffset="657"/>
        <location id="R.menu.wt_bottom_nav_menu"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/wt_bottom_nav_menu.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="19"
            endColumn="8"
            endOffset="729"/>
        <location id="R.menu.wt_registration_context_menu"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/wt_registration_context_menu.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="8"
            endOffset="386"/>
        <location id="R.menu.wt_student_context_menu"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/wt_student_context_menu.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="9"
            endColumn="8"
            endOffset="284"/>
        <location id="R.plurals.exercise_count"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="314"
            column="14"
            startOffset="18492"
            endLine="314"
            endColumn="35"
            endOffset="18513"/>
        <location id="R.string.active_status"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="83"
            column="13"
            startOffset="4182"
            endLine="83"
            endColumn="33"
            endOffset="4202"/>
        <location id="R.string.add_attachment"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="14"
            column="13"
            startOffset="663"
            endLine="14"
            endColumn="34"
            endOffset="684"/>
        <location id="R.string.add_exercise"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="283"
            column="13"
            startOffset="16377"
            endLine="283"
            endColumn="32"
            endOffset="16396"/>
        <location id="R.string.add_image"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="12"
            column="13"
            startOffset="565"
            endLine="12"
            endColumn="29"
            endOffset="581"/>
        <location id="R.string.add_photo"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="78"
            column="13"
            startOffset="3948"
            endLine="78"
            endColumn="29"
            endOffset="3964"/>
        <location id="R.string.add_program"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="276"
            column="13"
            startOffset="16025"
            endLine="276"
            endColumn="31"
            endOffset="16043"/>
        <location id="R.string.add_receipt"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="100"
            column="13"
            startOffset="5213"
            endLine="100"
            endColumn="31"
            endOffset="5231"/>
        <location id="R.string.add_registration"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="91"
            column="13"
            startOffset="4623"
            endLine="91"
            endColumn="36"
            endOffset="4646"/>
        <location id="R.string.add_student"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="84"
            column="13"
            startOffset="4239"
            endLine="84"
            endColumn="31"
            endOffset="4257"/>
        <location id="R.string.add_student_prompt"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="88"
            column="13"
            startOffset="4488"
            endLine="88"
            endColumn="38"
            endOffset="4513"/>
        <location id="R.string.add_task"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="161"
            column="13"
            startOffset="9156"
            endLine="161"
            endColumn="28"
            endOffset="9171"/>
        <location id="R.string.add_video"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="13"
            column="13"
            startOffset="614"
            endLine="13"
            endColumn="29"
            endOffset="630"/>
        <location id="R.string.all_months"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="112"
            column="13"
            startOffset="6107"
            endLine="112"
            endColumn="30"
            endOffset="6124"/>
        <location id="R.string.all_time_stats"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="293"
            column="13"
            startOffset="17008"
            endLine="293"
            endColumn="34"
            endOffset="17029"/>
        <location id="R.string.amount"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="4"
            column="13"
            startOffset="112"
            endLine="4"
            endColumn="26"
            endOffset="125"/>
        <location id="R.string.amount_format"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="115"
            column="13"
            startOffset="6261"
            endLine="115"
            endColumn="33"
            endOffset="6281"/>
        <location id="R.string.apply"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="111"
            column="13"
            startOffset="6066"
            endLine="111"
            endColumn="25"
            endOffset="6078"/>
        <location id="R.string.attachment_click_to_open"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="150"
            column="13"
            startOffset="8356"
            endLine="150"
            endColumn="44"
            endOffset="8387"/>
        <location id="R.string.attachments_only_for_paid_registrations"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="107"
            column="13"
            startOffset="5717"
            endLine="107"
            endColumn="59"
            endOffset="5763"/>
        <location id="R.string.avg_duration"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="323"
            column="13"
            startOffset="18867"
            endLine="323"
            endColumn="32"
            endOffset="18886"/>
        <location id="R.string.brightness"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="263"
            column="13"
            startOffset="15419"
            endLine="263"
            endColumn="30"
            endOffset="15436"/>
        <location id="R.string.brush_size"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="29"
            column="13"
            startOffset="1536"
            endLine="29"
            endColumn="30"
            endOffset="1553"/>
        <location id="R.string.camera_permission_message"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="122"
            column="13"
            startOffset="6604"
            endLine="122"
            endColumn="45"
            endOffset="6636"/>
        <location id="R.string.camera_permission_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="121"
            column="13"
            startOffset="6533"
            endLine="121"
            endColumn="43"
            endOffset="6563"/>
        <location id="R.string.cannot_delete"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="230"
            column="13"
            startOffset="13743"
            endLine="230"
            endColumn="33"
            endOffset="13763"/>
        <location id="R.string.choose_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="33"
            column="13"
            startOffset="1785"
            endLine="33"
            endColumn="32"
            endOffset="1804"/>
        <location id="R.string.choose_from_gallery"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="120"
            column="13"
            startOffset="6464"
            endLine="120"
            endColumn="39"
            endOffset="6490"/>
        <location id="R.string.clear"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="30"
            column="13"
            startOffset="1588"
            endLine="30"
            endColumn="25"
            endOffset="1600"/>
        <location id="R.string.close"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="310"
            column="13"
            startOffset="18352"
            endLine="310"
            endColumn="25"
            endOffset="18364"/>
        <location id="R.string.color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="37"
            column="13"
            startOffset="1994"
            endLine="37"
            endColumn="25"
            endOffset="2006"/>
        <location id="R.string.color_black"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="40"
            column="13"
            startOffset="2072"
            endLine="40"
            endColumn="31"
            endOffset="2090"/>
        <location id="R.string.color_blue"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="43"
            column="13"
            startOffset="2227"
            endLine="43"
            endColumn="30"
            endOffset="2244"/>
        <location id="R.string.color_brown"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="47"
            column="13"
            startOffset="2443"
            endLine="47"
            endColumn="31"
            endOffset="2461"/>
        <location id="R.string.color_green"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="42"
            column="13"
            startOffset="2174"
            endLine="42"
            endColumn="31"
            endOffset="2192"/>
        <location id="R.string.color_orange"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="46"
            column="13"
            startOffset="2388"
            endLine="46"
            endColumn="32"
            endOffset="2407"/>
        <location id="R.string.color_pink"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="49"
            column="13"
            startOffset="2547"
            endLine="49"
            endColumn="30"
            endOffset="2564"/>
        <location id="R.string.color_purple"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="45"
            column="13"
            startOffset="2333"
            endLine="45"
            endColumn="32"
            endOffset="2352"/>
        <location id="R.string.color_red"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="41"
            column="13"
            startOffset="2125"
            endLine="41"
            endColumn="29"
            endOffset="2141"/>
        <location id="R.string.color_teal"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="48"
            column="13"
            startOffset="2496"
            endLine="48"
            endColumn="30"
            endOffset="2513"/>
        <location id="R.string.color_yellow"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="44"
            column="13"
            startOffset="2278"
            endLine="44"
            endColumn="32"
            endOffset="2297"/>
        <location id="R.string.create_drawing"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="23"
            column="13"
            startOffset="1141"
            endLine="23"
            endColumn="34"
            endOffset="1162"/>
        <location id="R.string.create_note"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="143"
            column="13"
            startOffset="7973"
            endLine="143"
            endColumn="31"
            endOffset="7991"/>
        <location id="R.string.create_workout"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="275"
            column="13"
            startOffset="15966"
            endLine="275"
            endColumn="34"
            endOffset="15987"/>
        <location id="R.string.custom_workout"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="287"
            column="13"
            startOffset="16609"
            endLine="287"
            endColumn="34"
            endOffset="16630"/>
        <location id="R.string.delete_image"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="15"
            column="13"
            startOffset="722"
            endLine="15"
            endColumn="32"
            endOffset="741"/>
        <location id="R.string.delete_image_confirmation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="18"
            column="13"
            startOffset="931"
            endLine="18"
            endColumn="45"
            endOffset="963"/>
        <location id="R.string.delete_item"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="66"
            column="13"
            startOffset="3348"
            endLine="66"
            endColumn="31"
            endOffset="3366"/>
        <location id="R.string.delete_note"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="235"
            column="13"
            startOffset="13988"
            endLine="235"
            endColumn="31"
            endOffset="14006"/>
        <location id="R.string.delete_note_confirmation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="236"
            column="13"
            startOffset="14041"
            endLine="236"
            endColumn="44"
            endOffset="14072"/>
        <location id="R.string.delete_program"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="306"
            column="13"
            startOffset="18009"
            endLine="306"
            endColumn="34"
            endOffset="18030"/>
        <location id="R.string.delete_program_confirmation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="307"
            column="13"
            startOffset="18068"
            endLine="307"
            endColumn="47"
            endOffset="18102"/>
        <location id="R.string.delete_registration"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="137"
            column="13"
            startOffset="7528"
            endLine="137"
            endColumn="39"
            endOffset="7554"/>
        <location id="R.string.delete_registration_confirmation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="138"
            column="13"
            startOffset="7597"
            endLine="138"
            endColumn="52"
            endOffset="7636"/>
        <location id="R.string.delete_student"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="227"
            column="13"
            startOffset="13492"
            endLine="227"
            endColumn="34"
            endOffset="13513"/>
        <location id="R.string.delete_student_confirmation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="228"
            column="13"
            startOffset="13551"
            endLine="228"
            endColumn="47"
            endOffset="13585"/>
        <location id="R.string.delete_task"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="169"
            column="13"
            startOffset="9640"
            endLine="169"
            endColumn="31"
            endOffset="9658"/>
        <location id="R.string.delete_task_confirmation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="170"
            column="13"
            startOffset="9693"
            endLine="170"
            endColumn="44"
            endOffset="9724"/>
        <location id="R.string.delete_video"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="16"
            column="13"
            startOffset="777"
            endLine="16"
            endColumn="32"
            endOffset="796"/>
        <location id="R.string.delete_video_confirmation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="17"
            column="13"
            startOffset="832"
            endLine="17"
            endColumn="45"
            endOffset="864"/>
        <location id="R.string.delete_voice_note"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="242"
            column="13"
            startOffset="14343"
            endLine="242"
            endColumn="37"
            endOffset="14367"/>
        <location id="R.string.delete_voice_note_confirmation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="243"
            column="13"
            startOffset="14408"
            endLine="243"
            endColumn="50"
            endOffset="14445"/>
        <location id="R.string.delete_workout"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="303"
            column="13"
            startOffset="17746"
            endLine="303"
            endColumn="34"
            endOffset="17767"/>
        <location id="R.string.delete_workout_confirmation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="304"
            column="13"
            startOffset="17805"
            endLine="304"
            endColumn="47"
            endOffset="17839"/>
        <location id="R.string.deleting"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="141"
            column="13"
            startOffset="7882"
            endLine="141"
            endColumn="28"
            endOffset="7897"/>
        <location id="R.string.description"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="6"
            column="13"
            startOffset="194"
            endLine="6"
            endColumn="31"
            endOffset="212"/>
        <location id="R.string.drawing_added"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="27"
            column="13"
            startOffset="1400"
            endLine="27"
            endColumn="33"
            endOffset="1420"/>
        <location id="R.string.drawing_options"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="24"
            column="13"
            startOffset="1200"
            endLine="24"
            endColumn="35"
            endOffset="1222"/>
        <location id="R.string.drawing_saved_to_gallery"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="31"
            column="13"
            startOffset="1629"
            endLine="31"
            endColumn="44"
            endOffset="1660"/>
        <location id="R.string.edit"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="135"
            column="13"
            startOffset="7446"
            endLine="135"
            endColumn="24"
            endOffset="7457"/>
        <location id="R.string.edit_coming_soon"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="309"
            column="13"
            startOffset="18275"
            endLine="309"
            endColumn="36"
            endOffset="18298"/>
        <location id="R.string.edit_note"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="144"
            column="13"
            startOffset="8026"
            endLine="144"
            endColumn="29"
            endOffset="8042"/>
        <location id="R.string.edit_registration"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="92"
            column="13"
            startOffset="4690"
            endLine="92"
            endColumn="37"
            endOffset="4714"/>
        <location id="R.string.edit_student"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="85"
            column="13"
            startOffset="4292"
            endLine="85"
            endColumn="32"
            endOffset="4311"/>
        <location id="R.string.edit_task"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="165"
            column="13"
            startOffset="9402"
            endLine="165"
            endColumn="29"
            endOffset="9418"/>
        <location id="R.string.email"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="81"
            column="13"
            startOffset="4091"
            endLine="81"
            endColumn="25"
            endOffset="4103"/>
        <location id="R.string.empty_history"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="62"
            column="13"
            startOffset="3097"
            endLine="62"
            endColumn="33"
            endOffset="3117"/>
        <location id="R.string.end_date"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="95"
            column="13"
            startOffset="4865"
            endLine="95"
            endColumn="28"
            endOffset="4880"/>
        <location id="R.string.enter_task_description"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="164"
            column="13"
            startOffset="9326"
            endLine="164"
            endColumn="42"
            endOffset="9355"/>
        <location id="R.string.error_adding_drawing"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="28"
            column="13"
            startOffset="1465"
            endLine="28"
            endColumn="40"
            endOffset="1492"/>
        <location id="R.string.error_camera"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="127"
            column="13"
            startOffset="7074"
            endLine="127"
            endColumn="32"
            endOffset="7093"/>
        <location id="R.string.error_deleting_registration"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="140"
            column="13"
            startOffset="7797"
            endLine="140"
            endColumn="47"
            endOffset="7831"/>
        <location id="R.string.error_opening_file"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="153"
            column="13"
            startOffset="8607"
            endLine="153"
            endColumn="38"
            endOffset="8632"/>
        <location id="R.string.error_playing_audio"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="252"
            column="13"
            startOffset="14991"
            endLine="252"
            endColumn="39"
            endOffset="15017"/>
        <location id="R.string.error_saving_drawing"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="32"
            column="13"
            startOffset="1708"
            endLine="32"
            endColumn="40"
            endOffset="1735"/>
        <location id="R.string.error_saving_recording"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="250"
            column="13"
            startOffset="14847"
            endLine="250"
            endColumn="42"
            endOffset="14876"/>
        <location id="R.string.exercise_name"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="278"
            column="13"
            startOffset="16133"
            endLine="278"
            endColumn="33"
            endOffset="16153"/>
        <location id="R.string.expand_collapse"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="192"
            column="13"
            startOffset="11017"
            endLine="192"
            endColumn="35"
            endOffset="11039"/>
        <location id="R.string.favorite_muscle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="325"
            column="13"
            startOffset="18973"
            endLine="325"
            endColumn="35"
            endOffset="18995"/>
        <location id="R.string.grant_permission"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="125"
            column="13"
            startOffset="6921"
            endLine="125"
            endColumn="36"
            endOffset="6944"/>
        <location id="R.string.group_created"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="187"
            column="13"
            startOffset="10729"
            endLine="187"
            endColumn="33"
            endOffset="10749"/>
        <location id="R.string.group_deleted"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="189"
            column="13"
            startOffset="10853"
            endLine="189"
            endColumn="33"
            endOffset="10873"/>
        <location id="R.string.group_updated"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="188"
            column="13"
            startOffset="10791"
            endLine="188"
            endColumn="33"
            endOffset="10811"/>
        <location id="R.string.history"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="61"
            column="13"
            startOffset="3052"
            endLine="61"
            endColumn="27"
            endOffset="3066"/>
        <location id="R.string.image_counter"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="329"
            column="13"
            startOffset="19124"
            endLine="329"
            endColumn="33"
            endOffset="19144"/>
        <location id="R.string.instagram"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="82"
            column="13"
            startOffset="4133"
            endLine="82"
            endColumn="29"
            endOffset="4149"/>
        <location id="R.string.investment_name"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="10"
            column="13"
            startOffset="443"
            endLine="10"
            endColumn="35"
            endOffset="465"/>
        <location id="R.string.investment_type"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="11"
            column="13"
            startOffset="504"
            endLine="11"
            endColumn="35"
            endOffset="526"/>
        <location id="R.string.item_type_icon"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="65"
            column="13"
            startOffset="3289"
            endLine="65"
            endColumn="34"
            endOffset="3310"/>
        <location id="R.string.manage_groups"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="184"
            column="13"
            startOffset="10470"
            endLine="184"
            endColumn="33"
            endOffset="10490"/>
        <location id="R.string.mark_completed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="167"
            column="13"
            startOffset="9514"
            endLine="167"
            endColumn="34"
            endOffset="9535"/>
        <location id="R.string.mark_incomplete"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="168"
            column="13"
            startOffset="9576"
            endLine="168"
            endColumn="35"
            endOffset="9598"/>
        <location id="R.string.most_recent_workout"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="291"
            column="13"
            startOffset="16872"
            endLine="291"
            endColumn="39"
            endOffset="16898"/>
        <location id="R.string.name"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="79"
            column="13"
            startOffset="3997"
            endLine="79"
            endColumn="24"
            endOffset="4008"/>
        <location id="R.string.network_unavailable"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="158"
            column="13"
            startOffset="8985"
            endLine="158"
            endColumn="39"
            endOffset="9011"/>
        <location id="R.string.new_task"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="163"
            column="13"
            startOffset="9279"
            endLine="163"
            endColumn="28"
            endOffset="9294"/>
        <location id="R.string.no_app_for_file_type"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="152"
            column="13"
            startOffset="8521"
            endLine="152"
            endColumn="40"
            endOffset="8548"/>
        <location id="R.string.no_group"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="190"
            column="13"
            startOffset="10915"
            endLine="190"
            endColumn="28"
            endOffset="10930"/>
        <location id="R.string.no_history_items"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="63"
            column="13"
            startOffset="3154"
            endLine="63"
            endColumn="36"
            endOffset="3177"/>
        <location id="R.string.no_programs"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="296"
            column="13"
            startOffset="17206"
            endLine="296"
            endColumn="31"
            endOffset="17224"/>
        <location id="R.string.no_recent_workouts"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="292"
            column="13"
            startOffset="16941"
            endLine="292"
            endColumn="38"
            endOffset="16966"/>
        <location id="R.string.no_registrations"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="108"
            column="13"
            startOffset="5839"
            endLine="108"
            endColumn="36"
            endOffset="5862"/>
        <location id="R.string.no_registrations_prompt"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="109"
            column="13"
            startOffset="5909"
            endLine="109"
            endColumn="43"
            endOffset="5939"/>
        <location id="R.string.no_students"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="87"
            column="13"
            startOffset="4435"
            endLine="87"
            endColumn="31"
            endOffset="4453"/>
        <location id="R.string.no_tasks_yet"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="162"
            column="13"
            startOffset="9203"
            endLine="162"
            endColumn="32"
            endOffset="9222"/>
        <location id="R.string.no_workout_history"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="297"
            column="13"
            startOffset="17300"
            endLine="297"
            endColumn="38"
            endOffset="17325"/>
        <location id="R.string.not_set"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="335"
            column="13"
            startOffset="19320"
            endLine="335"
            endColumn="27"
            endOffset="19334"/>
        <location id="R.string.note_deleted"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="148"
            column="13"
            startOffset="8250"
            endLine="148"
            endColumn="32"
            endOffset="8269"/>
        <location id="R.string.note_saved"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="146"
            column="13"
            startOffset="8144"
            endLine="146"
            endColumn="30"
            endOffset="8161"/>
        <location id="R.string.note_updated"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="147"
            column="13"
            startOffset="8195"
            endLine="147"
            endColumn="32"
            endOffset="8214"/>
        <location id="R.string.notes"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="159"
            column="13"
            startOffset="9074"
            endLine="159"
            endColumn="25"
            endOffset="9086"/>
        <location id="R.string.notes_optional"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="282"
            column="13"
            startOffset="16316"
            endLine="282"
            endColumn="34"
            endOffset="16337"/>
        <location id="R.string.ok"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="232"
            column="13"
            startOffset="13923"
            endLine="232"
            endColumn="22"
            endOffset="13932"/>
        <location id="R.string.pause_workout"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="272"
            column="13"
            startOffset="15815"
            endLine="272"
            endColumn="33"
            endOffset="15835"/>
        <location id="R.string.payment_receipt"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="99"
            column="13"
            startOffset="5152"
            endLine="99"
            endColumn="35"
            endOffset="5174"/>
        <location id="R.string.payment_received"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="96"
            column="13"
            startOffset="4912"
            endLine="96"
            endColumn="36"
            endOffset="4935"/>
        <location id="R.string.permission_denied"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="126"
            column="13"
            startOffset="6984"
            endLine="126"
            endColumn="37"
            endOffset="7008"/>
        <location id="R.string.phone_number"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="80"
            column="13"
            startOffset="4036"
            endLine="80"
            endColumn="32"
            endOffset="4055"/>
        <location id="R.string.please_enter_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="145"
            column="13"
            startOffset="8075"
            endLine="145"
            endColumn="38"
            endOffset="8100"/>
        <location id="R.string.please_enter_valid_amount"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="106"
            column="13"
            startOffset="5636"
            endLine="106"
            endColumn="45"
            endOffset="5668"/>
        <location id="R.string.please_select_end_date"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="105"
            column="13"
            startOffset="5561"
            endLine="105"
            endColumn="42"
            endOffset="5590"/>
        <location id="R.string.please_select_start_date"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="104"
            column="13"
            startOffset="5482"
            endLine="104"
            endColumn="44"
            endOffset="5513"/>
        <location id="R.string.please_select_student"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="103"
            column="13"
            startOffset="5407"
            endLine="103"
            endColumn="41"
            endOffset="5435"/>
        <location id="R.string.program_created"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="300"
            column="13"
            startOffset="17563"
            endLine="300"
            endColumn="35"
            endOffset="17585"/>
        <location id="R.string.program_deleted"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="308"
            column="13"
            startOffset="18201"
            endLine="308"
            endColumn="35"
            endOffset="18223"/>
        <location id="R.string.program_details"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="301"
            column="13"
            startOffset="17624"
            endLine="301"
            endColumn="35"
            endOffset="17646"/>
        <location id="R.string.program_name"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="277"
            column="13"
            startOffset="16078"
            endLine="277"
            endColumn="32"
            endOffset="16097"/>
        <location id="R.string.program_updated"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="311"
            column="13"
            startOffset="18393"
            endLine="311"
            endColumn="35"
            endOffset="18415"/>
        <location id="R.string.pull_to_refresh"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="64"
            column="13"
            startOffset="3223"
            endLine="64"
            endColumn="35"
            endOffset="3245"/>
        <location id="R.string.receipt_unavailable"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="101"
            column="13"
            startOffset="5266"
            endLine="101"
            endColumn="39"
            endOffset="5292"/>
        <location id="R.string.record"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="244"
            column="13"
            startOffset="14517"
            endLine="244"
            endColumn="26"
            endOffset="14530"/>
        <location id="R.string.recording"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="245"
            column="13"
            startOffset="14560"
            endLine="245"
            endColumn="29"
            endOffset="14576"/>
        <location id="R.string.recording_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="249"
            column="13"
            startOffset="14775"
            endLine="249"
            endColumn="36"
            endOffset="14798"/>
        <location id="R.string.recording_saved"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="248"
            column="13"
            startOffset="14714"
            endLine="248"
            endColumn="35"
            endOffset="14736"/>
        <location id="R.string.recording_started"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="247"
            column="13"
            startOffset="14649"
            endLine="247"
            endColumn="37"
            endOffset="14673"/>
        <location id="R.string.registration_deleted"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="139"
            column="13"
            startOffset="7713"
            endLine="139"
            endColumn="40"
            endOffset="7740"/>
        <location id="R.string.registration_success"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="97"
            column="13"
            startOffset="4975"
            endLine="97"
            endColumn="40"
            endOffset="5002"/>
        <location id="R.string.registration_updated"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="98"
            column="13"
            startOffset="5068"
            endLine="98"
            endColumn="40"
            endOffset="5095"/>
        <location id="R.string.remove_exercise"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="284"
            column="13"
            startOffset="16432"
            endLine="284"
            endColumn="35"
            endOffset="16454"/>
        <location id="R.string.reps"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="280"
            column="13"
            startOffset="16229"
            endLine="280"
            endColumn="24"
            endOffset="16240"/>
        <location id="R.string.required_fields_missing"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="86"
            column="13"
            startOffset="4347"
            endLine="86"
            endColumn="43"
            endOffset="4377"/>
        <location id="R.string.retry"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="142"
            column="13"
            startOffset="7932"
            endLine="142"
            endColumn="25"
            endOffset="7944"/>
        <location id="R.string.save_changes"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="7"
            column="13"
            startOffset="247"
            endLine="7"
            endColumn="32"
            endOffset="266"/>
        <location id="R.string.save_to_note"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="25"
            column="13"
            startOffset="1261"
            endLine="25"
            endColumn="32"
            endOffset="1280"/>
        <location id="R.string.save_to_note_and_gallery"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="26"
            column="13"
            startOffset="1321"
            endLine="26"
            endColumn="44"
            endOffset="1352"/>
        <location id="R.string.save_workout"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="274"
            column="13"
            startOffset="15911"
            endLine="274"
            endColumn="32"
            endOffset="15930"/>
        <location id="R.string.saving"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="255"
            column="13"
            startOffset="15094"
            endLine="255"
            endColumn="26"
            endOffset="15107"/>
        <location id="R.string.search"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="259"
            column="13"
            startOffset="15236"
            endLine="259"
            endColumn="26"
            endOffset="15249"/>
        <location id="R.string.search_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="260"
            column="13"
            startOffset="15300"
            endLine="260"
            endColumn="31"
            endOffset="15318"/>
        <location id="R.string.select"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="36"
            column="13"
            startOffset="1951"
            endLine="36"
            endColumn="26"
            endOffset="1964"/>
        <location id="R.string.select_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="34"
            column="13"
            startOffset="1842"
            endLine="34"
            endColumn="32"
            endOffset="1861"/>
        <location id="R.string.select_group"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="191"
            column="13"
            startOffset="10962"
            endLine="191"
            endColumn="32"
            endOffset="10981"/>
        <location id="R.string.select_month"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="110"
            column="13"
            startOffset="6011"
            endLine="110"
            endColumn="32"
            endOffset="6030"/>
        <location id="R.string.select_profile_photo"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="118"
            column="13"
            startOffset="6342"
            endLine="118"
            endColumn="40"
            endOffset="6369"/>
        <location id="R.string.select_program"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="286"
            column="13"
            startOffset="16550"
            endLine="286"
            endColumn="34"
            endOffset="16571"/>
        <location id="R.string.select_student"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="93"
            column="13"
            startOffset="4755"
            endLine="93"
            endColumn="34"
            endOffset="4776"/>
        <location id="R.string.select_type"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="9"
            column="13"
            startOffset="378"
            endLine="9"
            endColumn="31"
            endOffset="396"/>
        <location id="R.string.selected_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="35"
            column="13"
            startOffset="1897"
            endLine="35"
            endColumn="34"
            endOffset="1918"/>
        <location id="R.string.sets"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="279"
            column="13"
            startOffset="16190"
            endLine="279"
            endColumn="24"
            endOffset="16201"/>
        <location id="R.string.share_note"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="149"
            column="13"
            startOffset="8305"
            endLine="149"
            endColumn="30"
            endOffset="8322"/>
        <location id="R.string.share_registration"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="102"
            column="13"
            startOffset="5335"
            endLine="102"
            endColumn="38"
            endOffset="5360"/>
        <location id="R.string.start_date"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="94"
            column="13"
            startOffset="4814"
            endLine="94"
            endColumn="30"
            endOffset="4831"/>
        <location id="R.string.start_workout"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="271"
            column="13"
            startOffset="15758"
            endLine="271"
            endColumn="33"
            endOffset="15778"/>
        <location id="R.string.status_paid_desc"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="128"
            column="13"
            startOffset="7145"
            endLine="128"
            endColumn="36"
            endOffset="7168"/>
        <location id="R.string.status_unpaid_desc"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="129"
            column="13"
            startOffset="7221"
            endLine="129"
            endColumn="38"
            endOffset="7246"/>
        <location id="R.string.stop"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="246"
            column="13"
            startOffset="14610"
            endLine="246"
            endColumn="24"
            endOffset="14621"/>
        <location id="R.string.stop_workout"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="273"
            column="13"
            startOffset="15864"
            endLine="273"
            endColumn="32"
            endOffset="15883"/>
        <location id="R.string.stop_workout_confirmation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="298"
            column="13"
            startOffset="17407"
            endLine="298"
            endColumn="45"
            endOffset="17439"/>
        <location id="R.string.storage_permission_message"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="124"
            column="13"
            startOffset="6797"
            endLine="124"
            endColumn="46"
            endOffset="6830"/>
        <location id="R.string.storage_permission_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="123"
            column="13"
            startOffset="6724"
            endLine="123"
            endColumn="44"
            endOffset="6755"/>
        <location id="R.string.student_deleted"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="229"
            column="13"
            startOffset="13669"
            endLine="229"
            endColumn="35"
            endOffset="13691"/>
        <location id="R.string.student_has_active_registrations"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="231"
            column="13"
            startOffset="13800"
            endLine="231"
            endColumn="52"
            endOffset="13839"/>
        <location id="R.string.switch_to_grouped_view"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="194"
            column="13"
            startOffset="11131"
            endLine="194"
            endColumn="42"
            endOffset="11160"/>
        <location id="R.string.switch_to_list_view"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="195"
            column="13"
            startOffset="11206"
            endLine="195"
            endColumn="39"
            endOffset="11232"/>
        <location id="R.string.take_photo"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="119"
            column="13"
            startOffset="6413"
            endLine="119"
            endColumn="30"
            endOffset="6430"/>
        <location id="R.string.tap_to_view_attachment"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="151"
            column="13"
            startOffset="8446"
            endLine="151"
            endColumn="42"
            endOffset="8475"/>
        <location id="R.string.task_added"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="171"
            column="13"
            startOffset="9790"
            endLine="171"
            endColumn="30"
            endOffset="9807"/>
        <location id="R.string.task_completed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="174"
            column="13"
            startOffset="9951"
            endLine="174"
            endColumn="34"
            endOffset="9972"/>
        <location id="R.string.task_deleted"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="173"
            column="13"
            startOffset="9896"
            endLine="173"
            endColumn="32"
            endOffset="9915"/>
        <location id="R.string.task_description"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="166"
            column="13"
            startOffset="9451"
            endLine="166"
            endColumn="36"
            endOffset="9474"/>
        <location id="R.string.task_groups"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="183"
            column="13"
            startOffset="10417"
            endLine="183"
            endColumn="31"
            endOffset="10435"/>
        <location id="R.string.task_incomplete"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="175"
            column="13"
            startOffset="10010"
            endLine="175"
            endColumn="35"
            endOffset="10032"/>
        <location id="R.string.task_name"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="332"
            column="13"
            startOffset="19224"
            endLine="332"
            endColumn="29"
            endOffset="19240"/>
        <location id="R.string.task_updated"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="172"
            column="13"
            startOffset="9841"
            endLine="172"
            endColumn="32"
            endOffset="9860"/>
        <location id="R.string.tasks"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="160"
            column="13"
            startOffset="9115"
            endLine="160"
            endColumn="25"
            endOffset="9127"/>
        <location id="R.string.title_calendar"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="76"
            column="13"
            startOffset="3838"
            endLine="76"
            endColumn="34"
            endOffset="3859"/>
        <location id="R.string.title_history"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="74"
            column="13"
            startOffset="3720"
            endLine="74"
            endColumn="33"
            endOffset="3740"/>
        <location id="R.string.title_investments"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="69"
            column="13"
            startOffset="3435"
            endLine="69"
            endColumn="37"
            endOffset="3459"/>
        <location id="R.string.title_lesson_schedule"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="75"
            column="13"
            startOffset="3771"
            endLine="75"
            endColumn="41"
            endOffset="3799"/>
        <location id="R.string.title_notes"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="70"
            column="13"
            startOffset="3494"
            endLine="70"
            endColumn="31"
            endOffset="3512"/>
        <location id="R.string.title_register"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="73"
            column="13"
            startOffset="3667"
            endLine="73"
            endColumn="34"
            endOffset="3688"/>
        <location id="R.string.title_seminars"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="154"
            column="13"
            startOffset="8680"
            endLine="154"
            endColumn="34"
            endOffset="8701"/>
        <location id="R.string.title_students"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="72"
            column="13"
            startOffset="3614"
            endLine="72"
            endColumn="34"
            endOffset="3635"/>
        <location id="R.string.title_wing_tzun_registry"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="71"
            column="13"
            startOffset="3541"
            endLine="71"
            endColumn="44"
            endOffset="3572"/>
        <location id="R.string.toggle_view"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="193"
            column="13"
            startOffset="11078"
            endLine="193"
            endColumn="31"
            endOffset="11096"/>
        <location id="R.string.total"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="113"
            column="13"
            startOffset="6158"
            endLine="113"
            endColumn="25"
            endOffset="6170"/>
        <location id="R.string.total_amount_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="114"
            column="13"
            startOffset="6199"
            endLine="114"
            endColumn="38"
            endOffset="6224"/>
        <location id="R.string.total_time"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="324"
            column="13"
            startOffset="18922"
            endLine="324"
            endColumn="30"
            endOffset="18939"/>
        <location id="R.string.total_workout_count"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="294"
            column="13"
            startOffset="17067"
            endLine="294"
            endColumn="39"
            endOffset="17093"/>
        <location id="R.string.total_workout_duration"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="295"
            column="13"
            startOffset="17135"
            endLine="295"
            endColumn="42"
            endOffset="17164"/>
        <location id="R.string.total_workouts"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="322"
            column="13"
            startOffset="18808"
            endLine="322"
            endColumn="34"
            endOffset="18829"/>
        <location id="R.string.type"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="5"
            column="13"
            startOffset="155"
            endLine="5"
            endColumn="24"
            endOffset="166"/>
        <location id="R.string.type_input_description"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="8"
            column="13"
            startOffset="302"
            endLine="8"
            endColumn="42"
            endOffset="331"/>
        <location id="R.string.uploading_attachments"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="256"
            column="13"
            startOffset="15137"
            endLine="256"
            endColumn="41"
            endOffset="15165"/>
        <location id="R.string.voice_note_deleted"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="251"
            column="13"
            startOffset="14924"
            endLine="251"
            endColumn="38"
            endOffset="14949"/>
        <location id="R.string.weekly_summary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="288"
            column="13"
            startOffset="16668"
            endLine="288"
            endColumn="34"
            endOffset="16689"/>
        <location id="R.string.weekly_workout_count"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="289"
            column="13"
            startOffset="16727"
            endLine="289"
            endColumn="40"
            endOffset="16754"/>
        <location id="R.string.weekly_workout_duration"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="290"
            column="13"
            startOffset="16800"
            endLine="290"
            endColumn="43"
            endOffset="16830"/>
        <location id="R.string.weight"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="281"
            column="13"
            startOffset="16268"
            endLine="281"
            endColumn="26"
            endOffset="16281"/>
        <location id="R.string.workout"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="266"
            column="13"
            startOffset="15503"
            endLine="266"
            endColumn="27"
            endOffset="15517"/>
        <location id="R.string.workout_dashboard"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="267"
            column="13"
            startOffset="15548"
            endLine="267"
            endColumn="37"
            endOffset="15572"/>
        <location id="R.string.workout_deleted"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="305"
            column="13"
            startOffset="17948"
            endLine="305"
            endColumn="35"
            endOffset="17970"/>
        <location id="R.string.workout_details"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="302"
            column="13"
            startOffset="17685"
            endLine="302"
            endColumn="35"
            endOffset="17707"/>
        <location id="R.string.workout_exercise"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="268"
            column="13"
            startOffset="15605"
            endLine="268"
            endColumn="36"
            endOffset="15628"/>
        <location id="R.string.workout_history"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="326"
            column="13"
            startOffset="19034"
            endLine="326"
            endColumn="35"
            endOffset="19056"/>
        <location id="R.string.workout_log"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="270"
            column="13"
            startOffset="15713"
            endLine="270"
            endColumn="31"
            endOffset="15731"/>
        <location id="R.string.workout_program"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="269"
            column="13"
            startOffset="15660"
            endLine="269"
            endColumn="35"
            endOffset="15682"/>
        <location id="R.string.workout_saved"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="299"
            column="13"
            startOffset="17506"
            endLine="299"
            endColumn="33"
            endOffset="17526"/>
        <location id="R.string.workout_statistics"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="321"
            column="13"
            startOffset="18741"
            endLine="321"
            endColumn="38"
            endOffset="18766"/>
        <location id="R.string.workout_stats"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="320"
            column="13"
            startOffset="18692"
            endLine="320"
            endColumn="33"
            endOffset="18712"/>
        <location id="R.string.workout_timer"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="285"
            column="13"
            startOffset="16493"
            endLine="285"
            endColumn="33"
            endOffset="16513"/>
        <location id="R.style.BoldText"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="26"
            column="12"
            startOffset="1030"
            endLine="26"
            endColumn="27"
            endOffset="1045"/>
        <location id="R.style.BottomNavigationStyle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="14"
            column="12"
            startOffset="555"
            endLine="14"
            endColumn="40"
            endOffset="583"/>
        <location id="R.style.CalendarDayStyle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="4"
            column="12"
            startOffset="97"
            endLine="4"
            endColumn="35"
            endOffset="120"/>
        <location id="R.style.FilterButtonStyle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="32"
            column="12"
            startOffset="1263"
            endLine="32"
            endColumn="36"
            endOffset="1287"/>
        <location id="R.style.FilterDropdownStyle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="39"
            column="12"
            startOffset="1574"
            endLine="39"
            endColumn="38"
            endOffset="1600"/>
        <location id="R.style.Theme_AllinOne_SplashText"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="38"
            column="12"
            startOffset="1940"
            endLine="38"
            endColumn="44"
            endOffset="1972"/>
        <location id="R.style.Widget_App_BottomNavigationView"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/themes.xml"
            line="95"
            column="12"
            startOffset="5333"
            endLine="95"
            endColumn="50"
            endOffset="5371"/>
        <location id="R.style.Widget_App_Button_TextButton_Dialog_Action"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/themes.xml"
            line="119"
            column="12"
            startOffset="6681"
            endLine="119"
            endColumn="61"
            endOffset="6730"/>
        <location id="R.style.Widget_App_Button_TextButton_Dialog_Delete"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/themes.xml"
            line="111"
            column="12"
            startOffset="6191"
            endLine="111"
            endColumn="61"
            endOffset="6240"/>
        <location id="R.style.Widget_App_NavigationView"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/themes.xml"
            line="103"
            column="12"
            startOffset="5777"
            endLine="103"
            endColumn="44"
            endOffset="5809"/>
        <entry
            name="model"
            string="attr[colorPrimary(R),colorSurface(E),colorControlNormal(R),colorOnSurface(R),actionBarSize(R),textAppearanceHeadline6(R),textAppearanceBody2(R),selectableItemBackground(E),textAppearanceBody1(R),textAppearanceSubtitle1(R),textAppearanceCaption(R),colorOnSurfaceVariant(R),selectableItemBackgroundBorderless(R),textAppearanceHeadline5(R),textAppearanceHeadline4(E),colorNavigationItem(D),colorNavigationItemSelected(D)],color[bottom_nav_item_color(D),bright_tab_selected(U),white(U),bottom_nav_item_color_light(D),black(U),gray_dark(U),chip_background_selector(D),dialog_action_button_color(D),colorPrimary(U),dialog_delete_button_color(D),red(U),drawer_item_color(U),text_input_box_stroke(U),gray(U),text_input_text_color(U),wt_bottom_nav_item_color(D),green(U),bg_tag_blue(D),light_gray(U),navy_surface(U),blue_500(U),green_500(U),red_500(U),orange_500(U),purple_500(U),navy_accent(U),design_default_color_primary(R),excellent_green(U),colorPrimaryLight(U),ic_launcher_background(U),gray_light(D),colorPrimaryDark(U),colorAccent(U),expiration_color(U),start_color(U),default_event_color(U),lesson_event_color(D),textPrimary(D),dark_gray(D),medium_gray(D),colorError(D),colorSuccess(U),colorWarning(U),navy_primary(U),navy_background(U),navy_variant(U),navy_text_secondary(D),bright_tab_unselected(D),error_light(D),warning_light(D),purple(D),registration_start(D),registration_end(D),feedCard(D),reelsCard(D),storyCard(D),albumCard(D),cardStroke(D),boldTextColor(D),reels_background(D),video_background(D),carousel_background(D),good_orange(D),poor_red(D),reels_purple(D),video_blue(D),carousel_orange(D),image_green(D),light_blue(D),text_primary(D),error_dark(D),background_color(D),surface_color(D),on_surface_color(D),user_message_bg(D),ai_message_bg(D),task_checkbox_color(D),mtrl_textinput_default_box_stroke_color(D)],dimen[task_indent_margin(D)],drawable[bg_category_chip(D),bg_current_day(D),bg_day_with_event(D),bg_day_with_events(D),bg_day_with_lesson(D),bg_day_with_registration(D),bg_selected_day(D),bg_tag_blue(D),bg_tag_green(D),bg_tag_red(D),border_background(D),circle_background(U),circle_background_red(U),circle_shape(U),completed_exercise_background(D),default_profile(U),dialog_rounded_bg(U),error_image(U),fully_uncompleted_exercise_background(D),ic_add(D),ic_add_photo(D),ic_attach_image(D),ic_back(D),ic_backup(D),ic_calendar(D),ic_call(D),ic_category_all(U),ic_category_bills(U),ic_category_food(U),ic_category_game(U),ic_category_general(U),ic_category_investment(U),ic_category_salary(U),ic_category_shopping(U),ic_category_sports(U),ic_category_transport(U),ic_category_wing_tzun(U),ic_checkbox(D),ic_chevron_left(D),ic_chevron_right(D),ic_clear_data(D),ic_cleardata(D),ic_close(U),ic_code(D),ic_dashboard(D),ic_database(D),ic_delete(D),ic_draw(D),ic_edit(D),ic_empty_state(D),ic_error(D),ic_exercise(D),ic_expand_less(D),ic_expand_more(D),ic_expense(D),ic_file(D),ic_fitness(D),ic_folder(D),ic_format_bold(D),ic_format_italic(D),ic_format_list_bulleted(D),ic_format_list_numbered(D),ic_format_text(D),ic_format_underline(D),ic_graduation(D),ic_history(D),ic_home(D),ic_image(U),ic_income(D),ic_instagram(D),ic_instagram_posts(D),ic_investment(D),ic_investments(D),ic_launcher_background(U),ic_launcher_foreground(U),ic_lessons(D),ic_log(D),ic_menu(D),ic_no_registrations(D),ic_no_students(D),ic_note(D),ic_notes(D),ic_notification(U),ic_pause(D),ic_play(D),ic_play_circle(U),ic_program(D),ic_registration(D),ic_remove(D),ic_reports(D),ic_save(D),ic_search_white(D),ic_share(U),ic_stats(D),ic_stop(D),ic_student(D),ic_students(D),ic_tasks(U),ic_transactions(U),ic_video_error(U),ic_video_placeholder(U),ic_view_list(D),ic_whatsapp(D),ic_wt(D),ic_wt_registers(D),placeholder_image(U),rounded_background(U),rounded_corner_bg(D),selected_circle_shape(U),simple_text_splash(D),splash_layout_drawable(D),transparent(U),uncompleted_exercise_background(D),ic_launcher_foreground_1(R)],font[opensans(U),opensans_regular(R),opensans_bold(R)],id[toolbar(U),backup_card(U),create_backup_button(D),backups_card(D),no_backups_text(D),backups_recycler_view(D),progress_bar(D),eventTitleInput(D),eventTimeInput(D),eventEndTimeInput(D),eventDescriptionInput(D),program_name_input(D),program_description_input(D),exercises_container(D),add_exercise_button(D),profileImageView(D),nameInputLayout(D),nameEditText(D),phoneInputLayout(D),phoneEditText(D),emailInputLayout(D),emailEditText(D),instagramInputLayout(D),instagramEditText(D),activeSwitch(D),cancelButton(U),saveButton(D),taskNameInput(D),taskDescriptionInput(D),groupSpinner(D),dueDateLabel(D),dueDateText(D),pickDueDateButton(D),clearDueDateButton(D),nameInput(U),typeInput(U),amountInput(U),descriptionInput(U),imagesRecyclerView(U),isPastInvestmentCheckbox(U),addImageButton(U),dayText(D),startTimeField(D),endTimeField(D),nameLayout(D),dateLayout(D),dateInput(D),startTimeLayout(D),endTimeLayout(D),startTimeInput(D),endTimeInput(D),descriptionLayout(D),studentDropdown(D),startDateInput(D),endDateInput(D),amountLayout(D),notesEditText(D),paidSwitch(D),attachmentNameText(D),addAttachmentButton(D),attachmentPreview(D),titleText(D),investmentDropdownLayout(D),investmentDropdown(D),emptyStateText(U),newInvestmentButton(D),fullscreenViewPager(U),imageCounterText(U),positionSymbolText(U),positionDetailsText(U),takeProfitLayout(U),takeProfitInput(U),stopLossLayout(U),stopLossInput(U),confirmButton(U),closePositionButton(U),dialogTitle(U),pinEditText(D),postDetailThumbnail(D),postCaptionLabel(D),postCaption(D),postDateLabel(D),postDate(D),postTypeLabel(D),postType(D),postLinkLabel(D),postLink(D),divider(U),insightsLabel(D),postInsights(D),closeButton(D),profitLossAmountInput(U),profitRadio(U),lossRadio(D),program_name_text(D),program_description_text(D),exercises_recycler_view(D),progress_indicator(D),progress_text(D),nameTextView(D),detailsTextView(D),callButton(D),whatsappButton(D),instagramButton(D),dialog_title(D),group_title_layout(D),group_title_edit(D),group_description_layout(D),group_description_edit(D),color_blue(U),color_green(U),color_red(U),color_orange(U),color_purple(U),btn_cancel(D),btn_delete(D),btn_save(D),workout_name_text(D),workout_date_text(D),workout_duration_text(D),futuresTabLayout(U),futuresViewPager(U),futuresSwipeRefreshLayout(U),summaryCard(U),balanceTitleText(U),balanceValueText(U),pnlTitleText(U),pnlValueText(U),marginBalanceTitleText(U),marginBalanceValueText(U),positionsTitle(U),positionsRecyclerView(U),loadingProgress(U),fragment_container(D),investmentTabLayout(U),investmentViewPager(U),addInvestmentButton(U),swipeRefreshLayout(U),totalInvestmentsText(U),investmentCountText(U),investmentsRecyclerView(U),filterCard(U),dateRangeLayout(U),dateRangeAutoComplete(D),categoryLayout(U),categoryAutoComplete(D),applyFiltersButton(U),totalIncomeText(U),totalExpenseText(U),balanceText(U),categorySpendingCard(U),categoryPieChart(U),topCategoriesRecyclerView(U),insightsCard(U),largestExpenseText(U),mostSpentCategoryText(U),averageTransactionText(U),transactionCountText(U),chartCard(D),cashFlowTitle(D),lineChart(U),incomeText(D),expenseText(D),pieChart(D),typeLayout(D),typeDropdown(D),addExpenseButton(D),addIncomeButton(D),transactionsCard(D),transactionsRecyclerView(D),emptyTransactionsText(D),paginationControls(D),prevPageButton(D),pageIndicator(D),nextPageButton(D),exercise_name_input(D),remove_exercise_button(D),exercise_sets_input(D),exercise_reps_input(D),exercise_weight_input(D),muscle_group_dropdown(D),exercise_notes_input(D),backup_name(U),backup_size(U),restore_button(U),share_button(U),delete_button(U),symbolText(U),leverageText(U),pnlLabel(U),pnlValue(U),roiLabel(U),roiValue(U),sizeLabel(U),sizeValue(U),marginLabel(U),marginValue(U),entryPriceLabel(U),entryPriceValue(U),markPriceLabel(U),markPriceValue(U),liqPriceLabel(U),liqPriceValue(U),tpslLabel(U),tpslValue(U),categoryIcon(U),categoryName(U),categoryColorIndicator(U),categoryNameText(U),categoryAmountText(U),categoryPercentText(U),categoryText(U),amountText(U),avatarAI(U),cardAIMessage(U),textAIMessage(U),layoutConfidence(U),textConfidence(U),layoutSources(U),recyclerSources(U),textAITimestamp(U),layoutTyping(U),textSourceEmoji(U),textSourcePost(U),textSourceMetrics(U),textSourceScore(U),cardUserMessage(D),textUserMessage(U),textUserTimestamp(U),fileIcon(D),fileName(D),fullscreenImageItem(U),positionSideText(U),pnlText(U),positionAmtLabelText(U),positionAmtText(U),leverageLabelText(U),entryPriceLabelText(U),entryPriceText(U),markPriceLabelText(U),markPriceText(U),liquidationPriceLabelText(U),liquidationPriceText(U),marginTypeLabelText(U),marginTypeText(U),tpSlText(U),investmentName(U),investmentType(U),investmentAmount(U),investmentCurrentValue(U),investmentProfitLoss(U),investmentDate(U),investmentDescription(U),imageContainer(U),imageView(U),deleteButton(U),timeText(D),editButton(D),noteTitle(U),shareButton(U),noteDate(U),noteContent(U),attachmentsSection(U),voiceNoteIndicator(U),voiceNoteCountText(U),videoThumbnail(U),progressBar(U),seminarNameText(U),seminarDateText(U),seminarTimeText(U),seminarDescriptionText(U),typeText(U),dateText(U),descriptionText(U),transactionTitle(U),transactionDescription(U),transactionDate(U),transactionAmount(U),categoryChip(U),voiceNoteTitle(U),voiceNoteCurrentTime(U),timeSlash(U),voiceNoteDuration(U),playPauseButton(U),deleteVoiceNoteButton(U),eventTitle(U),eventDate(U),eventDescription(U),studentPhoto(U),amount(U),studentName(U),startDate(U),endDate(U),paymentStatusChip(U),profileImage(U),statusIndicator(U),phoneNumber(U),email(U),drawer_button(D),page_title(D),offline_status_card(U),offline_icon(D),offline_status_title(D),offline_status_message(U),pending_operations_count(U),tooltipText(D),appNameText(D),themeSwitch(D),homeFragment(D),nav_investments(D),nav_transaction_report(D),nav_transactions(D),nav_wt_registry(D),nav_calendar(D),nav_notes(D),nav_tasks(D),nav_instagram_business(D),nav_workout(D),nav_history(D),group_settings(D),nav_database_management(D),nav_backup(D),nav_error_logs(D),nav_clear_data(D),nav_clear_db(D),tab_posts(D),tab_insight(D),tab_ask_ai(D),action_delete(D),action_edit_group(D),action_delete_group(D),action_edit(D),action_toggle_view(D),action_manage_groups(D),action_search(D),workout_dashboard(D),workout_exercise(D),workout_program(D),workout_stats(D),wtStudentsFragment(D),wtRegisterFragment(D),wtLessonsFragment(D),wtSeminarsFragment(D)],integer[google_play_services_version(R)],layout[activity_backup(U),dialog_add_event(D),dialog_add_program(D),dialog_add_student(D),dialog_add_task(D),dialog_edit_investment(U),dialog_edit_lesson(D),dialog_edit_program(D),dialog_edit_seminar(D),dialog_edit_wt_student(D),dialog_expense_investment(D),dialog_fullscreen_image(U),dialog_futures_tp_sl(U),dialog_income_investment(D),dialog_loading(U),dialog_pin_input(D),dialog_post_details(D),dialog_profit_loss(U),dialog_program_details(D),item_program_exercise(E),dialog_progress(D),dialog_student_details(D),dialog_task_group(U),dialog_workout_details(D),item_workout_exercise(E),dropdown_item(U),fragment_futures(U),fragment_futures_tab(U),fragment_home(D),fragment_investments(U),fragment_investments_tab(U),fragment_transaction_report(U),fragment_transactions_overview(D),item_add_exercise(D),item_backup(U),item_binance_position(U),item_category_dropdown(U),item_category_spending(U),item_category_summary(U),item_chat_ai(U),item_chat_source(U),item_chat_user(U),item_file_structure(D),item_fullscreen_image(U),item_futures_position(U),item_investment(U),item_investment_dropdown(U),item_investment_image(U),item_investment_selection(U),item_lesson(D),item_note(U),item_note_image(U),item_note_video(U),item_seminar(U),item_transaction(U),item_transaction_report(U),item_voice_note(U),item_wt_event(U),item_wt_registration(U),item_wt_student(U),layout_page_header(D),nav_header(D),offline_status_view(U),pie_chart_tooltip(D),splash_text_layout(D),theme_switch_layout(D)],menu[bottom_nav_menu(D),drawer_menu(D),instagram_bottom_nav_menu(D),menu_edit_note(D),menu_group_options(D),menu_task_options(D),menu_tasks(D),search_history(D),search_notes(D),search_register(D),search_students(D),workout_bottom_nav_menu(D),wt_bottom_nav_menu(D),wt_registration_context_menu(D),wt_student_context_menu(D)],mipmap[ic_launcher(U),ic_launcher_round(U)],plurals[exercise_count(D)],string[app_name(U),app_id(U),profile_image(U),name(D),phone_number(D),email(D),instagram(D),active_status(D),cancel(U),save(U),task_name(D),task_description(D),select_group(D),select_student(D),start_date(D),end_date(D),notes(D),payment_received(D),payment_receipt(D),receipt_unavailable(D),add_receipt(D),create_task_group(U),group_title(U),group_description(U),group_color(U),delete(U),voice_notes(U),offline_status(U),offline_mode(U),offline_message(U),pending_operations(U),tasks(D),history(D),edit(D),toggle_view(D),manage_groups(D),search(D),title_students(D),title_register(D),title_lesson_schedule(D),title_seminars(D),amount(D),type(D),description(D),save_changes(D),type_input_description(D),select_type(D),investment_name(D),investment_type(D),add_image(D),add_video(D),add_attachment(D),delete_image(D),delete_video(D),delete_video_confirmation(D),delete_image_confirmation(D),create_drawing(D),drawing_options(D),save_to_note(D),save_to_note_and_gallery(D),drawing_added(D),error_adding_drawing(D),brush_size(D),clear(D),drawing_saved_to_gallery(D),error_saving_drawing(D),choose_color(D),select_color(D),selected_color(D),select(D),color(D),color_black(D),color_red(D),color_green(D),color_blue(D),color_yellow(D),color_purple(D),color_orange(D),color_brown(D),color_teal(D),color_pink(D),empty_history(D),no_history_items(D),pull_to_refresh(D),item_type_icon(D),delete_item(D),title_investments(D),title_notes(D),title_wing_tzun_registry(D),title_history(D),title_calendar(D),add_photo(D),add_student(D),edit_student(D),required_fields_missing(D),no_students(D),add_student_prompt(D),add_registration(D),edit_registration(D),registration_success(D),registration_updated(D),share_registration(D),please_select_student(D),please_select_start_date(D),please_select_end_date(D),please_enter_valid_amount(D),attachments_only_for_paid_registrations(D),no_registrations(D),no_registrations_prompt(D),select_month(D),apply(D),all_months(D),total(D),total_amount_label(D),amount_format(D),select_profile_photo(D),take_photo(D),choose_from_gallery(D),camera_permission_title(D),camera_permission_message(D),storage_permission_title(D),storage_permission_message(D),grant_permission(D),permission_denied(D),error_camera(D),status_paid_desc(D),status_unpaid_desc(D),field_required(U),delete_registration(D),delete_registration_confirmation(D),registration_deleted(D),error_deleting_registration(D),deleting(D),retry(D),create_note(D),edit_note(D),please_enter_title(D),note_saved(D),note_updated(D),note_deleted(D),share_note(D),attachment_click_to_open(D),tap_to_view_attachment(D),no_app_for_file_type(D),error_opening_file(D),status_inactive_desc(U),status_registered_desc(U),status_active_desc(U),network_unavailable(D),add_task(D),no_tasks_yet(D),new_task(D),enter_task_description(D),edit_task(D),mark_completed(D),mark_incomplete(D),delete_task(D),delete_task_confirmation(D),task_added(D),task_updated(D),task_deleted(D),task_completed(D),task_incomplete(D),edit_task_group(U),task_groups(D),delete_group(U),delete_group_confirmation(U),group_created(D),group_updated(D),group_deleted(D),no_group(D),expand_collapse(D),switch_to_grouped_view(D),switch_to_list_view(D),error_network(U),error_network_unavailable(U),error_unknown(U),error_firebase_network(U),error_authentication(U),error_user_not_found(U),error_wrong_password(U),error_user_disabled(U),error_too_many_requests(U),error_requires_recent_login(U),error_database(U),error_permission_denied(U),error_service_unavailable(U),error_document_already_exists(U),error_document_not_found(U),error_quota_exceeded(U),error_storage(U),error_file_not_found(U),error_storage_quota_exceeded(U),error_not_authenticated(U),error_not_authorized(U),delete_student(D),delete_student_confirmation(D),student_deleted(D),cannot_delete(D),student_has_active_registrations(D),ok(D),delete_note(D),delete_note_confirmation(D),voice_note_singular(U),voice_note_plural(U),delete_voice_note(D),delete_voice_note_confirmation(D),record(D),recording(D),stop(D),recording_started(D),recording_saved(D),recording_failed(D),error_saving_recording(D),voice_note_deleted(D),error_playing_audio(D),saving(D),uploading_attachments(D),search_hint(D),brightness(D),workout(D),workout_dashboard(D),workout_exercise(D),workout_program(D),workout_log(D),start_workout(D),pause_workout(D),stop_workout(D),save_workout(D),create_workout(D),add_program(D),program_name(D),exercise_name(D),sets(D),reps(D),weight(D),notes_optional(D),add_exercise(D),remove_exercise(D),workout_timer(D),select_program(D),custom_workout(D),weekly_summary(D),weekly_workout_count(D),weekly_workout_duration(D),most_recent_workout(D),no_recent_workouts(D),all_time_stats(D),total_workout_count(D),total_workout_duration(D),no_programs(D),no_workout_history(D),stop_workout_confirmation(D),workout_saved(D),program_created(D),program_details(D),workout_details(D),delete_workout(D),delete_workout_confirmation(D),workout_deleted(D),delete_program(D),delete_program_confirmation(D),program_deleted(D),edit_coming_soon(D),close(D),program_updated(D),workout_stats(D),workout_statistics(D),total_workouts(D),avg_duration(D),total_time(D),favorite_muscle(D),workout_history(D),image_counter(D),not_set(D)],style[Theme_AllinOne(U),Theme_AllinOne_Starting(U),Theme_AllinOne_NoActionBar(U),ThemeOverlay_AppCompat_Dark_ActionBar(R),ThemeOverlay_AppCompat_Light(R),Widget_MaterialComponents_Button_OutlinedButton(E),circleImageView(U),Widget_MaterialComponents_TextInputLayout_OutlinedBox(R),Widget_MaterialComponents_Button_TextButton(R),Widget_MaterialComponents_Button(R),Widget_MaterialComponents_TextInputLayout_OutlinedBox_ExposedDropdownMenu(R),BoldText(D),Widget_Material3_Button_TextButton(R),Widget_Material3_Button(R),ThemeOverlay_AppCompat_Dark(R),CalendarDayStyle(D),BottomNavigationStyle(D),FilterButtonStyle(D),FilterDropdownStyle(D),Theme_MaterialComponents_DayNight_NoActionBar(R),Theme_AllinOne_SplashText(D),Theme_SplashScreen(R),Widget_App_TextInputLayout(U),Widget_App_EditText(U),Widget_App_Button(U),ThemeOverlay_App_MaterialAlertDialog(U),Widget_MaterialComponents_TextInputEditText_OutlinedBox(R),ThemeOverlay_MaterialComponents_MaterialAlertDialog(R),MaterialAlertDialog_App_Title_Text(U),MaterialAlertDialog_App_Body_Text(U),MaterialAlertDialog_MaterialComponents_Title_Text(R),MaterialAlertDialog_MaterialComponents_Body_Text(R),Widget_App_BottomNavigationView(D),Widget_MaterialComponents_BottomNavigationView(E),Widget_App_NavigationView(D),Widget_MaterialComponents_NavigationView(E),Widget_App_Button_TextButton_Dialog_Delete(D),TextAppearance_MaterialComponents_Button(E),Widget_App_Button_TextButton_Dialog_Action(D)],xml[data_extraction_rules(U),backup_rules(U),network_security_config(U),file_paths(U)];11^12^13,14^15^16,17^0^1,18^19^13,19^3c,1a^1b,1c^12^13,1d^19^1e^13,1f^19^1e^15^13,20^12^13,34^24,36^13,45^1b,4b^15^13,4c^47,4d^48,4e^49,5e^1e^13,62^21,63^19,64^22,65^1b,6b^23,6d^21,6f^2,70^24,78^2,79^2,7d^2,85^3,86^2,87^2,8b^2,8d^2,8f^2,90^2,92^2,94^3,95^3,9e^2,aa^d1,ae^2,af^2,b2^2,bc^2,c0^2,c1^3,c5^3,cc^0,ce^15,d2^d3^d4,22d^0^4^397^398^d5^5^6^d6^24^3a2^13,22e^13,22f^5^73^399,230^283^7^6f^39a^284^39b^285^286^287^288^289^39c^28a^39d,231^28b^28c^70^28d^8^78^6^3^399^39c,232^13^39b^39e,233^39f,234^5^73^399,235^39b^101^105^102^104,236^28e^39e^28f^39b^290^291^292^293^9^294^295,237^5^112^39e^113^115^39c,239^5^8^39b^39c,23a^9^39e^39c,23b^8,23c^39f,23d^123^124^125^126^127^128^129^12a^12b^12c^12d^12e,23e^1d^1f^8^a^b,23f^5^6^9^240,241^8,242^283^6f^39a^39f^79^0^39c^c6^a5,243^70^296^3^1d^0^297^1f^298^299^6d^25^c^26^27^28^29^289^3a0^29a^28a^3a1,244^5^6^9^245,246^9,247^14d,248^9^d^151^153^152^6^12c^8^155^5^150^157,24a^15b,24b^5^8^150,24c^15^39e^162^16^21^1b^2a^150^16b^16e,24d^1^8^d2^e^5^39b^39e^1e^399,24e^6a^c^b8^39e,24f^9^6^18b^2b^18e^18c^39c^18f^18d^24^13^12^1b,250^190^192^195^194^196^199^198^19a^19b^19c^19d^19e^1a0,251^c2,252^6d^15^16,253^8^1a9,254^6b^23^1aa^2c^255^1ab,255^2c,256^2c,257^97,259^9^190^6^8^1bf^1c0^1c1^1c2^1c4^1c6^1c7^1c8,25a^5^6^8^0^a,25b^0,25c^8a^13,25d^9^6^8,25e^39f^c,25f^5^1d8^c^bc^a^1d7^6^1d9^1da^ca^29b^3,260^6b^8a^13,261^c4^b5^6c^8a,262^1d8^1e0^1e1^1e2^c,263^9^a^6,264^1e^21^16,265^c^3,267^283^6f^1f6^39a^2d^9^1d8^1f5^c^bc^a^1f9^1f7^1f8^6^1fa^13,268^283^6f^39a^9^1fc^1fb^6^1f7^a^1fd,269^0^c^ad^5^1ff,26a^15,26b^29c^29d^29e^29f,26d^15^13,26f^a2^a8^b9,270^c2^c8^78^b1^c1^2a0^a5^98^a1^2a1^8d^77^92^88,271^a6^b9^b1,272^8e,273^90^2a2^8e^29a,274^90^2a2^8e^29a,275^c5^2a3^99^2a4,276^bb^2a5,277^bb^2a5,278^bb^2a5,279^bb^2a5,27a^8c^93^b6^bd,27b^c0^2a6^c7^2a7^ab^2a8^78^2a9,27c^90^2a2^8e^29a,27e^2e^aa^a9,27f^2e^aa,394^3a7^15^16^13^24^12^3e^3d^3aa^3ab^3ac^3ad^1c,395^3a9^15^cf^394,396^394,3a3^7,3a4^11^12,3a5^39d,3a8^394^15,3aa^39b^13^1f^1d^1b,3ab^3ae^1f^13,3ac^39d^13,3ad^3af^13^3b0^3b1,3b0^3b2^13,3b1^3b3^13,3b4^3b5^11^12,3b6^3b7^1c^7,3b8^39c^1a^1b^3b9,3ba^39c^18^13^3b9;;;"/>
    </map>

</incidents>
