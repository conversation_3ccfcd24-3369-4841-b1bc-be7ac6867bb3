<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <!-- Header -->
    <TextView
        android:id="@+id/textAITitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="🤖 Ask AI About Your Instagram"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:layout_marginBottom="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Chat Messages RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerChatMessages"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:clipToPadding="false"
        android:paddingBottom="8dp"
        app:layout_constraintBottom_toTopOf="@+id/cardSuggestedQuestions"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textAITitle"
        tools:listitem="@layout/item_chat_ai" />

    <!-- Empty State -->
    <LinearLayout
        android:id="@+id/emptyState"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="32dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@+id/recyclerChatMessages"
        app:layout_constraintEnd_toEndOf="@+id/recyclerChatMessages"
        app:layout_constraintStart_toStartOf="@+id/recyclerChatMessages"
        app:layout_constraintTop_toTopOf="@+id/recyclerChatMessages">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="💬"
            android:textSize="48sp"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Ask me anything about your Instagram!"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="I can analyze your posts, engagement, hashtags, and provide insights to help grow your Instagram presence."
            android:textSize="14sp"
            android:textAlignment="center"
            android:alpha="0.7" />
    </LinearLayout>

    <!-- Suggested Questions Card -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardSuggestedQuestions"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:layout_constraintBottom_toTopOf="@+id/inputCard"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="💡 Suggested Questions"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                android:layout_marginBottom="8dp" />

            <com.google.android.material.chip.ChipGroup
                android:id="@+id/chipGroupSuggestions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:chipSpacingHorizontal="8dp"
                app:chipSpacingVertical="4dp">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipBestPosts"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="What are my best performing posts?"
                    style="@style/Widget.MaterialComponents.Chip.Action"
                    app:chipBackgroundColor="@color/excellent_green"
                    app:chipStrokeWidth="0dp" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipHashtags"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Which hashtags work best?"
                    style="@style/Widget.MaterialComponents.Chip.Action"
                    app:chipBackgroundColor="@color/good_orange"
                    app:chipStrokeWidth="0dp" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipImprove"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="How can I improve engagement?"
                    style="@style/Widget.MaterialComponents.Chip.Action"
                    app:chipBackgroundColor="@color/video_blue"
                    app:chipStrokeWidth="0dp" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipContent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="What content should I post more?"
                    style="@style/Widget.MaterialComponents.Chip.Action"
                    app:chipBackgroundColor="@color/reels_purple"
                    app:chipStrokeWidth="0dp" />

            </com.google.android.material.chip.ChipGroup>
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- Input Card -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/inputCard"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:cardCornerRadius="25dp"
        app:cardElevation="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- ✅ NEW: Attachment Preview (shows above input when file is selected) -->
        <LinearLayout
            android:id="@+id/layoutAttachmentPreview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp"
            android:background="@color/light_gray"
            android:visibility="gone"
            android:gravity="center_vertical">

            <ImageView
                android:id="@+id/imgAttachmentIcon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_attach_image"
                android:layout_marginEnd="8dp" />

            <TextView
                android:id="@+id/textAttachmentName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="file_name.jpg"
                android:textSize="14sp"
                android:textColor="@android:color/black"
                android:singleLine="true"
                android:ellipsize="end" />

            <ImageButton
                android:id="@+id/btnRemoveAttachment"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_close"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="Remove attachment" />

        </LinearLayout>

        <!-- ✅ NEW: Audio Recording UI (shows when recording) -->
        <LinearLayout
            android:id="@+id/layoutAudioRecording"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:background="@color/excellent_green"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🎤"
                    android:textSize="20sp"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Recording audio..."
                    android:textColor="@android:color/white"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/textRecordingDuration"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="00:00"
                    android:textColor="@android:color/white"
                    android:layout_marginEnd="8dp" />

                <ImageButton
                    android:id="@+id/btnStopRecording"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/ic_stop"
                    android:background="@drawable/circle_background_red"
                    android:contentDescription="Stop recording" />

            </LinearLayout>

            <!-- ✅ Audio Waveform Visualization (simple progress bar for now) -->
            <ProgressBar
                android:id="@+id/progressAudioWave"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="4dp"
                android:layout_marginTop="8dp"
                android:progressTint="@android:color/white"
                android:progress="50" />

        </LinearLayout>

        <!-- Main Input Layout -->
        <LinearLayout
            android:id="@+id/layoutMainInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="8dp"
            android:gravity="center_vertical">

            <!-- ✅ NEW: Attachment Button (Plus Icon) -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnAttachments"
                style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_margin="4dp"
                android:insetLeft="0dp"
                android:insetTop="0dp"
                android:insetRight="0dp"
                android:insetBottom="0dp"
                android:text="+"
                android:textSize="24sp"
                android:textColor="@android:color/black"
                app:backgroundTint="@color/light_gray"
                app:cornerRadius="24dp"
                android:contentDescription="Add attachments" />

            <EditText
                android:id="@+id/editTextQuestion"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@null"
                android:hint="Ask about your Instagram..."
                android:inputType="textMultiLine|textCapSentences"
                android:maxLines="4"
                android:minHeight="48dp"
                android:padding="12dp"
                android:textSize="16sp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSendQuestion"
                style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_margin="4dp"
                android:insetLeft="0dp"
                android:insetTop="0dp"
                android:insetRight="0dp"
                android:insetBottom="0dp"
                android:text="➤"
                android:textSize="20sp"
                app:backgroundTint="@color/excellent_green"
                app:cornerRadius="24dp" />
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- ✅ NEW: Attachment Options Menu (BottomSheet style) -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardAttachmentOptions"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:visibility="gone"
        app:cardCornerRadius="16dp"
        app:cardElevation="12dp"
        app:layout_constraintBottom_toTopOf="@+id/inputCard"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="📎 Add Content"
                android:textStyle="bold"
                android:textSize="16sp"
                android:textColor="@android:color/black"
                android:layout_marginBottom="12dp" />

            <!-- Attachment Options Grid -->
            <GridLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:columnCount="2"
                android:useDefaultMargins="true">

                <!-- Image Upload -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cardImageUpload"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_columnWeight="1"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="2dp"
                    app:cardBackgroundColor="@color/light_blue">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="📱"
                            android:textSize="24sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Image"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:textColor="@android:color/black" />

                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <!-- Voice Recording -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cardVoiceRecord"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_columnWeight="1"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="2dp"
                    app:cardBackgroundColor="@color/excellent_green">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🎤"
                            android:textSize="24sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Record"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:textColor="@android:color/white" />

                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <!-- Audio Upload -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cardAudioUpload"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_columnWeight="1"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="2dp"
                    app:cardBackgroundColor="@color/good_orange">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🎵"
                            android:textSize="24sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Audio"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:textColor="@android:color/white" />

                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <!-- PDF Upload -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cardPDFUpload"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_columnWeight="1"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="2dp"
                    app:cardBackgroundColor="@color/reels_purple">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="📄"
                            android:textSize="24sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="PDF"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:textColor="@android:color/white" />

                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

            </GridLayout>

            <!-- URL Input (Instagram Analysis) -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardURLAnalysis"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="@color/video_blue">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🔗"
                        android:textSize="20sp"
                        android:layout_marginEnd="8dp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Analyze Instagram URL"
                        android:textStyle="bold"
                        android:textColor="@android:color/white" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Profiles • Posts • Reels"
                        android:textSize="11sp"
                        android:textColor="@android:color/white"
                        android:alpha="0.8" />

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- Loading Overlay -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/loadingOverlay"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#80FFFFFF"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="Analyzing your Instagram data..."
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/progressBar" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 