// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.chip.ChipGroup;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemDatabaseRecordBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final Button deleteButton;

  @NonNull
  public final Button detailsButton;

  @NonNull
  public final TextView recordDate;

  @NonNull
  public final TextView recordDetails;

  @NonNull
  public final TextView recordId;

  @NonNull
  public final TextView recordSubtitle;

  @NonNull
  public final ChipGroup recordTags;

  @NonNull
  public final TextView recordTitle;

  private ItemDatabaseRecordBinding(@NonNull MaterialCardView rootView,
      @NonNull Button deleteButton, @NonNull Button detailsButton, @NonNull TextView recordDate,
      @NonNull TextView recordDetails, @NonNull TextView recordId, @NonNull TextView recordSubtitle,
      @NonNull ChipGroup recordTags, @NonNull TextView recordTitle) {
    this.rootView = rootView;
    this.deleteButton = deleteButton;
    this.detailsButton = detailsButton;
    this.recordDate = recordDate;
    this.recordDetails = recordDetails;
    this.recordId = recordId;
    this.recordSubtitle = recordSubtitle;
    this.recordTags = recordTags;
    this.recordTitle = recordTitle;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemDatabaseRecordBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemDatabaseRecordBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_database_record, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemDatabaseRecordBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.delete_button;
      Button deleteButton = ViewBindings.findChildViewById(rootView, id);
      if (deleteButton == null) {
        break missingId;
      }

      id = R.id.details_button;
      Button detailsButton = ViewBindings.findChildViewById(rootView, id);
      if (detailsButton == null) {
        break missingId;
      }

      id = R.id.record_date;
      TextView recordDate = ViewBindings.findChildViewById(rootView, id);
      if (recordDate == null) {
        break missingId;
      }

      id = R.id.record_details;
      TextView recordDetails = ViewBindings.findChildViewById(rootView, id);
      if (recordDetails == null) {
        break missingId;
      }

      id = R.id.record_id;
      TextView recordId = ViewBindings.findChildViewById(rootView, id);
      if (recordId == null) {
        break missingId;
      }

      id = R.id.record_subtitle;
      TextView recordSubtitle = ViewBindings.findChildViewById(rootView, id);
      if (recordSubtitle == null) {
        break missingId;
      }

      id = R.id.record_tags;
      ChipGroup recordTags = ViewBindings.findChildViewById(rootView, id);
      if (recordTags == null) {
        break missingId;
      }

      id = R.id.record_title;
      TextView recordTitle = ViewBindings.findChildViewById(rootView, id);
      if (recordTitle == null) {
        break missingId;
      }

      return new ItemDatabaseRecordBinding((MaterialCardView) rootView, deleteButton, detailsButton,
          recordDate, recordDetails, recordId, recordSubtitle, recordTags, recordTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
