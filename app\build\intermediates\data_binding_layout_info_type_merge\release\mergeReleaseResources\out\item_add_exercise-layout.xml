<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_add_exercise" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_add_exercise.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_add_exercise_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="129" endOffset="14"/></Target><Target id="@+id/exercise_name_input" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="22" startOffset="12" endLine="27" endOffset="59"/></Target><Target id="@+id/remove_exercise_button" view="ImageButton"><Expressions/><location startLine="30" startOffset="8" endLine="39" endOffset="47"/></Target><Target id="@+id/exercise_sets_input" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="56" startOffset="12" endLine="61" endOffset="59"/></Target><Target id="@+id/exercise_reps_input" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="73" startOffset="12" endLine="78" endOffset="59"/></Target><Target id="@+id/exercise_weight_input" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="89" startOffset="12" endLine="94" endOffset="59"/></Target><Target id="@+id/muscle_group_dropdown" view="AutoCompleteTextView"><Expressions/><location startLine="105" startOffset="8" endLine="110" endOffset="55"/></Target><Target id="@+id/exercise_notes_input" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="120" startOffset="8" endLine="126" endOffset="55"/></Target></Targets></Layout>