[{"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/dialog_expense_investment.xml", "source": "com.example.allinone.app-main-112:/layout/dialog_expense_investment.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/dialog_progress.xml", "source": "com.example.allinone.app-main-112:/layout/dialog_progress.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_transaction.xml", "source": "com.example.allinone.app-main-112:/layout/item_transaction.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_category_dropdown.xml", "source": "com.example.allinone.app-main-112:/layout/item_category_dropdown.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/dialog_edit_investment.xml", "source": "com.example.allinone.app-main-112:/layout/dialog_edit_investment.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_chat_user.xml", "source": "com.example.allinone.app-main-112:/layout/item_chat_user.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/dialog_program_details.xml", "source": "com.example.allinone.app-main-112:/layout/dialog_program_details.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_fullscreen_image.xml", "source": "com.example.allinone.app-main-112:/layout/item_fullscreen_image.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/fragment_investments_tab.xml", "source": "com.example.allinone.app-main-112:/layout/fragment_investments_tab.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/layout_page_header.xml", "source": "com.example.allinone.app-main-112:/layout/layout_page_header.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/dialog_edit_lesson.xml", "source": "com.example.allinone.app-main-112:/layout/dialog_edit_lesson.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_chat_source.xml", "source": "com.example.allinone.app-main-112:/layout/item_chat_source.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/dialog_add_student.xml", "source": "com.example.allinone.app-main-112:/layout/dialog_add_student.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_wt_registration.xml", "source": "com.example.allinone.app-main-112:/layout/item_wt_registration.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/offline_status_view.xml", "source": "com.example.allinone.app-main-112:/layout/offline_status_view.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/dialog_edit_seminar.xml", "source": "com.example.allinone.app-main-112:/layout/dialog_edit_seminar.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_add_exercise.xml", "source": "com.example.allinone.app-main-112:/layout/item_add_exercise.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_investment.xml", "source": "com.example.allinone.app-main-112:/layout/item_investment.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_note_video.xml", "source": "com.example.allinone.app-main-112:/layout/item_note_video.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_wt_event.xml", "source": "com.example.allinone.app-main-112:/layout/item_wt_event.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/dialog_add_event.xml", "source": "com.example.allinone.app-main-112:/layout/dialog_add_event.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_note_image.xml", "source": "com.example.allinone.app-main-112:/layout/item_note_image.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/dialog_income_investment.xml", "source": "com.example.allinone.app-main-112:/layout/dialog_income_investment.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/dialog_edit_program.xml", "source": "com.example.allinone.app-main-112:/layout/dialog_edit_program.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/pie_chart_tooltip.xml", "source": "com.example.allinone.app-main-112:/layout/pie_chart_tooltip.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/fragment_transaction_report.xml", "source": "com.example.allinone.app-main-112:/layout/fragment_transaction_report.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_chat_ai.xml", "source": "com.example.allinone.app-main-112:/layout/item_chat_ai.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/activity_backup.xml", "source": "com.example.allinone.app-main-112:/layout/activity_backup.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/fragment_transactions_overview.xml", "source": "com.example.allinone.app-main-112:/layout/fragment_transactions_overview.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/dialog_profit_loss.xml", "source": "com.example.allinone.app-main-112:/layout/dialog_profit_loss.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/fragment_futures_tab.xml", "source": "com.example.allinone.app-main-112:/layout/fragment_futures_tab.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/dialog_workout_details.xml", "source": "com.example.allinone.app-main-112:/layout/dialog_workout_details.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/dialog_add_task.xml", "source": "com.example.allinone.app-main-112:/layout/dialog_add_task.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_file_structure.xml", "source": "com.example.allinone.app-main-112:/layout/item_file_structure.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/splash_text_layout.xml", "source": "com.example.allinone.app-main-112:/layout/splash_text_layout.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_investment_image.xml", "source": "com.example.allinone.app-main-112:/layout/item_investment_image.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/dialog_edit_wt_student.xml", "source": "com.example.allinone.app-main-112:/layout/dialog_edit_wt_student.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_investment_selection.xml", "source": "com.example.allinone.app-main-112:/layout/item_investment_selection.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_binance_position.xml", "source": "com.example.allinone.app-main-112:/layout/item_binance_position.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/fragment_investments.xml", "source": "com.example.allinone.app-main-112:/layout/fragment_investments.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_lesson.xml", "source": "com.example.allinone.app-main-112:/layout/item_lesson.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/dialog_loading.xml", "source": "com.example.allinone.app-main-112:/layout/dialog_loading.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_futures_position.xml", "source": "com.example.allinone.app-main-112:/layout/item_futures_position.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/dialog_post_details.xml", "source": "com.example.allinone.app-main-112:/layout/dialog_post_details.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/fragment_home.xml", "source": "com.example.allinone.app-main-112:/layout/fragment_home.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_wt_student.xml", "source": "com.example.allinone.app-main-112:/layout/item_wt_student.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/dialog_pin_input.xml", "source": "com.example.allinone.app-main-112:/layout/dialog_pin_input.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_category_spending.xml", "source": "com.example.allinone.app-main-112:/layout/item_category_spending.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_note.xml", "source": "com.example.allinone.app-main-112:/layout/item_note.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_transaction_report.xml", "source": "com.example.allinone.app-main-112:/layout/item_transaction_report.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_category_summary.xml", "source": "com.example.allinone.app-main-112:/layout/item_category_summary.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_investment_dropdown.xml", "source": "com.example.allinone.app-main-112:/layout/item_investment_dropdown.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/theme_switch_layout.xml", "source": "com.example.allinone.app-main-112:/layout/theme_switch_layout.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/dialog_task_group.xml", "source": "com.example.allinone.app-main-112:/layout/dialog_task_group.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/dialog_fullscreen_image.xml", "source": "com.example.allinone.app-main-112:/layout/dialog_fullscreen_image.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/nav_header.xml", "source": "com.example.allinone.app-main-112:/layout/nav_header.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_backup.xml", "source": "com.example.allinone.app-main-112:/layout/item_backup.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/dialog_add_program.xml", "source": "com.example.allinone.app-main-112:/layout/dialog_add_program.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/dialog_student_details.xml", "source": "com.example.allinone.app-main-112:/layout/dialog_student_details.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_voice_note.xml", "source": "com.example.allinone.app-main-112:/layout/item_voice_note.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/item_seminar.xml", "source": "com.example.allinone.app-main-112:/layout/item_seminar.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/dialog_futures_tp_sl.xml", "source": "com.example.allinone.app-main-112:/layout/dialog_futures_tp_sl.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/dropdown_item.xml", "source": "com.example.allinone.app-main-112:/layout/dropdown_item.xml"}, {"merged": "com.example.allinone.app-mergeReleaseResources-109:/layout/fragment_futures.xml", "source": "com.example.allinone.app-main-112:/layout/fragment_futures.xml"}]