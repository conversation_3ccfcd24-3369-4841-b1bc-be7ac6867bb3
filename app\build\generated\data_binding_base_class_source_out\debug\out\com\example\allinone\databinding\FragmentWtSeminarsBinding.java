// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentWtSeminarsBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final FloatingActionButton addSeminarFab;

  @NonNull
  public final TextView emptyStateText;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView seminarsRecyclerView;

  private FragmentWtSeminarsBinding(@NonNull CoordinatorLayout rootView,
      @NonNull FloatingActionButton addSeminarFab, @NonNull TextView emptyStateText,
      @NonNull ProgressBar progressBar, @NonNull RecyclerView seminarsRecyclerView) {
    this.rootView = rootView;
    this.addSeminarFab = addSeminarFab;
    this.emptyStateText = emptyStateText;
    this.progressBar = progressBar;
    this.seminarsRecyclerView = seminarsRecyclerView;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentWtSeminarsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentWtSeminarsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_wt_seminars, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentWtSeminarsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addSeminarFab;
      FloatingActionButton addSeminarFab = ViewBindings.findChildViewById(rootView, id);
      if (addSeminarFab == null) {
        break missingId;
      }

      id = R.id.emptyStateText;
      TextView emptyStateText = ViewBindings.findChildViewById(rootView, id);
      if (emptyStateText == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.seminarsRecyclerView;
      RecyclerView seminarsRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (seminarsRecyclerView == null) {
        break missingId;
      }

      return new FragmentWtSeminarsBinding((CoordinatorLayout) rootView, addSeminarFab,
          emptyStateText, progressBar, seminarsRecyclerView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
