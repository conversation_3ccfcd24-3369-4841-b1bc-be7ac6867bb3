// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentCalendarBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ConstraintLayout calendarHeader;

  @NonNull
  public final TextView day1;

  @NonNull
  public final TextView day10;

  @NonNull
  public final TextView day11;

  @NonNull
  public final TextView day12;

  @NonNull
  public final TextView day13;

  @NonNull
  public final TextView day14;

  @NonNull
  public final TextView day15;

  @NonNull
  public final TextView day16;

  @NonNull
  public final TextView day17;

  @NonNull
  public final TextView day18;

  @NonNull
  public final TextView day19;

  @NonNull
  public final TextView day2;

  @NonNull
  public final TextView day20;

  @NonNull
  public final TextView day21;

  @NonNull
  public final TextView day22;

  @NonNull
  public final TextView day23;

  @NonNull
  public final TextView day24;

  @NonNull
  public final TextView day25;

  @NonNull
  public final TextView day26;

  @NonNull
  public final TextView day27;

  @NonNull
  public final TextView day28;

  @NonNull
  public final TextView day29;

  @NonNull
  public final TextView day3;

  @NonNull
  public final TextView day30;

  @NonNull
  public final TextView day31;

  @NonNull
  public final TextView day32;

  @NonNull
  public final TextView day33;

  @NonNull
  public final TextView day34;

  @NonNull
  public final TextView day35;

  @NonNull
  public final TextView day36;

  @NonNull
  public final TextView day37;

  @NonNull
  public final TextView day38;

  @NonNull
  public final TextView day39;

  @NonNull
  public final TextView day4;

  @NonNull
  public final TextView day40;

  @NonNull
  public final TextView day41;

  @NonNull
  public final TextView day42;

  @NonNull
  public final TextView day5;

  @NonNull
  public final TextView day6;

  @NonNull
  public final TextView day7;

  @NonNull
  public final TextView day8;

  @NonNull
  public final TextView day9;

  @NonNull
  public final TextView emptyEventsText;

  @NonNull
  public final TextView eventsHeader;

  @NonNull
  public final RecyclerView eventsRecyclerView;

  @NonNull
  public final TextView infoText;

  @NonNull
  public final TextView monthYearText;

  @NonNull
  public final ImageButton nextMonthButton;

  @NonNull
  public final ImageButton prevMonthButton;

  @NonNull
  public final LinearLayout week1;

  @NonNull
  public final LinearLayout week2;

  @NonNull
  public final LinearLayout week3;

  @NonNull
  public final LinearLayout week4;

  @NonNull
  public final LinearLayout week5;

  @NonNull
  public final LinearLayout week6;

  @NonNull
  public final LinearLayout weekDaysHeader;

  private FragmentCalendarBinding(@NonNull ConstraintLayout rootView,
      @NonNull ConstraintLayout calendarHeader, @NonNull TextView day1, @NonNull TextView day10,
      @NonNull TextView day11, @NonNull TextView day12, @NonNull TextView day13,
      @NonNull TextView day14, @NonNull TextView day15, @NonNull TextView day16,
      @NonNull TextView day17, @NonNull TextView day18, @NonNull TextView day19,
      @NonNull TextView day2, @NonNull TextView day20, @NonNull TextView day21,
      @NonNull TextView day22, @NonNull TextView day23, @NonNull TextView day24,
      @NonNull TextView day25, @NonNull TextView day26, @NonNull TextView day27,
      @NonNull TextView day28, @NonNull TextView day29, @NonNull TextView day3,
      @NonNull TextView day30, @NonNull TextView day31, @NonNull TextView day32,
      @NonNull TextView day33, @NonNull TextView day34, @NonNull TextView day35,
      @NonNull TextView day36, @NonNull TextView day37, @NonNull TextView day38,
      @NonNull TextView day39, @NonNull TextView day4, @NonNull TextView day40,
      @NonNull TextView day41, @NonNull TextView day42, @NonNull TextView day5,
      @NonNull TextView day6, @NonNull TextView day7, @NonNull TextView day8,
      @NonNull TextView day9, @NonNull TextView emptyEventsText, @NonNull TextView eventsHeader,
      @NonNull RecyclerView eventsRecyclerView, @NonNull TextView infoText,
      @NonNull TextView monthYearText, @NonNull ImageButton nextMonthButton,
      @NonNull ImageButton prevMonthButton, @NonNull LinearLayout week1,
      @NonNull LinearLayout week2, @NonNull LinearLayout week3, @NonNull LinearLayout week4,
      @NonNull LinearLayout week5, @NonNull LinearLayout week6,
      @NonNull LinearLayout weekDaysHeader) {
    this.rootView = rootView;
    this.calendarHeader = calendarHeader;
    this.day1 = day1;
    this.day10 = day10;
    this.day11 = day11;
    this.day12 = day12;
    this.day13 = day13;
    this.day14 = day14;
    this.day15 = day15;
    this.day16 = day16;
    this.day17 = day17;
    this.day18 = day18;
    this.day19 = day19;
    this.day2 = day2;
    this.day20 = day20;
    this.day21 = day21;
    this.day22 = day22;
    this.day23 = day23;
    this.day24 = day24;
    this.day25 = day25;
    this.day26 = day26;
    this.day27 = day27;
    this.day28 = day28;
    this.day29 = day29;
    this.day3 = day3;
    this.day30 = day30;
    this.day31 = day31;
    this.day32 = day32;
    this.day33 = day33;
    this.day34 = day34;
    this.day35 = day35;
    this.day36 = day36;
    this.day37 = day37;
    this.day38 = day38;
    this.day39 = day39;
    this.day4 = day4;
    this.day40 = day40;
    this.day41 = day41;
    this.day42 = day42;
    this.day5 = day5;
    this.day6 = day6;
    this.day7 = day7;
    this.day8 = day8;
    this.day9 = day9;
    this.emptyEventsText = emptyEventsText;
    this.eventsHeader = eventsHeader;
    this.eventsRecyclerView = eventsRecyclerView;
    this.infoText = infoText;
    this.monthYearText = monthYearText;
    this.nextMonthButton = nextMonthButton;
    this.prevMonthButton = prevMonthButton;
    this.week1 = week1;
    this.week2 = week2;
    this.week3 = week3;
    this.week4 = week4;
    this.week5 = week5;
    this.week6 = week6;
    this.weekDaysHeader = weekDaysHeader;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentCalendarBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentCalendarBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_calendar, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentCalendarBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.calendarHeader;
      ConstraintLayout calendarHeader = ViewBindings.findChildViewById(rootView, id);
      if (calendarHeader == null) {
        break missingId;
      }

      id = R.id.day1;
      TextView day1 = ViewBindings.findChildViewById(rootView, id);
      if (day1 == null) {
        break missingId;
      }

      id = R.id.day10;
      TextView day10 = ViewBindings.findChildViewById(rootView, id);
      if (day10 == null) {
        break missingId;
      }

      id = R.id.day11;
      TextView day11 = ViewBindings.findChildViewById(rootView, id);
      if (day11 == null) {
        break missingId;
      }

      id = R.id.day12;
      TextView day12 = ViewBindings.findChildViewById(rootView, id);
      if (day12 == null) {
        break missingId;
      }

      id = R.id.day13;
      TextView day13 = ViewBindings.findChildViewById(rootView, id);
      if (day13 == null) {
        break missingId;
      }

      id = R.id.day14;
      TextView day14 = ViewBindings.findChildViewById(rootView, id);
      if (day14 == null) {
        break missingId;
      }

      id = R.id.day15;
      TextView day15 = ViewBindings.findChildViewById(rootView, id);
      if (day15 == null) {
        break missingId;
      }

      id = R.id.day16;
      TextView day16 = ViewBindings.findChildViewById(rootView, id);
      if (day16 == null) {
        break missingId;
      }

      id = R.id.day17;
      TextView day17 = ViewBindings.findChildViewById(rootView, id);
      if (day17 == null) {
        break missingId;
      }

      id = R.id.day18;
      TextView day18 = ViewBindings.findChildViewById(rootView, id);
      if (day18 == null) {
        break missingId;
      }

      id = R.id.day19;
      TextView day19 = ViewBindings.findChildViewById(rootView, id);
      if (day19 == null) {
        break missingId;
      }

      id = R.id.day2;
      TextView day2 = ViewBindings.findChildViewById(rootView, id);
      if (day2 == null) {
        break missingId;
      }

      id = R.id.day20;
      TextView day20 = ViewBindings.findChildViewById(rootView, id);
      if (day20 == null) {
        break missingId;
      }

      id = R.id.day21;
      TextView day21 = ViewBindings.findChildViewById(rootView, id);
      if (day21 == null) {
        break missingId;
      }

      id = R.id.day22;
      TextView day22 = ViewBindings.findChildViewById(rootView, id);
      if (day22 == null) {
        break missingId;
      }

      id = R.id.day23;
      TextView day23 = ViewBindings.findChildViewById(rootView, id);
      if (day23 == null) {
        break missingId;
      }

      id = R.id.day24;
      TextView day24 = ViewBindings.findChildViewById(rootView, id);
      if (day24 == null) {
        break missingId;
      }

      id = R.id.day25;
      TextView day25 = ViewBindings.findChildViewById(rootView, id);
      if (day25 == null) {
        break missingId;
      }

      id = R.id.day26;
      TextView day26 = ViewBindings.findChildViewById(rootView, id);
      if (day26 == null) {
        break missingId;
      }

      id = R.id.day27;
      TextView day27 = ViewBindings.findChildViewById(rootView, id);
      if (day27 == null) {
        break missingId;
      }

      id = R.id.day28;
      TextView day28 = ViewBindings.findChildViewById(rootView, id);
      if (day28 == null) {
        break missingId;
      }

      id = R.id.day29;
      TextView day29 = ViewBindings.findChildViewById(rootView, id);
      if (day29 == null) {
        break missingId;
      }

      id = R.id.day3;
      TextView day3 = ViewBindings.findChildViewById(rootView, id);
      if (day3 == null) {
        break missingId;
      }

      id = R.id.day30;
      TextView day30 = ViewBindings.findChildViewById(rootView, id);
      if (day30 == null) {
        break missingId;
      }

      id = R.id.day31;
      TextView day31 = ViewBindings.findChildViewById(rootView, id);
      if (day31 == null) {
        break missingId;
      }

      id = R.id.day32;
      TextView day32 = ViewBindings.findChildViewById(rootView, id);
      if (day32 == null) {
        break missingId;
      }

      id = R.id.day33;
      TextView day33 = ViewBindings.findChildViewById(rootView, id);
      if (day33 == null) {
        break missingId;
      }

      id = R.id.day34;
      TextView day34 = ViewBindings.findChildViewById(rootView, id);
      if (day34 == null) {
        break missingId;
      }

      id = R.id.day35;
      TextView day35 = ViewBindings.findChildViewById(rootView, id);
      if (day35 == null) {
        break missingId;
      }

      id = R.id.day36;
      TextView day36 = ViewBindings.findChildViewById(rootView, id);
      if (day36 == null) {
        break missingId;
      }

      id = R.id.day37;
      TextView day37 = ViewBindings.findChildViewById(rootView, id);
      if (day37 == null) {
        break missingId;
      }

      id = R.id.day38;
      TextView day38 = ViewBindings.findChildViewById(rootView, id);
      if (day38 == null) {
        break missingId;
      }

      id = R.id.day39;
      TextView day39 = ViewBindings.findChildViewById(rootView, id);
      if (day39 == null) {
        break missingId;
      }

      id = R.id.day4;
      TextView day4 = ViewBindings.findChildViewById(rootView, id);
      if (day4 == null) {
        break missingId;
      }

      id = R.id.day40;
      TextView day40 = ViewBindings.findChildViewById(rootView, id);
      if (day40 == null) {
        break missingId;
      }

      id = R.id.day41;
      TextView day41 = ViewBindings.findChildViewById(rootView, id);
      if (day41 == null) {
        break missingId;
      }

      id = R.id.day42;
      TextView day42 = ViewBindings.findChildViewById(rootView, id);
      if (day42 == null) {
        break missingId;
      }

      id = R.id.day5;
      TextView day5 = ViewBindings.findChildViewById(rootView, id);
      if (day5 == null) {
        break missingId;
      }

      id = R.id.day6;
      TextView day6 = ViewBindings.findChildViewById(rootView, id);
      if (day6 == null) {
        break missingId;
      }

      id = R.id.day7;
      TextView day7 = ViewBindings.findChildViewById(rootView, id);
      if (day7 == null) {
        break missingId;
      }

      id = R.id.day8;
      TextView day8 = ViewBindings.findChildViewById(rootView, id);
      if (day8 == null) {
        break missingId;
      }

      id = R.id.day9;
      TextView day9 = ViewBindings.findChildViewById(rootView, id);
      if (day9 == null) {
        break missingId;
      }

      id = R.id.emptyEventsText;
      TextView emptyEventsText = ViewBindings.findChildViewById(rootView, id);
      if (emptyEventsText == null) {
        break missingId;
      }

      id = R.id.eventsHeader;
      TextView eventsHeader = ViewBindings.findChildViewById(rootView, id);
      if (eventsHeader == null) {
        break missingId;
      }

      id = R.id.eventsRecyclerView;
      RecyclerView eventsRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (eventsRecyclerView == null) {
        break missingId;
      }

      id = R.id.infoText;
      TextView infoText = ViewBindings.findChildViewById(rootView, id);
      if (infoText == null) {
        break missingId;
      }

      id = R.id.monthYearText;
      TextView monthYearText = ViewBindings.findChildViewById(rootView, id);
      if (monthYearText == null) {
        break missingId;
      }

      id = R.id.nextMonthButton;
      ImageButton nextMonthButton = ViewBindings.findChildViewById(rootView, id);
      if (nextMonthButton == null) {
        break missingId;
      }

      id = R.id.prevMonthButton;
      ImageButton prevMonthButton = ViewBindings.findChildViewById(rootView, id);
      if (prevMonthButton == null) {
        break missingId;
      }

      id = R.id.week1;
      LinearLayout week1 = ViewBindings.findChildViewById(rootView, id);
      if (week1 == null) {
        break missingId;
      }

      id = R.id.week2;
      LinearLayout week2 = ViewBindings.findChildViewById(rootView, id);
      if (week2 == null) {
        break missingId;
      }

      id = R.id.week3;
      LinearLayout week3 = ViewBindings.findChildViewById(rootView, id);
      if (week3 == null) {
        break missingId;
      }

      id = R.id.week4;
      LinearLayout week4 = ViewBindings.findChildViewById(rootView, id);
      if (week4 == null) {
        break missingId;
      }

      id = R.id.week5;
      LinearLayout week5 = ViewBindings.findChildViewById(rootView, id);
      if (week5 == null) {
        break missingId;
      }

      id = R.id.week6;
      LinearLayout week6 = ViewBindings.findChildViewById(rootView, id);
      if (week6 == null) {
        break missingId;
      }

      id = R.id.weekDaysHeader;
      LinearLayout weekDaysHeader = ViewBindings.findChildViewById(rootView, id);
      if (weekDaysHeader == null) {
        break missingId;
      }

      return new FragmentCalendarBinding((ConstraintLayout) rootView, calendarHeader, day1, day10,
          day11, day12, day13, day14, day15, day16, day17, day18, day19, day2, day20, day21, day22,
          day23, day24, day25, day26, day27, day28, day29, day3, day30, day31, day32, day33, day34,
          day35, day36, day37, day38, day39, day4, day40, day41, day42, day5, day6, day7, day8,
          day9, emptyEventsText, eventsHeader, eventsRecyclerView, infoText, monthYearText,
          nextMonthButton, prevMonthButton, week1, week2, week3, week4, week5, week6,
          weekDaysHeader);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
