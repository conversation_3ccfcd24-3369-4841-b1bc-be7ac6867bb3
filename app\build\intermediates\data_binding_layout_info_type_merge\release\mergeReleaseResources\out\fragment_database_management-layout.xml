<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_database_management" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\fragment_database_management.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_database_management_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="172" endOffset="51"/></Target><Target id="@+id/title_text" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="18" endOffset="50"/></Target><Target id="@+id/tab_layout" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="20" startOffset="4" endLine="80" endOffset="48"/></Target><Target id="@+id/data_container" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="82" startOffset="4" endLine="170" endOffset="55"/></Target><Target id="@+id/data_card" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="92" startOffset="8" endLine="169" endOffset="59"/></Target><Target id="@+id/data_header" view="LinearLayout"><Expressions/><location startLine="103" startOffset="16" endLine="135" endOffset="30"/></Target><Target id="@+id/collection_name" view="TextView"><Expressions/><location startLine="111" startOffset="20" endLine="118" endOffset="51"/></Target><Target id="@+id/item_count" view="TextView"><Expressions/><location startLine="120" startOffset="20" endLine="125" endOffset="47"/></Target><Target id="@+id/refresh_button" view="ImageButton"><Expressions/><location startLine="127" startOffset="20" endLine="134" endOffset="71"/></Target><Target id="@+id/data_recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="137" startOffset="16" endLine="144" endOffset="69"/></Target><Target id="@+id/data_loading_progress" view="ProgressBar"><Expressions/><location startLine="146" startOffset="16" endLine="154" endOffset="63"/></Target><Target id="@+id/empty_view" view="TextView"><Expressions/><location startLine="156" startOffset="16" endLine="166" endOffset="69"/></Target></Targets></Layout>