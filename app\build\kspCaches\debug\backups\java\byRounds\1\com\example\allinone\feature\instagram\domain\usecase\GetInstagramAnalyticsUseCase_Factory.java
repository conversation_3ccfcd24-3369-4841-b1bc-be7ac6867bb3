package com.example.allinone.feature.instagram.domain.usecase;

import com.example.allinone.feature.instagram.domain.repository.InstagramRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class GetInstagramAnalyticsUseCase_Factory implements Factory<GetInstagramAnalyticsUseCase> {
  private final Provider<InstagramRepository> repositoryProvider;

  public GetInstagramAnalyticsUseCase_Factory(Provider<InstagramRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public GetInstagramAnalyticsUseCase get() {
    return newInstance(repositoryProvider.get());
  }

  public static GetInstagramAnalyticsUseCase_Factory create(
      Provider<InstagramRepository> repositoryProvider) {
    return new GetInstagramAnalyticsUseCase_Factory(repositoryProvider);
  }

  public static GetInstagramAnalyticsUseCase newInstance(InstagramRepository repository) {
    return new GetInstagramAnalyticsUseCase(repository);
  }
}
