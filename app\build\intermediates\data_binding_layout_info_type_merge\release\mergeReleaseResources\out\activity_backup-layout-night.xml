<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_backup" modulePackage="com.example.allinone" filePath="app\src\main\res\layout-night\activity_backup.xml" directory="layout-night" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout-night/activity_backup_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="120" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="20" endOffset="40"/></Target><Target id="@+id/backup_card" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="22" startOffset="4" endLine="64" endOffset="39"/></Target><Target id="@+id/create_backup_button" view="Button"><Expressions/><location startLine="55" startOffset="12" endLine="62" endOffset="50"/></Target><Target id="@+id/backups_card" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="66" startOffset="4" endLine="108" endOffset="39"/></Target><Target id="@+id/no_backups_text" view="TextView"><Expressions/><location startLine="92" startOffset="12" endLine="100" endOffset="43"/></Target><Target id="@+id/backups_recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="102" startOffset="12" endLine="106" endOffset="48"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="110" startOffset="4" endLine="118" endOffset="51"/></Target></Targets></Layout>