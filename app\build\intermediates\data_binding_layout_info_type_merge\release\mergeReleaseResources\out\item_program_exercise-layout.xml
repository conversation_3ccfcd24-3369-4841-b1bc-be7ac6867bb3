<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_program_exercise" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\item_program_exercise.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_program_exercise_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="87" endOffset="51"/></Target><Target id="@+id/exercise_name" view="TextView"><Expressions/><location startLine="16" startOffset="8" endLine="21" endOffset="38"/></Target><Target id="@+id/exercise_sets" view="TextView"><Expressions/><location startLine="36" startOffset="12" endLine="42" endOffset="32"/></Target><Target id="@+id/exercise_reps" view="TextView"><Expressions/><location startLine="51" startOffset="12" endLine="57" endOffset="33"/></Target><Target id="@+id/exercise_weight" view="TextView"><Expressions/><location startLine="66" startOffset="12" endLine="71" endOffset="36"/></Target><Target id="@+id/exercise_notes" view="TextView"><Expressions/><location startLine="75" startOffset="8" endLine="83" endOffset="40"/></Target></Targets></Layout>