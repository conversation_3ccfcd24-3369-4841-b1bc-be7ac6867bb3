// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemCategorySpendingBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView categoryAmountText;

  @NonNull
  public final View categoryColorIndicator;

  @NonNull
  public final TextView categoryNameText;

  @NonNull
  public final TextView categoryPercentText;

  private ItemCategorySpendingBinding(@NonNull LinearLayout rootView,
      @NonNull TextView categoryAmountText, @NonNull View categoryColorIndicator,
      @NonNull TextView categoryNameText, @NonNull TextView categoryPercentText) {
    this.rootView = rootView;
    this.categoryAmountText = categoryAmountText;
    this.categoryColorIndicator = categoryColorIndicator;
    this.categoryNameText = categoryNameText;
    this.categoryPercentText = categoryPercentText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemCategorySpendingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemCategorySpendingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_category_spending, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemCategorySpendingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.categoryAmountText;
      TextView categoryAmountText = ViewBindings.findChildViewById(rootView, id);
      if (categoryAmountText == null) {
        break missingId;
      }

      id = R.id.categoryColorIndicator;
      View categoryColorIndicator = ViewBindings.findChildViewById(rootView, id);
      if (categoryColorIndicator == null) {
        break missingId;
      }

      id = R.id.categoryNameText;
      TextView categoryNameText = ViewBindings.findChildViewById(rootView, id);
      if (categoryNameText == null) {
        break missingId;
      }

      id = R.id.categoryPercentText;
      TextView categoryPercentText = ViewBindings.findChildViewById(rootView, id);
      if (categoryPercentText == null) {
        break missingId;
      }

      return new ItemCategorySpendingBinding((LinearLayout) rootView, categoryAmountText,
          categoryColorIndicator, categoryNameText, categoryPercentText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
