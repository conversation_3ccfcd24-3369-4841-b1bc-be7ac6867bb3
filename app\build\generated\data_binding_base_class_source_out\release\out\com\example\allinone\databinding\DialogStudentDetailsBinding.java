// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.imageview.ShapeableImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogStudentDetailsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton callButton;

  @NonNull
  public final TextView detailsTextView;

  @NonNull
  public final MaterialButton instagramButton;

  @NonNull
  public final TextView nameTextView;

  @NonNull
  public final ShapeableImageView profileImageView;

  @NonNull
  public final MaterialButton whatsappButton;

  private DialogStudentDetailsBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton callButton, @NonNull TextView detailsTextView,
      @NonNull MaterialButton instagramButton, @NonNull TextView nameTextView,
      @NonNull ShapeableImageView profileImageView, @NonNull MaterialButton whatsappButton) {
    this.rootView = rootView;
    this.callButton = callButton;
    this.detailsTextView = detailsTextView;
    this.instagramButton = instagramButton;
    this.nameTextView = nameTextView;
    this.profileImageView = profileImageView;
    this.whatsappButton = whatsappButton;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogStudentDetailsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogStudentDetailsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_student_details, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogStudentDetailsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.callButton;
      MaterialButton callButton = ViewBindings.findChildViewById(rootView, id);
      if (callButton == null) {
        break missingId;
      }

      id = R.id.detailsTextView;
      TextView detailsTextView = ViewBindings.findChildViewById(rootView, id);
      if (detailsTextView == null) {
        break missingId;
      }

      id = R.id.instagramButton;
      MaterialButton instagramButton = ViewBindings.findChildViewById(rootView, id);
      if (instagramButton == null) {
        break missingId;
      }

      id = R.id.nameTextView;
      TextView nameTextView = ViewBindings.findChildViewById(rootView, id);
      if (nameTextView == null) {
        break missingId;
      }

      id = R.id.profileImageView;
      ShapeableImageView profileImageView = ViewBindings.findChildViewById(rootView, id);
      if (profileImageView == null) {
        break missingId;
      }

      id = R.id.whatsappButton;
      MaterialButton whatsappButton = ViewBindings.findChildViewById(rootView, id);
      if (whatsappButton == null) {
        break missingId;
      }

      return new DialogStudentDetailsBinding((LinearLayout) rootView, callButton, detailsTextView,
          instagramButton, nameTextView, profileImageView, whatsappButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
