com.example.allinone:xml/network_security_config = 0x7f160005
com.example.allinone:xml/image_share_filepaths = 0x7f160004
com.example.allinone:xml/data_extraction_rules = 0x7f160001
com.example.allinone:styleable/ViewTransition = 0x7f1500b1
com.example.allinone:styleable/ViewPager2 = 0x7f1500af
com.example.allinone:styleable/ViewBackgroundHelper = 0x7f1500ae
com.example.allinone:styleable/View = 0x7f1500ad
com.example.allinone:styleable/Transition = 0x7f1500ab
com.example.allinone:styleable/Transform = 0x7f1500aa
com.example.allinone:styleable/Toolbar = 0x7f1500a8
com.example.allinone:styleable/TextInputLayout = 0x7f1500a6
com.example.allinone:styleable/TextEffects = 0x7f1500a4
com.example.allinone:styleable/TextAppearance = 0x7f1500a3
com.example.allinone:styleable/TabLayout = 0x7f1500a2
com.example.allinone:styleable/TabItem = 0x7f1500a1
com.example.allinone:styleable/StyledPlayerView = 0x7f15009d
com.example.allinone:styleable/StyledPlayerControlView = 0x7f15009c
com.example.allinone:styleable/StateSet = 0x7f15009b
com.example.allinone:styleable/StateListDrawableItem = 0x7f15009a
com.example.allinone:styleable/StateListDrawable = 0x7f150099
com.example.allinone:styleable/State = 0x7f150098
com.example.allinone:styleable/SplitPairFilter = 0x7f150095
com.example.allinone:styleable/Spinner = 0x7f150094
com.example.allinone:styleable/SnackbarLayout = 0x7f150093
com.example.allinone:styleable/Slider = 0x7f150091
com.example.allinone:styleable/SignInButton = 0x7f150090
com.example.allinone:styleable/ShapeableImageView = 0x7f15008e
com.example.allinone:styleable/SearchBar = 0x7f15008b
com.example.allinone:styleable/ScrollingViewBehavior_Layout = 0x7f15008a
com.example.allinone:styleable/ScrimInsetsFrameLayout = 0x7f150089
com.example.allinone:styleable/RecyclerView = 0x7f150088
com.example.allinone:styleable/RecycleListView = 0x7f150087
com.example.allinone:styleable/RangeSlider = 0x7f150086
com.example.allinone:styleable/PropertySet = 0x7f150084
com.example.allinone:styleable/PopupWindow = 0x7f150082
com.example.allinone:styleable/PlayerView = 0x7f150081
com.example.allinone:styleable/OnSwipe = 0x7f15007f
com.example.allinone:styleable/OnClick = 0x7f15007e
com.example.allinone:styleable/Navigator = 0x7f15007d
com.example.allinone:styleable/NavigationBarActiveIndicator = 0x7f150079
com.example.allinone:styleable/NavInclude = 0x7f150078
com.example.allinone:styleable/NavHost = 0x7f150076
com.example.allinone:styleable/NavArgument = 0x7f150073
com.example.allinone:styleable/NavAction = 0x7f150072
com.example.allinone:styleable/MotionTelltales = 0x7f150071
com.example.allinone:styleable/MotionScene = 0x7f150070
com.example.allinone:styleable/MotionLayout = 0x7f15006f
com.example.allinone:styleable/MotionEffect = 0x7f15006c
com.example.allinone:styleable/Motion = 0x7f15006b
com.example.allinone:styleable/MockView = 0x7f15006a
com.example.allinone:styleable/MenuView = 0x7f150069
com.example.allinone:styleable/MenuGroup = 0x7f150067
com.example.allinone:styleable/MaterialTextView = 0x7f150064
com.example.allinone:styleable/MaterialTextAppearance = 0x7f150063
com.example.allinone:styleable/MaterialSwitch = 0x7f150062
com.example.allinone:styleable/MaterialShape = 0x7f150061
com.example.allinone:styleable/MaterialCheckBox = 0x7f15005d
com.example.allinone:styleable/MaterialCardView = 0x7f15005c
com.example.allinone:styleable/MaterialCalendarItem = 0x7f15005b
com.example.allinone:styleable/MaterialCalendar = 0x7f15005a
com.example.allinone:styleable/MaterialAlertDialog = 0x7f150055
com.example.allinone:styleable/ListPopupWindow = 0x7f150053
com.example.allinone:styleable/LinearLayoutCompat = 0x7f150050
com.example.allinone:styleable/Layout = 0x7f15004f
com.example.allinone:styleable/KeyTrigger = 0x7f15004e
com.example.allinone:styleable/KeyTimeCycle = 0x7f15004d
com.example.allinone:styleable/KeyPosition = 0x7f15004c
com.example.allinone:styleable/KeyFramesAcceleration = 0x7f15004a
com.example.allinone:styleable/Insets = 0x7f150046
com.example.allinone:styleable/Grid = 0x7f150044
com.example.allinone:styleable/GradientColor = 0x7f150042
com.example.allinone:styleable/FragmentNavigator = 0x7f150041
com.example.allinone:styleable/ForegroundLinearLayout = 0x7f15003e
com.example.allinone:styleable/FontFamily = 0x7f15003c
com.example.allinone:styleable/FloatingActionButton = 0x7f150039
com.example.allinone:styleable/ExtendedFloatingActionButton = 0x7f150037
com.example.allinone:styleable/DrawerArrowToggle = 0x7f150035
com.example.allinone:styleable/DialogFragmentNavigator = 0x7f150034
com.example.allinone:styleable/CoordinatorLayout_Layout = 0x7f150031
com.example.allinone:styleable/CoordinatorLayout = 0x7f150030
com.example.allinone:styleable/ConstraintSet = 0x7f15002f
com.example.allinone:styleable/ConstraintOverride = 0x7f15002e
com.example.allinone:styleable/ConstraintLayout_placeholder = 0x7f15002d
com.example.allinone:styleable/ColorStateListItem = 0x7f150028
com.example.allinone:styleable/CollapsingToolbarLayout = 0x7f150026
com.example.allinone:styleable/ClockFaceView = 0x7f150024
com.example.allinone:styleable/CircularProgressIndicator = 0x7f150023
com.example.allinone:styleable/Chip = 0x7f150021
com.example.allinone:styleable/Carousel = 0x7f15001f
com.example.allinone:styleable/CardView = 0x7f15001e
com.example.allinone:styleable/Capability = 0x7f15001d
com.example.allinone:styleable/ButtonBarLayout = 0x7f15001c
com.example.allinone:styleable/BottomNavigationView = 0x7f15001a
com.example.allinone:styleable/BottomAppBar = 0x7f150019
com.example.allinone:styleable/BaseProgressIndicator = 0x7f150018
com.example.allinone:styleable/AppCompatTheme = 0x7f150015
com.example.allinone:styleable/AppCompatTextView = 0x7f150014
com.example.allinone:styleable/AppCompatTextHelper = 0x7f150013
com.example.allinone:styleable/AppBarLayoutStates = 0x7f15000e
com.example.allinone:styleable/AnimatedStateListDrawableItem = 0x7f15000b
com.example.allinone:styleable/AnimatedStateListDrawableCompat = 0x7f15000a
com.example.allinone:styleable/AlertDialog = 0x7f150009
com.example.allinone:styleable/ActivityRule = 0x7f150008
com.example.allinone:styleable/ActivityNavigator = 0x7f150007
com.example.allinone:styleable/ActivityFilter = 0x7f150006
com.example.allinone:styleable/ActionMode = 0x7f150004
com.example.allinone:styleable/ActionMenuView = 0x7f150003
com.example.allinone:styleable/ActionMenuItemView = 0x7f150002
com.example.allinone:styleable/ActionBarLayout = 0x7f150001
com.example.allinone:style/Widget.Support.CoordinatorLayout = 0x7f1404ac
com.example.allinone:style/Widget.MaterialComponents.TimePicker.ImageButton = 0x7f1404a5
com.example.allinone:style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout = 0x7f1404a4
com.example.allinone:style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f1404a3
com.example.allinone:style/Widget.MaterialComponents.TimePicker.Display.HelperText = 0x7f1404a2
com.example.allinone:style/Widget.MaterialComponents.TimePicker.Display.Divider = 0x7f1404a1
com.example.allinone:style/Widget.MaterialComponents.TimePicker = 0x7f14049d
com.example.allinone:style/Widget.MaterialComponents.TextView = 0x7f14049c
com.example.allinone:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox = 0x7f140498
com.example.allinone:style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f140497
com.example.allinone:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f140496
com.example.allinone:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense = 0x7f140495
com.example.allinone:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f140493
com.example.allinone:style/Widget.MaterialComponents.TabLayout.PrimarySurface = 0x7f14048f
com.example.allinone:style/Widget.MaterialComponents.TabLayout.Colored = 0x7f14048e
com.example.allinone:style/Widget.MaterialComponents.TabLayout = 0x7f14048d
com.example.allinone:style/Widget.MaterialComponents.Snackbar.FullWidth = 0x7f14048b
com.example.allinone:style/Widget.MaterialComponents.ShapeableImageView = 0x7f140488
com.example.allinone:style/Widget.MaterialComponents.PopupMenu.Overflow = 0x7f140486
com.example.allinone:style/Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f140484
com.example.allinone:style/Widget.MaterialComponents.NavigationView = 0x7f140482
com.example.allinone:style/Widget.MaterialComponents.NavigationRailView.PrimarySurface = 0x7f140481
com.example.allinone:style/Widget.MaterialComponents.NavigationRailView.Compact = 0x7f140480
com.example.allinone:style/Widget.MaterialComponents.NavigationRailView.Colored = 0x7f14047e
com.example.allinone:style/Widget.MaterialComponents.NavigationRailView = 0x7f14047d
com.example.allinone:style/Widget.MaterialComponents.MaterialDivider = 0x7f14047c
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar.YearNavigationButton = 0x7f14047b
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar.Year.Selected = 0x7f140479
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton = 0x7f140476
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f140474
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle = 0x7f140473
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f140472
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f140470
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton = 0x7f14046c
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f14046b
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar.Day = 0x7f140465
com.example.allinone:style/Widget.MaterialComponents.Light.ActionBar.Solid = 0x7f140461
com.example.allinone:style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon = 0x7f14045f
com.example.allinone:style/Widget.MaterialComponents.CompoundButton.Switch = 0x7f14045d
com.example.allinone:style/Widget.MaterialComponents.CompoundButton.RadioButton = 0x7f14045c
com.example.allinone:style/Widget.MaterialComponents.CollapsingToolbar = 0x7f14045a
com.example.allinone:style/Widget.MaterialComponents.CircularProgressIndicator.Medium = 0x7f140458
com.example.allinone:style/Widget.MaterialComponents.CircularProgressIndicator = 0x7f140456
com.example.allinone:style/Widget.MaterialComponents.Chip.Choice = 0x7f140452
com.example.allinone:style/Widget.MaterialComponents.CardView = 0x7f14044f
com.example.allinone:style/Widget.MaterialComponents.Button.TextButton.Snackbar = 0x7f14044c
com.example.allinone:style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush = 0x7f140449
com.example.allinone:style/Widget.MaterialComponents.Button.TextButton.Dialog = 0x7f140448
com.example.allinone:style/Widget.MaterialComponents.Button.TextButton = 0x7f140447
com.example.allinone:style/Widget.MaterialComponents.Button.OutlinedButton.Icon = 0x7f140446
com.example.allinone:style/Widget.MaterialComponents.Button.OutlinedButton = 0x7f140445
com.example.allinone:style/Widget.MaterialComponents.Button = 0x7f140443
com.example.allinone:style/Widget.MaterialComponents.BottomNavigationView.PrimarySurface = 0x7f140440
com.example.allinone:style/Widget.MaterialComponents.BottomNavigationView = 0x7f14043e
com.example.allinone:styleable/ActionBar = 0x7f150000
com.example.allinone:style/Widget.MaterialComponents.BottomAppBar = 0x7f14043b
com.example.allinone:style/Widget.MaterialComponents.Badge = 0x7f14043a
com.example.allinone:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f140437
com.example.allinone:style/Widget.MaterialComponents.AppBarLayout.PrimarySurface = 0x7f140434
com.example.allinone:style/Widget.MaterialComponents.AppBarLayout.Primary = 0x7f140433
com.example.allinone:style/Widget.MaterialComponents.ActionMode = 0x7f140432
com.example.allinone:style/Widget.MaterialComponents.ActionBar.Surface = 0x7f140431
com.example.allinone:style/Widget.MaterialComponents.ActionBar.Solid = 0x7f140430
com.example.allinone:style/Widget.MaterialComponents.ActionBar.PrimarySurface = 0x7f14042f
com.example.allinone:style/Widget.MaterialComponents.ActionBar.Primary = 0x7f14042e
com.example.allinone:style/Widget.Material3.Tooltip = 0x7f14042d
com.example.allinone:style/Widget.Material3.Toolbar.OnSurface = 0x7f14042b
com.example.allinone:style/Widget.Material3.Toolbar = 0x7f14042a
com.example.allinone:style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f140429
com.example.allinone:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f140428
com.example.allinone:style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f140425
com.example.allinone:style/Widget.Material3.TextInputEditText.FilledBox = 0x7f14041e
com.example.allinone:style/Widget.Material3.TabLayout.Secondary = 0x7f14041d
com.example.allinone:style/Widget.Material3.TabLayout = 0x7f14041b
com.example.allinone:style/Widget.Material3.Snackbar.TextView = 0x7f14041a
com.example.allinone:style/Widget.Material3.Snackbar = 0x7f140418
com.example.allinone:style/Widget.Material3.Slider.Legacy = 0x7f140416
com.example.allinone:style/Widget.Material3.Slider.Label = 0x7f140415
com.example.allinone:style/Widget.Material3.SideSheet.Modal.Detached = 0x7f140413
com.example.allinone:style/Widget.Material3.SideSheet.Modal = 0x7f140412
com.example.allinone:style/Widget.Material3.SideSheet.Detached = 0x7f140411
com.example.allinone:style/Widget.Material3.SideSheet = 0x7f140410
com.example.allinone:style/Widget.Material3.SearchView.Toolbar = 0x7f14040f
com.example.allinone:style/Widget.Material3.SearchView.Prefix = 0x7f14040e
com.example.allinone:style/Widget.Material3.SearchView = 0x7f14040d
com.example.allinone:style/Widget.Material3.SearchBar.Outlined = 0x7f14040c
com.example.allinone:style/Widget.Material3.Search.Toolbar.Button.Navigation = 0x7f14040a
com.example.allinone:style/Widget.Material3.PopupMenu.Overflow = 0x7f140408
com.example.allinone:style/Widget.Material3.PopupMenu.ListPopupWindow = 0x7f140407
com.example.allinone:style/Widget.Material3.NavigationView = 0x7f140404
com.example.allinone:style/Widget.Material3.NavigationRailView.Badge = 0x7f140403
com.example.allinone:style/Widget.Material3.NavigationRailView.ActiveIndicator = 0x7f140402
com.example.allinone:style/Widget.Material3.NavigationRailView = 0x7f140401
com.example.allinone:style/Widget.Material3.MaterialTimePicker.ImageButton = 0x7f140400
com.example.allinone:style/Widget.Material3.MaterialTimePicker.Display.Divider = 0x7f1403fc
com.example.allinone:style/Widget.Material3.MaterialTimePicker.Display = 0x7f1403fb
com.example.allinone:style/Widget.Material3.MaterialTimePicker.Clock = 0x7f1403fa
com.example.allinone:style/Widget.Material3.MaterialTimePicker = 0x7f1403f8
com.example.allinone:style/Widget.Material3.MaterialCalendar.YearNavigationButton = 0x7f1403f5
com.example.allinone:style/Widget.Material3.MaterialCalendar.MonthNavigationButton = 0x7f1403f0
com.example.allinone:style/Widget.Material3.MaterialCalendar.Item = 0x7f1403ef
com.example.allinone:style/Widget.Material3.MaterialCalendar.HeaderTitle = 0x7f1403ed
com.example.allinone:style/Widget.Material3.MaterialCalendar.HeaderDivider = 0x7f1403e8
com.example.allinone:style/Widget.Material3.MaterialCalendar.Fullscreen = 0x7f1403e6
com.example.allinone:style/Widget.Material3.MaterialCalendar.DayTextView = 0x7f1403e5
com.example.allinone:style/Widget.Material3.MaterialCalendar.DayOfWeekLabel = 0x7f1403e4
com.example.allinone:style/Widget.Material3.MaterialCalendar.Day = 0x7f1403e0
com.example.allinone:style/Widget.Material3.MaterialButtonToggleGroup = 0x7f1403de
com.example.allinone:style/Widget.Material3.LinearProgressIndicator = 0x7f1403dc
com.example.allinone:style/Widget.Material3.Light.ActionBar.Solid = 0x7f1403db
com.example.allinone:style/Widget.Material3.FloatingActionButton.Tertiary = 0x7f1403da
com.example.allinone:style/Widget.Material3.FloatingActionButton.Surface = 0x7f1403d9
com.example.allinone:style/Widget.Material3.FloatingActionButton.Secondary = 0x7f1403d4
com.example.allinone:style/Widget.Material3.FloatingActionButton.Primary = 0x7f1403d3
com.example.allinone:styleable/NavigationBarView = 0x7f15007a
com.example.allinone:style/Widget.Material3.FloatingActionButton.Large.Surface = 0x7f1403d1
com.example.allinone:style/Widget.Material3.FloatingActionButton.Large.Secondary = 0x7f1403d0
com.example.allinone:style/Widget.Material3.FloatingActionButton.Large.Primary = 0x7f1403cf
com.example.allinone:style/Widget.MaterialComponents.Button.UnelevatedButton.Icon = 0x7f14044e
com.example.allinone:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary = 0x7f1403ca
com.example.allinone:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary = 0x7f1403c8
com.example.allinone:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary = 0x7f1403c7
com.example.allinone:style/Widget.Material3.CollapsingToolbar.Large = 0x7f1403c0
com.example.allinone:style/Widget.Material3.CollapsingToolbar = 0x7f1403bf
com.example.allinone:style/Widget.Material3.CircularProgressIndicator.Small = 0x7f1403be
com.example.allinone:style/Widget.Material3.CircularProgressIndicator.Legacy.Medium = 0x7f1403bb
com.example.allinone:style/Widget.Material3.CircularProgressIndicator.Legacy = 0x7f1403b9
com.example.allinone:style/Widget.Material3.CircularProgressIndicator.ExtraSmall = 0x7f1403b8
com.example.allinone:style/Widget.Material3.CircularProgressIndicator = 0x7f1403b7
com.example.allinone:style/Widget.Material3.Chip.Suggestion.Elevated = 0x7f1403b5
com.example.allinone:style/Widget.Material3.Chip.Suggestion = 0x7f1403b4
com.example.allinone:style/Widget.Material3.Chip.Input = 0x7f1403b0
com.example.allinone:style/Widget.Material3.CardView.Outlined = 0x7f1403aa
com.example.allinone:style/Widget.Material3.CardView.Filled = 0x7f1403a9
com.example.allinone:style/Widget.Material3.Button.TonalButton = 0x7f1403a5
com.example.allinone:style/Widget.Material3.Button.TextButton.Icon = 0x7f1403a3
com.example.allinone:style/Widget.Material3.Button.TextButton.Dialog.Icon = 0x7f1403a2
com.example.allinone:style/Widget.Material3.Button.TextButton.Dialog.Flush = 0x7f1403a1
com.example.allinone:style/Widget.Material3.Button.TextButton.Dialog = 0x7f1403a0
com.example.allinone:style/Widget.Material3.Button.TextButton = 0x7f14039f
com.example.allinone:style/Widget.Material3.Button.OutlinedButton.Icon = 0x7f14039e
com.example.allinone:style/Widget.Material3.Button.IconButton.Outlined = 0x7f14039c
com.example.allinone:style/Widget.Material3.Button.IconButton.Filled.Tonal = 0x7f14039b
com.example.allinone:style/Widget.Material3.Button.IconButton = 0x7f140399
com.example.allinone:style/Widget.Material3.Button.Icon = 0x7f140398
com.example.allinone:style/Widget.Material3.Button.ElevatedButton.Icon = 0x7f140397
com.example.allinone:style/Widget.Material3.Button = 0x7f140395
com.example.allinone:style/Widget.Material3.BottomSheet.Modal = 0x7f140394
com.example.allinone:style/Widget.Material3.BottomSheet.DragHandle = 0x7f140393
com.example.allinone:style/Widget.Material3.BottomNavigation.Badge = 0x7f14038f
com.example.allinone:style/Widget.Material3.BottomAppBar.Button.Navigation = 0x7f14038d
com.example.allinone:style/Widget.Material3.Badge.AdjustToBounds = 0x7f14038b
com.example.allinone:style/Widget.Material3.Badge = 0x7f14038a
com.example.allinone:style/Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f140389
com.example.allinone:style/Widget.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f140387
com.example.allinone:style/Widget.Material3.ActionMode = 0x7f140384
com.example.allinone:style/Widget.Design.TabLayout = 0x7f140380
com.example.allinone:style/Widget.Design.Snackbar = 0x7f14037f
com.example.allinone:style/Widget.Design.NavigationView = 0x7f14037d
com.example.allinone:style/Widget.Design.CollapsingToolbar = 0x7f14037b
com.example.allinone:style/Widget.Design.BottomSheet.Modal = 0x7f14037a
com.example.allinone:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f140373
com.example.allinone:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f140370
com.example.allinone:style/Widget.AppCompat.Spinner.DropDown = 0x7f14036f
com.example.allinone:style/Widget.AppCompat.SeekBar.Discrete = 0x7f14036d
com.example.allinone:style/Widget.AppCompat.SeekBar = 0x7f14036c
com.example.allinone:style/Widget.AppCompat.SearchView.ActionBar = 0x7f14036b
com.example.allinone:style/Widget.AppCompat.SearchView = 0x7f14036a
com.example.allinone:style/Widget.AppCompat.RatingBar.Small = 0x7f140369
com.example.allinone:style/Widget.AppCompat.RatingBar.Indicator = 0x7f140368
com.example.allinone:style/Widget.AppCompat.PopupWindow = 0x7f140364
com.example.allinone:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f140363
com.example.allinone:style/Widget.AppCompat.PopupMenu = 0x7f140362
com.example.allinone:style/Widget.AppCompat.ListView.DropDown = 0x7f140360
com.example.allinone:style/Widget.AppCompat.ListView = 0x7f14035f
com.example.allinone:style/Widget.AppCompat.ListPopupWindow = 0x7f14035e
com.example.allinone:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f14035c
com.example.allinone:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f140358
com.example.allinone:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f140356
com.example.allinone:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f140355
com.example.allinone:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f140353
com.example.allinone:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f140352
com.example.allinone:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f14034e
com.example.allinone:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f14034d
com.example.allinone:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f14034b
com.example.allinone:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f14034a
com.example.allinone:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f140349
com.example.allinone:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f140344
com.example.allinone:style/Widget.AppCompat.DrawerArrowToggle = 0x7f140343
com.example.allinone:styleable/AppCompatEmojiHelper = 0x7f150010
com.example.allinone:style/Widget.AppCompat.CompoundButton.Switch = 0x7f140342
com.example.allinone:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f140341
com.example.allinone:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f140340
com.example.allinone:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f14033f
com.example.allinone:style/Widget.AppCompat.ButtonBar = 0x7f14033e
com.example.allinone:style/Widget.AppCompat.Button.Small = 0x7f14033d
com.example.allinone:style/Widget.AppCompat.Button.Colored = 0x7f14033c
com.example.allinone:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f14033a
com.example.allinone:style/Widget.AppCompat.Button.Borderless = 0x7f140339
com.example.allinone:style/Widget.AppCompat.Button = 0x7f140338
com.example.allinone:style/Widget.AppCompat.AutoCompleteTextView = 0x7f140337
com.example.allinone:style/Widget.AppCompat.ActivityChooserView = 0x7f140336
com.example.allinone:style/Widget.AppCompat.ActionMode = 0x7f140335
com.example.allinone:style/Widget.AppCompat.ActionButton = 0x7f140332
com.example.allinone:styleable/PlayerControlView = 0x7f150080
com.example.allinone:style/Widget.AppCompat.ActionBar.Solid = 0x7f14032e
com.example.allinone:style/Widget.AppCompat.ActionBar = 0x7f14032d
com.example.allinone:style/Widget.App.NavigationView = 0x7f14032b
com.example.allinone:style/Widget.App.EditText = 0x7f14032a
com.example.allinone:style/Widget.App.BottomNavigationView = 0x7f140326
com.example.allinone:style/ThemeOverlay.MaterialComponents.Toolbar.Primary = 0x7f140324
com.example.allinone:style/ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary = 0x7f140323
com.example.allinone:style/ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f140322
com.example.allinone:style/ThemeOverlay.MaterialComponents.TimePicker.Display = 0x7f140321
com.example.allinone:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f14031f
com.example.allinone:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f14031d
com.example.allinone:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox = 0x7f14031c
com.example.allinone:style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f14031a
com.example.allinone:style/ThemeOverlay.MaterialComponents.MaterialCalendar = 0x7f140319
com.example.allinone:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered = 0x7f140313
com.example.allinone:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f140312
com.example.allinone:style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f140311
com.example.allinone:style/ThemeOverlay.MaterialComponents.Light = 0x7f140310
com.example.allinone:style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f14030f
com.example.allinone:style/ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f14030e
com.example.allinone:style/ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog = 0x7f14030c
com.example.allinone:style/ThemeOverlay.MaterialComponents.Dark = 0x7f14030a
com.example.allinone:style/ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f140309
com.example.allinone:style/ThemeOverlay.MaterialComponents.BottomAppBar.Surface = 0x7f140308
com.example.allinone:style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary = 0x7f140307
com.example.allinone:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f140305
com.example.allinone:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f140304
com.example.allinone:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f140303
com.example.allinone:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView = 0x7f140302
com.example.allinone:style/ThemeOverlay.MaterialComponents.ActionBar.Surface = 0x7f140301
com.example.allinone:style/ThemeOverlay.MaterialComponents = 0x7f1402fe
com.example.allinone:style/ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon = 0x7f1402fd
com.example.allinone:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox = 0x7f1402fa
com.example.allinone:style/ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense = 0x7f1402f9
com.example.allinone:style/ThemeOverlay.Material3.TextInputEditText = 0x7f1402f7
com.example.allinone:style/ThemeOverlay.Material3.TabLayout = 0x7f1402f6
com.example.allinone:style/ThemeOverlay.Material3.Snackbar = 0x7f1402f5
com.example.allinone:style/ThemeOverlay.Material3.Search = 0x7f1402f3
com.example.allinone:style/ThemeOverlay.Material3.PersonalizedColors = 0x7f1402f2
com.example.allinone:style/ThemeOverlay.Material3.NavigationView = 0x7f1402f1
com.example.allinone:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text = 0x7f140316
com.example.allinone:style/ThemeOverlay.Material3.NavigationRailView = 0x7f1402f0
com.example.allinone:style/ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1402ef
com.example.allinone:style/ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1402ed
com.example.allinone:style/ThemeOverlay.Material3.MaterialAlertDialog.Centered = 0x7f1402ea
com.example.allinone:styleable/FlowLayout = 0x7f15003b
com.example.allinone:style/ThemeOverlay.Material3.MaterialAlertDialog = 0x7f1402e9
com.example.allinone:style/ThemeOverlay.Material3.Light.Dialog.Alert.Framework = 0x7f1402e8
com.example.allinone:style/ThemeOverlay.Material3.HarmonizedColors.Empty = 0x7f1402e6
com.example.allinone:style/ThemeOverlay.Material3.FloatingActionButton.Tertiary = 0x7f1402e4
com.example.allinone:style/ThemeOverlay.Material3.FloatingActionButton.Secondary = 0x7f1402e2
com.example.allinone:styleable/MotionHelper = 0x7f15006d
com.example.allinone:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1402de
com.example.allinone:style/ThemeOverlay.Material3.DynamicColors.Dark = 0x7f1402da
com.example.allinone:style/ThemeOverlay.Material3.Dialog.Alert = 0x7f1402d8
com.example.allinone:style/ThemeOverlay.Material3.DayNight.SideSheetDialog = 0x7f1402d6
com.example.allinone:style/ThemeOverlay.Material3.DayNight.BottomSheetDialog = 0x7f1402d5
com.example.allinone:style/ThemeOverlay.Material3.Chip = 0x7f1402d1
com.example.allinone:style/ThemeOverlay.Material3.Button.TextButton = 0x7f1402ce
com.example.allinone:style/ThemeOverlay.Material3.Button.IconButton.Filled.Tonal = 0x7f1402cd
com.example.allinone:style/ThemeOverlay.Material3.Button.IconButton = 0x7f1402cb
com.example.allinone:style/ThemeOverlay.Material3.Button.ElevatedButton = 0x7f1402ca
com.example.allinone:style/ThemeOverlay.Material3.BottomSheetDialog = 0x7f1402c8
com.example.allinone:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1402c4
com.example.allinone:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f1402c2
com.example.allinone:style/ThemeOverlay.Material3.AutoCompleteTextView = 0x7f1402c0
com.example.allinone:style/ThemeOverlay.Material3 = 0x7f1402be
com.example.allinone:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f1402bb
com.example.allinone:style/ThemeOverlay.AppCompat.Dialog = 0x7f1402ba
com.example.allinone:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f1402b9
com.example.allinone:style/ThemeOverlay.AppCompat.DayNight = 0x7f1402b8
com.example.allinone:style/ThemeOverlay.AppCompat.ActionBar = 0x7f1402b5
com.example.allinone:style/Theme.SplashScreen.Common = 0x7f1402b1
com.example.allinone:style/Theme.SplashScreen = 0x7f1402b0
com.example.allinone:style/Theme.MaterialComponents.NoActionBar.Bridge = 0x7f1402ae
com.example.allinone:style/Theme.MaterialComponents.Light.NoActionBar.Bridge = 0x7f1402ac
com.example.allinone:style/Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge = 0x7f1402a7
com.example.allinone:style/Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f1402a5
com.example.allinone:style/Theme.MaterialComponents.Light.Dialog.Alert.Bridge = 0x7f1402a4
com.example.allinone:style/Theme.MaterialComponents.Light.Dialog.Alert = 0x7f1402a3
com.example.allinone:style/Theme.MaterialComponents.Light.Dialog = 0x7f1402a2
com.example.allinone:style/Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f1402a1
com.example.allinone:style/Theme.MaterialComponents.Light.Bridge = 0x7f14029f
com.example.allinone:style/Theme.MaterialComponents.Dialog.MinWidth.Bridge = 0x7f14029b
com.example.allinone:style/Theme.MaterialComponents.Dialog.MinWidth = 0x7f14029a
com.example.allinone:style/Theme.MaterialComponents.Dialog.FixedSize.Bridge = 0x7f140299
com.example.allinone:style/Theme.MaterialComponents.Dialog.Bridge = 0x7f140297
com.example.allinone:style/Theme.MaterialComponents.Dialog.Alert.Bridge = 0x7f140296
com.example.allinone:style/Theme.MaterialComponents.Dialog.Alert = 0x7f140295
com.example.allinone:style/Theme.MaterialComponents.Dialog = 0x7f140294
com.example.allinone:style/Theme.MaterialComponents.DayNight.NoActionBar.Bridge = 0x7f140293
com.example.allinone:style/Theme.MaterialComponents.DayNight.NoActionBar = 0x7f140292
com.example.allinone:style/Theme.MaterialComponents.DayNight.DialogWhenLarge = 0x7f140291
com.example.allinone:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge = 0x7f140290
com.example.allinone:styleable/ImageFilterView = 0x7f150045
com.example.allinone:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth = 0x7f14028f
com.example.allinone:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge = 0x7f14028e
com.example.allinone:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize = 0x7f14028d
com.example.allinone:style/Theme.MaterialComponents.DayNight.Dialog.Bridge = 0x7f14028c
com.example.allinone:style/Theme.MaterialComponents.DayNight.Dialog.Alert = 0x7f14028a
com.example.allinone:style/Widget.Material3.FloatingActionButton.Small.Primary = 0x7f1403d5
com.example.allinone:style/Theme.MaterialComponents.DayNight.Dialog = 0x7f140289
com.example.allinone:style/Theme.MaterialComponents.DayNight.DarkActionBar.Bridge = 0x7f140288
com.example.allinone:style/Theme.MaterialComponents.DayNight.DarkActionBar = 0x7f140287
com.example.allinone:style/Theme.MaterialComponents.DayNight.Bridge = 0x7f140286
com.example.allinone:style/Theme.MaterialComponents.DayNight.BottomSheetDialog = 0x7f140285
com.example.allinone:style/Theme.MaterialComponents.DayNight = 0x7f140284
com.example.allinone:style/Theme.MaterialComponents.BottomSheetDialog = 0x7f140281
com.example.allinone:style/Theme.MaterialComponents = 0x7f140280
com.example.allinone:style/Theme.Material3.Light.DialogWhenLarge = 0x7f14027d
com.example.allinone:style/Theme.Material3.Light.Dialog.MinWidth = 0x7f14027c
com.example.allinone:style/Theme.Material3.Light.Dialog.Alert = 0x7f14027b
com.example.allinone:style/Theme.Material3.DynamicColors.Light.NoActionBar = 0x7f140277
com.example.allinone:style/Theme.Material3.DynamicColors.DayNight = 0x7f140274
com.example.allinone:style/Theme.Material3.DynamicColors.Dark = 0x7f140272
com.example.allinone:style/Theme.Material3.DayNight.SideSheetDialog = 0x7f140271
com.example.allinone:style/Theme.Material3.DayNight.DialogWhenLarge = 0x7f14026f
com.example.allinone:style/Theme.Material3.DayNight.Dialog.Alert = 0x7f14026d
com.example.allinone:style/Theme.Material3.DayNight.Dialog = 0x7f14026c
com.example.allinone:style/Theme.Material3.DayNight.BottomSheetDialog = 0x7f14026b
com.example.allinone:style/Theme.Material3.DayNight = 0x7f14026a
com.example.allinone:style/ThemeOverlay.Material3.Button = 0x7f1402c9
com.example.allinone:style/Theme.Material3.Dark.SideSheetDialog = 0x7f140269
com.example.allinone:style/Theme.Material3.Dark.NoActionBar = 0x7f140268
com.example.allinone:style/Theme.Material3.Dark.Dialog.MinWidth = 0x7f140266
com.example.allinone:style/Theme.Material3.Dark.BottomSheetDialog = 0x7f140263
com.example.allinone:style/Theme.Material3.Dark = 0x7f140262
com.example.allinone:style/Theme.Design.Light.NoActionBar = 0x7f14025f
com.example.allinone:style/Theme.Design.Light.BottomSheetDialog = 0x7f14025e
com.example.allinone:style/Theme.Design = 0x7f14025b
com.example.allinone:style/Theme.AppCompat.Light.NoActionBar = 0x7f140259
com.example.allinone:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f140257
com.example.allinone:style/Theme.AppCompat.Light.DarkActionBar = 0x7f140254
com.example.allinone:style/Theme.AppCompat.DialogWhenLarge = 0x7f140251
com.example.allinone:style/Theme.AppCompat.Dialog.MinWidth = 0x7f140250
com.example.allinone:style/Theme.AppCompat.Dialog = 0x7f14024e
com.example.allinone:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f14024d
com.example.allinone:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f14024c
com.example.allinone:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f14024a
com.example.allinone:style/Theme.AppCompat.DayNight.Dialog = 0x7f140249
com.example.allinone:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f140248
com.example.allinone:style/Theme.AppCompat = 0x7f140245
com.example.allinone:style/Theme.AllinOne.Starting = 0x7f140244
com.example.allinone:style/Theme.AllinOne.NoActionBar = 0x7f140242
com.example.allinone:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f140240
com.example.allinone:style/TextAppearance.MaterialComponents.Tooltip = 0x7f14023d
com.example.allinone:style/TextAppearance.MaterialComponents.TimePicker.Title = 0x7f14023c
com.example.allinone:style/TextAppearance.MaterialComponents.Subtitle2 = 0x7f14023b
com.example.allinone:style/Widget.MaterialComponents.BottomSheet = 0x7f140441
com.example.allinone:style/TextAppearance.MaterialComponents.Subtitle1 = 0x7f14023a
com.example.allinone:style/TextAppearance.MaterialComponents.Headline6 = 0x7f140238
com.example.allinone:style/TextAppearance.MaterialComponents.Headline5 = 0x7f140237
com.example.allinone:style/TextAppearance.MaterialComponents.Headline4 = 0x7f140236
com.example.allinone:style/TextAppearance.MaterialComponents.Headline3 = 0x7f140235
com.example.allinone:style/TextAppearance.MaterialComponents.Headline2 = 0x7f140234
com.example.allinone:style/TextAppearance.MaterialComponents.Headline1 = 0x7f140233
com.example.allinone:style/TextAppearance.MaterialComponents.Chip = 0x7f140232
com.example.allinone:style/TextAppearance.MaterialComponents.Button = 0x7f140230
com.example.allinone:style/TextAppearance.MaterialComponents.Body2 = 0x7f14022f
com.example.allinone:style/TextAppearance.MaterialComponents.Body1 = 0x7f14022e
com.example.allinone:style/TextAppearance.Material3.TitleSmall = 0x7f14022c
com.example.allinone:style/TextAppearance.Material3.TitleMedium = 0x7f14022b
com.example.allinone:style/TextAppearance.Material3.TitleLarge = 0x7f14022a
com.example.allinone:style/TextAppearance.Material3.SearchView.Prefix = 0x7f140229
com.example.allinone:style/TextAppearance.Material3.LabelSmall = 0x7f140225
com.example.allinone:style/TextAppearance.Material3.LabelMedium = 0x7f140224
com.example.allinone:style/TextAppearance.Material3.HeadlineSmall = 0x7f140222
com.example.allinone:style/TextAppearance.Material3.HeadlineMedium = 0x7f140221
com.example.allinone:style/TextAppearance.Material3.HeadlineLarge = 0x7f140220
com.example.allinone:style/TextAppearance.Material3.DisplayMedium = 0x7f14021e
com.example.allinone:style/TextAppearance.Material3.BodyMedium = 0x7f14021b
com.example.allinone:style/TextAppearance.Material3.BodyLarge = 0x7f14021a
com.example.allinone:style/Widget.Material3.Chip.Input.Icon.Elevated = 0x7f1403b3
com.example.allinone:style/TextAppearance.Material3.ActionBar.Title = 0x7f140219
com.example.allinone:style/TextAppearance.M3.Sys.Typescale.TitleSmall = 0x7f140217
com.example.allinone:style/TextAppearance.M3.Sys.Typescale.TitleMedium = 0x7f140216
com.example.allinone:style/TextAppearance.M3.Sys.Typescale.TitleLarge = 0x7f140215
com.example.allinone:style/TextAppearance.M3.Sys.Typescale.LabelSmall = 0x7f140214
com.example.allinone:style/TextAppearance.M3.Sys.Typescale.LabelMedium = 0x7f140213
com.example.allinone:style/TextAppearance.M3.Sys.Typescale.HeadlineSmall = 0x7f140211
com.example.allinone:style/TextAppearance.M3.Sys.Typescale.HeadlineMedium = 0x7f140210
com.example.allinone:style/TextAppearance.M3.Sys.Typescale.BodySmall = 0x7f14020b
com.example.allinone:style/TextAppearance.M3.Sys.Typescale.BodyMedium = 0x7f14020a
com.example.allinone:style/Widget.MaterialComponents.TimePicker.Display = 0x7f1404a0
com.example.allinone:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f14034f
com.example.allinone:style/TextAppearance.M3.Sys.Typescale.BodyLarge = 0x7f140209
com.example.allinone:style/TextAppearance.Design.Tab = 0x7f140208
com.example.allinone:style/TextAppearance.Design.Suffix = 0x7f140207
com.example.allinone:style/TextAppearance.Design.Snackbar.Message = 0x7f140206
com.example.allinone:style/TextAppearance.Design.Placeholder = 0x7f140204
com.example.allinone:style/TextAppearance.Design.Hint = 0x7f140203
com.example.allinone:style/TextAppearance.Design.Error = 0x7f140201
com.example.allinone:style/TextAppearance.Design.Counter.Overflow = 0x7f140200
com.example.allinone:style/TextAppearance.Design.Counter = 0x7f1401ff
com.example.allinone:style/TextAppearance.Design.CollapsingToolbar.Expanded = 0x7f1401fe
com.example.allinone:style/TextAppearance.Compat.Notification.Title = 0x7f1401fc
com.example.allinone:style/ThemeOverlay.Material3.Dark = 0x7f1402d3
com.example.allinone:style/TextAppearance.Compat.Notification.Time = 0x7f1401fa
com.example.allinone:style/Widget.Material3.TextInputEditText.FilledBox.Dense = 0x7f14041f
com.example.allinone:style/TextAppearance.Compat.Notification.Line2 = 0x7f1401f7
com.example.allinone:style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f140491
com.example.allinone:style/Widget.App.Button.TextButton.Dialog.Delete = 0x7f140329
com.example.allinone:style/ThemeOverlay.Material3.MaterialTimePicker = 0x7f1402ee
com.example.allinone:style/TextAppearance.Compat.Notification.Info.Media = 0x7f1401f6
com.example.allinone:style/TextAppearance.Compat.Notification = 0x7f1401f4
com.example.allinone:style/Widget.Material3.ChipGroup = 0x7f1403b6
com.example.allinone:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f1401f3
com.example.allinone:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f1401ef
com.example.allinone:style/TextAppearance.AppCompat.Widget.Button = 0x7f1401ea
com.example.allinone:styleable/MaterialRadioButton = 0x7f150060
com.example.allinone:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f1401e5
com.example.allinone:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f1401e3
com.example.allinone:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f1401e1
com.example.allinone:macro/m3_comp_dialog_container_shape = 0x7f0e0023
com.example.allinone:style/TextAppearance.AppCompat.Tooltip = 0x7f1401e0
com.example.allinone:string/task_deleted = 0x7f13022b
com.example.allinone:style/TextAppearance.AppCompat.Subhead = 0x7f1401dc
com.example.allinone:style/TextAppearance.AppCompat.Small = 0x7f1401da
com.example.allinone:anim/abc_slide_in_bottom = 0x7f010006
com.example.allinone:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f1401d9
com.example.allinone:style/TextAppearance.AppCompat.Headline = 0x7f1401cd
com.example.allinone:string/m3c_date_input_label = 0x7f13012e
com.example.allinone:style/TextAppearance.AppCompat.Display2 = 0x7f1401ca
com.example.allinone:color/material_dynamic_neutral70 = 0x7f06026b
com.example.allinone:style/TextAppearance.AppCompat.Display1 = 0x7f1401c9
com.example.allinone:style/TextAppearance.AppCompat.Body2 = 0x7f1401c6
com.example.allinone:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year = 0x7f1401c2
com.example.allinone:style/circleImageView = 0x7f1404ad
com.example.allinone:style/ShapeAppearanceOverlay.MaterialAlertDialog.Material3 = 0x7f1401bb
com.example.allinone:style/Widget.Material3.Chip.Filter = 0x7f1403ae
com.example.allinone:style/ShapeAppearanceOverlay.Material3.SearchBar = 0x7f1401b9
com.example.allinone:style/ShapeAppearanceOverlay.Material3.NavigationView.Item = 0x7f1401b8
com.example.allinone:macro/m3_comp_primary_navigation_tab_with_icon_inactive_icon_color = 0x7f0e00d0
com.example.allinone:style/ShapeAppearanceOverlay.Material3.Corner.Right = 0x7f1401b5
com.example.allinone:drawable/exo_controls_pause = 0x7f0800b2
com.example.allinone:style/ShapeAppearance.MaterialComponents.SmallComponent = 0x7f1401af
com.example.allinone:attr/shortcutMatchRequired = 0x7f040405
com.example.allinone:style/ShapeAppearance.Material3.MediumComponent = 0x7f1401a7
com.example.allinone:style/ShapeAppearance.Material3.Corner.None = 0x7f1401a4
com.example.allinone:style/ShapeAppearance.Material3.Corner.Large = 0x7f1401a2
com.example.allinone:style/ShapeAppearance.Material3.Corner.Full = 0x7f1401a1
com.example.allinone:style/ShapeAppearance.Material3.Corner.ExtraSmall = 0x7f1401a0
com.example.allinone:styleable/FragmentContainerView = 0x7f150040
com.example.allinone:attr/itemShapeAppearanceOverlay = 0x7f04027f
com.example.allinone:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall = 0x7f140199
com.example.allinone:style/ShapeAppearance.M3.Comp.Switch.Handle.Shape = 0x7f140194
com.example.allinone:color/notification_icon_bg_color = 0x7f060337
com.example.allinone:style/ShapeAppearance.M3.Comp.Sheet.Side.Docked.Container.Shape = 0x7f140193
com.example.allinone:style/ShapeAppearance.M3.Comp.SearchView.FullScreen.Container.Shape = 0x7f140192
com.example.allinone:style/ShapeAppearance.M3.Comp.SearchBar.Container.Shape = 0x7f140191
com.example.allinone:style/ShapeAppearance.M3.Comp.NavigationDrawer.ActiveIndicator.Shape = 0x7f14018d
com.example.allinone:dimen/abc_cascading_menus_min_smallest_width = 0x7f070016
com.example.allinone:drawable/exo_notification_pause = 0x7f0800e0
com.example.allinone:style/ShapeAppearance.M3.Comp.Badge.Shape = 0x7f140187
com.example.allinone:style/Widget.Material3.MaterialCalendar.Day.Invalid = 0x7f1403e1
com.example.allinone:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f140183
com.example.allinone:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f140181
com.example.allinone:macro/m3_comp_search_bar_pressed_supporting_text_color = 0x7f0e00ed
com.example.allinone:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f14017e
com.example.allinone:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f14017b
com.example.allinone:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f14017a
com.example.allinone:attr/badgeVerticalPadding = 0x7f040065
com.example.allinone:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f140178
com.example.allinone:drawable/circle_background_red = 0x7f08008e
com.example.allinone:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f140177
com.example.allinone:style/Platform.V25.AppCompat.Light = 0x7f140173
com.example.allinone:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f14016f
com.example.allinone:attr/badgeWithTextRadius = 0x7f040069
com.example.allinone:dimen/mtrl_progress_circular_inset = 0x7f0702f9
com.example.allinone:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f14016e
com.example.allinone:id/dialogTitle = 0x7f0a0102
com.example.allinone:style/Platform.ThemeOverlay.AppCompat = 0x7f14016d
com.example.allinone:attr/textAppearanceBodySmall = 0x7f04048e
com.example.allinone:style/Platform.MaterialComponents.Light.Dialog = 0x7f14016c
com.example.allinone:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner = 0x7f140160
com.example.allinone:string/mtrl_timepicker_cancel = 0x7f1301b2
com.example.allinone:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar = 0x7f14015f
com.example.allinone:drawable/$mtrl_checkbox_button_icon_unchecked_checked__1 = 0x7f080019
com.example.allinone:style/MaterialAlertDialog.MaterialComponents = 0x7f14015d
com.example.allinone:style/MaterialAlertDialog.Material3.Title.Text.CenterStacked = 0x7f14015c
com.example.allinone:dimen/material_time_picker_minimum_screen_width = 0x7f07026a
com.example.allinone:style/MaterialAlertDialog.Material3.Title.Panel.CenterStacked = 0x7f14015a
com.example.allinone:style/MaterialAlertDialog.Material3.Title.Panel = 0x7f140159
com.example.allinone:style/MaterialAlertDialog.Material3.Title.Icon.CenterStacked = 0x7f140158
com.example.allinone:style/MaterialAlertDialog.Material3.Title.Icon = 0x7f140157
com.example.allinone:drawable/exo_ic_fullscreen_enter = 0x7f0800c3
com.example.allinone:style/MaterialAlertDialog.App.Title.Text = 0x7f140152
com.example.allinone:dimen/m3_chip_hovered_translation_z = 0x7f07011d
com.example.allinone:style/MaterialAlertDialog.App.Body.Text = 0x7f140151
com.example.allinone:style/Widget.AppCompat.Spinner.Underlined = 0x7f140371
com.example.allinone:id/phoneNumber = 0x7f0a02b1
com.example.allinone:style/FloatingDialogWindowTheme = 0x7f140150
com.example.allinone:style/FilterDropdownStyle = 0x7f14014e
com.example.allinone:style/ExoStyledControls.TimeText.Separator = 0x7f14014c
com.example.allinone:style/ExoStyledControls.Button.Center.FfwdWithAmount = 0x7f140143
com.example.allinone:style/ExoStyledControls.Button.Center = 0x7f140142
com.example.allinone:style/ExoStyledControls.Button.Bottom.PlaybackSpeed = 0x7f14013d
com.example.allinone:style/ExoStyledControls.Button.Bottom.OverflowShow = 0x7f14013c
com.example.allinone:style/ExoStyledControls.Button.Bottom.AudioTrack = 0x7f140138
com.example.allinone:style/ExoMediaButton.VR = 0x7f140134
com.example.allinone:style/ExoMediaButton.Rewind = 0x7f140133
com.example.allinone:color/m3_sys_color_dark_error = 0x7f06019c
com.example.allinone:style/ExoMediaButton.Previous = 0x7f140132
com.example.allinone:style/ExoMediaButton.Play = 0x7f140131
com.example.allinone:attr/grid_spans = 0x7f040233
com.example.allinone:style/ExoMediaButton.FastForward = 0x7f14012e
com.example.allinone:style/DialogWindowTheme = 0x7f14012c
com.example.allinone:layout/notification_template_icon_group = 0x7f0d00bc
com.example.allinone:style/CardView.Light = 0x7f14012b
com.example.allinone:style/BottomNavigationStyle = 0x7f140127
com.example.allinone:color/material_dynamic_tertiary10 = 0x7f060298
com.example.allinone:style/BoldText = 0x7f140126
com.example.allinone:style/Widget.Material3.MaterialTimePicker.Button = 0x7f1403f9
com.example.allinone:style/Base.v21.Theme.SplashScreen = 0x7f140122
com.example.allinone:macro/m3_comp_outlined_card_container_shape = 0x7f0e00a9
com.example.allinone:style/ExoStyledControls.Button.Bottom.Settings = 0x7f14013f
com.example.allinone:style/Base.Widget.MaterialComponents.TextInputEditText = 0x7f14011f
com.example.allinone:style/Base.Widget.MaterialComponents.Snackbar = 0x7f14011e
com.example.allinone:style/Base.Widget.MaterialComponents.Slider = 0x7f14011d
com.example.allinone:style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f14011a
com.example.allinone:attr/clockNumberTextColor = 0x7f0400ee
com.example.allinone:style/Base.Widget.MaterialComponents.PopupMenu = 0x7f140119
com.example.allinone:style/Base.Widget.Material3.TabLayout = 0x7f140111
com.example.allinone:drawable/exo_ic_play_circle_filled = 0x7f0800c6
com.example.allinone:style/Base.Widget.Material3.MaterialCalendar.NavigationButton = 0x7f14010f
com.example.allinone:style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon = 0x7f14010a
com.example.allinone:style/Base.Widget.Material3.CompoundButton.RadioButton = 0x7f140107
com.example.allinone:style/Base.Widget.Material3.CollapsingToolbar = 0x7f140105
com.example.allinone:attr/backgroundColor = 0x7f040052
com.example.allinone:id/leverageLabelText = 0x7f0a01f1
com.example.allinone:style/Base.Widget.Material3.CardView = 0x7f140103
com.example.allinone:style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet = 0x7f1401bc
com.example.allinone:style/Base.Widget.Material3.Snackbar = 0x7f140110
com.example.allinone:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f140182
com.example.allinone:style/Base.Widget.Material3.BottomNavigationView = 0x7f140102
com.example.allinone:style/Base.Widget.Design.TabLayout = 0x7f1400ff
com.example.allinone:string/m3c_date_picker_switch_to_day_selection = 0x7f130138
com.example.allinone:attr/motionEffect_translationY = 0x7f04036e
com.example.allinone:dimen/m3_sys_motion_easing_legacy_control_y1 = 0x7f070229
com.example.allinone:style/Base.Widget.AppCompat.TextView = 0x7f1400fb
com.example.allinone:color/m3_sys_color_light_on_error = 0x7f060214
com.example.allinone:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f1400fa
com.example.allinone:dimen/m3_comp_divider_thickness = 0x7f07012c
com.example.allinone:dimen/mtrl_chip_pressed_translation_z = 0x7f0702c6
com.example.allinone:style/Base.Widget.AppCompat.Spinner = 0x7f1400f9
com.example.allinone:dimen/m3_comp_bottom_app_bar_container_elevation = 0x7f070126
com.example.allinone:id/emptyTransactionsText = 0x7f0a012b
com.example.allinone:style/Base.Widget.AppCompat.SeekBar = 0x7f1400f7
com.example.allinone:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f1400f3
com.example.allinone:style/Base.Widget.AppCompat.ProgressBar = 0x7f1400f0
com.example.allinone:dimen/m3_bottomappbar_height = 0x7f0700ee
com.example.allinone:style/Base.Widget.AppCompat.ListView.Menu = 0x7f1400ec
com.example.allinone:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f1400e9
com.example.allinone:style/Base.Widget.AppCompat.ListMenuView = 0x7f1400e8
com.example.allinone:id/search_badge = 0x7f0a02ff
com.example.allinone:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f1400e7
com.example.allinone:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f1400e6
com.example.allinone:attr/contentPaddingBottom = 0x7f040154
com.example.allinone:attr/polarRelativeTo = 0x7f0403b2
com.example.allinone:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f1400e5
com.example.allinone:layout/material_clock_period_toggle = 0x7f0d0087
com.example.allinone:raw/firebase_crashlytics_keep = 0x7f120001
com.example.allinone:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f1400e4
com.example.allinone:id/accessibility_custom_action_31 = 0x7f0a002a
com.example.allinone:string/exo_track_selection_title_text = 0x7f1300ed
com.example.allinone:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f1400e1
com.example.allinone:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f1400e0
com.example.allinone:string/edit_student = 0x7f130099
com.example.allinone:style/Base.Widget.AppCompat.ImageButton = 0x7f1400df
com.example.allinone:id/backups_card = 0x7f0a0080
com.example.allinone:style/Base.Widget.AppCompat.EditText = 0x7f1400de
com.example.allinone:drawable/m3_popupmenu_background_overlay = 0x7f080171
com.example.allinone:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f1400db
com.example.allinone:style/TextAppearance.AppCompat.Inverse = 0x7f1401ce
com.example.allinone:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f1400da
com.example.allinone:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f1400d2
com.example.allinone:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f1400cf
com.example.allinone:id/surface_view = 0x7f0a034f
com.example.allinone:style/ShapeAppearance.M3.Comp.NavigationRail.Container.Shape = 0x7f14018f
com.example.allinone:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f1400cc
com.example.allinone:id/vertical = 0x7f0a03bc
com.example.allinone:string/m3_sys_motion_easing_legacy_accelerate = 0x7f13011e
com.example.allinone:style/Base.Widget.AppCompat.ActionButton = 0x7f1400ca
com.example.allinone:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f1400c8
com.example.allinone:dimen/mtrl_min_touch_target_size = 0x7f0702e7
com.example.allinone:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f1400c2
com.example.allinone:style/Base.V7.Theme.AppCompat.Light = 0x7f1400bf
com.example.allinone:dimen/m3_comp_outlined_autocomplete_menu_container_elevation = 0x7f070172
com.example.allinone:style/Base.V7.Theme.AppCompat.Dialog = 0x7f1400be
com.example.allinone:style/Base.V24.Theme.Material3.Dark.Dialog = 0x7f1400b5
com.example.allinone:style/Base.V23.Theme.AppCompat.Light = 0x7f1400b3
com.example.allinone:style/Base.V23.Theme.AppCompat = 0x7f1400b2
com.example.allinone:dimen/m3_comp_slider_stop_indicator_size = 0x7f0701ad
com.example.allinone:style/Base.V22.Theme.AppCompat = 0x7f1400b0
com.example.allinone:style/Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1400af
com.example.allinone:color/m3_slider_thumb_color = 0x7f060197
com.example.allinone:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f1400ac
com.example.allinone:drawable/ic_transactions = 0x7f080164
com.example.allinone:style/Base.V21.Theme.MaterialComponents.Light = 0x7f1400aa
com.example.allinone:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f1400a7
com.example.allinone:color/design_default_color_primary = 0x7f06006c
com.example.allinone:dimen/mtrl_calendar_day_horizontal_padding = 0x7f07029d
com.example.allinone:style/Base.V21.Theme.AppCompat = 0x7f1400a4
com.example.allinone:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog = 0x7f1400a0
com.example.allinone:style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f14009c
com.example.allinone:layout/abc_list_menu_item_radio = 0x7f0d0011
com.example.allinone:style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f14009a
com.example.allinone:id/accessibility_custom_action_13 = 0x7f0a0016
com.example.allinone:style/Base.V14.Theme.Material3.Light.Dialog = 0x7f140092
com.example.allinone:attr/listLayout = 0x7f0402ef
com.example.allinone:style/Base.V14.Theme.Material3.Light.BottomSheetDialog = 0x7f140091
com.example.allinone:string/error_not_authorized = 0x7f1300ae
com.example.allinone:style/Base.V14.Theme.Material3.Light = 0x7f140090
com.example.allinone:styleable/NavGraphNavigator = 0x7f150075
com.example.allinone:style/Base.V14.Theme.Material3.Dark.SideSheetDialog = 0x7f14008f
com.example.allinone:styleable/MaterialCheckBoxStates = 0x7f15005e
com.example.allinone:string/m3c_date_picker_scroll_to_later_years = 0x7f130136
com.example.allinone:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f140089
com.example.allinone:id/add = 0x7f0a0050
com.example.allinone:id/action_context_bar = 0x7f0a003d
com.example.allinone:style/Base.ThemeOverlay.MaterialComponents.Dialog = 0x7f140087
com.example.allinone:style/Base.ThemeOverlay.Material3.SideSheetDialog = 0x7f140085
com.example.allinone:anim/design_snackbar_out = 0x7f01001b
com.example.allinone:style/Base.ThemeOverlay.Material3.Dialog = 0x7f140084
com.example.allinone:style/Base.ThemeOverlay.Material3.BottomSheetDialog = 0x7f140083
com.example.allinone:style/Base.ThemeOverlay.AppCompat.Light = 0x7f140081
com.example.allinone:dimen/m3_searchview_elevation = 0x7f070208
com.example.allinone:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f140080
com.example.allinone:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f14007e
com.example.allinone:style/Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f1402a6
com.example.allinone:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f14007d
com.example.allinone:style/TextAppearance.Compat.Notification.Time.Media = 0x7f1401fb
com.example.allinone:style/Base.Theme.SplashScreen.Light = 0x7f14007a
com.example.allinone:style/Base.Theme.SplashScreen.DayNight = 0x7f140079
com.example.allinone:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f1400ee
com.example.allinone:style/Base.Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f140077
com.example.allinone:style/Base.Theme.MaterialComponents.Light = 0x7f14006e
com.example.allinone:style/Base.Theme.MaterialComponents.Dialog.MinWidth = 0x7f14006c
com.example.allinone:attr/windowMinWidthMajor = 0x7f04053d
com.example.allinone:style/Base.Theme.MaterialComponents.Dialog.FixedSize = 0x7f14006b
com.example.allinone:style/Base.Theme.MaterialComponents.CompactMenu = 0x7f140067
com.example.allinone:style/Base.Theme.Material3.Light.DialogWhenLarge = 0x7f140063
com.example.allinone:style/Base.Theme.Material3.Light.Dialog.FixedSize = 0x7f140062
com.example.allinone:color/m3_sys_color_dark_surface_container_lowest = 0x7f0601b8
com.example.allinone:style/Base.Theme.Material3.Light.Dialog = 0x7f140061
com.example.allinone:style/Base.Theme.Material3.Light.BottomSheetDialog = 0x7f140060
com.example.allinone:string/exo_download_description = 0x7f1300da
com.example.allinone:style/Base.Theme.Material3.Light = 0x7f14005f
com.example.allinone:style/Base.Theme.Material3.Dark.SideSheetDialog = 0x7f14005e
com.example.allinone:color/m3_simple_item_ripple_color = 0x7f060191
com.example.allinone:macro/m3_comp_fab_primary_container_color = 0x7f0e0036
com.example.allinone:style/Base.Theme.Material3.Dark.DialogWhenLarge = 0x7f14005d
com.example.allinone:style/Base.Theme.Material3.Dark.Dialog = 0x7f14005b
com.example.allinone:id/dataBinding = 0x7f0a00e5
com.example.allinone:attr/played_ad_marker_color = 0x7f0403af
com.example.allinone:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f140057
com.example.allinone:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f140056
com.example.allinone:style/Base.Theme.AppCompat.Light = 0x7f140052
com.example.allinone:style/Base.Theme.AppCompat.CompactMenu = 0x7f14004c
com.example.allinone:string/exo_track_selection_title_audio = 0x7f1300ec
com.example.allinone:style/Base.TextAppearance.MaterialComponents.Headline6 = 0x7f140046
com.example.allinone:style/Base.TextAppearance.MaterialComponents.Button = 0x7f140045
com.example.allinone:style/Base.TextAppearance.MaterialComponents.Badge = 0x7f140044
com.example.allinone:style/ShapeAppearance.M3.Comp.Switch.StateLayer.Shape = 0x7f140195
com.example.allinone:style/Base.TextAppearance.Material3.Search = 0x7f140043
com.example.allinone:attr/windowActionModeOverlay = 0x7f040538
com.example.allinone:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f140042
com.example.allinone:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f140041
com.example.allinone:styleable/LinearLayoutCompat_Layout = 0x7f150051
com.example.allinone:style/Base.V7.Theme.AppCompat = 0x7f1400bd
com.example.allinone:attr/onTouchUp = 0x7f04038e
com.example.allinone:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f140040
com.example.allinone:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f14003a
com.example.allinone:attr/toolbarSurfaceStyle = 0x7f0404fb
com.example.allinone:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f140039
com.example.allinone:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f140032
com.example.allinone:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f140030
com.example.allinone:attr/itemIconTint = 0x7f040277
com.example.allinone:style/Base.TextAppearance.AppCompat.Medium = 0x7f140025
com.example.allinone:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f140023
com.example.allinone:style/Base.TextAppearance.AppCompat.Display4 = 0x7f14001e
com.example.allinone:string/mtrl_switch_thumb_path_checked = 0x7f1301ab
com.example.allinone:style/ExoStyledControls.Button.Bottom = 0x7f140137
com.example.allinone:style/Base.TextAppearance.AppCompat.Display1 = 0x7f14001b
com.example.allinone:style/Base.TextAppearance.AppCompat.Button = 0x7f140019
com.example.allinone:style/Base.TextAppearance.AppCompat = 0x7f140016
com.example.allinone:attr/popUpTo = 0x7f0403b5
com.example.allinone:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f14002c
com.example.allinone:dimen/mtrl_navigation_item_icon_size = 0x7f0702ed
com.example.allinone:id/SHOW_ALL = 0x7f0a0008
com.example.allinone:style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f140014
com.example.allinone:style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f140013
com.example.allinone:attr/sideSheetDialogTheme = 0x7f04041b
com.example.allinone:style/Base.CardView = 0x7f140010
com.example.allinone:style/Widget.Material3.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f1403ea
com.example.allinone:style/Base.Animation.AppCompat.Tooltip = 0x7f14000f
com.example.allinone:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f1400d9
com.example.allinone:style/Base.AlertDialog.AppCompat = 0x7f14000b
com.example.allinone:style/Animation.MaterialComponents.BottomSheetDialog = 0x7f14000a
com.example.allinone:style/Animation.Material3.SideSheetDialog.Right = 0x7f140009
com.example.allinone:color/m3_sys_color_dark_on_secondary = 0x7f0601a6
com.example.allinone:style/Animation.Material3.SideSheetDialog = 0x7f140007
com.example.allinone:style/AlertDialog.AppCompat.Light = 0x7f140001
com.example.allinone:style/AlertDialog.AppCompat = 0x7f140000
com.example.allinone:string/workout_stats = 0x7f13025a
com.example.allinone:anim/abc_slide_in_top = 0x7f010007
com.example.allinone:string/workout_log = 0x7f130256
com.example.allinone:string/workout_deleted = 0x7f130252
com.example.allinone:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
com.example.allinone:string/workout_dashboard = 0x7f130251
com.example.allinone:string/weight = 0x7f13024f
com.example.allinone:string/mtrl_picker_range_header_title = 0x7f13019b
com.example.allinone:string/voice_notes = 0x7f13024b
com.example.allinone:string/voice_note_singular = 0x7f13024a
com.example.allinone:color/black = 0x7f060025
com.example.allinone:string/voice_note_plural = 0x7f130249
com.example.allinone:dimen/mtrl_tooltip_minHeight = 0x7f07032d
com.example.allinone:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f1401d3
com.example.allinone:style/TextAppearance.AppCompat.Large = 0x7f1401cf
com.example.allinone:string/uploading_attachments = 0x7f130247
com.example.allinone:string/type = 0x7f130245
com.example.allinone:string/total_workout_count = 0x7f130242
com.example.allinone:string/tooltip_description = 0x7f13023d
com.example.allinone:string/pause_workout = 0x7f1301d4
com.example.allinone:string/title_wing_tzun_registry = 0x7f13023b
com.example.allinone:string/title_register = 0x7f130238
com.example.allinone:id/studentsRecyclerView = 0x7f0a0349
com.example.allinone:string/title_notes = 0x7f130237
com.example.allinone:string/title_lesson_schedule = 0x7f130236
com.example.allinone:string/title_history = 0x7f130234
com.example.allinone:dimen/m3_comp_filled_card_icon_size = 0x7f07014d
com.example.allinone:macro/m3_comp_switch_unselected_pressed_handle_color = 0x7f0e013b
com.example.allinone:string/title_calendar = 0x7f130233
com.example.allinone:string/task_incomplete = 0x7f13022e
com.example.allinone:string/task_groups = 0x7f13022d
com.example.allinone:string/task_description = 0x7f13022c
com.example.allinone:string/switch_to_grouped_view = 0x7f130224
com.example.allinone:string/storage_permission_title = 0x7f130220
com.example.allinone:string/stop_workout_confirmation = 0x7f13021e
com.example.allinone:string/stop_workout = 0x7f13021d
com.example.allinone:attr/behavior_hideable = 0x7f040079
com.example.allinone:string/stop = 0x7f13021c
com.example.allinone:string/status_unpaid_desc = 0x7f13021b
com.example.allinone:string/status_inactive_desc = 0x7f130218
com.example.allinone:string/status_bar_notification_info_overflow = 0x7f130217
com.example.allinone:style/ThemeOverlay.AppCompat = 0x7f1402b4
com.example.allinone:string/state_on = 0x7f130215
com.example.allinone:string/error_saving_drawing = 0x7f1300b4
com.example.allinone:string/state_empty = 0x7f130213
com.example.allinone:attr/height = 0x7f04023b
com.example.allinone:style/Base.Animation.AppCompat.Dialog = 0x7f14000d
com.example.allinone:dimen/m3_navigation_rail_item_active_indicator_margin_horizontal = 0x7f0701f2
com.example.allinone:string/start_date = 0x7f130211
com.example.allinone:string/share_registration = 0x7f13020e
com.example.allinone:string/selected = 0x7f13020a
com.example.allinone:style/MaterialAlertDialog.Material3.Title.Text = 0x7f14015b
com.example.allinone:string/select_student = 0x7f130208
com.example.allinone:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f140492
com.example.allinone:style/Widget.Material3.Button.ElevatedButton = 0x7f140396
com.example.allinone:string/select_program = 0x7f130207
com.example.allinone:color/material_timepicker_button_background = 0x7f0602f3
com.example.allinone:dimen/m3_comp_search_view_full_screen_header_container_height = 0x7f07019a
com.example.allinone:style/Animation.AppCompat.DropDownUp = 0x7f140003
com.example.allinone:string/select_group = 0x7f130204
com.example.allinone:string/searchview_navigation_content_description = 0x7f130201
com.example.allinone:string/searchview_clear_text_content_description = 0x7f130200
com.example.allinone:string/search_menu_title = 0x7f1301fe
com.example.allinone:string/search_hint = 0x7f1301fd
com.example.allinone:string/save_workout = 0x7f1301fa
com.example.allinone:string/save_changes = 0x7f1301f7
com.example.allinone:string/registration_deleted = 0x7f1301ef
com.example.allinone:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
com.example.allinone:string/recording_saved = 0x7f1301ed
com.example.allinone:string/record = 0x7f1301ea
com.example.allinone:string/range_start = 0x7f1301e8
com.example.allinone:attr/boxCornerRadiusBottomEnd = 0x7f04008d
com.example.allinone:string/range_end = 0x7f1301e7
com.example.allinone:string/pull_to_refresh = 0x7f1301e6
com.example.allinone:string/program_details = 0x7f1301e2
com.example.allinone:string/program_created = 0x7f1301e0
com.example.allinone:id/markPriceLabel = 0x7f0a0209
com.example.allinone:string/profile_image = 0x7f1301df
com.example.allinone:string/please_select_start_date = 0x7f1301dd
com.example.allinone:string/payment_received = 0x7f1301d6
com.example.allinone:anim/m3_motion_fade_exit = 0x7f010024
com.example.allinone:dimen/mtrl_calendar_pre_l_text_clip_padding = 0x7f0702b3
com.example.allinone:string/payment_receipt = 0x7f1301d5
com.example.allinone:attr/cornerFamilyBottomLeft = 0x7f040161
com.example.allinone:string/path_password_strike_through = 0x7f1301d3
com.example.allinone:string/workout_details = 0x7f130253
com.example.allinone:style/Widget.Material3.PopupMenu.ContextMenu = 0x7f140406
com.example.allinone:string/path_password_eye_mask_visible = 0x7f1301d2
com.example.allinone:id/addImageButton = 0x7f0a0053
com.example.allinone:string/path_password_eye_mask_strike_through = 0x7f1301d1
com.example.allinone:string/ok = 0x7f1301ce
com.example.allinone:string/offline_status = 0x7f1301cd
com.example.allinone:id/nav_clear_data = 0x7f0a0256
com.example.allinone:string/offline_mode = 0x7f1301cc
com.example.allinone:color/m3_ref_palette_black = 0x7f0600de
com.example.allinone:string/notes = 0x7f1301c9
com.example.allinone:string/m3_sys_motion_easing_legacy_decelerate = 0x7f13011f
com.example.allinone:string/note_updated = 0x7f1301c8
com.example.allinone:string/not_set = 0x7f1301c5
com.example.allinone:string/not_selected = 0x7f1301c4
com.example.allinone:string/no_workout_history = 0x7f1301c3
com.example.allinone:macro/m3_comp_fab_tertiary_icon_color = 0x7f0e0040
com.example.allinone:layout/design_navigation_item = 0x7f0d0029
com.example.allinone:string/no_tasks_yet = 0x7f1301c2
com.example.allinone:attr/tickColor = 0x7f0404db
com.example.allinone:id/insightsLabel = 0x7f0a01c9
com.example.allinone:string/no_students = 0x7f1301c1
com.example.allinone:attr/liftOnScrollColor = 0x7f0402e4
com.example.allinone:string/no_registrations_prompt = 0x7f1301c0
com.example.allinone:id/btn_delete = 0x7f0a0099
com.example.allinone:string/no_recent_workouts = 0x7f1301be
com.example.allinone:string/new_task = 0x7f1301b9
com.example.allinone:string/navigation_menu = 0x7f1301b7
com.example.allinone:string/nav_app_bar_open_drawer_description = 0x7f1301b6
com.example.allinone:string/mtrl_timepicker_confirm = 0x7f1301b3
com.example.allinone:string/mtrl_switch_thumb_path_unchecked = 0x7f1301af
com.example.allinone:string/clear_text_end_icon_content_description = 0x7f13004e
com.example.allinone:string/mtrl_switch_thumb_path_morphing = 0x7f1301ac
com.example.allinone:string/mtrl_picker_toggle_to_year_selection = 0x7f1301a9
com.example.allinone:string/mtrl_picker_toggle_to_text_input_mode = 0x7f1301a8
com.example.allinone:string/mtrl_picker_toggle_to_day_selection = 0x7f1301a7
com.example.allinone:id/open_search_view_status_bar_spacer = 0x7f0a0298
com.example.allinone:string/mtrl_picker_text_input_year_abbr = 0x7f1301a4
com.example.allinone:string/mtrl_picker_text_input_month_abbr = 0x7f1301a3
com.example.allinone:string/mtrl_picker_text_input_date_range_start_hint = 0x7f1301a1
com.example.allinone:id/tag_accessibility_clickable_spans = 0x7f0a0359
com.example.allinone:style/Base.v27.Theme.SplashScreen.Light = 0x7f140125
com.example.allinone:string/mtrl_picker_text_input_date_range_end_hint = 0x7f1301a0
com.example.allinone:string/mtrl_picker_start_date_description = 0x7f13019e
com.example.allinone:string/permission_denied = 0x7f1301d8
com.example.allinone:string/mtrl_picker_navigate_to_year_description = 0x7f130196
com.example.allinone:string/mtrl_picker_invalid_range = 0x7f130194
com.example.allinone:string/mtrl_picker_end_date_description = 0x7f130190
com.example.allinone:attr/tint = 0x7f0404e5
com.example.allinone:string/mtrl_picker_day_of_week_column_header = 0x7f13018f
com.example.allinone:string/mtrl_picker_date_header_selected = 0x7f13018c
com.example.allinone:id/addLessonButton = 0x7f0a0056
com.example.allinone:string/mtrl_picker_confirm = 0x7f13018b
com.example.allinone:animator/m3_extended_fab_change_size_collapse_motion_spec = 0x7f020010
com.example.allinone:string/mtrl_picker_cancel = 0x7f13018a
com.example.allinone:string/mtrl_picker_announce_current_selection = 0x7f130188
com.example.allinone:style/MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f140163
com.example.allinone:style/Widget.Material3.MaterialCalendar.MonthTextView = 0x7f1403f1
com.example.allinone:id/exo_vr = 0x7f0a017b
com.example.allinone:string/mtrl_picker_a11y_next_month = 0x7f130185
com.example.allinone:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f14003e
com.example.allinone:string/mtrl_exceed_max_badge_number_suffix = 0x7f130184
com.example.allinone:string/mtrl_chip_close_icon_content_description = 0x7f130182
com.example.allinone:string/mtrl_checkbox_state_description_indeterminate = 0x7f130180
com.example.allinone:string/mtrl_checkbox_state_description_checked = 0x7f13017f
com.example.allinone:string/material_timepicker_text_input_mode_description = 0x7f130174
com.example.allinone:string/material_slider_range_end = 0x7f13016b
com.example.allinone:string/material_motion_easing_standard = 0x7f13016a
com.example.allinone:attr/colorSurfaceInverse = 0x7f04013b
com.example.allinone:string/material_motion_easing_linear = 0x7f130169
com.example.allinone:string/material_motion_easing_accelerated = 0x7f130166
com.example.allinone:dimen/m3_comp_extended_fab_primary_hover_container_elevation = 0x7f070135
com.example.allinone:string/material_minute_suffix = 0x7f130165
com.example.allinone:style/ExoStyledControls.Button.Bottom.OverflowHide = 0x7f14013b
com.example.allinone:string/material_minute_selection = 0x7f130164
com.example.allinone:string/material_hour_24h_suffix = 0x7f130161
com.example.allinone:string/mark_completed = 0x7f13015d
com.example.allinone:string/m3c_time_picker_pm = 0x7f130159
com.example.allinone:attr/closeIconVisible = 0x7f0400f5
com.example.allinone:string/m3c_time_picker_minute_suffix = 0x7f130156
com.example.allinone:string/m3c_time_picker_hour_selection = 0x7f130151
com.example.allinone:style/Base.Widget.Material3.Light.ActionBar.Solid = 0x7f14010e
com.example.allinone:string/m3c_time_picker_hour = 0x7f13014f
com.example.allinone:string/workout_history = 0x7f130255
com.example.allinone:string/m3c_snackbar_dismiss = 0x7f13014c
com.example.allinone:id/animateToEnd = 0x7f0a0068
com.example.allinone:string/m3c_date_range_picker_end_headline = 0x7f130143
com.example.allinone:string/m3c_date_range_input_invalid_range_input = 0x7f130140
com.example.allinone:string/material_hour_suffix = 0x7f130163
com.example.allinone:layout/mtrl_calendar_days_of_week = 0x7f0d009b
com.example.allinone:style/Base.Widget.AppCompat.Button = 0x7f1400d0
com.example.allinone:id/asConfigured = 0x7f0a0071
com.example.allinone:string/m3c_date_picker_today_description = 0x7f13013e
com.example.allinone:attr/constraintSetStart = 0x7f040147
com.example.allinone:string/m3c_date_picker_switch_to_year_selection = 0x7f13013c
com.example.allinone:string/m3c_date_picker_switch_to_next_month = 0x7f13013a
com.example.allinone:string/m3c_date_picker_switch_to_calendar_mode = 0x7f130137
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar.Item = 0x7f140475
com.example.allinone:id/cut = 0x7f0a00e3
com.example.allinone:string/m3c_date_picker_no_selection_description = 0x7f130134
com.example.allinone:color/m3_sys_color_dark_on_tertiary_container = 0x7f0601ab
com.example.allinone:string/m3c_date_input_title = 0x7f130130
com.example.allinone:drawable/selected_circle_shape = 0x7f0801b8
com.example.allinone:string/m3c_date_input_invalid_not_allowed = 0x7f13012c
com.example.allinone:string/m3c_date_input_headline = 0x7f130129
com.example.allinone:string/m3c_bottom_sheet_drag_handle_description = 0x7f130126
com.example.allinone:style/TextAppearance.AppCompat.Display3 = 0x7f1401cb
com.example.allinone:string/m3_sys_motion_easing_standard_decelerate = 0x7f130123
com.example.allinone:string/please_select_student = 0x7f1301de
com.example.allinone:dimen/m3_chip_disabled_translation_z = 0x7f07011a
com.example.allinone:string/m3_sys_motion_easing_standard = 0x7f130121
com.example.allinone:dimen/m3_navigation_rail_item_padding_bottom_with_large_font = 0x7f0701f6
com.example.allinone:string/m3_sys_motion_easing_emphasized = 0x7f130119
com.example.allinone:string/m3_ref_typeface_brand_regular = 0x7f130116
com.example.allinone:string/m3_ref_typeface_brand_medium = 0x7f130115
com.example.allinone:dimen/exo_settings_height = 0x7f07009f
com.example.allinone:string/instagram = 0x7f13010f
com.example.allinone:string/indeterminate = 0x7f13010e
com.example.allinone:color/m3_sys_color_dark_surface_dim = 0x7f0601b9
com.example.allinone:string/hide_bottom_view_on_scroll_behavior = 0x7f130109
com.example.allinone:attr/circularflow_angles = 0x7f0400e3
com.example.allinone:string/group_description = 0x7f130106
com.example.allinone:id/futuresSwipeRefreshLayout = 0x7f0a019b
com.example.allinone:string/state_off = 0x7f130214
com.example.allinone:attr/actionModePasteDrawable = 0x7f04001a
com.example.allinone:string/group_deleted = 0x7f130105
com.example.allinone:string/group_created = 0x7f130104
com.example.allinone:style/Widget.MaterialComponents.Button.TextButton.Icon = 0x7f14044b
com.example.allinone:string/google_api_key = 0x7f1300fe
com.example.allinone:string/gcm_defaultSenderId = 0x7f1300fd
com.example.allinone:string/favorite_muscle = 0x7f1300fb
com.example.allinone:string/fallback_menu_item_copy_link = 0x7f1300f8
com.example.allinone:style/Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f14008a
com.example.allinone:string/fab_transformation_sheet_behavior = 0x7f1300f7
com.example.allinone:string/fab_transformation_scrim_behavior = 0x7f1300f6
com.example.allinone:string/exo_track_role_supplementary = 0x7f1300e9
com.example.allinone:string/exo_track_role_closed_captions = 0x7f1300e7
com.example.allinone:string/exo_track_mono = 0x7f1300e4
com.example.allinone:string/exo_item_list = 0x7f1300e2
com.example.allinone:string/workout_saved = 0x7f130258
com.example.allinone:attr/flow_firstHorizontalBias = 0x7f040205
com.example.allinone:string/exo_download_removing = 0x7f1300e1
com.example.allinone:string/exo_download_paused = 0x7f1300de
com.example.allinone:string/exo_download_failed = 0x7f1300dc
com.example.allinone:string/exo_download_downloading = 0x7f1300db
com.example.allinone:attr/scrubber_enabled_size = 0x7f0403ed
com.example.allinone:string/exo_controls_shuffle_on_description = 0x7f1300d5
com.example.allinone:string/exo_controls_show = 0x7f1300d3
com.example.allinone:string/status_active_desc = 0x7f130216
com.example.allinone:string/exo_controls_seek_bar_description = 0x7f1300d1
com.example.allinone:string/exo_controls_repeat_off_description = 0x7f1300ce
com.example.allinone:string/exo_controls_previous_description = 0x7f1300cc
com.example.allinone:layout/mtrl_alert_dialog = 0x7f0d0092
com.example.allinone:id/rtl = 0x7f0a02f1
com.example.allinone:string/exo_controls_pause_description = 0x7f1300c9
com.example.allinone:string/exo_controls_fullscreen_exit_description = 0x7f1300c4
com.example.allinone:dimen/m3_comp_time_picker_time_selector_pressed_state_layer_opacity = 0x7f0701cc
com.example.allinone:string/exo_controls_cc_disabled_description = 0x7f1300bf
com.example.allinone:string/type_input_description = 0x7f130246
com.example.allinone:string/error_wrong_password = 0x7f1300bd
com.example.allinone:color/user_message_bg = 0x7f060363
com.example.allinone:string/error_user_disabled = 0x7f1300bb
com.example.allinone:string/error_too_many_requests = 0x7f1300b9
com.example.allinone:attr/itemHorizontalTranslationEnabled = 0x7f040274
com.example.allinone:style/Base.Widget.AppCompat.PopupWindow = 0x7f1400ef
com.example.allinone:macro/m3_comp_switch_disabled_unselected_track_color = 0x7f0e011e
com.example.allinone:string/error_service_unavailable = 0x7f1300b6
com.example.allinone:string/error_playing_audio = 0x7f1300b1
com.example.allinone:string/error_network_unavailable = 0x7f1300ac
com.example.allinone:macro/m3_comp_secondary_navigation_tab_hover_state_layer_color = 0x7f0e00fe
com.example.allinone:string/error_network = 0x7f1300ab
com.example.allinone:string/error_firebase_network = 0x7f1300a9
com.example.allinone:style/Widget.Material3.CompoundButton.Switch = 0x7f1403c5
com.example.allinone:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f14003b
com.example.allinone:attr/trackThickness = 0x7f040510
com.example.allinone:string/error_database = 0x7f1300a4
com.example.allinone:string/error_camera = 0x7f1300a3
com.example.allinone:string/error_a11y_label = 0x7f1300a0
com.example.allinone:string/empty_history = 0x7f13009d
com.example.allinone:string/edit_task_group = 0x7f13009b
com.example.allinone:string/password_toggle_content_description = 0x7f1301cf
com.example.allinone:string/edit_task = 0x7f13009a
com.example.allinone:macro/m3_comp_navigation_drawer_active_focus_icon_color = 0x7f0e0078
com.example.allinone:string/edit = 0x7f130095
com.example.allinone:drawable/$avd_show_password__2 = 0x7f080005
com.example.allinone:string/description = 0x7f13008f
com.example.allinone:attr/altSrc = 0x7f040035
com.example.allinone:string/delete_workout_confirmation = 0x7f13008d
com.example.allinone:string/delete_workout = 0x7f13008c
com.example.allinone:string/delete_voice_note_confirmation = 0x7f13008b
com.example.allinone:string/delete_task_confirmation = 0x7f130087
com.example.allinone:dimen/m3_comp_fab_primary_pressed_container_elevation = 0x7f070142
com.example.allinone:string/delete_task = 0x7f130086
com.example.allinone:style/ShapeAppearance.MaterialComponents = 0x7f1401ab
com.example.allinone:string/delete_registration_confirmation = 0x7f130083
com.example.allinone:style/Theme.MaterialComponents.Bridge = 0x7f140282
com.example.allinone:string/delete_program_confirmation = 0x7f130081
com.example.allinone:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f1401ee
com.example.allinone:string/delete_program = 0x7f130080
com.example.allinone:string/delete_item = 0x7f13007d
com.example.allinone:layout/fragment_history = 0x7f0d0055
com.example.allinone:string/delete_image = 0x7f13007b
com.example.allinone:string/delete_group_confirmation = 0x7f13007a
com.example.allinone:attr/actionLayout = 0x7f040010
com.example.allinone:color/m3_ref_palette_dynamic_neutral80 = 0x7f0600ee
com.example.allinone:string/create_workout = 0x7f130074
com.example.allinone:attr/curveFit = 0x7f040175
com.example.allinone:string/create_task_group = 0x7f130073
com.example.allinone:string/create_drawing = 0x7f130071
com.example.allinone:id/textWatcher = 0x7f0a0379
com.example.allinone:string/copy_toast_msg = 0x7f130070
com.example.allinone:id/exo_shuffle = 0x7f0a0173
com.example.allinone:string/search = 0x7f1301fc
com.example.allinone:string/common_signin_button_text = 0x7f13006e
com.example.allinone:string/common_google_play_services_wear_update_text = 0x7f13006c
com.example.allinone:attr/motionEasingEmphasized = 0x7f04035e
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant50 = 0x7f060102
com.example.allinone:string/common_google_play_services_update_title = 0x7f13006a
com.example.allinone:styleable/AspectRatioFrameLayout = 0x7f150016
com.example.allinone:string/common_google_play_services_notification_ticker = 0x7f130065
com.example.allinone:string/common_google_play_services_install_button = 0x7f130061
com.example.allinone:id/entryPriceValue = 0x7f0a013a
com.example.allinone:string/common_google_play_services_enable_title = 0x7f130060
com.example.allinone:style/Widget.MaterialComponents.Toolbar = 0x7f1404a7
com.example.allinone:string/common_google_play_services_enable_text = 0x7f13005f
com.example.allinone:style/Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f140424
com.example.allinone:string/common_google_play_services_enable_button = 0x7f13005e
com.example.allinone:string/color_yellow = 0x7f13005c
com.example.allinone:attr/verticalOffset = 0x7f040526
com.example.allinone:integer/mtrl_card_anim_duration_ms = 0x7f0b0039
com.example.allinone:string/color_pink = 0x7f130058
com.example.allinone:string/color_orange = 0x7f130057
com.example.allinone:style/Widget.Material3.BottomNavigationView = 0x7f140390
com.example.allinone:string/color_green = 0x7f130056
com.example.allinone:attr/windowSplashScreenBackground = 0x7f040542
com.example.allinone:string/clear = 0x7f13004d
com.example.allinone:string/choose_from_gallery = 0x7f13004c
com.example.allinone:string/choose_color = 0x7f13004b
com.example.allinone:attr/commitIcon = 0x7f040142
com.example.allinone:string/material_motion_easing_decelerated = 0x7f130167
com.example.allinone:string/character_counter_overflowed_content_description = 0x7f130049
com.example.allinone:string/cannot_delete = 0x7f130047
com.example.allinone:bool/enable_system_job_service_default = 0x7f050004
com.example.allinone:string/storage_permission_message = 0x7f13021f
com.example.allinone:string/cancel = 0x7f130046
com.example.allinone:string/camera_permission_message = 0x7f130044
com.example.allinone:drawable/ic_arrow_back_black_24 = 0x7f080102
com.example.allinone:string/call_notification_ongoing_text = 0x7f130042
com.example.allinone:attr/tabIconTintMode = 0x7f04046b
com.example.allinone:string/abc_prepend_shortcut_label = 0x7f130011
com.example.allinone:style/Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton = 0x7f140118
com.example.allinone:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f1400e3
com.example.allinone:string/call_notification_hang_up_action = 0x7f130040
com.example.allinone:string/call_notification_decline_action = 0x7f13003f
com.example.allinone:string/bottomsheet_drag_handle_content_description = 0x7f13003a
com.example.allinone:macro/m3_comp_navigation_drawer_active_icon_color = 0x7f0e007e
com.example.allinone:string/bottomsheet_drag_handle_clicked = 0x7f130039
com.example.allinone:color/task_checkbox_color = 0x7f06035c
com.example.allinone:style/Base.TextAppearance.MaterialComponents.Subtitle2 = 0x7f140047
com.example.allinone:string/bottomsheet_action_expand_halfway = 0x7f130038
com.example.allinone:string/bottomsheet_action_expand = 0x7f130037
com.example.allinone:string/bottomsheet_action_collapse = 0x7f130036
com.example.allinone:style/ShapeAppearance.Material3.SmallComponent = 0x7f1401a9
com.example.allinone:string/attachments_only_for_paid_registrations = 0x7f130033
com.example.allinone:color/m3_ref_palette_dynamic_primary60 = 0x7f060116
com.example.allinone:dimen/m3_comp_extended_fab_primary_hover_state_layer_opacity = 0x7f070136
com.example.allinone:string/apply = 0x7f130031
com.example.allinone:string/appbar_scrolling_view_behavior = 0x7f130030
com.example.allinone:string/abc_action_bar_home_description = 0x7f130000
com.example.allinone:string/app_name = 0x7f13002f
com.example.allinone:string/android.credentials.TYPE_PASSWORD_CREDENTIAL = 0x7f13002b
com.example.allinone:string/all_time_stats = 0x7f130028
com.example.allinone:string/all_months = 0x7f130027
com.example.allinone:id/action_container = 0x7f0a003c
com.example.allinone:string/add_task = 0x7f130025
com.example.allinone:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f140375
com.example.allinone:style/Widget.AppCompat.ActionButton.Overflow = 0x7f140334
com.example.allinone:color/mtrl_btn_ripple_color = 0x7f0602fa
com.example.allinone:string/add_student_prompt = 0x7f130024
com.example.allinone:string/add_student = 0x7f130023
com.example.allinone:string/add_program = 0x7f130020
com.example.allinone:layout/mtrl_search_bar = 0x7f0d00b0
com.example.allinone:string/add_photo = 0x7f13001f
com.example.allinone:string/m3c_date_input_headline_description = 0x7f13012a
com.example.allinone:string/add_image = 0x7f13001e
com.example.allinone:string/abc_shareactionprovider_share_with_application = 0x7f130019
com.example.allinone:string/abc_shareactionprovider_share_with = 0x7f130018
com.example.allinone:style/Base.V14.ThemeOverlay.Material3.BottomSheetDialog = 0x7f14009d
com.example.allinone:string/mtrl_picker_range_header_selected = 0x7f13019a
com.example.allinone:string/abc_searchview_description_voice = 0x7f130017
com.example.allinone:color/mtrl_choice_chip_text_color = 0x7f06030b
com.example.allinone:string/abc_searchview_description_query = 0x7f130014
com.example.allinone:attr/motionEffect_start = 0x7f04036b
com.example.allinone:style/Base.TextAppearance.AppCompat.Display3 = 0x7f14001d
com.example.allinone:integer/mtrl_switch_thumb_viewport_size = 0x7f0b0040
com.example.allinone:string/abc_search_hint = 0x7f130012
com.example.allinone:dimen/m3_comp_radio_button_disabled_selected_icon_opacity = 0x7f07018a
com.example.allinone:string/abc_menu_shift_shortcut_label = 0x7f13000e
com.example.allinone:string/abc_menu_meta_shortcut_label = 0x7f13000d
com.example.allinone:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f080047
com.example.allinone:string/abc_menu_function_shortcut_label = 0x7f13000c
com.example.allinone:string/abc_menu_delete_shortcut_label = 0x7f13000a
com.example.allinone:attr/tabIndicatorFullWidth = 0x7f040470
com.example.allinone:string/abc_menu_ctrl_shortcut_label = 0x7f130009
com.example.allinone:dimen/mtrl_alert_dialog_background_inset_bottom = 0x7f07026b
com.example.allinone:drawable/ic_expand_less = 0x7f08012a
com.example.allinone:string/delete_note_confirmation = 0x7f13007f
com.example.allinone:id/group_description_layout = 0x7f0a01a6
com.example.allinone:string/abc_capital_off = 0x7f130006
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant4 = 0x7f060100
com.example.allinone:string/abc_activitychooserview_choose_application = 0x7f130005
com.example.allinone:string/abc_action_mode_done = 0x7f130003
com.example.allinone:string/abc_action_menu_overflow_description = 0x7f130002
com.example.allinone:string/abc_action_bar_up_description = 0x7f130001
com.example.allinone:dimen/m3_sys_motion_easing_legacy_accelerate_control_y1 = 0x7f070225
com.example.allinone:raw/firebase_common_keep = 0x7f120000
com.example.allinone:plurals/mtrl_badge_content_description = 0x7f110003
com.example.allinone:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f140438
com.example.allinone:id/graph_wrap = 0x7f0a01a3
com.example.allinone:plurals/exercise_count = 0x7f110000
com.example.allinone:mipmap/ic_launcher_round = 0x7f100001
com.example.allinone:dimen/mtrl_btn_padding_left = 0x7f07028b
com.example.allinone:menu/wt_registration_context_menu = 0x7f0f000d
com.example.allinone:drawable/ic_format_underline = 0x7f080135
com.example.allinone:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f1400dd
com.example.allinone:menu/wt_bottom_nav_menu = 0x7f0f000c
com.example.allinone:menu/instagram_bottom_nav_menu = 0x7f0f0002
com.example.allinone:styleable/CustomAttribute = 0x7f150032
com.example.allinone:menu/bottom_nav_menu = 0x7f0f0000
com.example.allinone:macro/m3_sys_color_dark_surface_tint = 0x7f0e0175
com.example.allinone:macro/m3_comp_top_app_bar_small_trailing_icon_color = 0x7f0e0174
com.example.allinone:macro/m3_comp_top_app_bar_small_on_scroll_container_color = 0x7f0e0173
com.example.allinone:macro/m3_comp_top_app_bar_small_leading_icon_color = 0x7f0e0172
com.example.allinone:macro/m3_comp_top_app_bar_small_headline_color = 0x7f0e0170
com.example.allinone:style/Widget.AppCompat.Light.ActionBar = 0x7f140347
com.example.allinone:attr/counterOverflowTextColor = 0x7f04016e
com.example.allinone:macro/m3_comp_top_app_bar_small_container_color = 0x7f0e016f
com.example.allinone:styleable/CompoundButton = 0x7f150029
com.example.allinone:macro/m3_comp_time_picker_time_selector_selected_focus_state_layer_color = 0x7f0e0161
com.example.allinone:style/Platform.MaterialComponents.Light = 0x7f14016b
com.example.allinone:attr/floatingActionButtonSmallTertiaryStyle = 0x7f040201
com.example.allinone:macro/m3_comp_top_app_bar_large_headline_type = 0x7f0e016c
com.example.allinone:macro/m3_comp_top_app_bar_large_headline_color = 0x7f0e016b
com.example.allinone:macro/m3_comp_time_picker_time_selector_unselected_label_text_color = 0x7f0e016a
com.example.allinone:style/Base.TextAppearance.AppCompat.Caption = 0x7f14001a
com.example.allinone:color/error_color_material_dark = 0x7f060083
com.example.allinone:id/dimensions = 0x7f0a0105
com.example.allinone:macro/m3_comp_time_picker_time_selector_unselected_focus_state_layer_color = 0x7f0e0168
com.example.allinone:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f14003f
com.example.allinone:macro/m3_comp_time_picker_time_selector_selected_pressed_state_layer_color = 0x7f0e0164
com.example.allinone:macro/m3_comp_time_picker_time_selector_selected_label_text_color = 0x7f0e0163
com.example.allinone:macro/m3_comp_time_picker_time_selector_selected_container_color = 0x7f0e0160
com.example.allinone:macro/m3_comp_time_picker_time_selector_container_shape = 0x7f0e015e
com.example.allinone:id/media_controller_compat_view_tag = 0x7f0a0227
com.example.allinone:macro/m3_comp_time_picker_container_shape = 0x7f0e014f
com.example.allinone:macro/m3_comp_time_picker_period_selector_unselected_hover_state_layer_color = 0x7f0e015b
com.example.allinone:macro/m3_comp_time_picker_period_selector_unselected_focus_state_layer_color = 0x7f0e015a
com.example.allinone:macro/m3_comp_time_picker_period_selector_selected_label_text_color = 0x7f0e0158
com.example.allinone:macro/m3_comp_time_picker_period_selector_selected_focus_state_layer_color = 0x7f0e0156
com.example.allinone:macro/m3_comp_time_picker_clock_dial_color = 0x7f0e014c
com.example.allinone:macro/m3_comp_time_input_time_input_field_supporting_text_type = 0x7f0e014b
com.example.allinone:macro/m3_comp_time_input_time_input_field_supporting_text_color = 0x7f0e014a
com.example.allinone:attr/viewInflaterClass = 0x7f040528
com.example.allinone:macro/m3_comp_text_button_label_text_type = 0x7f0e0145
com.example.allinone:string/drawing_options = 0x7f130092
com.example.allinone:drawable/material_ic_keyboard_arrow_right_black_24dp = 0x7f08017f
com.example.allinone:macro/m3_comp_text_button_hover_state_layer_color = 0x7f0e0143
com.example.allinone:string/material_timepicker_select_time = 0x7f130173
com.example.allinone:attr/colorOnPrimaryFixedVariant = 0x7f040116
com.example.allinone:macro/m3_comp_text_button_focus_state_layer_color = 0x7f0e0142
com.example.allinone:macro/m3_comp_switch_unselected_track_color = 0x7f0e0140
com.example.allinone:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f080088
com.example.allinone:macro/m3_comp_switch_unselected_pressed_track_color = 0x7f0e013e
com.example.allinone:macro/m3_comp_switch_unselected_pressed_icon_color = 0x7f0e013c
com.example.allinone:macro/m3_comp_switch_unselected_hover_track_color = 0x7f0e0138
com.example.allinone:macro/m3_comp_switch_unselected_hover_handle_color = 0x7f0e0135
com.example.allinone:macro/m3_comp_switch_unselected_focus_state_layer_color = 0x7f0e0131
com.example.allinone:drawable/exo_ic_fullscreen_exit = 0x7f0800c4
com.example.allinone:macro/m3_comp_switch_unselected_focus_icon_color = 0x7f0e0130
com.example.allinone:string/common_google_play_services_unknown_issue = 0x7f130066
com.example.allinone:style/Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f1403ec
com.example.allinone:macro/m3_comp_switch_selected_pressed_state_layer_color = 0x7f0e012c
com.example.allinone:macro/m3_comp_switch_selected_icon_color = 0x7f0e0129
com.example.allinone:macro/m3_comp_switch_selected_hover_track_color = 0x7f0e0128
com.example.allinone:id/browser_actions_menu_item_icon = 0x7f0a0094
com.example.allinone:macro/m3_comp_switch_selected_handle_color = 0x7f0e0124
com.example.allinone:macro/m3_comp_switch_selected_focus_state_layer_color = 0x7f0e0122
com.example.allinone:dimen/abc_action_button_min_width_material = 0x7f07000e
com.example.allinone:macro/m3_comp_switch_selected_focus_icon_color = 0x7f0e0121
com.example.allinone:color/m3_sys_color_light_on_primary = 0x7f060216
com.example.allinone:style/Base.Widget.Material3.TabLayout.Secondary = 0x7f140113
com.example.allinone:style/TextAppearance.M3.Sys.Typescale.DisplayMedium = 0x7f14020d
com.example.allinone:attr/motionDurationMedium1 = 0x7f040354
com.example.allinone:macro/m3_comp_switch_disabled_unselected_track_outline_color = 0x7f0e011f
com.example.allinone:color/design_error = 0x7f060072
com.example.allinone:macro/m3_comp_switch_disabled_selected_track_color = 0x7f0e011b
com.example.allinone:macro/m3_comp_time_picker_container_color = 0x7f0e014e
com.example.allinone:macro/m3_comp_switch_disabled_selected_handle_color = 0x7f0e0119
com.example.allinone:macro/m3_comp_suggestion_chip_label_text_type = 0x7f0e0118
com.example.allinone:style/Widget.MaterialComponents.BottomAppBar.PrimarySurface = 0x7f14043d
com.example.allinone:macro/m3_comp_suggestion_chip_container_shape = 0x7f0e0117
com.example.allinone:attr/materialButtonStyle = 0x7f040308
com.example.allinone:macro/m3_comp_navigation_rail_inactive_hover_state_layer_color = 0x7f0e009b
com.example.allinone:macro/m3_comp_slider_inactive_track_color = 0x7f0e0110
com.example.allinone:macro/m3_comp_slider_disabled_inactive_track_color = 0x7f0e010e
com.example.allinone:macro/m3_comp_slider_active_track_color = 0x7f0e010b
com.example.allinone:macro/m3_comp_sheet_side_docked_standard_container_color = 0x7f0e010a
com.example.allinone:macro/m3_comp_sheet_side_docked_modal_container_shape = 0x7f0e0109
com.example.allinone:string/mtrl_picker_out_of_range = 0x7f130197
com.example.allinone:dimen/m3_bottomappbar_fab_cradle_margin = 0x7f0700ea
com.example.allinone:macro/m3_comp_sheet_side_docked_modal_container_color = 0x7f0e0108
com.example.allinone:string/error_not_authenticated = 0x7f1300ad
com.example.allinone:macro/m3_comp_sheet_side_detached_container_shape = 0x7f0e0107
com.example.allinone:macro/m3_comp_sheet_bottom_docked_container_color = 0x7f0e0104
com.example.allinone:macro/m3_comp_secondary_navigation_tab_with_icon_inactive_icon_color = 0x7f0e0103
com.example.allinone:dimen/m3_navigation_rail_default_width = 0x7f0701ee
com.example.allinone:macro/m3_comp_secondary_navigation_tab_with_icon_active_icon_color = 0x7f0e0102
com.example.allinone:macro/m3_comp_secondary_navigation_tab_label_text_type = 0x7f0e0100
com.example.allinone:macro/m3_comp_secondary_navigation_tab_inactive_label_text_color = 0x7f0e00ff
com.example.allinone:macro/m3_comp_search_view_header_trailing_icon_color = 0x7f0e00f9
com.example.allinone:id/open_search_view_root = 0x7f0a0295
com.example.allinone:macro/m3_comp_search_view_header_input_text_color = 0x7f0e00f4
com.example.allinone:style/Base.Theme.Material3.Dark.Dialog.FixedSize = 0x7f14005c
com.example.allinone:macro/m3_comp_search_bar_trailing_icon_color = 0x7f0e00f0
com.example.allinone:drawable/googleg_standard_color_18 = 0x7f0800ff
com.example.allinone:macro/m3_comp_search_bar_supporting_text_type = 0x7f0e00ef
com.example.allinone:macro/m3_comp_icon_button_unselected_icon_color = 0x7f0e005a
com.example.allinone:anim/design_bottom_sheet_slide_out = 0x7f010019
com.example.allinone:macro/m3_comp_radio_button_unselected_pressed_state_layer_color = 0x7f0e00e5
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel = 0x7f140469
com.example.allinone:attr/scaleFromTextSize = 0x7f0403e4
com.example.allinone:macro/m3_comp_radio_button_unselected_icon_color = 0x7f0e00e3
com.example.allinone:id/edge = 0x7f0a011e
com.example.allinone:attr/indicatorDirectionCircular = 0x7f040265
com.example.allinone:dimen/mtrl_btn_stroke_size = 0x7f070290
com.example.allinone:dimen/mtrl_shape_corner_size_large_component = 0x7f070307
com.example.allinone:macro/m3_comp_radio_button_unselected_hover_icon_color = 0x7f0e00e1
com.example.allinone:macro/m3_comp_radio_button_unselected_focus_icon_color = 0x7f0e00df
com.example.allinone:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar = 0x7f140315
com.example.allinone:macro/m3_comp_radio_button_selected_hover_state_layer_color = 0x7f0e00db
com.example.allinone:style/TextAppearance.MaterialComponents.Overline = 0x7f140239
com.example.allinone:color/mtrl_btn_text_btn_ripple_color = 0x7f0602fd
com.example.allinone:macro/m3_comp_radio_button_selected_hover_icon_color = 0x7f0e00da
com.example.allinone:macro/m3_comp_radio_button_disabled_unselected_icon_color = 0x7f0e00d7
com.example.allinone:string/icon_content_description = 0x7f13010b
com.example.allinone:layout/design_navigation_menu = 0x7f0d002d
com.example.allinone:macro/m3_comp_primary_navigation_tab_with_label_text_label_text_type = 0x7f0e00d3
com.example.allinone:macro/m3_comp_primary_navigation_tab_with_label_text_inactive_label_text_color = 0x7f0e00d2
com.example.allinone:macro/m3_comp_primary_navigation_tab_with_icon_active_icon_color = 0x7f0e00cf
com.example.allinone:macro/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_color = 0x7f0e00ce
com.example.allinone:attr/expandedTitleMarginStart = 0x7f0401d9
com.example.allinone:macro/m3_comp_primary_navigation_tab_inactive_hover_state_layer_color = 0x7f0e00cd
com.example.allinone:macro/m3_comp_primary_navigation_tab_inactive_focus_state_layer_color = 0x7f0e00cc
com.example.allinone:dimen/material_input_text_to_prefix_suffix_padding = 0x7f070265
com.example.allinone:macro/m3_comp_primary_navigation_tab_active_pressed_state_layer_color = 0x7f0e00ca
com.example.allinone:macro/m3_comp_primary_navigation_tab_active_hover_state_layer_color = 0x7f0e00c8
com.example.allinone:macro/m3_comp_plain_tooltip_supporting_text_type = 0x7f0e00c6
com.example.allinone:attr/auto_show = 0x7f04004f
com.example.allinone:macro/m3_comp_outlined_text_field_supporting_text_type = 0x7f0e00c5
com.example.allinone:dimen/abc_disabled_alpha_material_dark = 0x7f070027
com.example.allinone:macro/m3_comp_sheet_bottom_docked_container_shape = 0x7f0e0105
com.example.allinone:color/m3_ref_palette_dynamic_neutral70 = 0x7f0600ed
com.example.allinone:macro/m3_comp_outlined_text_field_outline_color = 0x7f0e00c3
com.example.allinone:macro/m3_comp_outlined_text_field_error_trailing_icon_color = 0x7f0e00b8
com.example.allinone:macro/m3_comp_outlined_text_field_error_outline_color = 0x7f0e00b6
com.example.allinone:macro/m3_comp_outlined_card_outline_color = 0x7f0e00ae
com.example.allinone:macro/m3_comp_outlined_card_hover_outline_color = 0x7f0e00ad
com.example.allinone:attr/show_rewind_button = 0x7f040414
com.example.allinone:macro/m3_comp_outlined_card_focus_outline_color = 0x7f0e00ac
com.example.allinone:dimen/mtrl_calendar_month_horizontal_padding = 0x7f0702ae
com.example.allinone:macro/m3_comp_outlined_card_disabled_outline_color = 0x7f0e00aa
com.example.allinone:plurals/exo_controls_rewind_by_amount_description = 0x7f110002
com.example.allinone:macro/m3_comp_outlined_card_container_color = 0x7f0e00a8
com.example.allinone:interpolator/m3_sys_motion_easing_standard_decelerate = 0x7f0c000d
com.example.allinone:macro/m3_comp_outlined_button_pressed_outline_color = 0x7f0e00a7
com.example.allinone:id/action_edit_group = 0x7f0a0042
com.example.allinone:macro/m3_comp_outlined_button_disabled_outline_color = 0x7f0e00a3
com.example.allinone:drawable/exo_ic_rewind = 0x7f0800c7
com.example.allinone:macro/m3_comp_outlined_autocomplete_text_field_caret_color = 0x7f0e00a1
com.example.allinone:dimen/sliding_pane_detail_pane_width = 0x7f070340
com.example.allinone:style/ShapeAppearance.MaterialComponents.Tooltip = 0x7f1401b0
com.example.allinone:macro/m3_comp_outlined_autocomplete_menu_container_color = 0x7f0e00a0
com.example.allinone:macro/m3_comp_navigation_rail_inactive_pressed_state_layer_color = 0x7f0e009e
com.example.allinone:attr/textOutlineColor = 0x7f0404c1
com.example.allinone:macro/m3_comp_navigation_rail_active_pressed_state_layer_color = 0x7f0e0098
com.example.allinone:color/colorPrimary = 0x7f060044
com.example.allinone:drawable/ic_save = 0x7f08015b
com.example.allinone:macro/m3_comp_navigation_rail_active_icon_color = 0x7f0e0095
com.example.allinone:dimen/exo_styled_progress_enabled_thumb_size = 0x7f0700b1
com.example.allinone:integer/m3_sys_shape_corner_large_corner_family = 0x7f0b0028
com.example.allinone:style/Animation.Material3.SideSheetDialog.Left = 0x7f140008
com.example.allinone:macro/m3_comp_navigation_rail_active_hover_state_layer_color = 0x7f0e0094
com.example.allinone:macro/m3_comp_extended_fab_secondary_icon_color = 0x7f0e0031
com.example.allinone:macro/m3_comp_navigation_drawer_modal_container_color = 0x7f0e0092
com.example.allinone:color/material_on_surface_disabled = 0x7f0602b5
com.example.allinone:macro/m3_comp_navigation_drawer_inactive_pressed_label_text_color = 0x7f0e008f
com.example.allinone:id/design_bottom_sheet = 0x7f0a00fc
com.example.allinone:string/androidx.credentials.TYPE_PUBLIC_KEY_CREDENTIAL = 0x7f13002c
com.example.allinone:style/Base.TextAppearance.AppCompat.Large = 0x7f140021
com.example.allinone:macro/m3_comp_navigation_drawer_inactive_pressed_icon_color = 0x7f0e008e
com.example.allinone:color/m3_ref_palette_dynamic_neutral0 = 0x7f0600df
com.example.allinone:id/investmentsRecyclerView = 0x7f0a01da
com.example.allinone:macro/m3_comp_navigation_drawer_inactive_label_text_color = 0x7f0e008d
com.example.allinone:macro/m3_comp_navigation_drawer_inactive_icon_color = 0x7f0e008c
com.example.allinone:color/mtrl_switch_thumb_tint = 0x7f060322
com.example.allinone:macro/m3_comp_navigation_drawer_inactive_focus_icon_color = 0x7f0e0086
com.example.allinone:attr/paddingEnd = 0x7f040393
com.example.allinone:macro/m3_comp_navigation_drawer_headline_color = 0x7f0e0084
com.example.allinone:dimen/mtrl_bottomappbar_fab_cradle_margin = 0x7f07027a
com.example.allinone:macro/m3_comp_navigation_drawer_active_pressed_label_text_color = 0x7f0e0082
com.example.allinone:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f1401e4
com.example.allinone:style/ShapeAppearance.Material3.Corner.Small = 0x7f1401a5
com.example.allinone:macro/m3_comp_navigation_drawer_active_pressed_icon_color = 0x7f0e0081
com.example.allinone:macro/m3_comp_navigation_drawer_active_label_text_color = 0x7f0e0080
com.example.allinone:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f14007c
com.example.allinone:macro/m3_comp_navigation_drawer_active_focus_state_layer_color = 0x7f0e007a
com.example.allinone:string/common_open_on_phone = 0x7f13006d
com.example.allinone:macro/m3_comp_navigation_drawer_active_focus_label_text_color = 0x7f0e0079
com.example.allinone:macro/m3_comp_navigation_bar_label_text_type = 0x7f0e0077
com.example.allinone:dimen/m3_sys_motion_easing_linear_control_x1 = 0x7f07022f
com.example.allinone:id/summaryCard = 0x7f0a034c
com.example.allinone:macro/m3_comp_navigation_bar_inactive_pressed_state_layer_color = 0x7f0e0076
com.example.allinone:string/exo_controls_settings_description = 0x7f1300d2
com.example.allinone:macro/m3_comp_navigation_bar_inactive_pressed_icon_color = 0x7f0e0074
com.example.allinone:macro/m3_comp_navigation_bar_inactive_label_text_color = 0x7f0e0073
com.example.allinone:macro/m3_comp_navigation_bar_inactive_icon_color = 0x7f0e0072
com.example.allinone:id/material_hour_text_input = 0x7f0a0219
com.example.allinone:macro/m3_comp_navigation_bar_inactive_hover_label_text_color = 0x7f0e0070
com.example.allinone:style/Widget.Design.FloatingActionButton = 0x7f14037c
com.example.allinone:macro/m3_comp_navigation_bar_inactive_hover_icon_color = 0x7f0e006f
com.example.allinone:style/Widget.Material3.MaterialTimePicker.Display.HelperText = 0x7f1403fd
com.example.allinone:macro/m3_comp_navigation_bar_inactive_focus_label_text_color = 0x7f0e006d
com.example.allinone:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f140033
com.example.allinone:attr/tickRadiusActive = 0x7f0404e1
com.example.allinone:macro/m3_comp_navigation_bar_container_color = 0x7f0e006b
com.example.allinone:attr/fastScrollVerticalThumbDrawable = 0x7f0401f1
com.example.allinone:macro/m3_comp_switch_selected_hover_state_layer_color = 0x7f0e0127
com.example.allinone:macro/m3_comp_navigation_bar_active_pressed_state_layer_color = 0x7f0e006a
com.example.allinone:macro/m3_comp_navigation_bar_active_pressed_label_text_color = 0x7f0e0069
com.example.allinone:macro/m3_comp_outlined_text_field_input_text_type = 0x7f0e00c1
com.example.allinone:dimen/abc_disabled_alpha_material_light = 0x7f070028
com.example.allinone:macro/m3_comp_navigation_bar_active_icon_color = 0x7f0e0065
com.example.allinone:attr/colorControlNormal = 0x7f040109
com.example.allinone:macro/m3_comp_navigation_bar_active_focus_state_layer_color = 0x7f0e0061
com.example.allinone:macro/m3_comp_navigation_bar_active_focus_icon_color = 0x7f0e005f
com.example.allinone:dimen/m3_comp_navigation_bar_focus_state_layer_opacity = 0x7f07015f
com.example.allinone:macro/m3_comp_menu_container_color = 0x7f0e005d
com.example.allinone:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f14034c
com.example.allinone:macro/m3_comp_input_chip_container_shape = 0x7f0e005b
com.example.allinone:styleable/GradientColorItem = 0x7f150043
com.example.allinone:macro/m3_comp_icon_button_selected_icon_color = 0x7f0e0059
com.example.allinone:macro/m3_comp_filled_tonal_icon_button_toggle_selected_icon_color = 0x7f0e0055
com.example.allinone:macro/m3_comp_filled_tonal_button_container_color = 0x7f0e0052
com.example.allinone:macro/m3_comp_filled_text_field_supporting_text_type = 0x7f0e0051
com.example.allinone:macro/m3_comp_filled_text_field_error_active_indicator_color = 0x7f0e004d
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant80 = 0x7f060106
com.example.allinone:macro/m3_comp_filled_text_field_container_color = 0x7f0e004b
com.example.allinone:macro/m3_comp_filled_icon_button_container_color = 0x7f0e0048
com.example.allinone:macro/m3_comp_filled_button_label_text_type = 0x7f0e0045
com.example.allinone:style/Base.DialogWindowTitle.AppCompat = 0x7f140011
com.example.allinone:macro/m3_comp_filled_button_label_text_color = 0x7f0e0044
com.example.allinone:macro/m3_comp_filled_button_container_color = 0x7f0e0043
com.example.allinone:style/Widget.Material3.CompoundButton.RadioButton = 0x7f1403c4
com.example.allinone:macro/m3_comp_time_picker_period_selector_container_shape = 0x7f0e0152
com.example.allinone:macro/m3_comp_fab_surface_icon_color = 0x7f0e003e
com.example.allinone:attr/itemShapeInsetBottom = 0x7f040281
com.example.allinone:macro/m3_comp_fab_secondary_icon_color = 0x7f0e003c
com.example.allinone:macro/m3_comp_fab_secondary_container_color = 0x7f0e003b
com.example.allinone:macro/m3_comp_fab_primary_small_container_shape = 0x7f0e003a
com.example.allinone:integer/mtrl_card_anim_delay_ms = 0x7f0b0038
com.example.allinone:dimen/mtrl_low_ripple_focused_alpha = 0x7f0702e4
com.example.allinone:string/total_time = 0x7f130241
com.example.allinone:color/m3_ref_palette_dynamic_secondary50 = 0x7f060122
com.example.allinone:macro/m3_comp_fab_primary_icon_color = 0x7f0e0038
com.example.allinone:macro/m3_comp_extended_fab_tertiary_container_color = 0x7f0e0034
com.example.allinone:dimen/exo_settings_sub_text_size = 0x7f0700a3
com.example.allinone:drawable/ic_category_sports = 0x7f080116
com.example.allinone:macro/m3_comp_extended_fab_surface_container_color = 0x7f0e0032
com.example.allinone:macro/m3_comp_extended_fab_primary_label_text_type = 0x7f0e002f
com.example.allinone:macro/m3_comp_date_picker_modal_header_headline_type = 0x7f0e0016
com.example.allinone:macro/m3_comp_extended_fab_primary_container_color = 0x7f0e002c
com.example.allinone:dimen/design_fab_elevation = 0x7f070071
com.example.allinone:macro/m3_comp_dialog_supporting_text_type = 0x7f0e0027
com.example.allinone:macro/m3_comp_dialog_supporting_text_color = 0x7f0e0026
com.example.allinone:macro/m3_comp_dialog_headline_type = 0x7f0e0025
com.example.allinone:dimen/abc_button_inset_horizontal_material = 0x7f070012
com.example.allinone:macro/m3_comp_dialog_container_color = 0x7f0e0022
com.example.allinone:macro/m3_comp_date_picker_modal_year_selection_year_unselected_label_text_color = 0x7f0e0021
com.example.allinone:macro/m3_comp_date_picker_modal_range_selection_month_subhead_type = 0x7f0e001c
com.example.allinone:macro/m3_comp_date_picker_modal_range_selection_header_headline_type = 0x7f0e001a
com.example.allinone:attr/track = 0x7f040505
com.example.allinone:macro/m3_comp_date_picker_modal_header_supporting_text_color = 0x7f0e0017
com.example.allinone:attr/layout_keyline = 0x7f0402dc
com.example.allinone:drawable/bg_category_chip = 0x7f08007a
com.example.allinone:macro/m3_comp_date_picker_modal_header_headline_color = 0x7f0e0015
com.example.allinone:macro/m3_comp_date_picker_modal_date_today_label_text_color = 0x7f0e0013
com.example.allinone:string/exposed_dropdown_menu_content_description = 0x7f1300f5
com.example.allinone:macro/m3_comp_date_picker_modal_date_today_container_outline_color = 0x7f0e0012
com.example.allinone:attr/textAppearanceHeadline6 = 0x7f040499
com.example.allinone:macro/m3_comp_slider_disabled_handle_color = 0x7f0e010d
com.example.allinone:macro/m3_comp_date_picker_modal_date_label_text_type = 0x7f0e000f
com.example.allinone:attr/customIntegerValue = 0x7f04017b
com.example.allinone:color/m3_sys_color_dark_surface_container = 0x7f0601b4
com.example.allinone:macro/m3_comp_checkbox_unselected_outline_color = 0x7f0e000c
com.example.allinone:macro/m3_comp_checkbox_selected_icon_color = 0x7f0e000b
com.example.allinone:color/m3_timepicker_display_background_color = 0x7f06024d
com.example.allinone:string/m3c_suggestions_available = 0x7f13014d
com.example.allinone:id/categoryPercentText = 0x7f0a00af
com.example.allinone:macro/m3_comp_checkbox_selected_error_container_color = 0x7f0e0009
com.example.allinone:macro/m3_comp_checkbox_selected_disabled_container_color = 0x7f0e0007
com.example.allinone:string/start_workout = 0x7f130212
com.example.allinone:attr/motionDurationExtraLong2 = 0x7f04034d
com.example.allinone:macro/m3_comp_bottom_app_bar_container_color = 0x7f0e0005
com.example.allinone:string/mtrl_picker_today_description = 0x7f1301a5
com.example.allinone:attr/contentInsetStart = 0x7f040151
com.example.allinone:macro/m3_comp_assist_chip_label_text_type = 0x7f0e0001
com.example.allinone:layout/splash_text_layout = 0x7f0d00c8
com.example.allinone:attr/backgroundStacked = 0x7f040059
com.example.allinone:style/Base.Theme.Material3.Light.SideSheetDialog = 0x7f140064
com.example.allinone:layout/splash_screen_view = 0x7f0d00c7
com.example.allinone:dimen/m3_btn_icon_btn_padding_right = 0x7f0700f7
com.example.allinone:layout/pie_chart_tooltip = 0x7f0d00c3
com.example.allinone:macro/m3_comp_search_bar_hover_state_layer_color = 0x7f0e00e7
com.example.allinone:color/drawer_item_color = 0x7f060082
com.example.allinone:layout/offline_status_view = 0x7f0d00c2
com.example.allinone:layout/notification_template_part_time = 0x7f0d00c1
com.example.allinone:drawable/dialog_rounded_bg = 0x7f0800ac
com.example.allinone:color/mtrl_on_surface_ripple_color = 0x7f06031c
com.example.allinone:dimen/abc_text_size_medium_material = 0x7f070049
com.example.allinone:layout/notification_template_part_chronometer = 0x7f0d00c0
com.example.allinone:layout/notification_template_media_custom = 0x7f0d00bf
com.example.allinone:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f140038
com.example.allinone:style/TextAppearance.Material3.MaterialTimePicker.Title = 0x7f140226
com.example.allinone:attr/itemShapeInsetEnd = 0x7f040282
com.example.allinone:layout/notification_template_media = 0x7f0d00be
com.example.allinone:layout/notification_template_big_media_narrow_custom = 0x7f0d00ba
com.example.allinone:layout/notification_template_big_media_narrow = 0x7f0d00b9
com.example.allinone:layout/notification_media_cancel_action = 0x7f0d00b6
com.example.allinone:style/Widget.Material3.TextInputLayout.FilledBox = 0x7f140422
com.example.allinone:id/dragClockwise = 0x7f0a010e
com.example.allinone:string/material_timepicker_hour = 0x7f130170
com.example.allinone:id/mtrl_child_content_container = 0x7f0a0240
com.example.allinone:layout/notification_action = 0x7f0d00b3
com.example.allinone:layout/mtrl_search_view = 0x7f0d00b1
com.example.allinone:styleable/AppCompatSeekBar = 0x7f150012
com.example.allinone:layout/mtrl_layout_snackbar_include = 0x7f0d00a4
com.example.allinone:color/mtrl_navigation_bar_colored_item_tint = 0x7f060314
com.example.allinone:style/ExoStyledControls.Button.Center.PlayPause = 0x7f140145
com.example.allinone:layout/mtrl_calendar_months = 0x7f0d00a0
com.example.allinone:layout/mtrl_calendar_month_labeled = 0x7f0d009e
com.example.allinone:layout/mtrl_calendar_horizontal = 0x7f0d009c
com.example.allinone:id/action_search = 0x7f0a004a
com.example.allinone:layout/mtrl_calendar_day_of_week = 0x7f0d009a
com.example.allinone:string/m3c_time_picker_minute_selection = 0x7f130155
com.example.allinone:string/m3c_date_picker_switch_to_input_mode = 0x7f130139
com.example.allinone:layout/mtrl_auto_complete_simple_item = 0x7f0d0098
com.example.allinone:layout/mtrl_alert_select_dialog_singlechoice = 0x7f0d0097
com.example.allinone:style/ShapeAppearance.Material3.Corner.Medium = 0x7f1401a3
com.example.allinone:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f140050
com.example.allinone:styleable/SwitchCompat = 0x7f15009f
com.example.allinone:macro/m3_comp_search_bar_input_text_type = 0x7f0e00ea
com.example.allinone:dimen/m3_comp_switch_unselected_hover_state_layer_opacity = 0x7f0701bf
com.example.allinone:string/m3c_time_picker_am = 0x7f13014e
com.example.allinone:layout/material_timepicker_dialog = 0x7f0d0090
com.example.allinone:layout/material_timepicker = 0x7f0d008f
com.example.allinone:string/retry = 0x7f1301f5
com.example.allinone:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f1400fe
com.example.allinone:layout/material_time_input = 0x7f0d008e
com.example.allinone:layout/material_time_chip = 0x7f0d008d
com.example.allinone:style/Widget.MaterialComponents.PopupMenu = 0x7f140483
com.example.allinone:style/ThemeOverlay.Material3.Chip.Assist = 0x7f1402d2
com.example.allinone:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f140031
com.example.allinone:layout/material_textinput_timepicker = 0x7f0d008c
com.example.allinone:layout/material_clock_period_toggle_land = 0x7f0d0088
com.example.allinone:layout/material_chip_input_combo = 0x7f0d0084
com.example.allinone:style/Base.Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f140075
com.example.allinone:layout/layout_page_header = 0x7f0d007e
com.example.allinone:layout/item_note_image = 0x7f0d0075
com.example.allinone:id/fragment_container = 0x7f0a0194
com.example.allinone:layout/item_lesson = 0x7f0d0073
com.example.allinone:layout/item_investment_selection = 0x7f0d0072
com.example.allinone:attr/actionModeSelectAllDrawable = 0x7f04001c
com.example.allinone:layout/item_investment_image = 0x7f0d0071
com.example.allinone:layout/item_investment = 0x7f0d006f
com.example.allinone:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f080041
com.example.allinone:layout/item_futures_position = 0x7f0d006d
com.example.allinone:id/remove_exercise_button = 0x7f0a02e5
com.example.allinone:layout/notification_media_action = 0x7f0d00b5
com.example.allinone:layout/item_fullscreen_image = 0x7f0d006c
com.example.allinone:string/abc_toolbar_collapse_description = 0x7f13001a
com.example.allinone:layout/item_category_spending = 0x7f0d0066
com.example.allinone:id/exo_minimal_fullscreen = 0x7f0a0161
com.example.allinone:layout/item_category_dropdown = 0x7f0d0065
com.example.allinone:drawable/exo_styled_controls_shuffle_off = 0x7f0800f7
com.example.allinone:style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked = 0x7f140164
com.example.allinone:layout/item_add_exercise = 0x7f0d0062
com.example.allinone:style/Base.V24.Theme.Material3.Light.Dialog = 0x7f1400b7
com.example.allinone:attr/layout_wrapBehaviorInParent = 0x7f0402e2
com.example.allinone:style/Animation.Design.BottomSheetDialog = 0x7f140005
com.example.allinone:layout/ime_secondary_split_test_activity = 0x7f0d0061
com.example.allinone:styleable/AppCompatImageView = 0x7f150011
com.example.allinone:macro/m3_comp_navigation_drawer_active_pressed_state_layer_color = 0x7f0e0083
com.example.allinone:string/required_fields_missing = 0x7f1301f4
com.example.allinone:string/title_seminars = 0x7f130239
com.example.allinone:attr/searchHintIcon = 0x7f0403ee
com.example.allinone:drawable/$mtrl_checkbox_button_icon_unchecked_checked__0 = 0x7f080018
com.example.allinone:layout/fragment_transaction_report = 0x7f0d0059
com.example.allinone:layout/fragment_investments_tab = 0x7f0d0058
com.example.allinone:style/Widget.Material3.AutoCompleteTextView.OutlinedBox = 0x7f140388
com.example.allinone:string/investment_name = 0x7f130110
com.example.allinone:layout/fragment_home = 0x7f0d0056
com.example.allinone:drawable/m3_bottom_sheet_drag_handle = 0x7f08016f
com.example.allinone:style/ShapeAppearanceOverlay.Material3.SearchView = 0x7f1401ba
com.example.allinone:string/m3_sys_motion_easing_legacy = 0x7f13011d
com.example.allinone:drawable/exo_icon_repeat_one = 0x7f0800d8
com.example.allinone:layout/fragment_futures = 0x7f0d0053
com.example.allinone:macro/m3_comp_date_picker_modal_weekdays_label_text_type = 0x7f0e001e
com.example.allinone:string/no_app_for_file_type = 0x7f1301ba
com.example.allinone:layout/exo_styled_sub_settings_list_item = 0x7f0d0051
com.example.allinone:drawable/common_google_signin_btn_text_dark_focused = 0x7f08009b
com.example.allinone:layout/exo_styled_settings_list_item = 0x7f0d0050
com.example.allinone:color/primary_dark_material_dark = 0x7f06033c
com.example.allinone:string/program_deleted = 0x7f1301e1
com.example.allinone:id/positionSymbolText = 0x7f0a02c2
com.example.allinone:layout/exo_styled_player_control_view = 0x7f0d004d
com.example.allinone:layout/exo_styled_player_control_rewind_button = 0x7f0d004c
com.example.allinone:animator/mtrl_extended_fab_hide_motion_spec = 0x7f02001b
com.example.allinone:dimen/tooltip_y_offset_touch = 0x7f070350
com.example.allinone:layout/exo_styled_player_control_ffwd_button = 0x7f0d004b
com.example.allinone:layout/exo_player_view = 0x7f0d004a
com.example.allinone:string/mtrl_picker_invalid_format = 0x7f130191
com.example.allinone:layout/dropdown_item = 0x7f0d0047
com.example.allinone:style/ThemeOverlay.Material3.Button.TonalButton = 0x7f1402d0
com.example.allinone:macro/m3_comp_text_button_pressed_state_layer_color = 0x7f0e0146
com.example.allinone:id/dateLayout = 0x7f0a00e7
com.example.allinone:id/material_timepicker_container = 0x7f0a0220
com.example.allinone:layout/dialog_task_group = 0x7f0d0045
com.example.allinone:layout/dialog_progress = 0x7f0d0043
com.example.allinone:layout/dialog_program_details = 0x7f0d0042
com.example.allinone:attr/closeIcon = 0x7f0400ef
com.example.allinone:layout/dialog_profit_loss = 0x7f0d0041
com.example.allinone:layout/dialog_pin_input = 0x7f0d003f
com.example.allinone:string/m3c_date_picker_switch_to_previous_month = 0x7f13013b
com.example.allinone:dimen/m3_comp_elevated_card_container_elevation = 0x7f07012f
com.example.allinone:string/common_google_play_services_unsupported_text = 0x7f130067
com.example.allinone:macro/m3_comp_switch_unselected_hover_track_outline_color = 0x7f0e0139
com.example.allinone:dimen/m3_sys_motion_easing_legacy_accelerate_control_x1 = 0x7f070223
com.example.allinone:layout/dialog_fullscreen_image = 0x7f0d003b
com.example.allinone:string/bottom_sheet_behavior = 0x7f130035
com.example.allinone:layout/dialog_edit_seminar = 0x7f0d0038
com.example.allinone:layout/dialog_edit_program = 0x7f0d0037
com.example.allinone:style/TextAppearance.AppCompat = 0x7f1401c4
com.example.allinone:dimen/mtrl_shape_corner_size_medium_component = 0x7f070308
com.example.allinone:layout/dialog_edit_lesson = 0x7f0d0036
com.example.allinone:layout/dialog_edit_investment = 0x7f0d0035
com.example.allinone:layout/dialog_add_task = 0x7f0d0034
com.example.allinone:drawable/mtrl_navigation_bar_item_background = 0x7f080197
com.example.allinone:string/edit_note = 0x7f130097
com.example.allinone:layout/dialog_add_program = 0x7f0d0032
com.example.allinone:drawable/exo_styled_controls_fullscreen_exit = 0x7f0800eb
com.example.allinone:layout/design_text_input_start_icon = 0x7f0d0030
com.example.allinone:color/material_personalized_color_control_highlight = 0x7f0602bd
com.example.allinone:style/ExoStyledControls.TimeText.Position = 0x7f14014b
com.example.allinone:layout/design_navigation_menu_item = 0x7f0d002e
com.example.allinone:macro/m3_comp_filter_chip_label_text_type = 0x7f0e0058
com.example.allinone:dimen/exo_styled_bottom_bar_time_padding = 0x7f0700ac
com.example.allinone:id/triangle = 0x7f0a03af
com.example.allinone:layout/abc_action_bar_up_container = 0x7f0d0001
com.example.allinone:layout/design_navigation_item_subheader = 0x7f0d002c
com.example.allinone:attr/materialCalendarMonthNavigationButton = 0x7f040315
com.example.allinone:layout/design_navigation_item_separator = 0x7f0d002b
com.example.allinone:string/close_sheet = 0x7f130051
com.example.allinone:animator/fragment_open_enter = 0x7f020007
com.example.allinone:attr/show_shuffle_button = 0x7f040415
com.example.allinone:layout/design_navigation_item_header = 0x7f0d002a
com.example.allinone:style/ThemeOverlay.Material3.BottomAppBar.Legacy = 0x7f1402c6
com.example.allinone:attr/constraints = 0x7f04014a
com.example.allinone:layout/design_menu_item_action_area = 0x7f0d0028
com.example.allinone:layout/design_layout_snackbar_include = 0x7f0d0025
com.example.allinone:id/src_atop = 0x7f0a0331
com.example.allinone:layout/design_layout_snackbar = 0x7f0d0024
com.example.allinone:layout/custom_dialog = 0x7f0d0021
com.example.allinone:layout/browser_actions_context_menu_row = 0x7f0d0020
com.example.allinone:integer/m3_sys_motion_duration_long3 = 0x7f0b001a
com.example.allinone:layout/browser_actions_context_menu_page = 0x7f0d001f
com.example.allinone:layout/item_backup = 0x7f0d0063
com.example.allinone:layout/activity_main = 0x7f0d001e
com.example.allinone:attr/actionTextColorAlpha = 0x7f040025
com.example.allinone:macro/m3_comp_outlined_text_field_hover_supporting_text_color = 0x7f0e00bf
com.example.allinone:layout/activity_edit_note = 0x7f0d001d
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant22 = 0x7f0600fd
com.example.allinone:layout/abc_tooltip = 0x7f0d001b
com.example.allinone:layout/abc_search_view = 0x7f0d0019
com.example.allinone:dimen/exo_icon_padding_bottom = 0x7f070099
com.example.allinone:style/Base.Widget.AppCompat.Toolbar = 0x7f1400fd
com.example.allinone:layout/abc_search_dropdown_item_icons_2line = 0x7f0d0018
com.example.allinone:style/ShapeAppearance.Material3.Tooltip = 0x7f1401aa
com.example.allinone:style/ExoMediaButton.Pause = 0x7f140130
com.example.allinone:layout/abc_screen_simple_overlay_action_mode = 0x7f0d0016
com.example.allinone:layout/abc_popup_menu_item_layout = 0x7f0d0013
com.example.allinone:dimen/mtrl_high_ripple_pressed_alpha = 0x7f0702e2
com.example.allinone:layout/abc_expanded_menu_layout = 0x7f0d000d
com.example.allinone:id/open_search_view_clear_button = 0x7f0a028f
com.example.allinone:layout/abc_cascading_menu_item_layout = 0x7f0d000b
com.example.allinone:layout/abc_alert_dialog_title_material = 0x7f0d000a
com.example.allinone:drawable/exo_styled_controls_repeat_one = 0x7f0800f4
com.example.allinone:layout/abc_alert_dialog_button_bar_material = 0x7f0d0008
com.example.allinone:color/m3_ref_palette_dynamic_primary40 = 0x7f060114
com.example.allinone:layout/abc_activity_chooser_view_list_item = 0x7f0d0007
com.example.allinone:id/startVertical = 0x7f0a033d
com.example.allinone:layout/abc_action_menu_item_layout = 0x7f0d0002
com.example.allinone:interpolator/mtrl_linear_out_slow_in = 0x7f0c0011
com.example.allinone:interpolator/mtrl_fast_out_slow_in = 0x7f0c000f
com.example.allinone:interpolator/mtrl_fast_out_linear_in = 0x7f0c000e
com.example.allinone:interpolator/m3_sys_motion_easing_emphasized_accelerate = 0x7f0c0008
com.example.allinone:string/exo_track_surround = 0x7f1300f0
com.example.allinone:attr/behavior_autoShrink = 0x7f040074
com.example.allinone:interpolator/fast_out_slow_in = 0x7f0c0006
com.example.allinone:drawable/ic_format_italic = 0x7f080131
com.example.allinone:string/delete_student_confirmation = 0x7f130085
com.example.allinone:string/add_receipt = 0x7f130021
com.example.allinone:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0c0004
com.example.allinone:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0c0002
com.example.allinone:integer/show_password_duration = 0x7f0b0047
com.example.allinone:style/TextAppearance.Material3.BodySmall = 0x7f14021c
com.example.allinone:integer/mtrl_view_visible = 0x7f0b0046
com.example.allinone:attr/iconPadding = 0x7f040253
com.example.allinone:style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton = 0x7f1401bf
com.example.allinone:string/material_timepicker_am = 0x7f13016e
com.example.allinone:integer/mtrl_view_gone = 0x7f0b0044
com.example.allinone:integer/mtrl_switch_track_viewport_height = 0x7f0b0041
com.example.allinone:styleable/AppBarLayout = 0x7f15000d
com.example.allinone:color/on_surface_color = 0x7f060339
com.example.allinone:integer/mtrl_switch_thumb_viewport_center_coordinate = 0x7f0b003f
com.example.allinone:integer/mtrl_switch_thumb_pressed_duration = 0x7f0b003e
com.example.allinone:integer/mtrl_switch_thumb_pre_morphing_duration = 0x7f0b003d
com.example.allinone:integer/mtrl_switch_thumb_post_morphing_duration = 0x7f0b003c
com.example.allinone:attr/colorOnSecondary = 0x7f040118
com.example.allinone:integer/mtrl_chip_anim_duration = 0x7f0b003a
com.example.allinone:style/Base.Theme.SplashScreen = 0x7f140078
com.example.allinone:attr/textInputFilledStyle = 0x7f0404ba
com.example.allinone:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f1400dc
com.example.allinone:string/mtrl_picker_text_input_day_abbr = 0x7f1301a2
com.example.allinone:integer/material_motion_path = 0x7f0b0031
com.example.allinone:color/m3_navigation_bar_item_with_indicator_label_tint = 0x7f0600d1
com.example.allinone:integer/material_motion_duration_short_1 = 0x7f0b002f
com.example.allinone:integer/material_motion_duration_medium_2 = 0x7f0b002e
com.example.allinone:integer/material_motion_duration_medium_1 = 0x7f0b002d
com.example.allinone:color/m3_text_button_foreground_color_selector = 0x7f060242
com.example.allinone:integer/material_motion_duration_long_1 = 0x7f0b002b
com.example.allinone:style/Base.ThemeOverlay.Material3.AutoCompleteTextView = 0x7f140082
com.example.allinone:integer/m3_sys_shape_corner_full_corner_family = 0x7f0b0027
com.example.allinone:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f140333
com.example.allinone:integer/m3_sys_shape_corner_extra_small_corner_family = 0x7f0b0026
com.example.allinone:layout/fragment_transactions_overview = 0x7f0d005a
com.example.allinone:integer/m3_sys_motion_duration_short3 = 0x7f0b0022
com.example.allinone:integer/m3_sys_motion_duration_medium3 = 0x7f0b001e
com.example.allinone:color/m3_selection_control_ripple_color_selector = 0x7f060190
com.example.allinone:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0c0003
com.example.allinone:integer/m3_sys_motion_duration_medium2 = 0x7f0b001d
com.example.allinone:attr/titleTextColor = 0x7f0404f4
com.example.allinone:integer/m3_sys_motion_duration_long4 = 0x7f0b001b
com.example.allinone:integer/m3_sys_motion_duration_long1 = 0x7f0b0018
com.example.allinone:layout/mtrl_picker_actions = 0x7f0d00a6
com.example.allinone:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f1400eb
com.example.allinone:drawable/ic_investment = 0x7f08013d
com.example.allinone:integer/m3_sys_motion_duration_extra_long3 = 0x7f0b0016
com.example.allinone:integer/m3_btn_anim_delay_ms = 0x7f0b000f
com.example.allinone:integer/exo_media_button_opacity_percentage_disabled = 0x7f0b000a
com.example.allinone:integer/design_snackbar_text_max_lines = 0x7f0b0008
com.example.allinone:integer/config_tooltipAnimTime = 0x7f0b0006
com.example.allinone:integer/config_navAnimTime = 0x7f0b0005
com.example.allinone:integer/cancel_button_image_alpha = 0x7f0b0004
com.example.allinone:integer/bottom_sheet_slide_duration = 0x7f0b0003
com.example.allinone:integer/app_bar_elevation_anim_duration = 0x7f0b0002
com.example.allinone:dimen/m3_comp_assist_chip_flat_container_elevation = 0x7f070121
com.example.allinone:integer/abc_config_activityShortDur = 0x7f0b0001
com.example.allinone:style/Widget.MaterialComponents.BottomSheet.Modal = 0x7f140442
com.example.allinone:attr/titleEnabled = 0x7f0404eb
com.example.allinone:id/x_left = 0x7f0a03e6
com.example.allinone:color/m3_sys_color_dark_inverse_surface = 0x7f0601a0
com.example.allinone:id/wtSeminarsFragment = 0x7f0a03e4
com.example.allinone:string/mtrl_checkbox_button_path_group_name = 0x7f13017c
com.example.allinone:id/wtRegisterFragment = 0x7f0a03e3
com.example.allinone:string/fallback_menu_item_open_in_browser = 0x7f1300f9
com.example.allinone:id/wtLessonsFragment = 0x7f0a03e2
com.example.allinone:id/wtBottomNavigation = 0x7f0a03e0
com.example.allinone:string/toggle_view = 0x7f13023c
com.example.allinone:integer/m3_sys_motion_duration_extra_long2 = 0x7f0b0015
com.example.allinone:attr/show_fastforward_button = 0x7f040411
com.example.allinone:id/wrapped_composition_tag = 0x7f0a03df
com.example.allinone:attr/dayInvalidStyle = 0x7f040182
com.example.allinone:id/action_toggle_view = 0x7f0a004c
com.example.allinone:id/wrap_content_constrained = 0x7f0a03de
com.example.allinone:id/workout_name_text = 0x7f0a03d9
com.example.allinone:id/workout_exercise = 0x7f0a03d8
com.example.allinone:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f14017f
com.example.allinone:style/TextAppearance.AppCompat.Small.Inverse = 0x7f1401db
com.example.allinone:attr/materialTimePickerTitleStyle = 0x7f04032f
com.example.allinone:macro/m3_comp_outlined_text_field_container_shape = 0x7f0e00b1
com.example.allinone:id/search_mag_icon = 0x7f0a0305
com.example.allinone:id/workout_dashboard = 0x7f0a03d5
com.example.allinone:id/with_icon = 0x7f0a03d3
com.example.allinone:string/abc_searchview_description_search = 0x7f130015
com.example.allinone:id/withText = 0x7f0a03d2
com.example.allinone:id/wide = 0x7f0a03d1
com.example.allinone:id/when_playing = 0x7f0a03d0
com.example.allinone:id/voiceNoteTitle = 0x7f0a03cc
com.example.allinone:attr/hide_during_ads = 0x7f040245
com.example.allinone:id/visible = 0x7f0a03c6
com.example.allinone:string/recording = 0x7f1301eb
com.example.allinone:id/swipe_refresh_layout = 0x7f0a0352
com.example.allinone:id/view_tree_view_model_store_owner = 0x7f0a03c5
com.example.allinone:dimen/m3_comp_filled_card_container_elevation = 0x7f070149
com.example.allinone:id/view_tree_saved_state_registry_owner = 0x7f0a03c4
com.example.allinone:id/view_tree_lifecycle_owner = 0x7f0a03c2
com.example.allinone:styleable/SplitPairRule = 0x7f150096
com.example.allinone:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f14004a
com.example.allinone:id/view_offset_helper = 0x7f0a03c0
com.example.allinone:string/exo_controls_vr_description = 0x7f1300d8
com.example.allinone:color/material_dynamic_primary70 = 0x7f060285
com.example.allinone:id/video_decoder_gl_surface_view = 0x7f0a03bf
com.example.allinone:id/up = 0x7f0a03ba
com.example.allinone:id/uniform = 0x7f0a03b8
com.example.allinone:string/exo_download_notification_channel_name = 0x7f1300dd
com.example.allinone:id/underlineButton = 0x7f0a03b7
com.example.allinone:id/unchecked = 0x7f0a03b6
com.example.allinone:attr/cornerSizeTopLeft = 0x7f040169
com.example.allinone:color/ai_message_bg = 0x7f06001b
com.example.allinone:id/typeLayout = 0x7f0a03b3
com.example.allinone:style/Widget.Material3.FloatingActionButton.Large.Tertiary = 0x7f1403d2
com.example.allinone:id/typeInput = 0x7f0a03b2
com.example.allinone:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f140366
com.example.allinone:macro/m3_comp_primary_navigation_tab_with_label_text_active_label_text_color = 0x7f0e00d1
com.example.allinone:style/Theme.AppCompat.Empty = 0x7f140252
com.example.allinone:color/m3_highlighted_text = 0x7f0600cd
com.example.allinone:id/typeDropdown = 0x7f0a03b1
com.example.allinone:style/Widget.Material3.BottomNavigationView.ActiveIndicator = 0x7f140391
com.example.allinone:id/tuesdayChip = 0x7f0a03b0
com.example.allinone:id/transition_transform = 0x7f0a03ae
com.example.allinone:attr/subtitle = 0x7f040458
com.example.allinone:id/transition_current_scene = 0x7f0a03a8
com.example.allinone:style/Widget.Material3.Chip.Input.Elevated = 0x7f1403b1
com.example.allinone:attr/submitBackground = 0x7f040457
com.example.allinone:attr/textAppearanceHeadlineSmall = 0x7f04049c
com.example.allinone:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0c0001
com.example.allinone:id/mtrl_card_checked_layer_id = 0x7f0a023f
com.example.allinone:id/transactionsRecyclerView = 0x7f0a03a4
com.example.allinone:style/Base.TextAppearance.AppCompat.Headline = 0x7f14001f
com.example.allinone:style/ThemeOverlay.AppCompat.Light = 0x7f1402bc
com.example.allinone:id/transactionTitle = 0x7f0a03a2
com.example.allinone:id/transactionDescription = 0x7f0a03a1
com.example.allinone:style/Theme.PlayCore.Transparent = 0x7f1402af
com.example.allinone:style/CalendarDayStyle = 0x7f140128
com.example.allinone:id/transactionCountText = 0x7f0a039f
com.example.allinone:id/transactionAmount = 0x7f0a039e
com.example.allinone:id/tpslValue = 0x7f0a039d
com.example.allinone:id/tpSlText = 0x7f0a039b
com.example.allinone:style/Base.MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f140015
com.example.allinone:id/touch_outside = 0x7f0a039a
com.example.allinone:styleable/NavigationView = 0x7f15007c
com.example.allinone:id/totalInvestmentsText = 0x7f0a0399
com.example.allinone:dimen/compat_button_inset_horizontal_material = 0x7f070058
com.example.allinone:attr/fabAnimationMode = 0x7f0401e8
com.example.allinone:id/totalIncomeText = 0x7f0a0398
com.example.allinone:string/m3c_bottom_sheet_pane_title = 0x7f130128
com.example.allinone:id/totalExpenseText = 0x7f0a0397
com.example.allinone:integer/mtrl_btn_anim_delay_ms = 0x7f0b0033
com.example.allinone:id/topCategoriesRecyclerView = 0x7f0a0394
com.example.allinone:id/top = 0x7f0a0393
com.example.allinone:id/toolbar_title = 0x7f0a0391
com.example.allinone:attr/dropDownListViewStyle = 0x7f0401ae
com.example.allinone:id/toolbar = 0x7f0a0390
com.example.allinone:id/title_text = 0x7f0a038e
com.example.allinone:style/Widget.AppCompat.ActionBar.TabView = 0x7f140331
com.example.allinone:id/title_template = 0x7f0a038d
com.example.allinone:string/exo_track_selection_title_video = 0x7f1300ee
com.example.allinone:id/timeText = 0x7f0a0389
com.example.allinone:style/ExoMediaButton.Next = 0x7f14012f
com.example.allinone:string/brush_size = 0x7f13003c
com.example.allinone:id/timeSlash = 0x7f0a0388
com.example.allinone:style/Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance = 0x7f1404a6
com.example.allinone:id/notesEditText = 0x7f0a027e
com.example.allinone:macro/m3_comp_navigation_bar_active_hover_icon_color = 0x7f0e0062
com.example.allinone:id/time = 0x7f0a0387
com.example.allinone:id/texture_view = 0x7f0a0383
com.example.allinone:id/textinput_suffix_text = 0x7f0a0382
com.example.allinone:id/textinput_placeholder = 0x7f0a0380
com.example.allinone:id/textSpacerNoButtons = 0x7f0a0373
com.example.allinone:layout/mtrl_alert_dialog_title = 0x7f0d0094
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton = 0x7f14046d
com.example.allinone:id/textinput_helper_text = 0x7f0a037f
com.example.allinone:string/group_updated = 0x7f130108
com.example.allinone:id/textinput_error = 0x7f0a037e
com.example.allinone:macro/m3_comp_search_view_container_color = 0x7f0e00f1
com.example.allinone:id/textinput_counter = 0x7f0a037d
com.example.allinone:id/text_input_start_icon = 0x7f0a037c
com.example.allinone:macro/m3_comp_snackbar_container_color = 0x7f0e0113
com.example.allinone:id/text_input_error_icon = 0x7f0a037b
com.example.allinone:id/text_input_end_icon = 0x7f0a037a
com.example.allinone:string/m3_sys_motion_easing_emphasized_path_data = 0x7f13011c
com.example.allinone:id/textUserMessage = 0x7f0a0377
com.example.allinone:color/m3_timepicker_button_ripple_color = 0x7f06024a
com.example.allinone:dimen/exo_settings_text_height = 0x7f0700a4
com.example.allinone:id/textTop = 0x7f0a0376
com.example.allinone:macro/m3_comp_switch_unselected_track_outline_color = 0x7f0e0141
com.example.allinone:id/textStart = 0x7f0a0375
com.example.allinone:style/Widget.MaterialComponents.TextInputEditText.FilledBox = 0x7f140490
com.example.allinone:layout/mtrl_calendar_year = 0x7f0d00a2
com.example.allinone:style/Base.V14.Theme.MaterialComponents.Light.Bridge = 0x7f140099
com.example.allinone:id/textSourceScore = 0x7f0a0372
com.example.allinone:id/textEnd = 0x7f0a036e
com.example.allinone:style/Widget.Material3.MaterialCalendar.HeaderSelection = 0x7f1403eb
com.example.allinone:id/textConfidence = 0x7f0a036d
com.example.allinone:layout/material_clockface_view = 0x7f0d008a
com.example.allinone:style/TextAppearance.Material3.ActionBar.Subtitle = 0x7f140218
com.example.allinone:layout/item_file_structure = 0x7f0d006b
com.example.allinone:id/textAITimestamp = 0x7f0a036c
com.example.allinone:id/textAIMessage = 0x7f0a036b
com.example.allinone:id/text2 = 0x7f0a036a
com.example.allinone:id/taskDescriptionInput = 0x7f0a0367
com.example.allinone:attr/layout_constraintGuide_begin = 0x7f0402b2
com.example.allinone:id/takeProfitLayout = 0x7f0a0366
com.example.allinone:id/takeProfitInput = 0x7f0a0365
com.example.allinone:style/ExoStyledControls.Button = 0x7f140136
com.example.allinone:style/TextAppearance.M3.Sys.Typescale.LabelLarge = 0x7f140212
com.example.allinone:id/tag_window_insets_animation_callback = 0x7f0a0364
com.example.allinone:dimen/m3_comp_extended_fab_primary_focus_container_elevation = 0x7f070133
com.example.allinone:string/m3c_time_picker_minute = 0x7f130154
com.example.allinone:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge = 0x7f140198
com.example.allinone:style/Base.Widget.MaterialComponents.PopupMenu.Overflow = 0x7f14011c
com.example.allinone:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f140357
com.example.allinone:id/tag_unhandled_key_listeners = 0x7f0a0363
com.example.allinone:id/tag_state_description = 0x7f0a0360
com.example.allinone:attr/gapBetweenBars = 0x7f040228
com.example.allinone:id/tag_screen_reader_focusable = 0x7f0a035f
com.example.allinone:id/tag_on_receive_content_mime_types = 0x7f0a035e
com.example.allinone:drawable/mtrl_checkbox_button_unchecked_checked = 0x7f08018c
com.example.allinone:id/tag_accessibility_pane_title = 0x7f0a035b
com.example.allinone:color/notification_material_background_media_default_color = 0x7f060338
com.example.allinone:string/m3_exceed_max_badge_text_suffix = 0x7f130114
com.example.allinone:color/abc_btn_colored_borderless_text_material = 0x7f060002
com.example.allinone:id/symbolText = 0x7f0a0353
com.example.allinone:id/swipe_refresh = 0x7f0a0351
com.example.allinone:id/sundayChip = 0x7f0a034d
com.example.allinone:drawable/$m3_avd_hide_password__1 = 0x7f080008
com.example.allinone:id/layoutSources = 0x7f0a01e8
com.example.allinone:id/studentDropdown = 0x7f0a0346
com.example.allinone:style/ThemeOverlay.Material3.Dark.ActionBar = 0x7f1402d4
com.example.allinone:dimen/mtrl_progress_circular_radius = 0x7f0702fd
com.example.allinone:id/stopLossInput = 0x7f0a0343
com.example.allinone:id/stop = 0x7f0a0342
com.example.allinone:macro/m3_comp_switch_disabled_selected_icon_color = 0x7f0e011a
com.example.allinone:id/status_bar_latest_event_content = 0x7f0a0341
com.example.allinone:styleable/NavHostFragment = 0x7f150077
com.example.allinone:dimen/exo_small_icon_padding_horizontal = 0x7f0700a7
com.example.allinone:id/statusIndicator = 0x7f0a0340
com.example.allinone:dimen/mtrl_calendar_header_content_padding = 0x7f0702a3
com.example.allinone:string/m3_ref_typeface_plain_regular = 0x7f130118
com.example.allinone:layout/item_wt_student = 0x7f0d007d
com.example.allinone:id/staticLayout = 0x7f0a033e
com.example.allinone:id/startToEnd = 0x7f0a033c
com.example.allinone:id/startTimeInput = 0x7f0a033a
com.example.allinone:id/startTimeField = 0x7f0a0339
com.example.allinone:attr/cardUseCompatPadding = 0x7f0400ae
com.example.allinone:id/fitEnd = 0x7f0a018a
com.example.allinone:id/startHorizontal = 0x7f0a0338
com.example.allinone:style/Base.Theme.MaterialComponents.Light.Dialog.Alert = 0x7f140073
com.example.allinone:drawable/abc_textfield_default_mtrl_alpha = 0x7f080073
com.example.allinone:id/startDate = 0x7f0a0336
com.example.allinone:id/start = 0x7f0a0335
com.example.allinone:attr/customReference = 0x7f04017e
com.example.allinone:color/m3_sys_color_on_tertiary_fixed = 0x7f060233
com.example.allinone:id/standard = 0x7f0a0334
com.example.allinone:id/src_in = 0x7f0a0332
com.example.allinone:layout/abc_action_menu_layout = 0x7f0d0003
com.example.allinone:id/square = 0x7f0a0330
com.example.allinone:id/spring = 0x7f0a032f
com.example.allinone:color/material_dynamic_neutral90 = 0x7f06026d
com.example.allinone:id/spread_inside = 0x7f0a032e
com.example.allinone:styleable/MaterialButton = 0x7f150058
com.example.allinone:id/spread = 0x7f0a032d
com.example.allinone:id/splashscreen_icon_view = 0x7f0a032a
com.example.allinone:id/categoryAutoComplete = 0x7f0a00a8
com.example.allinone:id/spherical_gl_surface_view = 0x7f0a0329
com.example.allinone:id/spacer = 0x7f0a0327
com.example.allinone:id/south = 0x7f0a0326
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant12 = 0x7f0600fa
com.example.allinone:id/snapMargins = 0x7f0a0325
com.example.allinone:id/snap = 0x7f0a0324
com.example.allinone:id/sliding_pane_layout = 0x7f0a0321
com.example.allinone:id/slide = 0x7f0a031f
com.example.allinone:style/ExoStyledControls.Button.Bottom.RepeatToggle = 0x7f14013e
com.example.allinone:id/skipped = 0x7f0a031e
com.example.allinone:macro/m3_comp_navigation_rail_inactive_focus_state_layer_color = 0x7f0e009a
com.example.allinone:id/sizeValue = 0x7f0a031c
com.example.allinone:color/m3_timepicker_secondary_text_button_text_color = 0x7f060251
com.example.allinone:string/androidx_startup = 0x7f13002d
com.example.allinone:id/sizeLabel = 0x7f0a031b
com.example.allinone:id/showTitle = 0x7f0a0319
com.example.allinone:id/showHome = 0x7f0a0318
com.example.allinone:id/showCustom = 0x7f0a0317
com.example.allinone:dimen/mtrl_exposed_dropdown_menu_popup_elevation = 0x7f0702c8
com.example.allinone:string/exo_controls_rewind_description = 0x7f1300d0
com.example.allinone:style/Theme.AppCompat.Dialog.Alert = 0x7f14024f
com.example.allinone:attr/selectableItemBackground = 0x7f0403f5
com.example.allinone:id/sharedValueSet = 0x7f0a0314
com.example.allinone:layout/abc_action_bar_title_item = 0x7f0d0000
com.example.allinone:id/shareNoteButton = 0x7f0a0312
com.example.allinone:style/Base.V21.Theme.MaterialComponents = 0x7f1400a8
com.example.allinone:macro/m3_comp_elevated_card_container_color = 0x7f0e002a
com.example.allinone:id/seminarsRecyclerView = 0x7f0a0310
com.example.allinone:id/seminarTimeText = 0x7f0a030f
com.example.allinone:id/seminarNameText = 0x7f0a030e
com.example.allinone:id/seminarDescriptionText = 0x7f0a030d
com.example.allinone:drawable/exo_styled_controls_rewind = 0x7f0800f5
com.example.allinone:macro/m3_comp_date_picker_modal_container_shape = 0x7f0e000e
com.example.allinone:attr/layout_constraintLeft_creator = 0x7f0402bd
com.example.allinone:id/search_go_btn = 0x7f0a0304
com.example.allinone:attr/itemTextAppearanceActiveBoldEnabled = 0x7f04028a
com.example.allinone:layout/exo_track_selection_dialog = 0x7f0d0052
com.example.allinone:id/search_bar = 0x7f0a0300
com.example.allinone:macro/m3_comp_time_picker_period_selector_selected_pressed_state_layer_color = 0x7f0e0159
com.example.allinone:id/scrollIndicatorDown = 0x7f0a02fb
com.example.allinone:string/delete_student = 0x7f130084
com.example.allinone:drawable/abc_list_pressed_holo_dark = 0x7f080051
com.example.allinone:id/screen = 0x7f0a02f9
com.example.allinone:id/sawtooth = 0x7f0a02f7
com.example.allinone:id/save_overlay_view = 0x7f0a02f6
com.example.allinone:id/save_non_transition_alpha = 0x7f0a02f5
com.example.allinone:style/Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1400a2
com.example.allinone:id/saveButton = 0x7f0a02f3
com.example.allinone:dimen/notification_subtext_size = 0x7f07033d
com.example.allinone:id/saturdayChip = 0x7f0a02f2
com.example.allinone:id/rounded = 0x7f0a02ef
com.example.allinone:id/roiValue = 0x7f0a02ee
com.example.allinone:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f1401e9
com.example.allinone:id/roiLabel = 0x7f0a02ed
com.example.allinone:id/italic = 0x7f0a01df
com.example.allinone:string/title_investments = 0x7f130235
com.example.allinone:id/rightToLeft = 0x7f0a02ea
com.example.allinone:id/reverseSawtooth = 0x7f0a02e8
com.example.allinone:macro/m3_comp_radio_button_selected_pressed_state_layer_color = 0x7f0e00de
com.example.allinone:id/recycler_view = 0x7f0a02e4
com.example.allinone:color/m3_card_ripple_color = 0x7f0600ad
com.example.allinone:id/ratio = 0x7f0a02e1
com.example.allinone:id/progress_text = 0x7f0a02df
com.example.allinone:string/color_red = 0x7f13005a
com.example.allinone:id/progress_indicator = 0x7f0a02de
com.example.allinone:drawable/ic_dashboard = 0x7f080122
com.example.allinone:style/Base.V24.Theme.Material3.Dark = 0x7f1400b4
com.example.allinone:macro/m3_comp_navigation_rail_inactive_icon_color = 0x7f0e009c
com.example.allinone:id/progress_circular = 0x7f0a02dc
com.example.allinone:id/progressBar = 0x7f0a02da
com.example.allinone:id/program_name_input = 0x7f0a02d8
com.example.allinone:id/program_description_input = 0x7f0a02d6
com.example.allinone:id/profitRadio = 0x7f0a02d5
com.example.allinone:macro/m3_comp_outlined_text_field_disabled_label_text_color = 0x7f0e00b3
com.example.allinone:id/dateText = 0x7f0a00ea
com.example.allinone:id/profileImageView = 0x7f0a02d3
com.example.allinone:id/pressed = 0x7f0a02d0
com.example.allinone:id/postTypeLabel = 0x7f0a02cf
com.example.allinone:attr/colorBackgroundFloating = 0x7f040104
com.example.allinone:id/postType = 0x7f0a02ce
com.example.allinone:style/Widget.Material3.Toolbar.Surface = 0x7f14042c
com.example.allinone:attr/contentDescription = 0x7f04014c
com.example.allinone:id/postLinkLabel = 0x7f0a02cd
com.example.allinone:dimen/mtrl_slider_label_padding = 0x7f07030b
com.example.allinone:macro/m3_comp_navigation_drawer_inactive_hover_icon_color = 0x7f0e0089
com.example.allinone:id/postLink = 0x7f0a02cc
com.example.allinone:dimen/m3_comp_search_bar_hover_state_layer_opacity = 0x7f070196
com.example.allinone:integer/m3_sys_motion_duration_medium4 = 0x7f0b001f
com.example.allinone:id/postLayout = 0x7f0a02cb
com.example.allinone:string/mtrl_picker_toggle_to_calendar_input_mode = 0x7f1301a6
com.example.allinone:id/postInsights = 0x7f0a02ca
com.example.allinone:id/postDateLabel = 0x7f0a02c8
com.example.allinone:color/m3_dynamic_dark_hint_foreground = 0x7f0600c0
com.example.allinone:id/postCaptionLabel = 0x7f0a02c6
com.example.allinone:id/positionsRecyclerView = 0x7f0a02c3
com.example.allinone:style/ThemeOverlay.MaterialComponents.Dialog = 0x7f14030d
com.example.allinone:string/error_unknown = 0x7f1300ba
com.example.allinone:dimen/mtrl_bottomappbar_height = 0x7f07027d
com.example.allinone:string/saving = 0x7f1301fb
com.example.allinone:id/positionSideText = 0x7f0a02c1
com.example.allinone:attr/dynamicColorThemeOverlay = 0x7f0401b1
com.example.allinone:id/positionDetailsText = 0x7f0a02c0
com.example.allinone:drawable/design_password_eye = 0x7f0800aa
com.example.allinone:id/positionAmtText = 0x7f0a02bf
com.example.allinone:string/exo_track_role_alternate = 0x7f1300e6
com.example.allinone:anim/design_bottom_sheet_slide_in = 0x7f010018
com.example.allinone:id/positionAmtLabelText = 0x7f0a02be
com.example.allinone:style/ExoStyledControls.Button.Bottom.VR = 0x7f140141
com.example.allinone:id/position = 0x7f0a02bd
com.example.allinone:attr/rangeFillColor = 0x7f0403cc
com.example.allinone:attr/endIconTint = 0x7f0401c1
com.example.allinone:dimen/design_snackbar_padding_vertical = 0x7f070088
com.example.allinone:id/pnlValue = 0x7f0a02ba
com.example.allinone:id/pnlTitleText = 0x7f0a02b9
com.example.allinone:id/playPauseButton = 0x7f0a02b6
com.example.allinone:color/m3_ref_palette_secondary10 = 0x7f060176
com.example.allinone:id/pinEditText = 0x7f0a02b5
com.example.allinone:id/immediateStop = 0x7f0a01c3
com.example.allinone:dimen/m3_comp_filled_autocomplete_menu_container_elevation = 0x7f070146
com.example.allinone:id/tpslLabel = 0x7f0a039c
com.example.allinone:id/pin = 0x7f0a02b4
com.example.allinone:attr/shapeCornerFamily = 0x7f040404
com.example.allinone:macro/m3_comp_navigation_drawer_active_hover_label_text_color = 0x7f0e007c
com.example.allinone:style/Widget.Design.AppBarLayout = 0x7f140378
com.example.allinone:layout/mtrl_picker_text_input_date_range = 0x7f0d00af
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider = 0x7f14046e
com.example.allinone:id/pending_operations_count = 0x7f0a02ad
com.example.allinone:id/path = 0x7f0a02a9
com.example.allinone:id/parallax = 0x7f0a02a3
com.example.allinone:id/paginationControls = 0x7f0a02a1
com.example.allinone:style/Widget.Material3.FloatingActionButton.Small.Tertiary = 0x7f1403d8
com.example.allinone:id/page_title = 0x7f0a02a0
com.example.allinone:layout/mtrl_alert_select_dialog_multichoice = 0x7f0d0096
com.example.allinone:id/pageIndicator = 0x7f0a029f
com.example.allinone:attr/colorPrimaryInverse = 0x7f04012a
com.example.allinone:id/overshoot = 0x7f0a029d
com.example.allinone:style/Theme.Hidden = 0x7f140261
com.example.allinone:macro/m3_comp_outlined_text_field_disabled_outline_color = 0x7f0e00b4
com.example.allinone:color/m3_textfield_stroke_color = 0x7f060248
com.example.allinone:id/outward = 0x7f0a029c
com.example.allinone:integer/m3_sys_motion_duration_medium1 = 0x7f0b001c
com.example.allinone:color/abc_secondary_text_material_light = 0x7f060012
com.example.allinone:color/m3_tonal_button_ripple_color_selector = 0x7f060253
com.example.allinone:string/delete = 0x7f130078
com.example.allinone:attr/textEndPadding = 0x7f0404b6
com.example.allinone:id/open_search_view_search_prefix = 0x7f0a0297
com.example.allinone:id/open_search_view_header_container = 0x7f0a0294
com.example.allinone:id/open_search_view_dummy_toolbar = 0x7f0a0292
com.example.allinone:style/ThemeOverlay.Material3.SideSheetDialog = 0x7f1402f4
com.example.allinone:id/open_search_view_divider = 0x7f0a0291
com.example.allinone:id/open_search_bar_text_view = 0x7f0a028d
com.example.allinone:macro/m3_comp_date_picker_modal_date_selected_container_color = 0x7f0e0010
com.example.allinone:id/onInterceptTouchReturnSwipe = 0x7f0a028b
com.example.allinone:id/onAttachStateChangeListener = 0x7f0a0289
com.example.allinone:id/on = 0x7f0a0288
com.example.allinone:id/offline_status_view = 0x7f0a0287
com.example.allinone:macro/m3_comp_radio_button_unselected_pressed_icon_color = 0x7f0e00e4
com.example.allinone:id/offline_status_title = 0x7f0a0286
com.example.allinone:id/offline_icon = 0x7f0a0283
com.example.allinone:id/notification_main_column = 0x7f0a0280
com.example.allinone:menu/menu_tasks = 0x7f0f0006
com.example.allinone:id/noteDate = 0x7f0a027c
com.example.allinone:id/noteContent = 0x7f0a027b
com.example.allinone:id/normal = 0x7f0a0279
com.example.allinone:id/none = 0x7f0a0278
com.example.allinone:id/no_backups_text = 0x7f0a0277
com.example.allinone:id/noScroll = 0x7f0a0275
com.example.allinone:color/abc_tint_default = 0x7f060014
com.example.allinone:id/nextPageButton = 0x7f0a0273
com.example.allinone:macro/m3_comp_time_picker_time_selector_separator_type = 0x7f0e0166
com.example.allinone:attr/forceDefaultNavigationOnClickListener = 0x7f040225
com.example.allinone:id/newInvestmentButton = 0x7f0a0272
com.example.allinone:style/Base.V21.ThemeOverlay.Material3.SideSheetDialog = 0x7f1400ae
com.example.allinone:id/neverCompleteToStart = 0x7f0a0271
com.example.allinone:styleable/SplitPlaceholderRule = 0x7f150097
com.example.allinone:id/toggle = 0x7f0a038f
com.example.allinone:string/voice_note_deleted = 0x7f130248
com.example.allinone:id/never = 0x7f0a026f
com.example.allinone:drawable/transparent = 0x7f0801be
com.example.allinone:id/networkStatusBanner = 0x7f0a026e
com.example.allinone:string/exo_controls_next_description = 0x7f1300c6
com.example.allinone:layout/item_voice_note = 0x7f0d007a
com.example.allinone:id/navigation_bar_item_small_label_view = 0x7f0a026c
com.example.allinone:style/Base.Widget.Material3.CompoundButton.Switch = 0x7f140108
com.example.allinone:style/Animation.AppCompat.Dialog = 0x7f140002
com.example.allinone:dimen/m3_chip_corner_size = 0x7f070119
com.example.allinone:string/workout_statistics = 0x7f130259
com.example.allinone:id/navigation_bar_item_large_label_view = 0x7f0a026b
com.example.allinone:id/navigation_bar_item_icon_view = 0x7f0a0269
com.example.allinone:id/navigation_bar_item_active_indicator_view = 0x7f0a0267
com.example.allinone:color/m3_sys_color_on_tertiary_fixed_variant = 0x7f060234
com.example.allinone:id/fridayChip = 0x7f0a0196
com.example.allinone:attr/flow_maxElementsWrap = 0x7f040211
com.example.allinone:id/nav_view = 0x7f0a0264
com.example.allinone:id/nav_transactions = 0x7f0a0263
com.example.allinone:dimen/m3_bottom_sheet_drag_handle_bottom_padding = 0x7f0700e7
com.example.allinone:id/nav_transaction_report = 0x7f0a0262
com.example.allinone:color/m3_sys_color_dynamic_on_secondary_fixed = 0x7f060203
com.example.allinone:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f1401f0
com.example.allinone:style/Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f140485
com.example.allinone:attr/flow_horizontalAlign = 0x7f040209
com.example.allinone:id/nav_tasks = 0x7f0a0261
com.example.allinone:layout/select_dialog_multichoice_material = 0x7f0d00c5
com.example.allinone:string/m3c_time_picker_minute_text_field = 0x7f130157
com.example.allinone:id/nav_investments = 0x7f0a025f
com.example.allinone:dimen/m3_comp_outlined_button_disabled_outline_opacity = 0x7f070173
com.example.allinone:id/nav_instagram_business = 0x7f0a025e
com.example.allinone:string/exo_controls_shuffle_off_description = 0x7f1300d4
com.example.allinone:id/nav_host_fragment_container = 0x7f0a025d
com.example.allinone:id/lossRadio = 0x7f0a0200
com.example.allinone:id/material_clock_period_am_button = 0x7f0a0216
com.example.allinone:id/nav_host_fragment = 0x7f0a025c
com.example.allinone:layout/mtrl_calendar_vertical = 0x7f0d00a1
com.example.allinone:attr/layout_constraintHeight_min = 0x7f0402b8
com.example.allinone:id/nav_error_logs = 0x7f0a025a
com.example.allinone:dimen/design_snackbar_background_corner_radius = 0x7f070082
com.example.allinone:id/nav_clear_db = 0x7f0a0257
com.example.allinone:style/Theme.MaterialComponents.NoActionBar = 0x7f1402ad
com.example.allinone:dimen/abc_text_size_menu_material = 0x7f07004b
com.example.allinone:macro/m3_sys_color_light_surface_tint = 0x7f0e0176
com.example.allinone:string/exo_track_stereo = 0x7f1300ef
com.example.allinone:id/nav_calendar = 0x7f0a0255
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid = 0x7f140466
com.example.allinone:string/material_clock_display_divider = 0x7f13015f
com.example.allinone:id/nameInput = 0x7f0a0250
com.example.allinone:id/nameEditText = 0x7f0a024f
com.example.allinone:id/muscle_group_dropdown = 0x7f0a024e
com.example.allinone:id/multiply = 0x7f0a024d
com.example.allinone:style/TextAppearance.Compat.Notification.Media = 0x7f1401f9
com.example.allinone:id/transactionsCard = 0x7f0a03a3
com.example.allinone:color/m3_sys_color_dark_on_secondary_container = 0x7f0601a7
com.example.allinone:id/editNoteTitle = 0x7f0a0121
com.example.allinone:id/mtrl_picker_title_text = 0x7f0a024b
com.example.allinone:id/mtrl_picker_text_input_date = 0x7f0a0248
com.example.allinone:id/mtrl_picker_header_title_and_selection = 0x7f0a0246
com.example.allinone:id/mtrl_motion_snapshot_view = 0x7f0a0242
com.example.allinone:id/mtrl_calendar_selection_frame = 0x7f0a023c
com.example.allinone:id/mtrl_calendar_frame = 0x7f0a0239
com.example.allinone:style/Theme.AppCompat.Light.Dialog = 0x7f140255
com.example.allinone:id/mtrl_calendar_day_selector_frame = 0x7f0a0237
com.example.allinone:attr/clockHandColor = 0x7f0400ec
com.example.allinone:attr/primaryActivityName = 0x7f0403c2
com.example.allinone:id/month_title = 0x7f0a0233
com.example.allinone:animator/nav_default_exit_anim = 0x7f020023
com.example.allinone:id/month_navigation_next = 0x7f0a0231
com.example.allinone:id/month_navigation_fragment_toggle = 0x7f0a0230
com.example.allinone:interpolator/m3_sys_motion_easing_standard_accelerate = 0x7f0c000c
com.example.allinone:string/m3c_tooltip_pane_description = 0x7f13015b
com.example.allinone:id/imageCounterText = 0x7f0a01c0
com.example.allinone:id/month_grid = 0x7f0a022e
com.example.allinone:id/monthDropdown = 0x7f0a022d
com.example.allinone:id/mini = 0x7f0a022b
com.example.allinone:id/middle = 0x7f0a022a
com.example.allinone:id/matrix = 0x7f0a0225
com.example.allinone:macro/m3_comp_outlined_text_field_error_supporting_text_color = 0x7f0e00b7
com.example.allinone:dimen/mtrl_extended_fab_end_padding_icon = 0x7f0702d0
com.example.allinone:id/material_timepicker_view = 0x7f0a0223
com.example.allinone:style/TextAppearance.M3.Sys.Typescale.HeadlineLarge = 0x7f14020f
com.example.allinone:id/material_timepicker_ok_button = 0x7f0a0222
com.example.allinone:id/material_timepicker_mode_button = 0x7f0a0221
com.example.allinone:attr/animateRelativeTo = 0x7f04003a
com.example.allinone:id/transitionToEnd = 0x7f0a03a5
com.example.allinone:attr/motionEasingStandardInterpolator = 0x7f040367
com.example.allinone:id/material_minute_tv = 0x7f0a021d
com.example.allinone:id/material_label = 0x7f0a021b
com.example.allinone:macro/m3_comp_navigation_bar_active_label_text_color = 0x7f0e0067
com.example.allinone:id/material_clock_period_toggle = 0x7f0a0218
com.example.allinone:id/material_clock_period_pm_button = 0x7f0a0217
com.example.allinone:style/Widget.MaterialComponents.FloatingActionButton = 0x7f140460
com.example.allinone:color/navy_surface = 0x7f060333
com.example.allinone:id/staticPostLayout = 0x7f0a033f
com.example.allinone:id/material_clock_level = 0x7f0a0215
com.example.allinone:id/nav_workout = 0x7f0a0265
com.example.allinone:id/material_clock_display = 0x7f0a0211
com.example.allinone:id/match_parent = 0x7f0a0210
com.example.allinone:dimen/m3_comp_outlined_card_outline_width = 0x7f070178
com.example.allinone:id/match_constraint = 0x7f0a020f
com.example.allinone:layout/abc_list_menu_item_checkbox = 0x7f0d000e
com.example.allinone:id/masked = 0x7f0a020e
com.example.allinone:macro/m3_comp_extended_fab_surface_icon_color = 0x7f0e0033
com.example.allinone:color/m3_dynamic_dark_primary_text_disable_only = 0x7f0600c1
com.example.allinone:id/marquee = 0x7f0a020d
com.example.allinone:id/markPriceText = 0x7f0a020b
com.example.allinone:id/markPriceLabelText = 0x7f0a020a
com.example.allinone:macro/m3_comp_navigation_drawer_active_indicator_color = 0x7f0e007f
com.example.allinone:style/TextAppearance.M3.Sys.Typescale.DisplaySmall = 0x7f14020e
com.example.allinone:id/marginTypeText = 0x7f0a0207
com.example.allinone:id/marginTypeLabelText = 0x7f0a0206
com.example.allinone:drawable/ic_history = 0x7f080137
com.example.allinone:macro/m3_comp_switch_selected_hover_handle_color = 0x7f0e0125
com.example.allinone:id/marginLabel = 0x7f0a0205
com.example.allinone:color/primary_text_disabled_material_light = 0x7f060343
com.example.allinone:drawable/abc_ratingbar_indicator_material = 0x7f08005b
com.example.allinone:id/marginBalanceTitleText = 0x7f0a0203
com.example.allinone:string/exo_controls_repeat_one_description = 0x7f1300cf
com.example.allinone:id/m3_side_sheet = 0x7f0a0202
com.example.allinone:integer/material_motion_duration_short_2 = 0x7f0b0030
com.example.allinone:id/locale = 0x7f0a01ff
com.example.allinone:id/loadingProgress = 0x7f0a01fe
com.example.allinone:dimen/m3_sys_elevation_level5 = 0x7f07021a
com.example.allinone:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__2 = 0x7f08001d
com.example.allinone:id/list_item = 0x7f0a01fd
com.example.allinone:id/liqPriceValue = 0x7f0a01f9
com.example.allinone:id/lineChart = 0x7f0a01f6
com.example.allinone:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f1401e8
com.example.allinone:attr/flow_horizontalStyle = 0x7f04020c
com.example.allinone:id/line3 = 0x7f0a01f5
com.example.allinone:string/select_profile_photo = 0x7f130206
com.example.allinone:id/leverageText = 0x7f0a01f2
com.example.allinone:id/lessonsLabel = 0x7f0a01f0
com.example.allinone:id/lessonDaysLabel = 0x7f0a01ed
com.example.allinone:id/legacy = 0x7f0a01ec
com.example.allinone:id/layoutConfidence = 0x7f0a01e7
com.example.allinone:attr/show_next_button = 0x7f040412
com.example.allinone:id/jumpToStart = 0x7f0a01e3
com.example.allinone:styleable/ChipGroup = 0x7f150022
com.example.allinone:string/error_user_not_found = 0x7f1300bc
com.example.allinone:attr/popUpToInclusive = 0x7f0403b6
com.example.allinone:id/isPastInvestmentCheckbox = 0x7f0a01dd
com.example.allinone:id/inward = 0x7f0a01dc
com.example.allinone:attr/buttonIconTintMode = 0x7f0400a1
com.example.allinone:id/peekHeight = 0x7f0a02ac
com.example.allinone:id/nameTextView = 0x7f0a0253
com.example.allinone:id/investmentViewPager = 0x7f0a01d9
com.example.allinone:attr/layout_goneMarginTop = 0x7f0402da
com.example.allinone:integer/m3_sys_motion_duration_short1 = 0x7f0b0020
com.example.allinone:id/investmentType = 0x7f0a01d8
com.example.allinone:id/investmentTabLayout = 0x7f0a01d7
com.example.allinone:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
com.example.allinone:id/investmentName = 0x7f0a01d5
com.example.allinone:id/investmentDate = 0x7f0a01d1
com.example.allinone:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f140024
com.example.allinone:id/investmentCountText = 0x7f0a01cf
com.example.allinone:dimen/compat_button_inset_vertical_material = 0x7f070059
com.example.allinone:string/dest_title = 0x7f130090
com.example.allinone:color/carousel_background = 0x7f06003f
com.example.allinone:id/instagramInputLayout = 0x7f0a01cd
com.example.allinone:dimen/m3_comp_navigation_drawer_standard_container_elevation = 0x7f070169
com.example.allinone:id/instagramButton = 0x7f0a01cb
com.example.allinone:attr/motionDurationShort3 = 0x7f04035a
com.example.allinone:id/inspection_slot_table_set = 0x7f0a01ca
com.example.allinone:string/error_authentication = 0x7f1300a2
com.example.allinone:string/error_requires_recent_login = 0x7f1300b3
com.example.allinone:id/insightsCard = 0x7f0a01c8
com.example.allinone:id/info = 0x7f0a01c7
com.example.allinone:string/task_completed = 0x7f13022a
com.example.allinone:id/incomeText = 0x7f0a01c5
com.example.allinone:drawable/common_google_signin_btn_text_dark = 0x7f08009a
com.example.allinone:string/reps = 0x7f1301f3
com.example.allinone:id/included = 0x7f0a01c4
com.example.allinone:style/Widget.Material3.CircularProgressIndicator.Legacy.ExtraSmall = 0x7f1403ba
com.example.allinone:id/imagesRecyclerView = 0x7f0a01c2
com.example.allinone:id/imageContainer = 0x7f0a01bf
com.example.allinone:attr/chipSpacingVertical = 0x7f0400d9
com.example.allinone:color/mtrl_navigation_bar_colored_ripple_color = 0x7f060315
com.example.allinone:id/cache_measures = 0x7f0a009d
com.example.allinone:id/ifRoom = 0x7f0a01bb
com.example.allinone:id/icon = 0x7f0a01b8
com.example.allinone:id/horizontal_only = 0x7f0a01b7
com.example.allinone:id/north = 0x7f0a027a
com.example.allinone:id/horizontal = 0x7f0a01b6
com.example.allinone:id/honorRequest = 0x7f0a01b5
com.example.allinone:id/homeFragment = 0x7f0a01b4
com.example.allinone:id/hide_graphics_layer_in_inspector_tag = 0x7f0a01ae
com.example.allinone:drawable/exo_ic_skip_previous = 0x7f0800ca
com.example.allinone:id/linear = 0x7f0a01f7
com.example.allinone:id/header_title = 0x7f0a01ad
com.example.allinone:attr/scopeUris = 0x7f0403e5
com.example.allinone:id/group_settings = 0x7f0a01a8
com.example.allinone:id/group_divider = 0x7f0a01a7
com.example.allinone:style/CardView.Dark = 0x7f14012a
com.example.allinone:id/group_description_edit = 0x7f0a01a5
com.example.allinone:id/graph = 0x7f0a01a2
com.example.allinone:id/ghost_view_holder = 0x7f0a019f
com.example.allinone:id/futuresViewPager = 0x7f0a019d
com.example.allinone:styleable/MaterialAlertDialogTheme = 0x7f150056
com.example.allinone:style/ShapeAppearanceOverlay.Material3.Corner.Bottom = 0x7f1401b3
com.example.allinone:id/futuresTabLayout = 0x7f0a019c
com.example.allinone:id/fullscreen_header = 0x7f0a019a
com.example.allinone:id/fullscreenViewPager = 0x7f0a0199
com.example.allinone:macro/m3_comp_navigation_drawer_active_hover_icon_color = 0x7f0e007b
com.example.allinone:id/fragment_container_view_tag = 0x7f0a0195
com.example.allinone:color/abc_primary_text_disable_only_material_light = 0x7f06000a
com.example.allinone:string/add_video = 0x7f130026
com.example.allinone:id/floating = 0x7f0a0192
com.example.allinone:integer/mtrl_tab_indicator_anim_duration_ms = 0x7f0b0043
com.example.allinone:id/flip = 0x7f0a0191
com.example.allinone:integer/m3_chip_anim_duration = 0x7f0b0013
com.example.allinone:id/fixed_width = 0x7f0a0190
com.example.allinone:id/fixed_height = 0x7f0a018f
com.example.allinone:id/fitToContents = 0x7f0a018c
com.example.allinone:dimen/m3_comp_time_picker_period_selector_focus_state_layer_opacity = 0x7f0701c6
com.example.allinone:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f1400c1
com.example.allinone:dimen/m3_comp_text_button_focus_state_layer_opacity = 0x7f0701c1
com.example.allinone:id/fitStart = 0x7f0a018b
com.example.allinone:id/fit = 0x7f0a0188
com.example.allinone:id/clearDueDateButton = 0x7f0a00c1
com.example.allinone:style/MaterialAlertDialog.Material3.Body.Text.CenterStacked = 0x7f140156
com.example.allinone:style/ExoStyledControls.TimeBar = 0x7f140148
com.example.allinone:dimen/m3_navigation_rail_elevation = 0x7f0701ef
com.example.allinone:id/fill_vertical = 0x7f0a0184
com.example.allinone:id/fill_horizontal = 0x7f0a0183
com.example.allinone:macro/m3_comp_snackbar_supporting_text_type = 0x7f0e0116
com.example.allinone:style/Widget.MaterialComponents.Button.UnelevatedButton = 0x7f14044d
com.example.allinone:id/fill = 0x7f0a0182
com.example.allinone:id/expenseText = 0x7f0a017e
com.example.allinone:id/expanded_menu = 0x7f0a017d
com.example.allinone:dimen/m3_comp_primary_navigation_tab_active_hover_state_layer_opacity = 0x7f070180
com.example.allinone:id/expand_activities_button = 0x7f0a017c
com.example.allinone:dimen/m3_searchbar_outlined_stroke_width = 0x7f070203
com.example.allinone:id/exo_track_selection_view = 0x7f0a017a
com.example.allinone:style/ShapeAppearance.M3.Sys.Shape.Corner.Small = 0x7f14019e
com.example.allinone:id/exo_settings = 0x7f0a0171
com.example.allinone:color/material_personalized_hint_foreground_inverse = 0x7f0602ea
com.example.allinone:id/exo_rew_with_amount = 0x7f0a0170
com.example.allinone:id/exo_repeat_toggle = 0x7f0a016e
com.example.allinone:xml/ga_ad_services_config = 0x7f160003
com.example.allinone:layout/m3_side_sheet_dialog = 0x7f0d0083
com.example.allinone:id/exo_progress_placeholder = 0x7f0a016d
com.example.allinone:style/Base.v21.Theme.SplashScreen.Light = 0x7f140123
com.example.allinone:macro/m3_comp_time_picker_time_selector_unselected_container_color = 0x7f0e0167
com.example.allinone:string/exo_controls_fastforward_description = 0x7f1300c2
com.example.allinone:id/exo_progress = 0x7f0a016c
com.example.allinone:color/m3_sys_color_dynamic_dark_on_secondary_container = 0x7f0601c9
com.example.allinone:style/Base.ThemeOverlay.AppCompat = 0x7f14007b
com.example.allinone:string/abc_activity_chooser_view_see_all = 0x7f130004
com.example.allinone:id/exo_pause = 0x7f0a0166
com.example.allinone:id/exo_overflow_hide = 0x7f0a0163
com.example.allinone:styleable/Constraint = 0x7f15002a
com.example.allinone:styleable/CollapsingToolbarLayout_Layout = 0x7f150027
com.example.allinone:dimen/abc_dialog_fixed_width_minor = 0x7f07001f
com.example.allinone:dimen/notification_action_icon_size = 0x7f070331
com.example.allinone:macro/m3_comp_top_app_bar_medium_headline_type = 0x7f0e016e
com.example.allinone:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f1400c0
com.example.allinone:id/exo_icon = 0x7f0a015e
com.example.allinone:string/exo_track_surround_5_point_1 = 0x7f1300f1
com.example.allinone:dimen/tooltip_y_offset_non_touch = 0x7f07034f
com.example.allinone:dimen/mtrl_calendar_action_confirm_button_min_width = 0x7f070296
com.example.allinone:id/navigation_bar_item_icon_container = 0x7f0a0268
com.example.allinone:id/exo_fullscreen = 0x7f0a015d
com.example.allinone:style/Platform.V21.AppCompat = 0x7f140170
com.example.allinone:id/exo_ffwd_with_amount = 0x7f0a015c
com.example.allinone:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f140258
com.example.allinone:macro/m3_comp_navigation_drawer_inactive_hover_label_text_color = 0x7f0e008a
com.example.allinone:layout/theme_switch_layout = 0x7f0d00ca
com.example.allinone:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f0a03c3
com.example.allinone:id/exo_error_message = 0x7f0a0158
com.example.allinone:string/m3c_tooltip_long_press_label = 0x7f13015a
com.example.allinone:drawable/$ic_launcher_foreground__0 = 0x7f080006
com.example.allinone:id/exo_duration = 0x7f0a0157
com.example.allinone:id/exo_controls_background = 0x7f0a0156
com.example.allinone:layout/design_bottom_navigation_item = 0x7f0d0022
com.example.allinone:style/Base.V28.Theme.AppCompat = 0x7f1400bb
com.example.allinone:id/exo_controller_placeholder = 0x7f0a0155
com.example.allinone:id/exo_buffering = 0x7f0a0150
com.example.allinone:macro/m3_comp_primary_navigation_tab_container_color = 0x7f0e00cb
com.example.allinone:id/vertical_only = 0x7f0a03bd
com.example.allinone:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f1401f1
com.example.allinone:id/exo_bottom_bar = 0x7f0a014f
com.example.allinone:attr/layout_scrollFlags = 0x7f0402e0
com.example.allinone:id/exo_audio_track = 0x7f0a014d
com.example.allinone:id/exo_artwork = 0x7f0a014c
com.example.allinone:id/exitUntilCollapsed = 0x7f0a014a
com.example.allinone:id/exercise_reps_input = 0x7f0a0145
com.example.allinone:layout/abc_activity_chooser_view = 0x7f0d0006
com.example.allinone:id/exercise_notes_input = 0x7f0a0144
com.example.allinone:id/default_activity_button = 0x7f0a00f2
com.example.allinone:id/search_button = 0x7f0a0301
com.example.allinone:id/eventTitleInput = 0x7f0a0142
com.example.allinone:id/eventTitle = 0x7f0a0141
com.example.allinone:color/m3_sys_color_dynamic_dark_surface_container_low = 0x7f0601d9
com.example.allinone:id/eventEndTimeInput = 0x7f0a013f
com.example.allinone:id/eventDescriptionInput = 0x7f0a013e
com.example.allinone:macro/m3_comp_extended_fab_primary_icon_color = 0x7f0e002e
com.example.allinone:id/entryPriceText = 0x7f0a0139
com.example.allinone:style/Widget.Material3.PopupMenu = 0x7f140405
com.example.allinone:id/entryPriceLabelText = 0x7f0a0138
com.example.allinone:id/entryPriceLabel = 0x7f0a0137
com.example.allinone:color/m3_ref_palette_neutral87 = 0x7f060153
com.example.allinone:dimen/m3_carousel_small_item_size_min = 0x7f070117
com.example.allinone:id/supportScrollUp = 0x7f0a034e
com.example.allinone:drawable/exo_ic_check = 0x7f0800be
com.example.allinone:id/enterAlwaysCollapsed = 0x7f0a0136
com.example.allinone:layout/nav_header = 0x7f0d00b2
com.example.allinone:id/enterAlways = 0x7f0a0135
com.example.allinone:dimen/abc_action_bar_default_padding_start_material = 0x7f070004
com.example.allinone:id/end_padder = 0x7f0a0134
com.example.allinone:id/hide_in_inspector_tag = 0x7f0a01b0
com.example.allinone:id/endTimeLayout = 0x7f0a0132
com.example.allinone:id/recyclerSources = 0x7f0a02e3
com.example.allinone:macro/m3_comp_date_picker_modal_date_unselected_label_text_color = 0x7f0e0014
com.example.allinone:id/endTimeInput = 0x7f0a0131
com.example.allinone:attr/listChoiceBackgroundIndicator = 0x7f0402ea
com.example.allinone:id/empty_state = 0x7f0a012c
com.example.allinone:id/fade = 0x7f0a017f
com.example.allinone:id/emptyStateText = 0x7f0a012a
com.example.allinone:integer/google_play_services_version = 0x7f0b000c
com.example.allinone:id/x_right = 0x7f0a03e7
com.example.allinone:id/embed = 0x7f0a0128
com.example.allinone:attr/clockFaceBackgroundColor = 0x7f0400eb
com.example.allinone:id/emailInputLayout = 0x7f0a0127
com.example.allinone:macro/m3_comp_switch_selected_pressed_icon_color = 0x7f0e012b
com.example.allinone:dimen/m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity = 0x7f070183
com.example.allinone:id/emailEditText = 0x7f0a0126
com.example.allinone:integer/m3_sys_motion_duration_short4 = 0x7f0b0023
com.example.allinone:id/email = 0x7f0a0125
com.example.allinone:id/elastic = 0x7f0a0124
com.example.allinone:id/edit_query = 0x7f0a0122
com.example.allinone:id/menu_button = 0x7f0a0228
com.example.allinone:id/gone = 0x7f0a01a1
com.example.allinone:id/editNoteContent = 0x7f0a0120
com.example.allinone:id/exo_playback_speed = 0x7f0a0169
com.example.allinone:id/editButton = 0x7f0a011f
com.example.allinone:id/east = 0x7f0a011d
com.example.allinone:id/easeOut = 0x7f0a011c
com.example.allinone:id/forever = 0x7f0a0193
com.example.allinone:styleable/SwipeRefreshLayout = 0x7f15009e
com.example.allinone:id/easeInOut = 0x7f0a011b
com.example.allinone:id/easeIn = 0x7f0a011a
com.example.allinone:id/dueDateText = 0x7f0a0119
com.example.allinone:id/dueDateLabel = 0x7f0a0118
com.example.allinone:style/Base.Widget.AppCompat.PopupMenu = 0x7f1400ed
com.example.allinone:string/exo_track_selection_none = 0x7f1300eb
com.example.allinone:id/dropdown_menu = 0x7f0a0117
com.example.allinone:id/drawer_button = 0x7f0a0116
com.example.allinone:dimen/mtrl_progress_track_thickness = 0x7f070306
com.example.allinone:id/dragRight = 0x7f0a0112
com.example.allinone:dimen/abc_button_inset_vertical_material = 0x7f070013
com.example.allinone:id/dragLeft = 0x7f0a0111
com.example.allinone:id/cashFlowTitle = 0x7f0a00a6
com.example.allinone:integer/exo_media_button_opacity_percentage_enabled = 0x7f0b000b
com.example.allinone:id/dragEnd = 0x7f0a0110
com.example.allinone:layout/activity_backup = 0x7f0d001c
com.example.allinone:color/m3_ref_palette_secondary80 = 0x7f06017e
com.example.allinone:dimen/m3_bottom_nav_item_padding_bottom = 0x7f0700e4
com.example.allinone:id/jumpToEnd = 0x7f0a01e2
com.example.allinone:attr/trackInsideCornerSize = 0x7f04050e
com.example.allinone:id/dragAnticlockwise = 0x7f0a010d
com.example.allinone:id/wednesdayChip = 0x7f0a03cd
com.example.allinone:id/frost = 0x7f0a0197
com.example.allinone:drawable/abc_ic_ab_back_material = 0x7f08003e
com.example.allinone:id/divider = 0x7f0a010c
com.example.allinone:color/gray_dark = 0x7f060096
com.example.allinone:color/material_personalized_color_secondary_text = 0x7f0602d6
com.example.allinone:id/disjoint = 0x7f0a010b
com.example.allinone:id/disableScroll = 0x7f0a010a
com.example.allinone:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f1401d6
com.example.allinone:id/direct = 0x7f0a0106
com.example.allinone:color/material_dynamic_tertiary30 = 0x7f06029b
com.example.allinone:id/dialog_title = 0x7f0a0104
com.example.allinone:string/no_history_items = 0x7f1301bc
com.example.allinone:style/Base.V26.Theme.AppCompat.Light = 0x7f1400b9
com.example.allinone:id/scrollIndicatorUp = 0x7f0a02fc
com.example.allinone:color/m3_ref_palette_dynamic_primary30 = 0x7f060113
com.example.allinone:id/design_menu_item_action_area_stub = 0x7f0a00fe
com.example.allinone:id/wrap_content = 0x7f0a03dd
com.example.allinone:id/description_text = 0x7f0a00fb
com.example.allinone:id/descriptionLayout = 0x7f0a00f9
com.example.allinone:id/textinput_prefix_text = 0x7f0a0381
com.example.allinone:attr/chipGroupStyle = 0x7f0400cf
com.example.allinone:id/descriptionInput = 0x7f0a00f8
com.example.allinone:id/dependency_ordering = 0x7f0a00f7
com.example.allinone:id/deltaRelative = 0x7f0a00f6
com.example.allinone:style/Widget.MaterialComponents.TextInputLayout.FilledBox = 0x7f140494
com.example.allinone:id/selection_type = 0x7f0a030b
com.example.allinone:id/delete_button = 0x7f0a00f5
com.example.allinone:style/Widget.Material3.MaterialCalendar.HeaderLayout = 0x7f1403e9
com.example.allinone:attr/startIconScaleType = 0x7f040440
com.example.allinone:id/daysChipGroup = 0x7f0a00ee
com.example.allinone:id/dayText = 0x7f0a00ed
com.example.allinone:id/date_picker_actions = 0x7f0a00eb
com.example.allinone:dimen/mtrl_toolbar_default_height = 0x7f07032a
com.example.allinone:id/dateRangeLayout = 0x7f0a00e9
com.example.allinone:macro/m3_comp_time_picker_time_selector_separator_color = 0x7f0e0165
com.example.allinone:drawable/abc_text_cursor_material = 0x7f08006e
com.example.allinone:id/dateRangeAutoComplete = 0x7f0a00e8
com.example.allinone:id/dateInput = 0x7f0a00e6
com.example.allinone:attr/cornerSizeBottomLeft = 0x7f040167
com.example.allinone:id/custom = 0x7f0a00e1
com.example.allinone:dimen/mtrl_textinput_end_icon_margin_start = 0x7f070327
com.example.allinone:id/liqPriceLabel = 0x7f0a01f8
com.example.allinone:id/currentState = 0x7f0a00e0
com.example.allinone:dimen/mtrl_calendar_title_baseline_to_top = 0x7f0702b9
com.example.allinone:id/create_backup_button = 0x7f0a00df
com.example.allinone:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f1400e2
com.example.allinone:id/cradle = 0x7f0a00de
com.example.allinone:id/coordinator = 0x7f0a00db
com.example.allinone:id/contiguous = 0x7f0a00d9
com.example.allinone:id/content = 0x7f0a00d7
com.example.allinone:macro/m3_comp_slider_handle_color = 0x7f0e010f
com.example.allinone:attr/progressBarPadding = 0x7f0403c3
com.example.allinone:id/container = 0x7f0a00d6
com.example.allinone:macro/m3_comp_navigation_bar_inactive_focus_icon_color = 0x7f0e006c
com.example.allinone:id/consume_window_insets_tag = 0x7f0a00d5
com.example.allinone:id/constraint = 0x7f0a00d4
com.example.allinone:attr/ensureMinTouchTargetSize = 0x7f0401c5
com.example.allinone:id/endDateInput = 0x7f0a012f
com.example.allinone:id/confirm_button = 0x7f0a00d3
com.example.allinone:style/Base.Theme.MaterialComponents.Light.Bridge = 0x7f14006f
com.example.allinone:id/color_red = 0x7f0a00cf
com.example.allinone:id/color_purple = 0x7f0a00ce
com.example.allinone:id/color_blue = 0x7f0a00cb
com.example.allinone:id/collapseActionView = 0x7f0a00ca
com.example.allinone:id/coil_request_manager = 0x7f0a00c9
com.example.allinone:id/closePositionButton = 0x7f0a00c7
com.example.allinone:id/closeButton = 0x7f0a00c6
com.example.allinone:id/clip_vertical = 0x7f0a00c4
com.example.allinone:string/mtrl_picker_announce_current_selection_none = 0x7f130189
com.example.allinone:id/clear_text = 0x7f0a00c2
com.example.allinone:macro/m3_comp_switch_selected_pressed_handle_color = 0x7f0e012a
com.example.allinone:id/circle_center = 0x7f0a00c0
com.example.allinone:style/ShapeAppearance.Material3.NavigationBarView.ActiveIndicator = 0x7f1401a8
com.example.allinone:id/chronometer = 0x7f0a00bf
com.example.allinone:layout/exo_player_control_view = 0x7f0d0049
com.example.allinone:dimen/m3_divider_heavy_thickness = 0x7f0701d3
com.example.allinone:id/open_search_view_toolbar_container = 0x7f0a029a
com.example.allinone:id/chain2 = 0x7f0a00b9
com.example.allinone:id/center_horizontal = 0x7f0a00b6
com.example.allinone:id/centerInside = 0x7f0a00b5
com.example.allinone:id/categoryText = 0x7f0a00b2
com.example.allinone:id/categorySpendingCard = 0x7f0a00b1
com.example.allinone:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f140035
com.example.allinone:style/Theme.AppCompat.DayNight = 0x7f140247
com.example.allinone:id/categoryPieChart = 0x7f0a00b0
com.example.allinone:dimen/m3_ripple_pressed_alpha = 0x7f0701fd
com.example.allinone:string/call_notification_answer_video_action = 0x7f13003e
com.example.allinone:dimen/design_fab_size_mini = 0x7f070073
com.example.allinone:id/categoryNameText = 0x7f0a00ae
com.example.allinone:id/categoryLayout = 0x7f0a00ac
com.example.allinone:id/categoryChip = 0x7f0a00a9
com.example.allinone:style/Theme.MaterialComponents.Light.BottomSheetDialog = 0x7f14029e
com.example.allinone:id/eventDescription = 0x7f0a013d
com.example.allinone:id/tag_accessibility_heading = 0x7f0a035a
com.example.allinone:id/categoryAmountText = 0x7f0a00a7
com.example.allinone:id/cardUserMessage = 0x7f0a00a4
com.example.allinone:macro/m3_comp_navigation_bar_active_focus_label_text_color = 0x7f0e0060
com.example.allinone:attr/customColorDrawableValue = 0x7f040177
com.example.allinone:id/cancel_button = 0x7f0a00a2
com.example.allinone:id/cancelButton = 0x7f0a00a0
com.example.allinone:layout/item_binance_position = 0x7f0d0064
com.example.allinone:attr/colorNavigationItemSelected = 0x7f04010d
com.example.allinone:id/exo_extra_controls = 0x7f0a0159
com.example.allinone:id/callMeasure = 0x7f0a009f
com.example.allinone:string/recording_started = 0x7f1301ee
com.example.allinone:drawable/ic_clear_black_24 = 0x7f08011c
com.example.allinone:macro/m3_comp_date_picker_modal_date_selected_label_text_color = 0x7f0e0011
com.example.allinone:style/Theme.Material3.Dark.DialogWhenLarge = 0x7f140267
com.example.allinone:macro/m3_comp_time_picker_period_selector_outline_color = 0x7f0e0154
com.example.allinone:id/btn_save = 0x7f0a009a
com.example.allinone:id/labeled = 0x7f0a01e4
com.example.allinone:string/error_storage_quota_exceeded = 0x7f1300b8
com.example.allinone:id/btn_cancel = 0x7f0a0098
com.example.allinone:id/categoryName = 0x7f0a00ad
com.example.allinone:id/browser_actions_menu_view = 0x7f0a0097
com.example.allinone:color/light_gray = 0x7f0600a0
com.example.allinone:id/browser_actions_menu_item_text = 0x7f0a0095
com.example.allinone:style/ThemeOverlay.Material3.BottomAppBar = 0x7f1402c5
com.example.allinone:id/browser_actions_header_text = 0x7f0a0093
com.example.allinone:id/bounce = 0x7f0a008f
com.example.allinone:id/boldButton = 0x7f0a008c
com.example.allinone:styleable/ShapeAppearance = 0x7f15008d
com.example.allinone:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1402e0
com.example.allinone:id/bestChoice = 0x7f0a008a
com.example.allinone:id/exo_content_frame = 0x7f0a0153
com.example.allinone:attr/motionPath = 0x7f040371
com.example.allinone:id/below = 0x7f0a0089
com.example.allinone:id/beginning = 0x7f0a0088
com.example.allinone:dimen/m3_comp_outlined_text_field_focus_outline_width = 0x7f07017d
com.example.allinone:id/baseline = 0x7f0a0086
com.example.allinone:drawable/ic_checkbox = 0x7f080119
com.example.allinone:id/barrier = 0x7f0a0085
com.example.allinone:id/balanceTitleText = 0x7f0a0083
com.example.allinone:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense = 0x7f140427
com.example.allinone:color/material_personalized_color_surface_bright = 0x7f0602d9
com.example.allinone:id/backups_recycler_view = 0x7f0a0081
com.example.allinone:style/ShapeAppearance.Material3.LargeComponent = 0x7f1401a6
com.example.allinone:id/autoCompleteToStart = 0x7f0a0079
com.example.allinone:drawable/exo_styled_controls_shuffle_on = 0x7f0800f8
com.example.allinone:id/autoComplete = 0x7f0a0077
com.example.allinone:string/workout_program = 0x7f130257
com.example.allinone:string/total_amount_label = 0x7f130240
com.example.allinone:id/fitXY = 0x7f0a018d
com.example.allinone:id/attachmentPreview = 0x7f0a0074
com.example.allinone:dimen/mtrl_calendar_navigation_top_padding = 0x7f0702b2
com.example.allinone:macro/m3_comp_divider_color = 0x7f0e0028
com.example.allinone:id/arc = 0x7f0a0070
com.example.allinone:id/exo_next = 0x7f0a0162
com.example.allinone:id/applyFiltersButton = 0x7f0a006f
com.example.allinone:id/applyFilterButton = 0x7f0a006e
com.example.allinone:style/ThemeOverlay.Material3.MaterialCalendar = 0x7f1402eb
com.example.allinone:id/useLogo = 0x7f0a03bb
com.example.allinone:attr/listMenuViewStyle = 0x7f0402f0
com.example.allinone:id/appBarLayout = 0x7f0a006c
com.example.allinone:id/anticipate = 0x7f0a006b
com.example.allinone:string/no_programs = 0x7f1301bd
com.example.allinone:id/androidx_window_activity_scope = 0x7f0a0067
com.example.allinone:dimen/m3_sys_motion_easing_standard_control_x1 = 0x7f070237
com.example.allinone:style/Base.Theme.MaterialComponents.Light.Dialog = 0x7f140072
com.example.allinone:id/markPriceValue = 0x7f0a020c
com.example.allinone:id/noteTitle = 0x7f0a027d
com.example.allinone:id/androidx_compose_ui_view_composition_context = 0x7f0a0066
com.example.allinone:id/amount_text = 0x7f0a0065
com.example.allinone:id/amountText = 0x7f0a0064
com.example.allinone:attr/contentInsetLeft = 0x7f04014f
com.example.allinone:color/m3_sys_color_dynamic_light_surface_container_low = 0x7f0601fb
com.example.allinone:id/amountInput = 0x7f0a0062
com.example.allinone:attr/textBackgroundPanY = 0x7f0404b1
com.example.allinone:id/always = 0x7f0a0060
com.example.allinone:id/allStates = 0x7f0a005f
com.example.allinone:string/exo_track_unknown = 0x7f1300f3
com.example.allinone:macro/m3_comp_switch_unselected_focus_handle_color = 0x7f0e012f
com.example.allinone:id/adjust_height = 0x7f0a005a
com.example.allinone:id/add_exercise_button = 0x7f0a0059
com.example.allinone:style/ThemeOverlay.MaterialComponents.ActionBar = 0x7f1402ff
com.example.allinone:id/lessonsContainer = 0x7f0a01ef
com.example.allinone:macro/m3_comp_outlined_text_field_hover_input_text_color = 0x7f0e00bd
com.example.allinone:id/icon_group = 0x7f0a01b9
com.example.allinone:id/addSeminarFab = 0x7f0a0057
com.example.allinone:attr/animation_enabled = 0x7f04003c
com.example.allinone:macro/m3_comp_extended_fab_primary_container_shape = 0x7f0e002d
com.example.allinone:id/addIncomeButton = 0x7f0a0054
com.example.allinone:style/Base.V14.Theme.Material3.Dark = 0x7f14008c
com.example.allinone:macro/m3_comp_time_picker_headline_color = 0x7f0e0150
com.example.allinone:styleable/MaterialDivider = 0x7f15005f
com.example.allinone:drawable/exo_controls_shuffle_on = 0x7f0800ba
com.example.allinone:id/addExpenseButton = 0x7f0a0052
com.example.allinone:color/abc_tint_seek_thumb = 0x7f060016
com.example.allinone:id/addAttachmentButton = 0x7f0a0051
com.example.allinone:dimen/m3_sys_motion_easing_linear_control_y1 = 0x7f070231
com.example.allinone:id/activity_chooser_view_content = 0x7f0a004f
com.example.allinone:attr/motionEasingStandard = 0x7f040364
com.example.allinone:id/activeSwitch = 0x7f0a004e
com.example.allinone:color/m3_sys_color_dark_surface_container_high = 0x7f0601b5
com.example.allinone:id/actions = 0x7f0a004d
com.example.allinone:id/one = 0x7f0a028c
com.example.allinone:macro/m3_comp_navigation_bar_active_hover_label_text_color = 0x7f0e0063
com.example.allinone:id/action_mode_bar_stub = 0x7f0a0048
com.example.allinone:string/mtrl_checkbox_button_icon_path_checked = 0x7f130177
com.example.allinone:id/action_menu_divider = 0x7f0a0045
com.example.allinone:id/action_image = 0x7f0a0043
com.example.allinone:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f14004f
com.example.allinone:color/vector_tint_color = 0x7f060364
com.example.allinone:id/action_edit = 0x7f0a0041
com.example.allinone:id/pickDueDateButton = 0x7f0a02b2
com.example.allinone:id/action_divider = 0x7f0a0040
com.example.allinone:id/action_delete_group = 0x7f0a003f
com.example.allinone:id/BOTTOM_START = 0x7f0a0002
com.example.allinone:id/NO_DEBUG = 0x7f0a0006
com.example.allinone:id/mtrl_picker_text_input_range_end = 0x7f0a0249
com.example.allinone:dimen/abc_seekbar_track_progress_height_material = 0x7f070039
com.example.allinone:id/action_bar_title = 0x7f0a003b
com.example.allinone:id/clip_horizontal = 0x7f0a00c3
com.example.allinone:id/action_bar_subtitle = 0x7f0a003a
com.example.allinone:attr/cardViewStyle = 0x7f0400af
com.example.allinone:id/action_bar_activity_content = 0x7f0a0036
com.example.allinone:id/action_bar = 0x7f0a0035
com.example.allinone:id/actionUp = 0x7f0a0034
com.example.allinone:dimen/mtrl_slider_halo_radius = 0x7f07030a
com.example.allinone:id/actionDown = 0x7f0a0032
com.example.allinone:layout/dialog_add_event = 0x7f0d0031
com.example.allinone:integer/hide_password_duration = 0x7f0b000d
com.example.allinone:id/accessibility_custom_action_9 = 0x7f0a0030
com.example.allinone:string/m3c_date_range_input_title = 0x7f130141
com.example.allinone:id/accessibility_custom_action_8 = 0x7f0a002f
com.example.allinone:id/accessibility_custom_action_5 = 0x7f0a002c
com.example.allinone:color/m3_button_ripple_color = 0x7f0600a8
com.example.allinone:string/nav_app_bar_navigate_up_description = 0x7f1301b5
com.example.allinone:color/material_personalized_color_on_error = 0x7f0602c2
com.example.allinone:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f1400d7
com.example.allinone:id/accessibility_custom_action_30 = 0x7f0a0029
com.example.allinone:id/progress_bar = 0x7f0a02db
com.example.allinone:color/m3_ref_palette_error40 = 0x7f06013b
com.example.allinone:dimen/m3_btn_inset = 0x7f0700fc
com.example.allinone:id/accessibility_custom_action_29 = 0x7f0a0027
com.example.allinone:id/tabMode = 0x7f0a0354
com.example.allinone:styleable/KeyCycle = 0x7f150048
com.example.allinone:id/accessibility_custom_action_28 = 0x7f0a0026
com.example.allinone:id/open_search_view_edit_text = 0x7f0a0293
com.example.allinone:id/accessibility_custom_action_26 = 0x7f0a0024
com.example.allinone:styleable/FontFamilyFont = 0x7f15003d
com.example.allinone:id/accessibility_custom_action_25 = 0x7f0a0023
com.example.allinone:id/accessibility_custom_action_24 = 0x7f0a0022
com.example.allinone:id/accessibility_custom_action_23 = 0x7f0a0021
com.example.allinone:attr/headerLayout = 0x7f04023a
com.example.allinone:id/accessibility_custom_action_4 = 0x7f0a002b
com.example.allinone:animator/m3_chip_state_list_anim = 0x7f02000e
com.example.allinone:id/accessibility_custom_action_22 = 0x7f0a0020
com.example.allinone:attr/cornerFamilyTopRight = 0x7f040164
com.example.allinone:id/accessibility_custom_action_20 = 0x7f0a001e
com.example.allinone:id/accessibility_custom_action_2 = 0x7f0a001d
com.example.allinone:id/accessibility_custom_action_19 = 0x7f0a001c
com.example.allinone:dimen/cardview_default_radius = 0x7f070056
com.example.allinone:id/accessibility_custom_action_17 = 0x7f0a001a
com.example.allinone:id/onDateChanged = 0x7f0a028a
com.example.allinone:id/accessibility_custom_action_14 = 0x7f0a0017
com.example.allinone:style/Theme.Material3.Light.SideSheetDialog = 0x7f14027f
com.example.allinone:id/textUserTimestamp = 0x7f0a0378
com.example.allinone:dimen/m3_comp_filled_text_field_disabled_active_indicator_opacity = 0x7f07014f
com.example.allinone:string/abc_searchview_description_submit = 0x7f130016
com.example.allinone:id/color_orange = 0x7f0a00cd
com.example.allinone:id/accessibility_custom_action_12 = 0x7f0a0015
com.example.allinone:id/fixed = 0x7f0a018e
com.example.allinone:id/accessibility_custom_action_11 = 0x7f0a0014
com.example.allinone:attr/fabAlignmentMode = 0x7f0401e5
com.example.allinone:id/accessibility_custom_action_0 = 0x7f0a0011
com.example.allinone:id/accessibility_action_clickable_span = 0x7f0a0010
com.example.allinone:id/accelerate = 0x7f0a000f
com.example.allinone:style/ShapeAppearance.M3.Comp.BottomAppBar.Container.Shape = 0x7f140188
com.example.allinone:id/above = 0x7f0a000e
com.example.allinone:string/create_note = 0x7f130072
com.example.allinone:id/SYM = 0x7f0a000b
com.example.allinone:id/SHOW_PATH = 0x7f0a0009
com.example.allinone:style/ShapeAppearance.MaterialComponents.MediumComponent = 0x7f1401ae
com.example.allinone:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f1400a1
com.example.allinone:id/mtrl_anchor_parent = 0x7f0a0236
com.example.allinone:id/META = 0x7f0a0005
com.example.allinone:id/FUNCTION = 0x7f0a0004
com.example.allinone:dimen/mtrl_slider_thumb_radius = 0x7f07030f
com.example.allinone:id/mtrl_calendar_text_input_frame = 0x7f0a023d
com.example.allinone:id/BOTTOM_END = 0x7f0a0001
com.example.allinone:id/whatsappButton = 0x7f0a03cf
com.example.allinone:id/paymentStatusChip = 0x7f0a02ab
com.example.allinone:attr/cornerSize = 0x7f040166
com.example.allinone:font/roboto_medium_numbers = 0x7f090003
com.example.allinone:font/opensans_regular = 0x7f090002
com.example.allinone:style/Widget.Design.TextInputLayout = 0x7f140382
com.example.allinone:font/opensans_bold = 0x7f090001
com.example.allinone:string/error_document_already_exists = 0x7f1300a6
com.example.allinone:macro/m3_comp_switch_unselected_handle_color = 0x7f0e0134
com.example.allinone:id/material_clock_face = 0x7f0a0213
com.example.allinone:drawable/tooltip_frame_light = 0x7f0801bd
com.example.allinone:style/Widget.MaterialComponents.ExtendedFloatingActionButton = 0x7f14045e
com.example.allinone:string/exo_download_paused_for_network = 0x7f1300df
com.example.allinone:drawable/splash_layout_drawable = 0x7f0801ba
com.example.allinone:drawable/simple_text_splash = 0x7f0801b9
com.example.allinone:drawable/rounded_background = 0x7f0801b6
com.example.allinone:color/m3_ref_palette_dynamic_tertiary80 = 0x7f060132
com.example.allinone:color/switch_thumb_normal_material_light = 0x7f06035b
com.example.allinone:string/m3c_dropdown_menu_expanded = 0x7f13014a
com.example.allinone:style/Theme.Design.BottomSheetDialog = 0x7f14025c
com.example.allinone:layout/item_chat_ai = 0x7f0d0068
com.example.allinone:drawable/placeholder_image = 0x7f0801b5
com.example.allinone:attr/buttonGravity = 0x7f04009d
com.example.allinone:drawable/notification_tile_bg = 0x7f0801b3
com.example.allinone:menu/wt_student_context_menu = 0x7f0f000e
com.example.allinone:drawable/notification_template_icon_low_bg = 0x7f0801b2
com.example.allinone:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface = 0x7f1402df
com.example.allinone:drawable/notification_icon_background = 0x7f0801af
com.example.allinone:drawable/notification_bg_low_pressed = 0x7f0801ac
com.example.allinone:drawable/notification_bg = 0x7f0801a9
com.example.allinone:drawable/notification_action_background = 0x7f0801a8
com.example.allinone:drawable/mtrl_switch_track = 0x7f0801a4
com.example.allinone:style/Widget.Material3.CircularProgressIndicator.Legacy.Small = 0x7f1403bc
com.example.allinone:drawable/mtrl_switch_thumb_pressed_unchecked = 0x7f0801a0
com.example.allinone:drawable/mtrl_switch_thumb_pressed = 0x7f08019e
com.example.allinone:drawable/mtrl_switch_thumb_checked_pressed = 0x7f08019c
com.example.allinone:string/status_registered_desc = 0x7f13021a
com.example.allinone:drawable/mtrl_switch_thumb_checked = 0x7f08019b
com.example.allinone:style/Widget.MaterialComponents.Toolbar.Primary = 0x7f1404a8
com.example.allinone:drawable/mtrl_popupmenu_background = 0x7f080198
com.example.allinone:string/exo_controls_custom_playback_speed = 0x7f1300c1
com.example.allinone:drawable/mtrl_ic_checkbox_checked = 0x7f080193
com.example.allinone:drawable/mtrl_ic_cancel = 0x7f080191
com.example.allinone:style/ExoStyledControls.TimeText.Duration = 0x7f14014a
com.example.allinone:string/mtrl_checkbox_button_path_name = 0x7f13017d
com.example.allinone:drawable/mtrl_ic_arrow_drop_down = 0x7f08018f
com.example.allinone:drawable/mtrl_checkbox_button_icon_indeterminate_checked = 0x7f080188
com.example.allinone:drawable/mtrl_checkbox_button_icon_checked_indeterminate = 0x7f080186
com.example.allinone:color/abc_search_url_text = 0x7f06000d
com.example.allinone:drawable/mtrl_bottomsheet_drag_handle = 0x7f080182
com.example.allinone:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f14003c
com.example.allinone:drawable/material_ic_menu_arrow_up_black_24dp = 0x7f080181
com.example.allinone:style/Base.Theme.Material3.Dark.BottomSheetDialog = 0x7f14005a
com.example.allinone:drawable/material_ic_menu_arrow_down_black_24dp = 0x7f080180
com.example.allinone:drawable/material_ic_keyboard_arrow_previous_black_24dp = 0x7f08017e
com.example.allinone:drawable/material_ic_keyboard_arrow_next_black_24dp = 0x7f08017d
com.example.allinone:interpolator/m3_sys_motion_easing_standard = 0x7f0c000b
com.example.allinone:drawable/material_ic_edit_black_24dp = 0x7f08017b
com.example.allinone:drawable/material_ic_clear_black_24dp = 0x7f08017a
com.example.allinone:color/m3_ref_palette_tertiary70 = 0x7f06018a
com.example.allinone:string/program_updated = 0x7f1301e4
com.example.allinone:dimen/m3_comp_navigation_bar_container_elevation = 0x7f07015d
com.example.allinone:macro/m3_comp_navigation_drawer_inactive_pressed_state_layer_color = 0x7f0e0090
com.example.allinone:drawable/ic_clear_data = 0x7f08011d
com.example.allinone:drawable/material_ic_calendar_black_24dp = 0x7f080179
com.example.allinone:style/Widget.MaterialComponents.Tooltip = 0x7f1404ab
com.example.allinone:macro/m3_comp_search_view_divider_color = 0x7f0e00f2
com.example.allinone:drawable/m3_tabs_line_indicator = 0x7f080175
com.example.allinone:drawable/m3_selection_control_ripple = 0x7f080173
com.example.allinone:macro/m3_comp_navigation_drawer_inactive_focus_label_text_color = 0x7f0e0087
com.example.allinone:id/grouping = 0x7f0a01ab
com.example.allinone:drawable/m3_radiobutton_ripple = 0x7f080172
com.example.allinone:id/ignoreRequest = 0x7f0a01bd
com.example.allinone:drawable/m3_password_eye = 0x7f080170
com.example.allinone:string/exo_controls_play_description = 0x7f1300ca
com.example.allinone:color/material_on_background_emphasis_medium = 0x7f0602b1
com.example.allinone:id/share_button = 0x7f0a0313
com.example.allinone:macro/m3_comp_date_picker_modal_year_selection_year_selected_label_text_color = 0x7f0e0020
com.example.allinone:drawable/m3_avd_show_password = 0x7f08016e
com.example.allinone:drawable/m3_avd_hide_password = 0x7f08016d
com.example.allinone:macro/m3_comp_outlined_button_focus_outline_color = 0x7f0e00a4
com.example.allinone:drawable/indeterminate_static = 0x7f08016c
com.example.allinone:drawable/ic_wt_registers = 0x7f08016a
com.example.allinone:attr/textAppearanceListItem = 0x7f0404a2
com.example.allinone:drawable/abc_btn_check_material_anim = 0x7f08002d
com.example.allinone:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f1400f8
com.example.allinone:layout/mtrl_calendar_month_navigation = 0x7f0d009f
com.example.allinone:drawable/ic_wt = 0x7f080169
com.example.allinone:drawable/ic_view_list = 0x7f080167
com.example.allinone:drawable/ic_video_placeholder = 0x7f080166
com.example.allinone:attr/actionBarSplitStyle = 0x7f040007
com.example.allinone:drawable/ic_video_error = 0x7f080165
com.example.allinone:drawable/ic_tasks = 0x7f080163
com.example.allinone:drawable/ic_students = 0x7f080162
com.example.allinone:drawable/ic_student = 0x7f080161
com.example.allinone:drawable/ic_stop = 0x7f080160
com.example.allinone:dimen/abc_text_size_headline_material = 0x7f070047
com.example.allinone:drawable/ic_share = 0x7f08015e
com.example.allinone:dimen/m3_sys_elevation_level2 = 0x7f070217
com.example.allinone:drawable/ic_remove = 0x7f080159
com.example.allinone:color/m3_fab_efab_foreground_color_selector = 0x7f0600ca
com.example.allinone:color/design_dark_default_color_primary_dark = 0x7f060060
com.example.allinone:drawable/ic_registration = 0x7f080158
com.example.allinone:string/color = 0x7f130052
com.example.allinone:drawable/ic_program = 0x7f080157
com.example.allinone:drawable/ic_play_circle = 0x7f080156
com.example.allinone:drawable/ic_play = 0x7f080155
com.example.allinone:id/parent = 0x7f0a02a4
com.example.allinone:drawable/ic_notes = 0x7f08014f
com.example.allinone:drawable/ic_mtrl_chip_close_circle = 0x7f08014b
com.example.allinone:string/abc_menu_enter_shortcut_label = 0x7f13000b
com.example.allinone:dimen/design_bottom_navigation_active_item_min_width = 0x7f070062
com.example.allinone:dimen/abc_dialog_padding_top_material = 0x7f070025
com.example.allinone:drawable/ic_mtrl_chip_checked_circle = 0x7f08014a
com.example.allinone:drawable/ic_m3_chip_close = 0x7f080146
com.example.allinone:id/notification_background = 0x7f0a027f
com.example.allinone:drawable/ic_m3_chip_check = 0x7f080144
com.example.allinone:id/tag_accessibility_actions = 0x7f0a0358
com.example.allinone:drawable/ic_log = 0x7f080143
com.example.allinone:layout/material_clock_display = 0x7f0d0085
com.example.allinone:style/Theme.Design.NoActionBar = 0x7f140260
com.example.allinone:attr/thumbColor = 0x7f0404cc
com.example.allinone:color/abc_decor_view_status_guard_light = 0x7f060006
com.example.allinone:drawable/ic_launcher_foreground = 0x7f080141
com.example.allinone:drawable/ic_instagram_posts = 0x7f08013c
com.example.allinone:id/CTRL = 0x7f0a0003
com.example.allinone:dimen/mtrl_transition_shared_axis_slide_distance = 0x7f070330
com.example.allinone:drawable/ic_instagram = 0x7f08013b
com.example.allinone:drawable/ic_home = 0x7f080138
com.example.allinone:dimen/m3_comp_input_chip_unselected_outline_width = 0x7f070157
com.example.allinone:macro/m3_comp_radio_button_selected_icon_color = 0x7f0e00dc
com.example.allinone:attr/overlay = 0x7f040390
com.example.allinone:drawable/ic_format_list_numbered = 0x7f080133
com.example.allinone:drawable/ic_folder = 0x7f08012f
com.example.allinone:drawable/abc_list_selector_holo_dark = 0x7f080057
com.example.allinone:drawable/ic_expense = 0x7f08012c
com.example.allinone:drawable/ic_expand_more = 0x7f08012b
com.example.allinone:drawable/ic_exercise = 0x7f080129
com.example.allinone:dimen/m3_comp_sheet_side_docked_container_width = 0x7f0701a3
com.example.allinone:drawable/ic_empty_state = 0x7f080127
com.example.allinone:drawable/ic_edit = 0x7f080126
com.example.allinone:attr/textAppearanceLargePopupMenu = 0x7f0404a0
com.example.allinone:drawable/ic_close = 0x7f080120
com.example.allinone:drawable/ic_clock_black_24dp = 0x7f08011f
com.example.allinone:dimen/mtrl_progress_circular_size_extra_small = 0x7f0702ff
com.example.allinone:dimen/disabled_alpha_material_dark = 0x7f070090
com.example.allinone:string/material_timepicker_clock_mode_description = 0x7f13016f
com.example.allinone:string/color_blue = 0x7f130054
com.example.allinone:id/deleteButton = 0x7f0a00f3
com.example.allinone:attr/materialDividerStyle = 0x7f040321
com.example.allinone:drawable/ic_category_transport = 0x7f080117
com.example.allinone:macro/m3_comp_filter_chip_container_shape = 0x7f0e0057
com.example.allinone:drawable/ic_category_shopping = 0x7f080115
com.example.allinone:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f140184
com.example.allinone:style/Base.Theme.MaterialComponents.Bridge = 0x7f140066
com.example.allinone:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f08008a
com.example.allinone:string/expand_collapse = 0x7f1300f4
com.example.allinone:styleable/ActivityChooserView = 0x7f150005
com.example.allinone:drawable/ic_category_investment = 0x7f080113
com.example.allinone:drawable/ic_category_general = 0x7f080112
com.example.allinone:drawable/ic_category_game = 0x7f080111
com.example.allinone:drawable/ic_category_food = 0x7f080110
com.example.allinone:drawable/ic_category_all = 0x7f08010e
com.example.allinone:drawable/exo_controls_fastforward = 0x7f0800ae
com.example.allinone:id/selected = 0x7f0a030a
com.example.allinone:drawable/ic_call_answer_video = 0x7f08010a
com.example.allinone:attr/expandedTitleGravity = 0x7f0401d5
com.example.allinone:drawable/ic_call_answer_low = 0x7f080109
com.example.allinone:style/Widget.AppCompat.Light.SearchView = 0x7f14035b
com.example.allinone:string/error_document_not_found = 0x7f1300a7
com.example.allinone:drawable/ic_call_answer = 0x7f080108
com.example.allinone:id/pathRelative = 0x7f0a02aa
com.example.allinone:attr/layout_constraintGuide_percent = 0x7f0402b4
com.example.allinone:drawable/ic_calendar = 0x7f080106
com.example.allinone:drawable/ic_back = 0x7f080104
com.example.allinone:drawable/ic_attach_image = 0x7f080103
com.example.allinone:drawable/ic_add = 0x7f080100
com.example.allinone:style/MaterialAlertDialog.Material3.Body.Text = 0x7f140155
com.example.allinone:dimen/mtrl_tooltip_minWidth = 0x7f07032e
com.example.allinone:drawable/fully_uncompleted_exercise_background = 0x7f0800fd
com.example.allinone:attr/hideNavigationIcon = 0x7f040242
com.example.allinone:drawable/exo_styled_controls_vr = 0x7f0800fc
com.example.allinone:layout/item_wt_registration = 0x7f0d007c
com.example.allinone:drawable/exo_styled_controls_subtitle_off = 0x7f0800fa
com.example.allinone:color/m3_button_background_color_selector = 0x7f0600a5
com.example.allinone:macro/m3_comp_time_input_time_input_field_container_shape = 0x7f0e0147
com.example.allinone:drawable/exo_styled_controls_speed = 0x7f0800f9
com.example.allinone:dimen/splashscreen_icon_size_no_background = 0x7f070346
com.example.allinone:style/TextAppearance.AppCompat.Title = 0x7f1401de
com.example.allinone:style/Widget.Material3.TextInputLayout.FilledBox.Dense = 0x7f140423
com.example.allinone:color/gray = 0x7f060095
com.example.allinone:macro/m3_comp_search_view_docked_container_shape = 0x7f0e00f3
com.example.allinone:drawable/exo_styled_controls_settings = 0x7f0800f6
com.example.allinone:drawable/exo_styled_controls_repeat_off = 0x7f0800f3
com.example.allinone:drawable/exo_styled_controls_previous = 0x7f0800f1
com.example.allinone:dimen/m3_comp_top_app_bar_small_container_elevation = 0x7f0701cf
com.example.allinone:drawable/exo_styled_controls_pause = 0x7f0800ef
com.example.allinone:attr/cardForegroundColor = 0x7f0400ab
com.example.allinone:drawable/exo_styled_controls_overflow_show = 0x7f0800ee
com.example.allinone:color/m3_sys_color_dynamic_dark_error_container = 0x7f0601bf
com.example.allinone:drawable/exo_styled_controls_overflow_hide = 0x7f0800ed
com.example.allinone:drawable/exo_styled_controls_fullscreen_enter = 0x7f0800ea
com.example.allinone:id/investmentDropdownLayout = 0x7f0a01d4
com.example.allinone:drawable/exo_styled_controls_fastforward = 0x7f0800e9
com.example.allinone:style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked = 0x7f140162
com.example.allinone:style/Base.Animation.AppCompat.DropDownUp = 0x7f14000e
com.example.allinone:drawable/exo_notification_stop = 0x7f0800e5
com.example.allinone:drawable/exo_notification_small_icon = 0x7f0800e4
com.example.allinone:color/material_on_surface_emphasis_high_type = 0x7f0602b6
com.example.allinone:drawable/exo_notification_previous = 0x7f0800e2
com.example.allinone:drawable/exo_notification_next = 0x7f0800df
com.example.allinone:macro/m3_comp_filled_autocomplete_menu_container_color = 0x7f0e0041
com.example.allinone:drawable/exo_notification_fastforward = 0x7f0800de
com.example.allinone:drawable/exo_icon_vr = 0x7f0800dd
com.example.allinone:color/m3_bottom_sheet_drag_handle_color = 0x7f0600a4
com.example.allinone:drawable/exo_icon_stop = 0x7f0800dc
com.example.allinone:anim/m3_bottom_sheet_slide_out = 0x7f010022
com.example.allinone:macro/m3_comp_date_picker_modal_header_supporting_text_type = 0x7f0e0018
com.example.allinone:menu/search_history = 0x7f0f0007
com.example.allinone:drawable/exo_icon_shuffle_on = 0x7f0800db
com.example.allinone:attr/listPreferredItemPaddingEnd = 0x7f0402f5
com.example.allinone:drawable/exo_icon_shuffle_off = 0x7f0800da
com.example.allinone:layout/abc_action_mode_bar = 0x7f0d0004
com.example.allinone:drawable/mtrl_switch_track_decoration = 0x7f0801a5
com.example.allinone:string/side_sheet_accessibility_pane_title = 0x7f13020f
com.example.allinone:drawable/exo_icon_rewind = 0x7f0800d9
com.example.allinone:macro/m3_comp_navigation_rail_label_text_type = 0x7f0e009f
com.example.allinone:macro/m3_comp_checkbox_selected_container_color = 0x7f0e0006
com.example.allinone:color/bright_foreground_inverse_material_light = 0x7f06002d
com.example.allinone:id/themeToggleLayout = 0x7f0a0385
com.example.allinone:id/mtrl_picker_header_toggle = 0x7f0a0247
com.example.allinone:drawable/exo_icon_previous = 0x7f0800d5
com.example.allinone:drawable/exo_icon_next = 0x7f0800d2
com.example.allinone:drawable/exo_icon_fullscreen_enter = 0x7f0800d0
com.example.allinone:id/view_transition = 0x7f0a03c1
com.example.allinone:drawable/exo_icon_fastforward = 0x7f0800cf
com.example.allinone:drawable/exo_ic_settings = 0x7f0800c8
com.example.allinone:drawable/exo_icon_circular_play = 0x7f0800ce
com.example.allinone:macro/m3_comp_navigation_drawer_headline_type = 0x7f0e0085
com.example.allinone:layout/mtrl_calendar_day = 0x7f0d0099
com.example.allinone:style/Widget.Material3.BottomAppBar = 0x7f14038c
com.example.allinone:drawable/exo_ic_subtitle_off = 0x7f0800cc
com.example.allinone:drawable/exo_ic_skip_next = 0x7f0800c9
com.example.allinone:color/colorPrimaryLight = 0x7f060046
com.example.allinone:drawable/ic_format_list_bulleted = 0x7f080132
com.example.allinone:string/mtrl_checkbox_button_icon_path_name = 0x7f13017a
com.example.allinone:color/m3_ref_palette_neutral_variant95 = 0x7f060166
com.example.allinone:drawable/m3_tabs_transparent_background = 0x7f080177
com.example.allinone:attr/bar_gravity = 0x7f04006e
com.example.allinone:anim/mtrl_bottom_sheet_slide_in = 0x7f010029
com.example.allinone:drawable/exo_ic_default_album_image = 0x7f0800c1
com.example.allinone:drawable/exo_ic_chevron_left = 0x7f0800bf
com.example.allinone:style/Base.V14.Theme.Material3.Light.SideSheetDialog = 0x7f140093
com.example.allinone:attr/state_dragged = 0x7f040447
com.example.allinone:color/m3_ref_palette_dynamic_secondary20 = 0x7f06011f
com.example.allinone:string/drawing_added = 0x7f130091
com.example.allinone:drawable/exo_ic_audiotrack = 0x7f0800bd
com.example.allinone:drawable/mtrl_checkbox_button = 0x7f080183
com.example.allinone:drawable/exo_controls_vr = 0x7f0800bb
com.example.allinone:drawable/exo_controls_shuffle_off = 0x7f0800b9
com.example.allinone:drawable/exo_controls_rewind = 0x7f0800b8
com.example.allinone:drawable/exo_controls_repeat_all = 0x7f0800b5
com.example.allinone:style/Theme.Material3.Light.BottomSheetDialog = 0x7f140279
com.example.allinone:dimen/m3_alert_dialog_action_top_padding = 0x7f0700c3
com.example.allinone:drawable/exo_controls_previous = 0x7f0800b4
com.example.allinone:id/open_search_view_toolbar = 0x7f0a0299
com.example.allinone:drawable/exo_controls_play = 0x7f0800b3
com.example.allinone:attr/SharedValueId = 0x7f040001
com.example.allinone:drawable/exo_controls_next = 0x7f0800b1
com.example.allinone:string/searchbar_scrolling_view_behavior = 0x7f1301ff
com.example.allinone:attr/hide_on_touch = 0x7f040246
com.example.allinone:layout/m3_auto_complete_simple_item = 0x7f0d0082
com.example.allinone:drawable/exo_controls_fullscreen_enter = 0x7f0800af
com.example.allinone:dimen/abc_action_bar_elevation_material = 0x7f070005
com.example.allinone:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f14003d
com.example.allinone:style/Base.V14.Theme.Material3.Dark.Dialog = 0x7f14008e
com.example.allinone:id/deleteVoiceNoteButton = 0x7f0a00f4
com.example.allinone:macro/m3_comp_navigation_drawer_label_text_type = 0x7f0e0091
com.example.allinone:macro/m3_comp_time_picker_period_selector_unselected_label_text_color = 0x7f0e015c
com.example.allinone:drawable/design_ic_visibility_off = 0x7f0800a9
com.example.allinone:drawable/default_profile = 0x7f0800a6
com.example.allinone:drawable/completed_exercise_background = 0x7f0800a5
com.example.allinone:color/common_google_signin_btn_text_light_disabled = 0x7f060050
com.example.allinone:id/snackbar_action = 0x7f0a0322
com.example.allinone:drawable/compat_splash_screen = 0x7f0800a3
com.example.allinone:color/material_dynamic_color_dark_error = 0x7f06025b
com.example.allinone:string/m3c_date_picker_headline_description = 0x7f130132
com.example.allinone:drawable/common_google_signin_btn_text_light_normal = 0x7f0800a1
com.example.allinone:drawable/common_google_signin_btn_text_light_focused = 0x7f0800a0
com.example.allinone:layout/fragment_wt_registry = 0x7f0d005d
com.example.allinone:color/material_dynamic_neutral_variant95 = 0x7f06027b
com.example.allinone:drawable/common_google_signin_btn_text_dark_normal_background = 0x7f08009d
com.example.allinone:attr/horizontalOffset = 0x7f04024d
com.example.allinone:drawable/common_google_signin_btn_text_dark_normal = 0x7f08009c
com.example.allinone:style/Base.Widget.MaterialComponents.Chip = 0x7f140116
com.example.allinone:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f140354
com.example.allinone:dimen/abc_action_bar_default_padding_end_material = 0x7f070003
com.example.allinone:drawable/common_google_signin_btn_icon_light_normal_background = 0x7f080099
com.example.allinone:drawable/common_google_signin_btn_icon_light_normal = 0x7f080098
com.example.allinone:macro/m3_comp_outlined_autocomplete_text_field_input_text_type = 0x7f0e00a2
com.example.allinone:drawable/common_google_signin_btn_icon_light_focused = 0x7f080097
com.example.allinone:drawable/common_google_signin_btn_icon_dark_focused = 0x7f080092
com.example.allinone:drawable/common_google_signin_btn_icon_dark = 0x7f080091
com.example.allinone:color/mtrl_fab_icon_text_color_selector = 0x7f06030e
com.example.allinone:drawable/common_full_open_on_phone = 0x7f080090
com.example.allinone:macro/m3_comp_switch_unselected_focus_track_outline_color = 0x7f0e0133
com.example.allinone:id/mtrl_picker_text_input_range_start = 0x7f0a024a
com.example.allinone:attr/colorOnPrimaryFixed = 0x7f040115
com.example.allinone:drawable/circle_shape = 0x7f08008f
com.example.allinone:drawable/circle_background = 0x7f08008d
com.example.allinone:drawable/btn_radio_off_mtrl = 0x7f080089
com.example.allinone:attr/selectableItemBackgroundBorderless = 0x7f0403f6
com.example.allinone:drawable/btn_checkbox_unchecked_mtrl = 0x7f080087
com.example.allinone:attr/dragScale = 0x7f04019e
com.example.allinone:id/studentName = 0x7f0a0347
com.example.allinone:layout/notification_action_tombstone = 0x7f0d00b4
com.example.allinone:drawable/btn_checkbox_checked_mtrl = 0x7f080085
com.example.allinone:dimen/mtrl_switch_track_width = 0x7f070320
com.example.allinone:attr/motionDurationLong1 = 0x7f040350
com.example.allinone:drawable/border_background = 0x7f080084
com.example.allinone:drawable/bg_tag_blue = 0x7f080081
com.example.allinone:drawable/bg_selected_day = 0x7f080080
com.example.allinone:string/close = 0x7f13004f
com.example.allinone:drawable/bg_day_with_registration = 0x7f08007f
com.example.allinone:style/Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge = 0x7f1402a9
com.example.allinone:drawable/bg_day_with_lesson = 0x7f08007e
com.example.allinone:drawable/bg_current_day = 0x7f08007b
com.example.allinone:drawable/avd_show_password = 0x7f080079
com.example.allinone:dimen/exo_error_message_text_padding_horizontal = 0x7f070094
com.example.allinone:layout/material_clock_display_divider = 0x7f0d0086
com.example.allinone:drawable/avd_hide_password = 0x7f080078
com.example.allinone:attr/switchMinWidth = 0x7f040463
com.example.allinone:drawable/abc_vector_test = 0x7f080077
com.example.allinone:drawable/mtrl_checkbox_button_checked_unchecked = 0x7f080184
com.example.allinone:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f1401c0
com.example.allinone:layout/dialog_workout_details = 0x7f0d0046
com.example.allinone:attr/endIconDrawable = 0x7f0401bd
com.example.allinone:drawable/abc_textfield_search_material = 0x7f080076
com.example.allinone:drawable/design_fab_background = 0x7f0800a7
com.example.allinone:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f080074
com.example.allinone:attr/motionDurationExtraLong4 = 0x7f04034f
com.example.allinone:id/browser_actions_menu_items = 0x7f0a0096
com.example.allinone:drawable/abc_text_select_handle_right_mtrl = 0x7f080071
com.example.allinone:drawable/abc_text_select_handle_middle_mtrl = 0x7f080070
com.example.allinone:style/ShapeAppearance.M3.Comp.FilledButton.Container.Shape = 0x7f14018a
com.example.allinone:drawable/abc_text_select_handle_left_mtrl = 0x7f08006f
com.example.allinone:color/material_timepicker_clockface = 0x7f0602f6
com.example.allinone:drawable/abc_tab_indicator_material = 0x7f08006c
com.example.allinone:styleable/MotionLabel = 0x7f15006e
com.example.allinone:drawable/abc_switch_track_mtrl_alpha = 0x7f08006b
com.example.allinone:drawable/abc_switch_thumb_material = 0x7f08006a
com.example.allinone:drawable/abc_star_half_black_48dp = 0x7f080069
com.example.allinone:drawable/abc_star_black_48dp = 0x7f080068
com.example.allinone:drawable/abc_spinner_textfield_background_material = 0x7f080067
com.example.allinone:drawable/abc_spinner_mtrl_am_alpha = 0x7f080066
com.example.allinone:drawable/bg_tag_green = 0x7f080082
com.example.allinone:drawable/abc_seekbar_tick_mark_material = 0x7f080064
com.example.allinone:macro/m3_comp_date_picker_modal_range_selection_active_indicator_container_color = 0x7f0e0019
com.example.allinone:id/transition_clip = 0x7f0a03a7
com.example.allinone:id/italicButton = 0x7f0a01e0
com.example.allinone:drawable/abc_seekbar_thumb_material = 0x7f080063
com.example.allinone:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f080061
com.example.allinone:string/exo_controls_repeat_all_description = 0x7f1300cd
com.example.allinone:drawable/ic_call_decline = 0x7f08010c
com.example.allinone:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f080060
com.example.allinone:dimen/design_snackbar_action_inline_max_width = 0x7f070080
com.example.allinone:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f08005f
com.example.allinone:macro/m3_comp_badge_large_label_text_color = 0x7f0e0003
com.example.allinone:drawable/abc_ratingbar_small_material = 0x7f08005d
com.example.allinone:drawable/abc_ratingbar_material = 0x7f08005c
com.example.allinone:dimen/mtrl_progress_circular_inset_small = 0x7f0702fc
com.example.allinone:drawable/abc_popup_background_mtrl_mult = 0x7f08005a
com.example.allinone:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f080059
com.example.allinone:drawable/abc_list_selector_holo_light = 0x7f080058
com.example.allinone:style/ThemeOverlay.Design.TextInputEditText = 0x7f1402bd
com.example.allinone:drawable/abc_list_selector_disabled_holo_light = 0x7f080056
com.example.allinone:dimen/m3_comp_radio_button_selected_pressed_state_layer_opacity = 0x7f07018e
com.example.allinone:drawable/abc_list_longpressed_holo = 0x7f080050
com.example.allinone:attr/foregroundInsidePadding = 0x7f040226
com.example.allinone:attr/actionModeCopyDrawable = 0x7f040017
com.example.allinone:drawable/abc_list_focused_holo = 0x7f08004f
com.example.allinone:drawable/abc_list_divider_material = 0x7f08004d
com.example.allinone:macro/m3_comp_search_bar_input_text_color = 0x7f0e00e9
com.example.allinone:drawable/abc_item_background_holo_light = 0x7f08004c
com.example.allinone:drawable/abc_ic_voice_search_api_material = 0x7f08004a
com.example.allinone:drawable/abc_ic_search_api_material = 0x7f080049
com.example.allinone:style/Base.AlertDialog.AppCompat.Light = 0x7f14000c
com.example.allinone:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f080048
com.example.allinone:drawable/abc_ic_menu_overflow_material = 0x7f080045
com.example.allinone:layout/select_dialog_item_material = 0x7f0d00c4
com.example.allinone:id/categoryColorIndicator = 0x7f0a00aa
com.example.allinone:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f080044
com.example.allinone:macro/m3_comp_search_view_header_supporting_text_type = 0x7f0e00f8
com.example.allinone:id/navigation_header_container = 0x7f0a026d
com.example.allinone:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f080043
com.example.allinone:drawable/abc_ic_go_search_api_material = 0x7f080042
com.example.allinone:style/Widget.Material3.Button.TonalButton.Icon = 0x7f1403a6
com.example.allinone:string/abc_menu_space_shortcut_label = 0x7f13000f
com.example.allinone:id/drawerLayout = 0x7f0a0115
com.example.allinone:dimen/splashscreen_icon_mask_size_with_background = 0x7f070342
com.example.allinone:style/ExoStyledControls.Button.Bottom.CC = 0x7f140139
com.example.allinone:style/ThemeOverlay.Material3.Button.TextButton.Snackbar = 0x7f1402cf
com.example.allinone:id/seminarDateText = 0x7f0a030c
com.example.allinone:color/colorAccent = 0x7f060042
com.example.allinone:color/m3_ref_palette_tertiary0 = 0x7f060182
com.example.allinone:drawable/abc_ic_clear_material = 0x7f080040
com.example.allinone:id/exo_ad_overlay = 0x7f0a014b
com.example.allinone:id/action_mode_bar = 0x7f0a0047
com.example.allinone:drawable/abc_edit_text_material = 0x7f08003d
com.example.allinone:macro/m3_comp_checkbox_selected_error_icon_color = 0x7f0e000a
com.example.allinone:drawable/abc_cab_background_top_mtrl_alpha = 0x7f08003a
com.example.allinone:attr/searchViewStyle = 0x7f0403f1
com.example.allinone:drawable/abc_cab_background_top_material = 0x7f080039
com.example.allinone:id/mtrl_calendar_main_pane = 0x7f0a023a
com.example.allinone:drawable/ic_note = 0x7f08014e
com.example.allinone:color/error_color_material_light = 0x7f060084
com.example.allinone:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f080037
com.example.allinone:drawable/ic_whatsapp = 0x7f080168
com.example.allinone:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f080036
com.example.allinone:style/ThemeOverlay.Material3.ActionBar = 0x7f1402bf
com.example.allinone:drawable/abc_btn_default_mtrl_shape = 0x7f080031
com.example.allinone:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f08002f
com.example.allinone:color/common_google_signin_btn_text_light_focused = 0x7f060051
com.example.allinone:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f080029
com.example.allinone:attr/materialDisplayDividerStyle = 0x7f04031f
com.example.allinone:drawable/$mtrl_switch_thumb_unchecked_checked__1 = 0x7f080027
com.example.allinone:drawable/$mtrl_switch_thumb_pressed_checked__0 = 0x7f080024
com.example.allinone:drawable/ic_mtrl_checked_circle = 0x7f080148
com.example.allinone:drawable/$mtrl_switch_thumb_checked_unchecked__1 = 0x7f080023
com.example.allinone:drawable/$mtrl_checkbox_button_unchecked_checked__2 = 0x7f080020
com.example.allinone:attr/isMaterialTheme = 0x7f04026f
com.example.allinone:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__1 = 0x7f08001c
com.example.allinone:color/background_material_dark = 0x7f060022
com.example.allinone:drawable/$mtrl_checkbox_button_icon_indeterminate_checked__0 = 0x7f080014
com.example.allinone:attr/fontFamily = 0x7f040219
com.example.allinone:id/confirmButton = 0x7f0a00d2
com.example.allinone:drawable/$mtrl_checkbox_button_icon_checked_unchecked__0 = 0x7f080011
com.example.allinone:drawable/$mtrl_checkbox_button_checked_unchecked__0 = 0x7f08000d
com.example.allinone:dimen/m3_btn_text_btn_icon_padding_left = 0x7f070103
com.example.allinone:drawable/$m3_avd_show_password__2 = 0x7f08000c
com.example.allinone:drawable/$m3_avd_hide_password__2 = 0x7f080009
com.example.allinone:attr/shrinkMotionSpec = 0x7f040419
com.example.allinone:string/please_enter_title = 0x7f1301da
com.example.allinone:id/bottomNavigation = 0x7f0a008e
com.example.allinone:color/m3_sys_color_light_inverse_on_surface = 0x7f060210
com.example.allinone:attr/motionEasingDecelerated = 0x7f04035d
com.example.allinone:dimen/m3_comp_extended_fab_primary_container_height = 0x7f070132
com.example.allinone:drawable/$avd_show_password__1 = 0x7f080004
com.example.allinone:attr/suggestionRowLayout = 0x7f040460
com.example.allinone:style/ShapeAppearance.M3.Sys.Shape.Corner.None = 0x7f14019d
com.example.allinone:string/mtrl_picker_invalid_format_use = 0x7f130193
com.example.allinone:drawable/$avd_hide_password__1 = 0x7f080001
com.example.allinone:style/Base.Theme.MaterialComponents.Dialog.Bridge = 0x7f14006a
com.example.allinone:color/m3_sys_color_on_primary_fixed_variant = 0x7f060230
com.example.allinone:attr/layout_anchorGravity = 0x7f04029f
com.example.allinone:color/m3_ref_palette_neutral_variant80 = 0x7f060164
com.example.allinone:id/row_index_key = 0x7f0a02f0
com.example.allinone:dimen/tooltip_precise_anchor_extra_offset = 0x7f07034c
com.example.allinone:dimen/tooltip_margin = 0x7f07034b
com.example.allinone:drawable/m3_tabs_background = 0x7f080174
com.example.allinone:dimen/tooltip_horizontal_padding = 0x7f07034a
com.example.allinone:attr/tabPaddingStart = 0x7f04047a
com.example.allinone:drawable/mtrl_dropdown_arrow = 0x7f08018e
com.example.allinone:dimen/splashscreen_icon_size_with_background = 0x7f070347
com.example.allinone:id/TOP_END = 0x7f0a000c
com.example.allinone:dimen/splashscreen_icon_mask_stroke_with_background = 0x7f070344
com.example.allinone:dimen/mtrl_btn_pressed_z = 0x7f07028e
com.example.allinone:drawable/abc_control_background_material = 0x7f08003b
com.example.allinone:attr/borderRound = 0x7f040080
com.example.allinone:dimen/abc_dialog_min_width_major = 0x7f070022
com.example.allinone:dimen/splashscreen_icon_mask_stroke_no_background = 0x7f070343
com.example.allinone:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f1401dd
com.example.allinone:attr/appBarLayoutStyle = 0x7f04003d
com.example.allinone:dimen/m3_extended_fab_end_padding = 0x7f0701d5
com.example.allinone:dimen/splashscreen_icon_mask_size_no_background = 0x7f070341
com.example.allinone:dimen/m3_toolbar_text_size_title = 0x7f070245
com.example.allinone:dimen/notification_small_icon_size_as_large = 0x7f07033c
com.example.allinone:drawable/$m3_avd_show_password__1 = 0x7f08000b
com.example.allinone:color/m3_efab_ripple_color_selector = 0x7f0600c7
com.example.allinone:dimen/notification_right_side_padding_top = 0x7f07033a
com.example.allinone:layout/abc_select_dialog_material = 0x7f0d001a
com.example.allinone:dimen/notification_main_column_padding_top = 0x7f070337
com.example.allinone:dimen/notification_large_icon_height = 0x7f070335
com.example.allinone:dimen/notification_content_margin_start = 0x7f070334
com.example.allinone:style/Base.Widget.MaterialComponents.CheckedTextView = 0x7f140115
com.example.allinone:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f1400d8
com.example.allinone:dimen/m3_comp_text_button_pressed_state_layer_opacity = 0x7f0701c3
com.example.allinone:dimen/notification_action_text_size = 0x7f070332
com.example.allinone:layout/item_transaction = 0x7f0d0078
com.example.allinone:dimen/design_navigation_item_horizontal_padding = 0x7f07007a
com.example.allinone:drawable/ic_backup = 0x7f080105
com.example.allinone:color/material_personalized_color_surface_container = 0x7f0602da
com.example.allinone:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f1400c6
com.example.allinone:styleable/MaterialToolbar = 0x7f150066
com.example.allinone:dimen/mtrl_tooltip_padding = 0x7f07032f
com.example.allinone:dimen/mtrl_navigation_rail_icon_size = 0x7f0702f5
com.example.allinone:attr/colorSurface = 0x7f040133
com.example.allinone:dimen/mtrl_textinput_outline_box_expanded_padding = 0x7f070328
com.example.allinone:macro/m3_comp_outlined_text_field_supporting_text_color = 0x7f0e00c4
com.example.allinone:attr/resize_mode = 0x7f0403db
com.example.allinone:dimen/mtrl_textinput_box_stroke_width_default = 0x7f070324
com.example.allinone:dimen/mtrl_textinput_box_corner_radius_small = 0x7f070322
com.example.allinone:dimen/m3_carousel_debug_keyline_width = 0x7f070112
com.example.allinone:style/ShapeAppearance.M3.Comp.NavigationBar.Container.Shape = 0x7f14018c
com.example.allinone:dimen/mtrl_switch_track_height = 0x7f07031f
com.example.allinone:drawable/common_google_signin_btn_icon_disabled = 0x7f080095
com.example.allinone:id/sin = 0x7f0a031a
com.example.allinone:dimen/mtrl_calendar_header_toggle_margin_bottom = 0x7f0702aa
com.example.allinone:color/material_dynamic_tertiary60 = 0x7f06029e
com.example.allinone:dimen/mtrl_switch_thumb_icon_size = 0x7f07031d
com.example.allinone:color/mtrl_textinput_default_box_stroke_color = 0x7f06032b
com.example.allinone:dimen/mtrl_snackbar_message_margin_horizontal = 0x7f070319
com.example.allinone:dimen/mtrl_snackbar_background_overlay_color_alpha = 0x7f070317
com.example.allinone:style/ExoStyledControls.Button.Center.Next = 0x7f140144
com.example.allinone:dimen/mtrl_slider_tick_min_spacing = 0x7f070310
com.example.allinone:dimen/mtrl_slider_thumb_elevation = 0x7f07030e
com.example.allinone:style/Base.Widget.AppCompat.RatingBar = 0x7f1400f2
com.example.allinone:dimen/mtrl_progress_indicator_full_rounded_corner_radius = 0x7f070305
com.example.allinone:drawable/icon_background = 0x7f08016b
com.example.allinone:dimen/mtrl_progress_circular_track_thickness_small = 0x7f070304
com.example.allinone:dimen/mtrl_progress_circular_size_small = 0x7f070301
com.example.allinone:string/network_unavailable = 0x7f1301b8
com.example.allinone:dimen/mtrl_progress_circular_size = 0x7f0702fe
com.example.allinone:string/select_type = 0x7f130209
com.example.allinone:attr/dropdownListPreferredItemHeight = 0x7f0401af
com.example.allinone:attr/circleRadius = 0x7f0400e1
com.example.allinone:attr/content = 0x7f04014b
com.example.allinone:dimen/mtrl_navigation_rail_text_size = 0x7f0702f8
com.example.allinone:attr/grid_useRtl = 0x7f040234
com.example.allinone:dimen/mtrl_navigation_rail_icon_margin = 0x7f0702f4
com.example.allinone:color/start_color = 0x7f060353
com.example.allinone:dimen/mtrl_navigation_rail_default_width = 0x7f0702f2
com.example.allinone:dimen/mtrl_navigation_rail_compact_width = 0x7f0702f1
com.example.allinone:color/m3_fab_efab_background_color_selector = 0x7f0600c9
com.example.allinone:dimen/mtrl_navigation_rail_active_text_size = 0x7f0702f0
com.example.allinone:attr/played_color = 0x7f0403b0
com.example.allinone:id/is_pooling_container_tag = 0x7f0a01de
com.example.allinone:attr/logoScaleType = 0x7f0402fc
com.example.allinone:styleable/TextInputEditText = 0x7f1500a5
com.example.allinone:color/m3_sys_color_dynamic_dark_outline_variant = 0x7f0601cf
com.example.allinone:color/storyCard = 0x7f060354
com.example.allinone:dimen/mtrl_navigation_item_shape_vertical_margin = 0x7f0702ef
com.example.allinone:dimen/m3_comp_suggestion_chip_with_leading_icon_leading_icon_size = 0x7f0701b3
com.example.allinone:dimen/mtrl_navigation_item_shape_horizontal_margin = 0x7f0702ee
com.example.allinone:id/actionDownUp = 0x7f0a0033
com.example.allinone:dimen/mtrl_navigation_item_icon_padding = 0x7f0702ec
com.example.allinone:attr/extendedFloatingActionButtonTertiaryStyle = 0x7f0401e3
com.example.allinone:id/action0 = 0x7f0a0031
com.example.allinone:dimen/m3_comp_navigation_drawer_container_width = 0x7f070163
com.example.allinone:dimen/m3_badge_with_text_vertical_offset = 0x7f0700df
com.example.allinone:attr/initialActivityCount = 0x7f04026a
com.example.allinone:attr/springDamping = 0x7f040434
com.example.allinone:dimen/mtrl_navigation_bar_item_default_margin = 0x7f0702e9
com.example.allinone:style/Platform.MaterialComponents.Dialog = 0x7f14016a
com.example.allinone:dimen/mtrl_navigation_bar_item_default_icon_size = 0x7f0702e8
com.example.allinone:color/m3_ref_palette_dynamic_tertiary20 = 0x7f06012c
com.example.allinone:macro/m3_comp_navigation_bar_active_pressed_icon_color = 0x7f0e0068
com.example.allinone:attr/minTouchTargetSize = 0x7f040343
com.example.allinone:dimen/mtrl_low_ripple_pressed_alpha = 0x7f0702e6
com.example.allinone:macro/m3_comp_time_picker_time_selector_selected_hover_state_layer_color = 0x7f0e0162
com.example.allinone:color/m3_sys_color_dynamic_dark_primary_container = 0x7f0601d1
com.example.allinone:dimen/mtrl_high_ripple_hovered_alpha = 0x7f0702e1
com.example.allinone:drawable/abc_item_background_holo_dark = 0x7f08004b
com.example.allinone:dimen/mtrl_extended_fab_top_padding = 0x7f0702d7
com.example.allinone:id/dialog_button = 0x7f0a0103
com.example.allinone:attr/carousel_backwardTransition = 0x7f0400b1
com.example.allinone:dimen/mtrl_extended_fab_start_padding = 0x7f0702d5
com.example.allinone:drawable/notification_template_icon_bg = 0x7f0801b1
com.example.allinone:color/m3_navigation_item_ripple_color = 0x7f0600d5
com.example.allinone:dimen/mtrl_extended_fab_min_height = 0x7f0702d3
com.example.allinone:dimen/mtrl_extended_fab_icon_size = 0x7f0702d1
com.example.allinone:style/ThemeOverlay.MaterialComponents.TextInputEditText = 0x7f14031b
com.example.allinone:id/month_navigation_bar = 0x7f0a022f
com.example.allinone:drawable/compat_splash_screen_no_icon_background = 0x7f0800a4
com.example.allinone:dimen/mtrl_extended_fab_elevation = 0x7f0702ce
com.example.allinone:dimen/mtrl_extended_fab_disabled_translation_z = 0x7f0702cd
com.example.allinone:dimen/mtrl_extended_fab_bottom_padding = 0x7f0702cb
com.example.allinone:id/search_src_text = 0x7f0a0307
com.example.allinone:macro/m3_comp_top_app_bar_small_headline_type = 0x7f0e0171
com.example.allinone:attr/useCompatPadding = 0x7f040520
com.example.allinone:attr/maxCharacterCount = 0x7f040333
com.example.allinone:color/m3_sys_color_light_on_tertiary = 0x7f06021c
com.example.allinone:style/TextAppearance.AppCompat.Title.Inverse = 0x7f1401df
com.example.allinone:dimen/mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f0702ca
com.example.allinone:attr/subtitleTextStyle = 0x7f04045c
com.example.allinone:dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f0702c9
com.example.allinone:attr/wavePeriod = 0x7f040532
com.example.allinone:color/dark_gray = 0x7f060054
com.example.allinone:dimen/m3_comp_extended_fab_primary_pressed_state_layer_opacity = 0x7f070139
com.example.allinone:style/Widget.Material3.MaterialCalendar.Year.Today = 0x7f1403f4
com.example.allinone:dimen/mtrl_chip_text_size = 0x7f0702c7
com.example.allinone:style/TextAppearance.AppCompat.Display4 = 0x7f1401cc
com.example.allinone:dimen/mtrl_card_spacing = 0x7f0702c5
com.example.allinone:drawable/ic_menu = 0x7f080147
com.example.allinone:attr/listDividerAlertDialog = 0x7f0402ed
com.example.allinone:dimen/mtrl_card_checked_icon_size = 0x7f0702c1
com.example.allinone:dimen/mtrl_textinput_counter_margin_start = 0x7f070326
com.example.allinone:string/m3c_date_picker_navigate_to_year_description = 0x7f130133
com.example.allinone:attr/shapeAppearanceCornerExtraSmall = 0x7f0403fc
com.example.allinone:dimen/mtrl_card_checked_icon_margin = 0x7f0702c0
com.example.allinone:dimen/mtrl_calendar_year_vertical_padding = 0x7f0702be
com.example.allinone:dimen/mtrl_calendar_year_height = 0x7f0702bc
com.example.allinone:drawable/mtrl_checkbox_button_icon_unchecked_indeterminate = 0x7f08018b
com.example.allinone:string/error_storage = 0x7f1300b7
com.example.allinone:attr/touchRegionId = 0x7f040503
com.example.allinone:dimen/mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f0702ba
com.example.allinone:color/secondary_text_default_material_dark = 0x7f06034f
com.example.allinone:dimen/mtrl_calendar_text_input_padding_top = 0x7f0702b8
com.example.allinone:color/m3_ref_palette_dynamic_neutral4 = 0x7f0600e8
com.example.allinone:dimen/mtrl_calendar_selection_text_baseline_to_top = 0x7f0702b7
com.example.allinone:attr/subMenuArrow = 0x7f040452
com.example.allinone:color/m3_ref_palette_dynamic_secondary70 = 0x7f060124
com.example.allinone:color/colorWarning = 0x7f060048
com.example.allinone:dimen/mtrl_calendar_navigation_bottom_padding = 0x7f0702b0
com.example.allinone:dimen/material_textinput_max_width = 0x7f070267
com.example.allinone:color/m3_ref_palette_neutral_variant100 = 0x7f06015d
com.example.allinone:dimen/mtrl_calendar_month_vertical_padding = 0x7f0702af
com.example.allinone:macro/m3_comp_navigation_drawer_active_hover_state_layer_color = 0x7f0e007d
com.example.allinone:attr/viewTransitionOnNegativeCross = 0x7f04052b
com.example.allinone:attr/limitBoundsTo = 0x7f0402e6
com.example.allinone:color/m3_ref_palette_neutral_variant30 = 0x7f06015f
com.example.allinone:string/m3_sys_motion_easing_emphasized_decelerate = 0x7f13011b
com.example.allinone:dimen/m3_comp_filled_button_container_elevation = 0x7f070147
com.example.allinone:attr/font = 0x7f040218
com.example.allinone:dimen/mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f0702ad
com.example.allinone:dimen/splashscreen_icon_size = 0x7f070345
com.example.allinone:id/action_mode_close_button = 0x7f0a0049
com.example.allinone:color/background_material_light = 0x7f060023
com.example.allinone:dimen/mtrl_calendar_landscape_header_width = 0x7f0702ac
com.example.allinone:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f080035
com.example.allinone:dimen/mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f0702b4
com.example.allinone:string/title_students = 0x7f13023a
com.example.allinone:dimen/mtrl_calendar_header_height = 0x7f0702a6
com.example.allinone:dimen/mtrl_shape_corner_size_small_component = 0x7f070309
com.example.allinone:id/exo_play = 0x7f0a0167
com.example.allinone:attr/collapseIcon = 0x7f0400f8
com.example.allinone:drawable/ic_draw = 0x7f080125
com.example.allinone:dimen/mtrl_calendar_day_width = 0x7f0702a0
com.example.allinone:attr/motionEffect_move = 0x7f04036a
com.example.allinone:dimen/mtrl_calendar_day_today_stroke = 0x7f07029e
com.example.allinone:style/Platform.MaterialComponents = 0x7f140169
com.example.allinone:dimen/material_filled_edittext_font_2_0_padding_bottom = 0x7f07025e
com.example.allinone:dimen/mtrl_calendar_bottom_padding = 0x7f070299
com.example.allinone:id/wtStudentsFragment = 0x7f0a03e5
com.example.allinone:dimen/m3_comp_search_bar_pressed_state_layer_opacity = 0x7f070197
com.example.allinone:dimen/mtrl_btn_z = 0x7f070295
com.example.allinone:dimen/mtrl_btn_text_btn_icon_padding = 0x7f070291
com.example.allinone:dimen/mtrl_btn_padding_right = 0x7f07028c
com.example.allinone:attr/windowMinWidthMinor = 0x7f04053e
com.example.allinone:attr/windowSplashScreenIconBackgroundColor = 0x7f040543
com.example.allinone:dimen/mtrl_btn_inset = 0x7f070287
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar = 0x7f140464
com.example.allinone:dimen/mtrl_btn_icon_btn_padding_left = 0x7f070285
com.example.allinone:dimen/mtrl_btn_hovered_z = 0x7f070284
com.example.allinone:dimen/mtrl_btn_focused_z = 0x7f070283
com.example.allinone:attr/fabCradleRoundedCornerRadius = 0x7f0401ea
com.example.allinone:color/material_dynamic_secondary10 = 0x7f06028b
com.example.allinone:dimen/mtrl_btn_elevation = 0x7f070282
com.example.allinone:dimen/mtrl_btn_disabled_elevation = 0x7f070280
com.example.allinone:style/ThemeOverlay.Material3.Light = 0x7f1402e7
com.example.allinone:dimen/m3_navigation_item_icon_padding = 0x7f0701e6
com.example.allinone:dimen/mtrl_btn_dialog_btn_min_width = 0x7f07027f
com.example.allinone:dimen/mtrl_btn_corner_radius = 0x7f07027e
com.example.allinone:string/m3c_date_range_picker_day_in_range = 0x7f130142
com.example.allinone:attr/tabPaddingEnd = 0x7f040479
com.example.allinone:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner = 0x7f140318
com.example.allinone:attr/hideOnContentScroll = 0x7f040243
com.example.allinone:dimen/mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f07027c
com.example.allinone:string/abc_capital_on = 0x7f130007
com.example.allinone:id/textSourceEmoji = 0x7f0a036f
com.example.allinone:dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f07027b
com.example.allinone:dimen/notification_top_pad = 0x7f07033e
com.example.allinone:dimen/mtrl_badge_with_text_size = 0x7f070277
com.example.allinone:dimen/mtrl_badge_toolbar_action_menu_item_horizontal_offset = 0x7f070275
com.example.allinone:attr/motionEffect_end = 0x7f040369
com.example.allinone:macro/m3_comp_time_input_time_input_field_focus_outline_color = 0x7f0e0148
com.example.allinone:dimen/material_time_picker_minimum_screen_height = 0x7f070269
com.example.allinone:dimen/material_textinput_default_width = 0x7f070266
com.example.allinone:dimen/material_helper_text_font_1_3_padding_horizontal = 0x7f070263
com.example.allinone:attr/reactiveGuide_animateChange = 0x7f0403d0
com.example.allinone:string/exo_track_role_commentary = 0x7f1300e8
com.example.allinone:dimen/material_helper_text_default_padding_top = 0x7f070262
com.example.allinone:id/accessibility_custom_action_1 = 0x7f0a0012
com.example.allinone:attr/materialIconButtonStyle = 0x7f040325
com.example.allinone:dimen/material_filled_edittext_font_2_0_padding_top = 0x7f07025f
com.example.allinone:dimen/material_filled_edittext_font_1_3_padding_top = 0x7f07025d
com.example.allinone:string/amount_format = 0x7f13002a
com.example.allinone:id/offline_status_message = 0x7f0a0285
com.example.allinone:attr/textAppearanceDisplaySmall = 0x7f040493
com.example.allinone:dimen/material_emphasis_medium = 0x7f07025b
com.example.allinone:id/nav_history = 0x7f0a025b
com.example.allinone:dimen/material_emphasis_disabled_background = 0x7f070259
com.example.allinone:dimen/material_emphasis_disabled = 0x7f070258
com.example.allinone:id/material_clock_hand = 0x7f0a0214
com.example.allinone:dimen/material_divider_thickness = 0x7f070257
com.example.allinone:animator/nav_default_pop_enter_anim = 0x7f020024
com.example.allinone:dimen/material_clock_period_toggle_vertical_gap = 0x7f070252
com.example.allinone:dimen/material_clock_period_toggle_horizontal_gap = 0x7f070251
com.example.allinone:dimen/material_clock_number_text_size = 0x7f07024f
com.example.allinone:attr/statusBarScrim = 0x7f04044f
com.example.allinone:macro/m3_comp_switch_unselected_focus_track_color = 0x7f0e0132
com.example.allinone:dimen/material_clock_hand_stroke_width = 0x7f07024e
com.example.allinone:id/phoneEditText = 0x7f0a02af
com.example.allinone:id/alertTitle = 0x7f0a005c
com.example.allinone:dimen/material_clock_face_margin_bottom = 0x7f07024a
com.example.allinone:dimen/m3_snackbar_margin = 0x7f070214
com.example.allinone:dimen/material_clock_display_width = 0x7f070249
com.example.allinone:dimen/material_clock_display_padding = 0x7f070248
com.example.allinone:dimen/mtrl_btn_letter_spacing = 0x7f070288
com.example.allinone:color/material_grey_600 = 0x7f0602a7
com.example.allinone:id/bottom = 0x7f0a008d
com.example.allinone:id/noState = 0x7f0a0276
com.example.allinone:attr/numericModifiers = 0x7f040386
com.example.allinone:dimen/material_bottom_sheet_max_width = 0x7f070246
com.example.allinone:attr/maxHeight = 0x7f040334
com.example.allinone:attr/actionOverflowButtonStyle = 0x7f040022
com.example.allinone:dimen/m3_sys_state_hover_state_layer_opacity = 0x7f070241
com.example.allinone:styleable/Variant = 0x7f1500ac
com.example.allinone:dimen/m3_sys_state_dragged_state_layer_opacity = 0x7f07023f
com.example.allinone:style/ThemeOverlay.AppCompat.Dark = 0x7f1402b6
com.example.allinone:style/ShapeAppearance.M3.Comp.TextButton.Container.Shape = 0x7f140197
com.example.allinone:attr/colorSurfaceContainerHigh = 0x7f040136
com.example.allinone:drawable/abc_action_bar_item_background_material = 0x7f08002a
com.example.allinone:dimen/m3_sys_motion_easing_standard_control_x2 = 0x7f070238
com.example.allinone:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1402fb
com.example.allinone:dimen/m3_sys_motion_easing_standard_accelerate_control_y2 = 0x7f070236
com.example.allinone:dimen/m3_sys_motion_easing_standard_accelerate_control_x2 = 0x7f070234
com.example.allinone:dimen/m3_comp_sheet_bottom_docked_modal_container_elevation = 0x7f0701a1
com.example.allinone:id/open_search_view_content_container = 0x7f0a0290
com.example.allinone:attr/defaultQueryHint = 0x7f040189
com.example.allinone:dimen/abc_action_button_min_width_overflow_material = 0x7f07000f
com.example.allinone:dimen/m3_sys_motion_easing_linear_control_y2 = 0x7f070232
com.example.allinone:attr/flow_firstHorizontalStyle = 0x7f040206
com.example.allinone:style/Base.V21.Theme.AppCompat.Dialog = 0x7f1400a5
com.example.allinone:id/action_delete = 0x7f0a003e
com.example.allinone:dimen/m3_sys_motion_easing_legacy_decelerate_control_x1 = 0x7f07022b
com.example.allinone:color/browser_actions_bg_grey = 0x7f060032
com.example.allinone:dimen/m3_sys_motion_easing_legacy_control_y2 = 0x7f07022a
com.example.allinone:layout/material_timepicker_textinput_display = 0x7f0d0091
com.example.allinone:id/unlabeled = 0x7f0a03b9
com.example.allinone:id/exercise_sets_input = 0x7f0a0146
com.example.allinone:color/m3_ref_palette_secondary0 = 0x7f060175
com.example.allinone:dimen/m3_searchbar_text_size = 0x7f070206
com.example.allinone:dimen/m3_sys_motion_easing_legacy_accelerate_control_y2 = 0x7f070226
com.example.allinone:anim/linear_indeterminate_line2_tail_interpolator = 0x7f010020
com.example.allinone:dimen/tooltip_vertical_padding = 0x7f07034e
com.example.allinone:attr/fontProviderAuthority = 0x7f04021a
com.example.allinone:color/m3_navigation_rail_ripple_color_selector = 0x7f0600d9
com.example.allinone:dimen/m3_sys_motion_easing_legacy_accelerate_control_x2 = 0x7f070224
com.example.allinone:dimen/browser_actions_context_menu_max_width = 0x7f070052
com.example.allinone:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y2 = 0x7f070222
com.example.allinone:dimen/mtrl_calendar_header_height_fullscreen = 0x7f0702a7
com.example.allinone:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x2 = 0x7f070220
com.example.allinone:style/ThemeOverlay.MaterialComponents.ActionBar.Primary = 0x7f140300
com.example.allinone:string/total_workouts = 0x7f130244
com.example.allinone:macro/m3_comp_outlined_text_field_input_text_color = 0x7f0e00c0
com.example.allinone:dimen/m3_navigation_item_vertical_padding = 0x7f0701eb
com.example.allinone:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y2 = 0x7f07021e
com.example.allinone:attr/actionProviderClass = 0x7f040024
com.example.allinone:color/mtrl_chip_close_icon_tint = 0x7f060306
com.example.allinone:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y1 = 0x7f07021d
com.example.allinone:id/postCaption = 0x7f0a02c5
com.example.allinone:dimen/mtrl_extended_fab_translation_z_pressed = 0x7f0702da
com.example.allinone:dimen/m3_sys_elevation_level4 = 0x7f070219
com.example.allinone:style/Widget.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1403ce
com.example.allinone:style/Theme.MaterialComponents.Light.NoActionBar = 0x7f1402ab
com.example.allinone:dimen/m3_sys_elevation_level3 = 0x7f070218
com.example.allinone:color/m3_dynamic_default_color_secondary_text = 0x7f0600c3
com.example.allinone:dimen/m3_sys_elevation_level1 = 0x7f070216
com.example.allinone:dimen/appcompat_dialog_background_inset = 0x7f070051
com.example.allinone:dimen/m3_menu_elevation = 0x7f0701e1
com.example.allinone:style/Platform.AppCompat = 0x7f140167
com.example.allinone:attr/fastScrollEnabled = 0x7f0401ee
com.example.allinone:dimen/m3_snackbar_action_text_color_alpha = 0x7f070213
com.example.allinone:style/ShapeAppearance.M3.Comp.DatePicker.Modal.Date.Container.Shape = 0x7f140189
com.example.allinone:color/m3_sys_color_dynamic_light_inverse_primary = 0x7f0601e3
com.example.allinone:id/material_minute_text_input = 0x7f0a021c
com.example.allinone:attr/rippleColor = 0x7f0403de
com.example.allinone:dimen/m3_small_fab_size = 0x7f070212
com.example.allinone:color/abc_decor_view_status_guard = 0x7f060005
com.example.allinone:color/m3_ref_palette_secondary30 = 0x7f060179
com.example.allinone:dimen/m3_side_sheet_modal_elevation = 0x7f07020b
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant100 = 0x7f0600f9
com.example.allinone:dimen/m3_comp_primary_navigation_tab_active_focus_state_layer_opacity = 0x7f07017f
com.example.allinone:dimen/m3_side_sheet_margin_detached = 0x7f07020a
com.example.allinone:id/material_value_index = 0x7f0a0224
com.example.allinone:dimen/m3_searchview_height = 0x7f070209
com.example.allinone:integer/mtrl_view_invisible = 0x7f0b0045
com.example.allinone:id/accessibility_custom_action_3 = 0x7f0a0028
com.example.allinone:style/Theme.MaterialComponents.DialogWhenLarge = 0x7f14029c
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant90 = 0x7f060108
com.example.allinone:dimen/m3_searchview_divider_size = 0x7f070207
com.example.allinone:dimen/m3_searchbar_text_margin_start_no_navigation_icon = 0x7f070205
com.example.allinone:color/m3_sys_color_dynamic_light_outline_variant = 0x7f0601f1
com.example.allinone:color/mtrl_calendar_selected_range = 0x7f060302
com.example.allinone:dimen/m3_searchbar_margin_vertical = 0x7f070202
com.example.allinone:dimen/design_bottom_navigation_margin = 0x7f07006a
com.example.allinone:dimen/m3_ripple_focused_alpha = 0x7f0701fb
com.example.allinone:color/gray_light = 0x7f060097
com.example.allinone:dimen/m3_ripple_default_alpha = 0x7f0701fa
com.example.allinone:attr/clearsTag = 0x7f0400e9
com.example.allinone:color/material_dynamic_color_light_on_error_container = 0x7f060262
com.example.allinone:dimen/m3_navigation_rail_label_padding_horizontal = 0x7f0701f9
com.example.allinone:layout/mtrl_picker_header_dialog = 0x7f0d00a9
com.example.allinone:string/save_to_note_and_gallery = 0x7f1301f9
com.example.allinone:dimen/m3_navigation_rail_item_padding_top = 0x7f0701f7
com.example.allinone:dimen/m3_comp_slider_disabled_active_track_opacity = 0x7f0701a9
com.example.allinone:attr/largeFontVerticalOffsetAdjustment = 0x7f040296
com.example.allinone:dimen/mtrl_progress_circular_inset_medium = 0x7f0702fb
com.example.allinone:dimen/m3_navigation_rail_item_padding_bottom = 0x7f0701f5
com.example.allinone:string/phone_number = 0x7f1301d9
com.example.allinone:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f140179
com.example.allinone:string/offline_message = 0x7f1301cb
com.example.allinone:id/packed = 0x7f0a029e
com.example.allinone:attr/keyPositionType = 0x7f04028f
com.example.allinone:drawable/common_google_signin_btn_icon_dark_normal_background = 0x7f080094
com.example.allinone:style/ThemeOverlay.Material3.Dialog.Alert.Framework = 0x7f1402d9
com.example.allinone:attr/alphabeticModifiers = 0x7f040034
com.example.allinone:dimen/m3_navigation_item_shape_inset_top = 0x7f0701ea
com.example.allinone:dimen/m3_timepicker_window_elevation = 0x7f070244
com.example.allinone:attr/drawableTintMode = 0x7f0401a8
com.example.allinone:dimen/m3_nav_badge_with_text_vertical_offset = 0x7f0701e2
com.example.allinone:color/m3_ref_palette_neutral4 = 0x7f06014c
com.example.allinone:dimen/m3_large_text_vertical_offset_adjustment = 0x7f0701e0
com.example.allinone:dimen/m3_large_fab_size = 0x7f0701df
com.example.allinone:dimen/m3_large_fab_max_image_size = 0x7f0701de
com.example.allinone:dimen/m3_navigation_rail_item_padding_top_with_large_font = 0x7f0701f8
com.example.allinone:dimen/design_bottom_navigation_item_max_width = 0x7f070067
com.example.allinone:dimen/m3_extended_fab_top_padding = 0x7f0701d9
com.example.allinone:menu/drawer_menu = 0x7f0f0001
com.example.allinone:dimen/mtrl_badge_text_horizontal_edge_offset = 0x7f070273
com.example.allinone:dimen/m3_extended_fab_bottom_padding = 0x7f0701d4
com.example.allinone:attr/tabIndicatorGravity = 0x7f040471
com.example.allinone:dimen/m3_comp_top_app_bar_small_container_height = 0x7f0701d0
com.example.allinone:attr/actionButtonStyle = 0x7f04000e
com.example.allinone:attr/shapeAppearance = 0x7f0403fa
com.example.allinone:attr/materialCalendarHeaderConfirmButton = 0x7f04030e
com.example.allinone:id/animateToStart = 0x7f0a0069
com.example.allinone:attr/minHeight = 0x7f040340
com.example.allinone:dimen/m3_comp_top_app_bar_medium_container_height = 0x7f0701ce
com.example.allinone:dimen/m3_comp_time_picker_period_selector_pressed_state_layer_opacity = 0x7f0701c9
com.example.allinone:layout/mtrl_picker_header_title_text = 0x7f0d00ac
com.example.allinone:color/material_slider_inactive_track_color = 0x7f0602f1
com.example.allinone:dimen/m3_comp_time_picker_period_selector_hover_state_layer_opacity = 0x7f0701c7
com.example.allinone:dimen/m3_comp_time_picker_container_elevation = 0x7f0701c5
com.example.allinone:string/most_recent_workout = 0x7f130175
com.example.allinone:drawable/ic_mtrl_chip_checked_black = 0x7f080149
com.example.allinone:attr/layout_constraintWidth_max = 0x7f0402ce
com.example.allinone:dimen/m3_comp_switch_unselected_pressed_state_layer_opacity = 0x7f0701c0
com.example.allinone:dimen/mtrl_card_elevation = 0x7f0702c4
com.example.allinone:dimen/material_helper_text_font_1_3_padding_top = 0x7f070264
com.example.allinone:dimen/m3_comp_switch_unselected_focus_state_layer_opacity = 0x7f0701be
com.example.allinone:menu/menu_group_options = 0x7f0f0004
com.example.allinone:color/m3_ref_palette_dynamic_neutral95 = 0x7f0600f3
com.example.allinone:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x2 = 0x7f07021c
com.example.allinone:dimen/m3_comp_switch_selected_hover_state_layer_opacity = 0x7f0701ba
com.example.allinone:style/Widget.AppCompat.Light.ActionButton = 0x7f140350
com.example.allinone:dimen/m3_comp_switch_disabled_unselected_handle_opacity = 0x7f0701b7
com.example.allinone:style/Widget.Material3.FloatingActionButton.Small.Surface = 0x7f1403d7
com.example.allinone:string/field_required = 0x7f1300fc
com.example.allinone:macro/m3_comp_fab_primary_container_shape = 0x7f0e0037
com.example.allinone:id/radio = 0x7f0a02e0
com.example.allinone:dimen/material_cursor_width = 0x7f070256
com.example.allinone:dimen/m3_comp_switch_disabled_selected_icon_opacity = 0x7f0701b5
com.example.allinone:style/Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f140117
com.example.allinone:color/material_dynamic_neutral60 = 0x7f06026a
com.example.allinone:dimen/m3_comp_suggestion_chip_flat_outline_width = 0x7f0701b2
com.example.allinone:color/m3_ref_palette_primary0 = 0x7f060168
com.example.allinone:integer/mtrl_calendar_header_orientation = 0x7f0b0035
com.example.allinone:style/Theme.Material3.DynamicColors.Dark.NoActionBar = 0x7f140273
com.example.allinone:string/investment_type = 0x7f130111
com.example.allinone:dimen/m3_comp_suggestion_chip_container_height = 0x7f0701af
com.example.allinone:anim/m3_side_sheet_exit_to_right = 0x7f010028
com.example.allinone:dimen/m3_comp_snackbar_container_elevation = 0x7f0701ae
com.example.allinone:attr/constraintRotate = 0x7f040144
com.example.allinone:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f14031e
com.example.allinone:dimen/m3_comp_input_chip_with_leading_icon_leading_icon_size = 0x7f070159
com.example.allinone:dimen/m3_comp_slider_disabled_inactive_track_opacity = 0x7f0701ab
com.example.allinone:dimen/m3_comp_slider_disabled_handle_opacity = 0x7f0701aa
com.example.allinone:color/mtrl_switch_track_decoration_tint = 0x7f060323
com.example.allinone:dimen/m3_comp_slider_active_handle_leading_space = 0x7f0701a7
com.example.allinone:dimen/m3_comp_sheet_side_docked_standard_container_elevation = 0x7f0701a5
com.example.allinone:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f1401e7
com.example.allinone:string/exo_controls_cc_enabled_description = 0x7f1300c0
com.example.allinone:dimen/m3_comp_switch_disabled_track_opacity = 0x7f0701b6
com.example.allinone:dimen/m3_comp_sheet_side_docked_modal_container_elevation = 0x7f0701a4
com.example.allinone:dimen/m3_comp_sheet_bottom_docked_drag_handle_width = 0x7f0701a0
com.example.allinone:color/expiration_color = 0x7f060090
com.example.allinone:id/design_menu_item_action_area = 0x7f0a00fd
com.example.allinone:dimen/m3_comp_secondary_navigation_tab_hover_state_layer_opacity = 0x7f07019d
com.example.allinone:macro/m3_comp_outlined_text_field_focus_outline_color = 0x7f0e00bb
com.example.allinone:color/m3_ref_palette_primary100 = 0x7f06016a
com.example.allinone:attr/fabCradleMargin = 0x7f0401e9
com.example.allinone:dimen/m3_comp_search_view_docked_header_container_height = 0x7f070199
com.example.allinone:integer/mtrl_calendar_selection_text_lines = 0x7f0b0036
com.example.allinone:macro/m3_comp_switch_selected_focus_track_color = 0x7f0e0123
com.example.allinone:dimen/m3_comp_search_bar_avatar_size = 0x7f070193
com.example.allinone:dimen/m3_comp_scrim_container_opacity = 0x7f070192
com.example.allinone:style/Widget.MaterialComponents.Toolbar.Surface = 0x7f1404aa
com.example.allinone:attr/dragThreshold = 0x7f04019f
com.example.allinone:dimen/m3_comp_radio_button_unselected_focus_state_layer_opacity = 0x7f07018f
com.example.allinone:attr/layout_goneMarginEnd = 0x7f0402d6
com.example.allinone:dimen/mtrl_calendar_content_padding = 0x7f07029a
com.example.allinone:dimen/m3_comp_progress_indicator_stop_indicator_size = 0x7f070188
com.example.allinone:style/ShapeAppearance.MaterialComponents.Badge = 0x7f1401ac
com.example.allinone:dimen/m3_comp_primary_navigation_tab_with_icon_icon_size = 0x7f070186
com.example.allinone:dimen/m3_comp_outlined_text_field_disabled_label_text_opacity = 0x7f07017b
com.example.allinone:dimen/m3_btn_dialog_btn_spacing = 0x7f0700f1
com.example.allinone:style/ShapeAppearanceOverlay.Material3.Corner.Left = 0x7f1401b4
com.example.allinone:color/navy_background = 0x7f060331
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant17 = 0x7f0600fb
com.example.allinone:dimen/m3_comp_outlined_text_field_disabled_input_text_opacity = 0x7f07017a
com.example.allinone:color/m3_assist_chip_icon_tint_color = 0x7f0600a2
com.example.allinone:color/abc_tint_spinner = 0x7f060017
com.example.allinone:dimen/m3_navigation_item_horizontal_padding = 0x7f0701e5
com.example.allinone:integer/m3_sys_shape_corner_small_corner_family = 0x7f0b002a
com.example.allinone:dimen/m3_extended_fab_min_height = 0x7f0701d7
com.example.allinone:id/dragStart = 0x7f0a0113
com.example.allinone:attr/colorOnBackground = 0x7f04010e
com.example.allinone:dimen/m3_navigation_item_active_indicator_label_padding = 0x7f0701e4
com.example.allinone:styleable/ViewStubCompat = 0x7f1500b0
com.example.allinone:attr/colorOnSurface = 0x7f04011c
com.example.allinone:dimen/m3_comp_outlined_button_outline_width = 0x7f070174
com.example.allinone:dimen/m3_comp_navigation_rail_focus_state_layer_opacity = 0x7f07016e
com.example.allinone:style/Widget.App.Button.TextButton.Dialog.Action = 0x7f140328
com.example.allinone:macro/m3_comp_date_picker_modal_year_selection_year_selected_container_color = 0x7f0e001f
com.example.allinone:attr/placeholderText = 0x7f0403ab
com.example.allinone:style/Widget.AppCompat.Spinner = 0x7f14036e
com.example.allinone:dimen/m3_comp_navigation_rail_container_width = 0x7f07016d
com.example.allinone:drawable/abc_list_selector_disabled_holo_dark = 0x7f080055
com.example.allinone:attr/motionProgress = 0x7f040373
com.example.allinone:attr/trackColor = 0x7f040506
com.example.allinone:dimen/m3_comp_navigation_drawer_modal_container_elevation = 0x7f070167
com.example.allinone:attr/colorOnPrimary = 0x7f040113
com.example.allinone:dimen/m3_comp_navigation_bar_hover_state_layer_opacity = 0x7f070160
com.example.allinone:attr/singleLine = 0x7f040422
com.example.allinone:macro/m3_comp_navigation_bar_inactive_pressed_label_text_color = 0x7f0e0075
com.example.allinone:id/fileIcon = 0x7f0a0180
com.example.allinone:dimen/m3_comp_navigation_bar_active_indicator_height = 0x7f07015b
com.example.allinone:dimen/m3_comp_input_chip_with_avatar_avatar_size = 0x7f070158
com.example.allinone:attr/textAppearanceButton = 0x7f04048f
com.example.allinone:dimen/m3_comp_input_chip_container_height = 0x7f070156
com.example.allinone:style/ExoStyledControls.Button.Center.Previous = 0x7f140146
com.example.allinone:dimen/m3_comp_input_chip_container_elevation = 0x7f070155
com.example.allinone:color/material_dynamic_tertiary99 = 0x7f0602a3
com.example.allinone:dimen/m3_back_progress_bottom_container_max_scale_y_distance = 0x7f0700d2
com.example.allinone:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f14002a
com.example.allinone:dimen/m3_comp_filter_chip_flat_unselected_outline_width = 0x7f070153
com.example.allinone:style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f140076
com.example.allinone:dimen/m3_navigation_item_shape_inset_start = 0x7f0701e9
com.example.allinone:dimen/m3_comp_filter_chip_elevated_container_elevation = 0x7f070151
com.example.allinone:dimen/m3_comp_filter_chip_container_height = 0x7f070150
com.example.allinone:id/autoCompleteToEnd = 0x7f0a0078
com.example.allinone:string/common_signin_button_text_long = 0x7f13006f
com.example.allinone:string/note_saved = 0x7f1301c7
com.example.allinone:color/m3_sys_color_dark_tertiary = 0x7f0601bb
com.example.allinone:attr/counterTextAppearance = 0x7f04016f
com.example.allinone:dimen/m3_comp_filled_card_pressed_state_layer_opacity = 0x7f07014e
com.example.allinone:dimen/m3_comp_filled_card_hover_state_layer_opacity = 0x7f07014c
com.example.allinone:dimen/m3_navigation_rail_item_active_indicator_width = 0x7f0701f3
com.example.allinone:id/action_manage_groups = 0x7f0a0044
com.example.allinone:id/bounceBoth = 0x7f0a0090
com.example.allinone:dimen/m3_comp_filled_card_dragged_state_layer_opacity = 0x7f07014a
com.example.allinone:attr/backgroundTintMode = 0x7f04005b
com.example.allinone:id/transition_layout_save = 0x7f0a03aa
com.example.allinone:attr/expandedTitleMargin = 0x7f0401d6
com.example.allinone:attr/errorShown = 0x7f0401ce
com.example.allinone:color/material_personalized_color_primary_container = 0x7f0602d0
com.example.allinone:dimen/m3_comp_fab_primary_small_container_height = 0x7f070144
com.example.allinone:dimen/m3_comp_fab_primary_large_container_height = 0x7f070140
com.example.allinone:dimen/m3_comp_fab_primary_icon_size = 0x7f07013f
com.example.allinone:color/m3_sys_color_dynamic_secondary_fixed = 0x7f060209
com.example.allinone:color/m3_sys_color_dark_on_primary = 0x7f0601a4
com.example.allinone:dimen/m3_comp_fab_primary_hover_state_layer_opacity = 0x7f07013e
com.example.allinone:id/endDate = 0x7f0a012e
com.example.allinone:color/m3_slider_inactive_track_color = 0x7f060195
com.example.allinone:dimen/m3_comp_fab_primary_focus_state_layer_opacity = 0x7f07013c
com.example.allinone:color/material_dynamic_neutral_variant99 = 0x7f06027c
com.example.allinone:attr/errorContentDescription = 0x7f0401c9
com.example.allinone:dimen/mtrl_calendar_header_toggle_margin_top = 0x7f0702ab
com.example.allinone:dimen/m3_comp_fab_primary_container_height = 0x7f07013b
com.example.allinone:attr/marginLeftSystemWindowInsets = 0x7f0402fe
com.example.allinone:dimen/m3_comp_extended_fab_primary_pressed_container_elevation = 0x7f070138
com.example.allinone:dimen/m3_comp_extended_fab_primary_icon_size = 0x7f070137
com.example.allinone:color/secondary_text_disabled_material_dark = 0x7f060351
com.example.allinone:color/material_dynamic_secondary100 = 0x7f06028c
com.example.allinone:dimen/abc_text_size_body_2_material = 0x7f070040
com.example.allinone:attr/materialCardViewOutlinedStyle = 0x7f04031b
com.example.allinone:dimen/abc_alert_dialog_button_bar_height = 0x7f070010
com.example.allinone:mipmap/ic_launcher = 0x7f100000
com.example.allinone:id/addInvestmentButton = 0x7f0a0055
com.example.allinone:id/marginValue = 0x7f0a0208
com.example.allinone:dimen/design_tab_max_width = 0x7f07008b
com.example.allinone:attr/motionEasingAccelerated = 0x7f04035c
com.example.allinone:dimen/m3_comp_extended_fab_primary_container_elevation = 0x7f070131
com.example.allinone:id/progress_horizontal = 0x7f0a02dd
com.example.allinone:attr/layout_collapseParallaxMultiplier = 0x7f0402a2
com.example.allinone:attr/enforceTextAppearance = 0x7f0401c4
com.example.allinone:string/delete_image_confirmation = 0x7f13007c
com.example.allinone:dimen/m3_comp_date_picker_modal_header_container_height = 0x7f07012a
com.example.allinone:string/group_color = 0x7f130103
com.example.allinone:attr/extendedFloatingActionButtonSurfaceStyle = 0x7f0401e2
com.example.allinone:attr/removeEmbeddedFabElevation = 0x7f0403d9
com.example.allinone:attr/shapeAppearanceSmallComponent = 0x7f040403
com.example.allinone:attr/verticalOffsetWithText = 0x7f040527
com.example.allinone:dimen/m3_comp_checkbox_selected_disabled_container_opacity = 0x7f070128
com.example.allinone:dimen/notification_big_circle_margin = 0x7f070333
com.example.allinone:attr/linearProgressIndicatorStyle = 0x7f0402e9
com.example.allinone:dimen/m3_comp_badge_size = 0x7f070125
com.example.allinone:dimen/m3_comp_assist_chip_flat_outline_width = 0x7f070122
com.example.allinone:id/checkboxListButton = 0x7f0a00bd
com.example.allinone:attr/colorPrimarySurface = 0x7f04012b
com.example.allinone:dimen/mtrl_textinput_box_corner_radius_medium = 0x7f070321
com.example.allinone:attr/carousel_touchUp_velocityThreshold = 0x7f0400ba
com.example.allinone:attr/listPreferredItemHeight = 0x7f0402f2
com.example.allinone:attr/defaultNavHost = 0x7f040188
com.example.allinone:color/m3_sys_color_dynamic_dark_on_primary_container = 0x7f0601c7
com.example.allinone:dimen/m3_chip_elevated_elevation = 0x7f07011c
com.example.allinone:attr/colorOnContainerUnchecked = 0x7f040110
com.example.allinone:drawable/bg_day_with_events = 0x7f08007d
com.example.allinone:dimen/m3_chip_checked_hovered_translation_z = 0x7f070118
com.example.allinone:string/google_crash_reporting_api_key = 0x7f130100
com.example.allinone:dimen/m3_carousel_extra_small_item_size = 0x7f070113
com.example.allinone:dimen/m3_card_stroke_width = 0x7f070111
com.example.allinone:string/error_icon_content_description = 0x7f1300aa
com.example.allinone:id/scrollable = 0x7f0a02fe
com.example.allinone:id/positionsTitle = 0x7f0a02c4
com.example.allinone:color/design_default_color_secondary_variant = 0x7f060070
com.example.allinone:dimen/m3_card_elevated_hovered_z = 0x7f07010e
com.example.allinone:dimen/m3_card_elevated_dragged_z = 0x7f07010c
com.example.allinone:dimen/m3_card_elevated_disabled_z = 0x7f07010b
com.example.allinone:id/parentRelative = 0x7f0a02a6
com.example.allinone:dimen/m3_comp_elevated_button_container_elevation = 0x7f07012d
com.example.allinone:dimen/m3_card_dragged_z = 0x7f07010a
com.example.allinone:dimen/m3_bottom_nav_min_height = 0x7f0700e6
com.example.allinone:id/bounceEnd = 0x7f0a0091
com.example.allinone:attr/listItemLayout = 0x7f0402ee
com.example.allinone:dimen/m3_btn_translation_z_base = 0x7f070107
com.example.allinone:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f140185
com.example.allinone:dimen/mtrl_calendar_action_padding = 0x7f070298
com.example.allinone:color/material_dynamic_primary10 = 0x7f06027e
com.example.allinone:color/white = 0x7f060369
com.example.allinone:style/Widget.MaterialComponents.Toolbar.PrimarySurface = 0x7f1404a9
com.example.allinone:string/mtrl_switch_track_decoration_path = 0x7f1301b0
com.example.allinone:color/mtrl_chip_background_color = 0x7f060305
com.example.allinone:dimen/m3_btn_text_btn_padding_left = 0x7f070105
com.example.allinone:color/mtrl_textinput_filled_box_default_background_color = 0x7f06032d
com.example.allinone:dimen/mtrl_card_dragged_z = 0x7f0702c3
com.example.allinone:id/fitCenter = 0x7f0a0189
com.example.allinone:style/Widget.MaterialComponents.CompoundButton.CheckBox = 0x7f14045b
com.example.allinone:attr/carousel_touchUpMode = 0x7f0400b8
com.example.allinone:layout/mtrl_alert_select_dialog_item = 0x7f0d0095
com.example.allinone:attr/itemActiveIndicatorStyle = 0x7f040270
com.example.allinone:id/workout_date_text = 0x7f0a03d6
com.example.allinone:color/background_floating_material_dark = 0x7f060020
com.example.allinone:dimen/m3_btn_icon_only_default_size = 0x7f0700f9
com.example.allinone:attr/logoAdjustViewBounds = 0x7f0402fa
com.example.allinone:color/medium_gray = 0x7f0602f8
com.example.allinone:dimen/m3_btn_icon_only_default_padding = 0x7f0700f8
com.example.allinone:layout/mtrl_picker_header_toggle = 0x7f0d00ad
com.example.allinone:style/Base.V14.Theme.MaterialComponents.Bridge = 0x7f140095
com.example.allinone:attr/rotationCenterId = 0x7f0403df
com.example.allinone:color/material_dynamic_primary90 = 0x7f060287
com.example.allinone:dimen/m3_comp_extended_fab_primary_focus_state_layer_opacity = 0x7f070134
com.example.allinone:color/m3_sys_color_dynamic_primary_fixed = 0x7f060207
com.example.allinone:dimen/m3_btn_icon_btn_padding_left = 0x7f0700f6
com.example.allinone:color/m3_ref_palette_primary50 = 0x7f06016e
com.example.allinone:style/Base.V21.Theme.AppCompat.Light = 0x7f1400a6
com.example.allinone:dimen/m3_sys_motion_easing_legacy_control_x2 = 0x7f070228
com.example.allinone:dimen/m3_btn_elevation = 0x7f0700f5
com.example.allinone:dimen/m3_btn_disabled_translation_z = 0x7f0700f3
com.example.allinone:id/indeterminate = 0x7f0a01c6
com.example.allinone:color/material_personalized_color_outline = 0x7f0602cd
com.example.allinone:dimen/m3_sys_motion_easing_standard_decelerate_control_x1 = 0x7f07023b
com.example.allinone:color/design_fab_stroke_end_inner_color = 0x7f060076
com.example.allinone:attr/iconTint = 0x7f040256
com.example.allinone:color/m3_sys_color_dynamic_primary_fixed_dim = 0x7f060208
com.example.allinone:macro/m3_comp_switch_unselected_icon_color = 0x7f0e013a
com.example.allinone:dimen/m3_btn_disabled_elevation = 0x7f0700f2
com.example.allinone:style/Widget.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f140421
com.example.allinone:attr/paddingBottomNoButtons = 0x7f040391
com.example.allinone:attr/logo = 0x7f0402f9
com.example.allinone:attr/title = 0x7f0404e8
com.example.allinone:attr/marginTopSystemWindowInsets = 0x7f040300
com.example.allinone:color/lesson_event_color = 0x7f06009e
com.example.allinone:id/skipCollapsed = 0x7f0a031d
com.example.allinone:dimen/m3_bottomappbar_fab_end_margin = 0x7f0700ed
com.example.allinone:attr/textureBlurFactor = 0x7f0404c6
com.example.allinone:string/exo_download_completed = 0x7f1300d9
com.example.allinone:dimen/m3_bottomappbar_fab_cradle_vertical_offset = 0x7f0700ec
com.example.allinone:id/instagramEditText = 0x7f0a01cc
com.example.allinone:color/m3_ref_palette_neutral_variant0 = 0x7f06015b
com.example.allinone:attr/actionBarTabTextStyle = 0x7f04000b
com.example.allinone:string/total = 0x7f13023f
com.example.allinone:dimen/m3_bottom_sheet_modal_elevation = 0x7f0700e9
com.example.allinone:attr/actionBarSize = 0x7f040006
com.example.allinone:attr/enterAnim = 0x7f0401c6
com.example.allinone:dimen/m3_bottom_nav_item_padding_top = 0x7f0700e5
com.example.allinone:style/Widget.Compat.NotificationActionContainer = 0x7f140376
com.example.allinone:dimen/m3_bottom_nav_item_active_indicator_width = 0x7f0700e3
com.example.allinone:string/mtrl_exceed_max_badge_number_content_description = 0x7f130183
com.example.allinone:dimen/m3_bottom_nav_item_active_indicator_height = 0x7f0700e1
com.example.allinone:dimen/m3_comp_suggestion_chip_elevated_container_elevation = 0x7f0701b0
com.example.allinone:dimen/mtrl_progress_circular_track_thickness_medium = 0x7f070303
com.example.allinone:dimen/m3_badge_with_text_vertical_padding = 0x7f0700e0
com.example.allinone:macro/m3_comp_secondary_navigation_tab_pressed_state_layer_color = 0x7f0e0101
com.example.allinone:dimen/m3_badge_with_text_offset = 0x7f0700dd
com.example.allinone:id/async = 0x7f0a0072
com.example.allinone:attr/fastScrollVerticalTrackDrawable = 0x7f0401f2
com.example.allinone:dimen/m3_badge_vertical_offset = 0x7f0700db
com.example.allinone:attr/daySelectedStyle = 0x7f040183
com.example.allinone:dimen/m3_btn_icon_only_min_width = 0x7f0700fb
com.example.allinone:dimen/m3_badge_horizontal_offset = 0x7f0700d8
com.example.allinone:string/edit_registration = 0x7f130098
com.example.allinone:id/filterCard = 0x7f0a0186
com.example.allinone:id/adjust_width = 0x7f0a005b
com.example.allinone:style/Widget.MaterialComponents.BottomNavigationView.Colored = 0x7f14043f
com.example.allinone:style/Theme.Design.Light = 0x7f14025d
com.example.allinone:color/androidx_core_secondary_text_default_material_light = 0x7f06001e
com.example.allinone:dimen/m3_card_elevated_elevation = 0x7f07010d
com.example.allinone:dimen/m3_comp_date_picker_modal_date_today_container_outline_width = 0x7f070129
com.example.allinone:dimen/m3_back_progress_side_container_max_scale_x_distance_shrink = 0x7f0700d6
com.example.allinone:dimen/m3_back_progress_side_container_max_scale_x_distance_grow = 0x7f0700d5
com.example.allinone:id/nameInputLayout = 0x7f0a0251
com.example.allinone:dimen/m3_back_progress_main_container_min_edge_gap = 0x7f0700d4
com.example.allinone:string/image_counter = 0x7f13010c
com.example.allinone:attr/passwordToggleEnabled = 0x7f04039f
com.example.allinone:dimen/m3_appbar_scrim_height_trigger_medium = 0x7f0700cd
com.example.allinone:drawable/mtrl_ic_checkbox_unchecked = 0x7f080194
com.example.allinone:dimen/mtrl_calendar_selection_text_baseline_to_bottom = 0x7f0702b5
com.example.allinone:dimen/abc_text_size_subtitle_material_toolbar = 0x7f07004e
com.example.allinone:drawable/exo_ic_pause_circle_filled = 0x7f0800c5
com.example.allinone:dimen/m3_appbar_scrim_height_trigger_large = 0x7f0700cc
com.example.allinone:macro/m3_comp_radio_button_selected_pressed_icon_color = 0x7f0e00dd
com.example.allinone:dimen/m3_alert_dialog_icon_size = 0x7f0700c7
com.example.allinone:attr/liftOnScrollTargetViewId = 0x7f0402e5
com.example.allinone:id/neverCompleteToEnd = 0x7f0a0270
com.example.allinone:id/appNameText = 0x7f0a006d
com.example.allinone:dimen/m3_alert_dialog_icon_margin = 0x7f0700c6
com.example.allinone:attr/haloColor = 0x7f040238
com.example.allinone:dimen/mtrl_extended_fab_end_padding = 0x7f0702cf
com.example.allinone:dimen/m3_timepicker_display_stroke_width = 0x7f070243
com.example.allinone:attr/contentPaddingTop = 0x7f040159
com.example.allinone:dimen/m3_alert_dialog_action_bottom_padding = 0x7f0700c2
com.example.allinone:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f140022
com.example.allinone:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f0700c0
com.example.allinone:style/Widget.App.Button = 0x7f140327
com.example.allinone:string/common_google_play_services_updating_text = 0x7f13006b
com.example.allinone:id/endTimeField = 0x7f0a0130
com.example.allinone:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f0700bf
com.example.allinone:macro/m3_comp_switch_unselected_pressed_state_layer_color = 0x7f0e013d
com.example.allinone:attr/listPreferredItemHeightSmall = 0x7f0402f4
com.example.allinone:dimen/hint_alpha_material_dark = 0x7f0700bb
com.example.allinone:macro/m3_comp_filled_card_container_color = 0x7f0e0046
com.example.allinone:dimen/highlight_alpha_material_light = 0x7f0700ba
com.example.allinone:dimen/fastscroll_minimum_range = 0x7f0700b7
com.example.allinone:dimen/exo_styled_progress_layout_height = 0x7f0700b2
com.example.allinone:dimen/exo_styled_progress_dragged_thumb_size = 0x7f0700b0
com.example.allinone:dimen/exo_styled_minimal_controls_margin_bottom = 0x7f0700ae
com.example.allinone:id/material_textinput_timepicker = 0x7f0a021e
com.example.allinone:attr/animateCircleAngleTo = 0x7f040037
com.example.allinone:attr/actionModeCutDrawable = 0x7f040018
com.example.allinone:dimen/abc_select_dialog_padding_start_material = 0x7f07003a
com.example.allinone:dimen/exo_styled_controls_padding = 0x7f0700ad
com.example.allinone:dimen/exo_styled_progress_touch_target_height = 0x7f0700b4
com.example.allinone:color/ic_launcher_background = 0x7f06009c
com.example.allinone:string/exo_controls_stop_description = 0x7f1300d6
com.example.allinone:dimen/exo_styled_bottom_bar_margin_top = 0x7f0700ab
com.example.allinone:id/clockwise = 0x7f0a00c5
com.example.allinone:id/textSourcePost = 0x7f0a0371
com.example.allinone:dimen/exo_small_icon_horizontal_margin = 0x7f0700a6
com.example.allinone:dimen/exo_settings_offset = 0x7f0700a2
com.example.allinone:dimen/m3_comp_navigation_bar_active_indicator_width = 0x7f07015c
com.example.allinone:dimen/exo_settings_main_text_size = 0x7f0700a1
com.example.allinone:dimen/exo_media_button_width = 0x7f07009d
com.example.allinone:dimen/exo_media_button_height = 0x7f07009c
com.example.allinone:layout/abc_dialog_title_material = 0x7f0d000c
com.example.allinone:attr/flow_horizontalBias = 0x7f04020a
com.example.allinone:drawable/mtrl_switch_thumb = 0x7f08019a
com.example.allinone:dimen/m3_chip_icon_size = 0x7f07011e
com.example.allinone:dimen/exo_icon_text_size = 0x7f07009b
com.example.allinone:style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox = 0x7f1401c3
com.example.allinone:dimen/exo_icon_padding = 0x7f070098
com.example.allinone:color/mtrl_btn_stroke_color_selector = 0x7f0602fb
com.example.allinone:attr/extendedFloatingActionButtonSecondaryStyle = 0x7f0401e0
com.example.allinone:dimen/exo_error_message_text_size = 0x7f070096
com.example.allinone:color/material_dynamic_secondary70 = 0x7f060292
com.example.allinone:dimen/exo_error_message_margin_bottom = 0x7f070093
com.example.allinone:attr/boxCornerRadiusBottomStart = 0x7f04008e
com.example.allinone:dimen/design_textinput_caption_translate_y = 0x7f07008f
com.example.allinone:dimen/design_tab_scrollable_min_width = 0x7f07008c
com.example.allinone:drawable/ic_m3_chip_checked_circle = 0x7f080145
com.example.allinone:id/transactionDate = 0x7f0a03a0
com.example.allinone:drawable/exo_ic_chevron_right = 0x7f0800c0
com.example.allinone:drawable/design_ic_visibility = 0x7f0800a8
com.example.allinone:id/center_vertical = 0x7f0a00b7
com.example.allinone:dimen/design_snackbar_min_width = 0x7f070086
com.example.allinone:color/m3_sys_color_light_tertiary = 0x7f06022d
com.example.allinone:attr/actionModeTheme = 0x7f040020
com.example.allinone:dimen/design_snackbar_extra_spacing_horizontal = 0x7f070084
com.example.allinone:style/Base.V14.Theme.MaterialComponents.Dialog.Bridge = 0x7f140097
com.example.allinone:dimen/design_snackbar_elevation = 0x7f070083
com.example.allinone:string/m3_sys_motion_easing_standard_accelerate = 0x7f130122
com.example.allinone:macro/m3_comp_navigation_drawer_inactive_hover_state_layer_color = 0x7f0e008b
com.example.allinone:color/m3_sys_color_light_surface_container_highest = 0x7f060228
com.example.allinone:dimen/design_snackbar_action_text_color_alpha = 0x7f070081
com.example.allinone:color/m3_ref_palette_dynamic_primary99 = 0x7f06011b
com.example.allinone:attr/region_heightLessThan = 0x7f0403d5
com.example.allinone:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f1401d4
com.example.allinone:dimen/m3_navigation_menu_headline_horizontal_padding = 0x7f0701ed
com.example.allinone:style/Theme.AppCompat.NoActionBar = 0x7f14025a
com.example.allinone:string/notes_optional = 0x7f1301ca
com.example.allinone:style/Theme.AppCompat.CompactMenu = 0x7f140246
com.example.allinone:macro/m3_comp_switch_selected_hover_icon_color = 0x7f0e0126
com.example.allinone:macro/m3_comp_search_view_header_input_text_type = 0x7f0e00f5
com.example.allinone:dimen/design_navigation_padding_bottom = 0x7f07007e
com.example.allinone:attr/mock_diagonalsColor = 0x7f040345
com.example.allinone:dimen/design_navigation_max_width = 0x7f07007d
com.example.allinone:dimen/design_navigation_item_icon_padding = 0x7f07007b
com.example.allinone:dimen/design_navigation_icon_size = 0x7f070079
com.example.allinone:string/material_motion_easing_emphasized = 0x7f130168
com.example.allinone:attr/grid_horizontalGaps = 0x7f04022e
com.example.allinone:string/receipt_unavailable = 0x7f1301e9
com.example.allinone:attr/region_widthLessThan = 0x7f0403d7
com.example.allinone:dimen/design_navigation_icon_padding = 0x7f070078
com.example.allinone:string/error_adding_drawing = 0x7f1300a1
com.example.allinone:color/mtrl_filled_stroke_color = 0x7f060312
com.example.allinone:string/delete_voice_note = 0x7f13008a
com.example.allinone:style/Widget.Design.BottomNavigationView = 0x7f140379
com.example.allinone:macro/m3_comp_radio_button_unselected_hover_state_layer_color = 0x7f0e00e2
com.example.allinone:dimen/design_fab_border_width = 0x7f070070
com.example.allinone:id/accessibility_custom_action_18 = 0x7f0a001b
com.example.allinone:id/SHIFT = 0x7f0a0007
com.example.allinone:string/material_hour_selection = 0x7f130162
com.example.allinone:integer/status_bar_notification_info_maxnum = 0x7f0b0048
com.example.allinone:id/month_navigation_previous = 0x7f0a0232
com.example.allinone:dimen/m3_comp_top_app_bar_large_container_height = 0x7f0701cd
com.example.allinone:dimen/design_bottom_sheet_elevation = 0x7f07006d
com.example.allinone:macro/m3_comp_navigation_bar_inactive_focus_state_layer_color = 0x7f0e006e
com.example.allinone:attr/endIconContentDescription = 0x7f0401bc
com.example.allinone:dimen/design_bottom_navigation_label_padding = 0x7f070069
com.example.allinone:dimen/design_bottom_navigation_active_text_size = 0x7f070063
com.example.allinone:macro/m3_comp_time_picker_time_selector_unselected_hover_state_layer_color = 0x7f0e0169
com.example.allinone:dimen/m3_navigation_rail_item_min_height = 0x7f0701f4
com.example.allinone:drawable/ic_fitness = 0x7f08012e
com.example.allinone:dimen/def_drawer_elevation = 0x7f07005f
com.example.allinone:dimen/compat_notification_large_icon_max_height = 0x7f07005d
com.example.allinone:attr/materialDividerHeavyStyle = 0x7f040320
com.example.allinone:attr/helperTextTextColor = 0x7f04023f
com.example.allinone:attr/percentY = 0x7f0403a7
com.example.allinone:dimen/m3_comp_elevated_card_icon_size = 0x7f070130
com.example.allinone:dimen/compat_control_corner_material = 0x7f07005c
com.example.allinone:color/common_google_signin_btn_text_dark_pressed = 0x7f06004d
com.example.allinone:dimen/compat_button_padding_vertical_material = 0x7f07005b
com.example.allinone:color/exo_black_opacity_70 = 0x7f060089
com.example.allinone:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f1401ec
com.example.allinone:color/vector_tint_theme_color = 0x7f060365
com.example.allinone:dimen/cardview_default_elevation = 0x7f070055
com.example.allinone:style/Base.V26.Theme.AppCompat = 0x7f1400b8
com.example.allinone:attr/dividerInsetStart = 0x7f040199
com.example.allinone:dimen/cardview_compat_inset_shadow = 0x7f070054
com.example.allinone:id/attachmentNameText = 0x7f0a0073
com.example.allinone:attr/layout_constraintCircleAngle = 0x7f0402ad
com.example.allinone:attr/hintEnabled = 0x7f040248
com.example.allinone:id/profileImage = 0x7f0a02d2
com.example.allinone:color/video_blue = 0x7f060367
com.example.allinone:attr/path_percent = 0x7f0403a3
com.example.allinone:id/textSourceMetrics = 0x7f0a0370
com.example.allinone:id/pnlLabel = 0x7f0a02b7
com.example.allinone:drawable/ic_code = 0x7f080121
com.example.allinone:dimen/abc_text_size_subhead_material = 0x7f07004d
com.example.allinone:dimen/m3_comp_search_bar_container_height = 0x7f070195
com.example.allinone:style/Base.V22.Theme.AppCompat.Light = 0x7f1400b1
com.example.allinone:drawable/mtrl_switch_thumb_pressed_checked = 0x7f08019f
com.example.allinone:attr/collapseContentDescription = 0x7f0400f7
com.example.allinone:dimen/m3_btn_max_width = 0x7f0700fd
com.example.allinone:dimen/m3_sys_motion_easing_standard_decelerate_control_x2 = 0x7f07023c
com.example.allinone:string/exo_controls_playback_speed = 0x7f1300cb
com.example.allinone:color/design_default_color_on_secondary = 0x7f06006a
com.example.allinone:dimen/abc_text_size_display_4_material = 0x7f070046
com.example.allinone:string/select_month = 0x7f130205
com.example.allinone:color/m3_dynamic_default_color_primary_text = 0x7f0600c2
com.example.allinone:dimen/abc_text_size_display_3_material = 0x7f070045
com.example.allinone:dimen/abc_text_size_body_1_material = 0x7f07003f
com.example.allinone:dimen/abc_switch_padding = 0x7f07003e
com.example.allinone:macro/m3_comp_menu_list_item_selected_container_color = 0x7f0e005e
com.example.allinone:dimen/abc_star_medium = 0x7f07003c
com.example.allinone:style/TextAppearance.AppCompat.Widget.Switch = 0x7f1401f2
com.example.allinone:dimen/abc_star_big = 0x7f07003b
com.example.allinone:dimen/m3_comp_secondary_navigation_tab_pressed_state_layer_opacity = 0x7f07019e
com.example.allinone:macro/m3_comp_switch_unselected_pressed_track_outline_color = 0x7f0e013f
com.example.allinone:layout/mtrl_calendar_month = 0x7f0d009d
com.example.allinone:id/titleDividerNoCustom = 0x7f0a038b
com.example.allinone:dimen/abc_search_view_preferred_width = 0x7f070037
com.example.allinone:macro/m3_comp_time_picker_period_selector_unselected_pressed_state_layer_color = 0x7f0e015d
com.example.allinone:dimen/abc_search_view_preferred_height = 0x7f070036
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar.Day.Today = 0x7f140468
com.example.allinone:color/design_icon_tint = 0x7f06007a
com.example.allinone:color/ripple_material_dark = 0x7f06034d
com.example.allinone:attr/transitionEasing = 0x7f040515
com.example.allinone:dimen/design_tab_text_size_2line = 0x7f07008e
com.example.allinone:id/shortcut = 0x7f0a0316
com.example.allinone:dimen/abc_progress_bar_height_material = 0x7f070035
com.example.allinone:style/Widget.Material3.ActionBar.Solid = 0x7f140383
com.example.allinone:animator/m3_extended_fab_show_motion_spec = 0x7f020013
com.example.allinone:string/student_deleted = 0x7f130221
com.example.allinone:attr/borderWidth = 0x7f040082
com.example.allinone:id/action_bar_container = 0x7f0a0037
com.example.allinone:dimen/material_emphasis_high_type = 0x7f07025a
com.example.allinone:dimen/abc_panel_menu_list_width = 0x7f070034
com.example.allinone:dimen/m3_appbar_scrim_height_trigger = 0x7f0700cb
com.example.allinone:dimen/abc_list_item_height_small_material = 0x7f070032
com.example.allinone:dimen/abc_list_item_height_material = 0x7f070031
com.example.allinone:dimen/mtrl_calendar_header_selection_line_height = 0x7f0702a8
com.example.allinone:id/type_icon = 0x7f0a03b5
com.example.allinone:attr/actionModeWebSearchDrawable = 0x7f040021
com.example.allinone:dimen/abc_floating_window_z = 0x7f07002f
com.example.allinone:attr/colorErrorContainer = 0x7f04010b
com.example.allinone:dimen/hint_pressed_alpha_material_dark = 0x7f0700bd
com.example.allinone:dimen/abc_edit_text_inset_top_material = 0x7f07002e
com.example.allinone:dimen/abc_edit_text_inset_horizontal_material = 0x7f07002d
com.example.allinone:attr/cornerSizeTopRight = 0x7f04016a
com.example.allinone:style/Widget.AppCompat.RatingBar = 0x7f140367
com.example.allinone:color/albumCard = 0x7f06001c
com.example.allinone:dimen/abc_dropdownitem_icon_width = 0x7f070029
com.example.allinone:drawable/material_cursor_drawable = 0x7f080178
com.example.allinone:dimen/abc_dialog_title_divider_material = 0x7f070026
com.example.allinone:style/Base.Widget.AppCompat.SearchView = 0x7f1400f5
com.example.allinone:dimen/abc_dialog_min_width_minor = 0x7f070023
com.example.allinone:color/m3_ref_palette_dynamic_primary0 = 0x7f06010f
com.example.allinone:dimen/m3_btn_padding_top = 0x7f070101
com.example.allinone:dimen/m3_simple_item_color_selected_alpha = 0x7f07020f
com.example.allinone:style/Base.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f140074
com.example.allinone:dimen/abc_dialog_list_padding_top_no_title = 0x7f070021
com.example.allinone:dimen/abc_dialog_fixed_width_major = 0x7f07001e
com.example.allinone:attr/show_previous_button = 0x7f040413
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant40 = 0x7f060101
com.example.allinone:attr/materialSearchViewPrefixStyle = 0x7f040327
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant96 = 0x7f06010c
com.example.allinone:dimen/m3_navigation_menu_divider_horizontal_padding = 0x7f0701ec
com.example.allinone:string/weekly_workout_duration = 0x7f13024e
com.example.allinone:style/Widget.AppCompat.TextView = 0x7f140372
com.example.allinone:dimen/abc_control_padding_material = 0x7f07001a
com.example.allinone:color/m3_ref_palette_neutral_variant70 = 0x7f060163
com.example.allinone:integer/m3_btn_anim_duration_ms = 0x7f0b0010
com.example.allinone:attr/customColorValue = 0x7f040178
com.example.allinone:dimen/abc_control_corner_material = 0x7f070018
com.example.allinone:dimen/abc_action_bar_content_inset_material = 0x7f070000
com.example.allinone:dimen/abc_config_prefDialogWidth = 0x7f070017
com.example.allinone:attr/materialCircleRadius = 0x7f04031d
com.example.allinone:dimen/mtrl_bottomappbar_fab_bottom_margin = 0x7f070279
com.example.allinone:string/no_registrations = 0x7f1301bf
com.example.allinone:interpolator/m3_sys_motion_easing_linear = 0x7f0c000a
com.example.allinone:drawable/ic_pause = 0x7f080154
com.example.allinone:id/special_effects_controller_view_tag = 0x7f0a0328
com.example.allinone:attr/shapeAppearanceLargeComponent = 0x7f040400
com.example.allinone:attr/tabRippleColor = 0x7f04047c
com.example.allinone:attr/nestedScrollViewStyle = 0x7f040382
com.example.allinone:dimen/m3_badge_with_text_size = 0x7f0700de
com.example.allinone:string/call_notification_incoming_text = 0x7f130041
com.example.allinone:style/Base.Theme.MaterialComponents.DialogWhenLarge = 0x7f14006d
com.example.allinone:dimen/abc_button_padding_vertical_material = 0x7f070015
com.example.allinone:dimen/exo_settings_icon_size = 0x7f0700a0
com.example.allinone:string/share_note = 0x7f13020d
com.example.allinone:integer/material_motion_duration_long_2 = 0x7f0b002c
com.example.allinone:attr/nestedScrollFlags = 0x7f040381
com.example.allinone:dimen/abc_action_button_min_height_material = 0x7f07000d
com.example.allinone:attr/selectionRequired = 0x7f0403f7
com.example.allinone:drawable/mtrl_checkbox_button_icon_indeterminate_unchecked = 0x7f080189
com.example.allinone:color/material_personalized_color_text_hint_foreground_inverse = 0x7f0602e4
com.example.allinone:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f07000c
com.example.allinone:color/m3_radiobutton_ripple_tint = 0x7f0600dd
com.example.allinone:style/Base.Theme.MaterialComponents.Dialog = 0x7f140068
com.example.allinone:dimen/abc_action_bar_stacked_tab_max_width = 0x7f07000a
com.example.allinone:attr/keyboardIcon = 0x7f040290
com.example.allinone:style/Base.Widget.Material3.FloatingActionButton.Small = 0x7f14010d
com.example.allinone:style/TextAppearance.AppCompat.Menu = 0x7f1401d7
com.example.allinone:style/Widget.App.TextInputLayout = 0x7f14032c
com.example.allinone:macro/m3_comp_elevated_button_container_color = 0x7f0e0029
com.example.allinone:attr/scrubber_disabled_size = 0x7f0403ea
com.example.allinone:drawable/$mtrl_checkbox_button_icon_checked_unchecked__2 = 0x7f080013
com.example.allinone:macro/m3_comp_outlined_text_field_disabled_input_text_color = 0x7f0e00b2
com.example.allinone:attr/hideAnimationBehavior = 0x7f040240
com.example.allinone:color/design_dark_default_color_secondary = 0x7f060062
com.example.allinone:dimen/mtrl_btn_disabled_z = 0x7f070281
com.example.allinone:dimen/abc_action_bar_overflow_padding_start_material = 0x7f070008
com.example.allinone:attr/methodName = 0x7f04033e
com.example.allinone:dimen/m3_sys_motion_easing_standard_accelerate_control_y1 = 0x7f070235
com.example.allinone:string/error_permission_denied = 0x7f1300b0
com.example.allinone:attr/isLightTheme = 0x7f04026c
com.example.allinone:dimen/m3_btn_dialog_btn_min_width = 0x7f0700f0
com.example.allinone:dimen/abc_action_bar_overflow_padding_end_material = 0x7f070007
com.example.allinone:attr/actionModeSplitBackground = 0x7f04001e
com.example.allinone:color/primary_text_default_material_dark = 0x7f060340
com.example.allinone:dimen/m3_comp_text_button_hover_state_layer_opacity = 0x7f0701c2
com.example.allinone:dimen/abc_action_bar_content_inset_with_nav = 0x7f070001
com.example.allinone:attr/layout_constraintBottom_toBottomOf = 0x7f0402aa
com.example.allinone:style/ShapeAppearanceOverlay.Material3.Chip = 0x7f1401b2
com.example.allinone:style/Widget.Material3.ExtendedFloatingActionButton.Surface = 0x7f1403cd
com.example.allinone:dimen/m3_sys_state_pressed_state_layer_opacity = 0x7f070242
com.example.allinone:attr/textAppearanceSearchResultSubtitle = 0x7f0404a7
com.example.allinone:attr/helperTextEnabled = 0x7f04023d
com.example.allinone:color/wt_bottom_nav_item_color = 0x7f06036a
com.example.allinone:dimen/design_fab_translation_z_hovered_focused = 0x7f070075
com.example.allinone:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f140055
com.example.allinone:color/tooltip_background_dark = 0x7f060361
com.example.allinone:color/text_input_text_color = 0x7f06035f
com.example.allinone:attr/cardMaxElevation = 0x7f0400ac
com.example.allinone:macro/m3_comp_navigation_drawer_inactive_focus_state_layer_color = 0x7f0e0088
com.example.allinone:drawable/design_snackbar_background = 0x7f0800ab
com.example.allinone:attr/tooltipText = 0x7f0404ff
com.example.allinone:color/text_input_box_stroke = 0x7f06035e
com.example.allinone:id/contentPanel = 0x7f0a00d8
com.example.allinone:style/Widget.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1403fe
com.example.allinone:dimen/m3_comp_filter_chip_flat_container_elevation = 0x7f070152
com.example.allinone:attr/fastScrollHorizontalThumbDrawable = 0x7f0401ef
com.example.allinone:dimen/abc_text_size_menu_header_material = 0x7f07004a
com.example.allinone:color/material_personalized_color_text_primary_inverse_disable_only = 0x7f0602e6
com.example.allinone:color/mtrl_navigation_bar_item_tint = 0x7f060316
com.example.allinone:color/switch_thumb_material_dark = 0x7f060358
com.example.allinone:color/switch_thumb_disabled_material_light = 0x7f060357
com.example.allinone:drawable/mtrl_switch_thumb_unchecked_pressed = 0x7f0801a3
com.example.allinone:id/voiceNoteIndicator = 0x7f0a03cb
com.example.allinone:color/surface_color = 0x7f060355
com.example.allinone:color/secondary_text_disabled_material_light = 0x7f060352
com.example.allinone:string/material_slider_range_start = 0x7f13016c
com.example.allinone:attr/hintAnimationEnabled = 0x7f040247
com.example.allinone:dimen/m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity = 0x7f070182
com.example.allinone:color/secondary_text_default_material_light = 0x7f060350
com.example.allinone:attr/popupMenuStyle = 0x7f0403b9
com.example.allinone:attr/textAppearanceSmallPopupMenu = 0x7f0404a9
com.example.allinone:color/m3_sys_color_light_on_error_container = 0x7f060215
com.example.allinone:style/Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f1402a8
com.example.allinone:color/registration_end = 0x7f06034b
com.example.allinone:attr/theme = 0x7f0404ca
com.example.allinone:color/reelsCard = 0x7f060348
com.example.allinone:color/red = 0x7f060346
com.example.allinone:attr/layout_goneMarginRight = 0x7f0402d8
com.example.allinone:color/purple = 0x7f060344
com.example.allinone:color/primary_text_disabled_material_dark = 0x7f060342
com.example.allinone:id/exo_main_text = 0x7f0a015f
com.example.allinone:color/primary_text_default_material_light = 0x7f060341
com.example.allinone:drawable/ic_search_black_24 = 0x7f08015c
com.example.allinone:color/primary_material_light = 0x7f06033f
com.example.allinone:color/primary_material_dark = 0x7f06033e
com.example.allinone:dimen/exo_error_message_text_padding_vertical = 0x7f070095
com.example.allinone:color/poor_red = 0x7f06033b
com.example.allinone:styleable/CheckedTextView = 0x7f150020
com.example.allinone:color/red_500 = 0x7f060347
com.example.allinone:string/dropdown_menu = 0x7f130094
com.example.allinone:dimen/m3_comp_fab_primary_container_elevation = 0x7f07013a
com.example.allinone:color/notification_action_color_filter = 0x7f060336
com.example.allinone:color/navy_text_secondary = 0x7f060334
com.example.allinone:string/error_quota_exceeded = 0x7f1300b2
com.example.allinone:dimen/mtrl_badge_text_size = 0x7f070274
com.example.allinone:drawable/$mtrl_checkbox_button_unchecked_checked__0 = 0x7f08001e
com.example.allinone:dimen/mtrl_switch_thumb_elevation = 0x7f07031c
com.example.allinone:attr/checkMarkTint = 0x7f0400be
com.example.allinone:drawable/exo_ic_forward = 0x7f0800c2
com.example.allinone:drawable/common_google_signin_btn_icon_dark_normal = 0x7f080093
com.example.allinone:color/mtrl_textinput_disabled_color = 0x7f06032c
com.example.allinone:attr/fontProviderQuery = 0x7f04021f
com.example.allinone:color/m3_ref_palette_dynamic_neutral50 = 0x7f0600ea
com.example.allinone:color/mtrl_tabs_ripple_color = 0x7f060329
com.example.allinone:style/Base.TextAppearance.AppCompat.Display2 = 0x7f14001c
com.example.allinone:attr/itemShapeAppearance = 0x7f04027e
com.example.allinone:color/material_personalized_primary_inverse_text_disable_only = 0x7f0602eb
com.example.allinone:dimen/mtrl_low_ripple_default_alpha = 0x7f0702e3
com.example.allinone:color/mtrl_tabs_legacy_text_color_selector = 0x7f060328
com.example.allinone:dimen/design_bottom_sheet_peek_height_min = 0x7f07006f
com.example.allinone:color/mtrl_tabs_icon_color_selector = 0x7f060326
com.example.allinone:color/mtrl_switch_track_tint = 0x7f060324
com.example.allinone:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
com.example.allinone:attr/subheaderColor = 0x7f040453
com.example.allinone:color/mtrl_switch_thumb_icon_tint = 0x7f060321
com.example.allinone:color/mtrl_popupmenu_overlay_color = 0x7f06031f
com.example.allinone:drawable/exo_icon_repeat_off = 0x7f0800d7
com.example.allinone:dimen/abc_text_size_title_material_toolbar = 0x7f070050
com.example.allinone:anim/nav_default_pop_enter_anim = 0x7f01002e
com.example.allinone:color/mtrl_outlined_stroke_color = 0x7f06031e
com.example.allinone:color/mtrl_outlined_icon_tint = 0x7f06031d
com.example.allinone:string/delete_group = 0x7f130079
com.example.allinone:attr/itemRippleColor = 0x7f04027d
com.example.allinone:style/TextAppearance.Compat.Notification.Line2.Media = 0x7f1401f8
com.example.allinone:drawable/btn_radio_on_mtrl = 0x7f08008b
com.example.allinone:color/m3_sys_color_dark_inverse_on_surface = 0x7f06019e
com.example.allinone:color/mtrl_on_primary_text_btn_text_color_selector = 0x7f06031b
com.example.allinone:string/m3c_date_range_picker_scroll_to_next_month = 0x7f130144
com.example.allinone:dimen/mtrl_navigation_rail_text_bottom_margin = 0x7f0702f7
com.example.allinone:color/mtrl_navigation_item_text_color = 0x7f06031a
com.example.allinone:attr/motionEasingEmphasizedDecelerateInterpolator = 0x7f040360
com.example.allinone:id/sharedValueUnset = 0x7f0a0315
com.example.allinone:attr/contentPadding = 0x7f040153
com.example.allinone:color/mtrl_navigation_item_background_color = 0x7f060318
com.example.allinone:color/mtrl_indicator_text_color = 0x7f060313
com.example.allinone:drawable/test_level_drawable = 0x7f0801bb
com.example.allinone:attr/buttonBarStyle = 0x7f04009b
com.example.allinone:attr/titleMarginStart = 0x7f0404ef
com.example.allinone:color/material_dynamic_tertiary0 = 0x7f060297
com.example.allinone:dimen/m3_comp_suggestion_chip_flat_container_elevation = 0x7f0701b1
com.example.allinone:attr/controller_layout_id = 0x7f04015d
com.example.allinone:dimen/item_touch_helper_swipe_escape_velocity = 0x7f0700c1
com.example.allinone:style/Widget.Material3.CollapsingToolbar.Medium = 0x7f1403c1
com.example.allinone:style/Widget.Material3.BottomSheet = 0x7f140392
com.example.allinone:color/mtrl_filled_icon_tint = 0x7f060311
com.example.allinone:attr/buttonBarNegativeButtonStyle = 0x7f040098
com.example.allinone:style/Widget.Material3.Button.OutlinedButton = 0x7f14039d
com.example.allinone:color/mtrl_filled_background_color = 0x7f060310
com.example.allinone:color/mtrl_fab_bg_color_selector = 0x7f06030d
com.example.allinone:color/mtrl_card_view_ripple = 0x7f060304
com.example.allinone:drawable/ic_search_white = 0x7f08015d
com.example.allinone:color/mtrl_card_view_foreground = 0x7f060303
com.example.allinone:color/m3_ref_palette_neutral10 = 0x7f060144
com.example.allinone:attr/behavior_draggable = 0x7f040075
com.example.allinone:color/mtrl_btn_transparent_bg_color = 0x7f060300
com.example.allinone:styleable/AnimatedStateListDrawableTransition = 0x7f15000c
com.example.allinone:color/m3_sys_color_dynamic_light_surface_container_highest = 0x7f0601fa
com.example.allinone:color/mtrl_btn_text_color_disabled = 0x7f0602fe
com.example.allinone:color/mtrl_btn_text_btn_bg_color_selector = 0x7f0602fc
com.example.allinone:color/reels_purple = 0x7f06034a
com.example.allinone:color/mtrl_btn_bg_color_selector = 0x7f0602f9
com.example.allinone:font/opensans = 0x7f090000
com.example.allinone:dimen/mtrl_alert_dialog_background_inset_start = 0x7f07026d
com.example.allinone:color/material_timepicker_modebutton_tint = 0x7f0602f7
com.example.allinone:attr/flow_verticalGap = 0x7f040215
com.example.allinone:style/Widget.AppCompat.EditText = 0x7f140345
com.example.allinone:dimen/mtrl_extended_fab_icon_text_spacing = 0x7f0702d2
com.example.allinone:macro/m3_comp_navigation_rail_container_color = 0x7f0e0099
com.example.allinone:attr/viewTransitionOnPositiveCross = 0x7f04052c
com.example.allinone:dimen/mtrl_fab_elevation = 0x7f0702db
com.example.allinone:layout/abc_screen_toolbar = 0x7f0d0017
com.example.allinone:attr/bottomNavigationStyle = 0x7f040086
com.example.allinone:dimen/m3_carousel_gone_size = 0x7f070114
com.example.allinone:color/material_slider_thumb_color = 0x7f0602f2
com.example.allinone:attr/barrierMargin = 0x7f040072
com.example.allinone:dimen/m3_extended_fab_start_padding = 0x7f0701d8
com.example.allinone:animator/m3_extended_fab_state_list_animator = 0x7f020014
com.example.allinone:layout/notification_template_big_media_custom = 0x7f0d00b8
com.example.allinone:dimen/notification_large_icon_width = 0x7f070336
com.example.allinone:color/material_personalized_color_text_secondary_and_tertiary_inverse_disabled = 0x7f0602e8
com.example.allinone:style/Widget.AppCompat.ProgressBar = 0x7f140365
com.example.allinone:drawable/$mtrl_checkbox_button_icon_checked_unchecked__1 = 0x7f080012
com.example.allinone:dimen/m3_btn_stroke_size = 0x7f070102
com.example.allinone:styleable/KeyFramesVelocity = 0x7f15004b
com.example.allinone:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y1 = 0x7f070221
com.example.allinone:attr/showAsAction = 0x7f040408
com.example.allinone:attr/paddingTopNoTitle = 0x7f040398
com.example.allinone:style/Base.V7.Widget.AppCompat.EditText = 0x7f1400c3
com.example.allinone:color/m3_button_ripple_color_selector = 0x7f0600a9
com.example.allinone:color/material_harmonized_color_error_container = 0x7f0602ac
com.example.allinone:string/close_drawer = 0x7f130050
com.example.allinone:dimen/m3_appbar_size_medium = 0x7f0700d0
com.example.allinone:color/material_personalized_color_text_primary_inverse = 0x7f0602e5
com.example.allinone:string/task_updated = 0x7f130230
com.example.allinone:color/dim_foreground_material_light = 0x7f060081
com.example.allinone:attr/layout_constrainedHeight = 0x7f0402a3
com.example.allinone:color/material_personalized_color_tertiary = 0x7f0602e2
com.example.allinone:style/Base.V14.Theme.MaterialComponents = 0x7f140094
com.example.allinone:color/default_event_color = 0x7f060055
com.example.allinone:color/material_personalized_color_surface_variant = 0x7f0602e1
com.example.allinone:dimen/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity = 0x7f070185
com.example.allinone:style/ThemeOverlay.Material3.HarmonizedColors = 0x7f1402e5
com.example.allinone:id/tab_ask_ai = 0x7f0a0355
com.example.allinone:drawable/ic_notification = 0x7f080150
com.example.allinone:color/material_personalized_color_surface_container_low = 0x7f0602dd
com.example.allinone:dimen/disabled_alpha_material_light = 0x7f070091
com.example.allinone:xml/backup_rules = 0x7f160000
com.example.allinone:color/material_personalized_color_surface_container_highest = 0x7f0602dc
com.example.allinone:string/group_title = 0x7f130107
com.example.allinone:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f140439
com.example.allinone:id/tag_unhandled_key_event_manager = 0x7f0a0362
com.example.allinone:color/material_personalized_color_surface_container_high = 0x7f0602db
com.example.allinone:style/Widget.Material3.MaterialCalendar.Year = 0x7f1403f2
com.example.allinone:style/Base.V14.Theme.MaterialComponents.Light.Dialog = 0x7f14009b
com.example.allinone:color/material_personalized_color_secondary = 0x7f0602d4
com.example.allinone:layout/mtrl_picker_header_fullscreen = 0x7f0d00aa
com.example.allinone:style/Widget.MaterialComponents.BottomAppBar.Colored = 0x7f14043c
com.example.allinone:drawable/common_google_signin_btn_text_light_normal_background = 0x7f0800a2
com.example.allinone:styleable/SwitchMaterial = 0x7f1500a0
com.example.allinone:color/material_personalized_color_primary_text_inverse = 0x7f0602d3
com.example.allinone:dimen/m3_comp_navigation_rail_active_indicator_height = 0x7f07016a
com.example.allinone:color/material_personalized_color_primary_text = 0x7f0602d2
com.example.allinone:macro/m3_comp_snackbar_container_shape = 0x7f0e0114
com.example.allinone:id/transitionToStart = 0x7f0a03a6
com.example.allinone:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__1 = 0x7f080016
com.example.allinone:string/item_view_role_description = 0x7f130113
com.example.allinone:attr/keylines = 0x7f040291
com.example.allinone:color/material_personalized_color_on_surface_variant = 0x7f0602ca
com.example.allinone:color/androidx_core_ripple_material_light = 0x7f06001d
com.example.allinone:integer/m3_badge_max_number = 0x7f0b000e
com.example.allinone:color/material_personalized_color_on_surface_inverse = 0x7f0602c9
com.example.allinone:color/material_personalized_color_on_secondary_container = 0x7f0602c7
com.example.allinone:id/tab_posts = 0x7f0a0357
com.example.allinone:color/material_personalized_color_on_primary_container = 0x7f0602c5
com.example.allinone:string/mtrl_switch_thumb_path_pressed = 0x7f1301ae
com.example.allinone:id/imageView = 0x7f0a01c1
com.example.allinone:color/material_personalized_color_on_primary = 0x7f0602c4
com.example.allinone:color/material_personalized_color_on_error_container = 0x7f0602c3
com.example.allinone:style/Widget.MaterialComponents.Slider = 0x7f140489
com.example.allinone:style/MaterialAlertDialog.Material3 = 0x7f140153
com.example.allinone:style/Base.V14.Theme.Material3.Dark.BottomSheetDialog = 0x7f14008d
com.example.allinone:attr/textFillColor = 0x7f0404b7
com.example.allinone:color/material_personalized_color_on_background = 0x7f0602c1
com.example.allinone:style/ShapeAppearanceOverlay.Material3.FloatingActionButton = 0x7f1401b7
com.example.allinone:color/material_personalized_color_error_container = 0x7f0602c0
com.example.allinone:color/material_personalized_color_error = 0x7f0602bf
com.example.allinone:color/material_personalized_color_control_normal = 0x7f0602be
com.example.allinone:color/material_personalized_color_background = 0x7f0602bb
com.example.allinone:style/Widget.MaterialComponents.ProgressIndicator = 0x7f140487
com.example.allinone:string/workout_exercise = 0x7f130254
com.example.allinone:color/material_on_surface_stroke = 0x7f0602b8
com.example.allinone:color/material_on_surface_emphasis_medium = 0x7f0602b7
com.example.allinone:style/Theme.MaterialComponents.CompactMenu = 0x7f140283
com.example.allinone:string/mtrl_picker_range_header_unselected = 0x7f13019c
com.example.allinone:dimen/mtrl_slider_track_height = 0x7f070312
com.example.allinone:color/material_on_background_emphasis_high_type = 0x7f0602b0
com.example.allinone:color/material_on_background_disabled = 0x7f0602af
com.example.allinone:attr/telltales_tailScale = 0x7f040487
com.example.allinone:color/material_harmonized_color_on_error_container = 0x7f0602ae
com.example.allinone:style/TextAppearance.Material3.SearchView = 0x7f140228
com.example.allinone:attr/flow_firstVerticalBias = 0x7f040207
com.example.allinone:dimen/m3_comp_elevated_button_disabled_container_elevation = 0x7f07012e
com.example.allinone:color/material_harmonized_color_on_error = 0x7f0602ad
com.example.allinone:id/nav_wt_registry = 0x7f0a0266
com.example.allinone:color/material_grey_900 = 0x7f0602aa
com.example.allinone:color/material_grey_850 = 0x7f0602a9
com.example.allinone:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f140049
com.example.allinone:style/Base.Widget.AppCompat.ActionBar = 0x7f1400c5
com.example.allinone:id/exo_subtitles = 0x7f0a0177
com.example.allinone:color/material_dynamic_tertiary70 = 0x7f06029f
com.example.allinone:attr/scrubber_drawable = 0x7f0403ec
com.example.allinone:color/abc_tint_btn_checkable = 0x7f060013
com.example.allinone:attr/actionMenuTextColor = 0x7f040012
com.example.allinone:color/m3_sys_color_dynamic_tertiary_fixed_dim = 0x7f06020c
com.example.allinone:styleable/MaterialAutoCompleteTextView = 0x7f150057
com.example.allinone:color/material_dynamic_tertiary50 = 0x7f06029d
com.example.allinone:color/material_dynamic_tertiary40 = 0x7f06029c
com.example.allinone:color/m3_ref_palette_dynamic_primary20 = 0x7f060112
com.example.allinone:drawable/mtrl_switch_thumb_unchecked_checked = 0x7f0801a2
com.example.allinone:color/material_dynamic_tertiary20 = 0x7f06029a
com.example.allinone:drawable/$mtrl_checkbox_button_checked_unchecked__2 = 0x7f08000f
com.example.allinone:attr/autoCompleteTextViewStyle = 0x7f040047
com.example.allinone:color/material_dynamic_tertiary80 = 0x7f0602a0
com.example.allinone:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f140029
com.example.allinone:color/material_dynamic_secondary95 = 0x7f060295
com.example.allinone:color/material_dynamic_secondary90 = 0x7f060294
com.example.allinone:color/material_dynamic_secondary40 = 0x7f06028f
com.example.allinone:attr/layout_constraintBottom_creator = 0x7f0402a9
com.example.allinone:id/mtrl_picker_fullscreen = 0x7f0a0243
com.example.allinone:macro/m3_comp_time_picker_headline_type = 0x7f0e0151
com.example.allinone:macro/m3_comp_outlined_card_dragged_outline_color = 0x7f0e00ab
com.example.allinone:id/percent = 0x7f0a02ae
com.example.allinone:string/mtrl_picker_save = 0x7f13019d
com.example.allinone:style/Base.Widget.Material3.Chip = 0x7f140104
com.example.allinone:color/material_dynamic_secondary0 = 0x7f06028a
com.example.allinone:attr/colorSurfaceVariant = 0x7f04013c
com.example.allinone:color/m3_ref_palette_dynamic_tertiary0 = 0x7f060129
com.example.allinone:attr/drawerArrowStyle = 0x7f0401aa
com.example.allinone:color/material_dynamic_primary99 = 0x7f060289
com.example.allinone:color/m3_sys_color_light_inverse_surface = 0x7f060212
com.example.allinone:id/src_over = 0x7f0a0333
com.example.allinone:style/Base.V14.Widget.MaterialComponents.AutoCompleteTextView = 0x7f1400a3
com.example.allinone:color/material_dynamic_primary95 = 0x7f060288
com.example.allinone:color/m3_ref_palette_tertiary100 = 0x7f060184
com.example.allinone:id/topPanel = 0x7f0a0395
com.example.allinone:layout/exo_list_divider = 0x7f0d0048
com.example.allinone:id/pooling_container_listener_holder_tag = 0x7f0a02bc
com.example.allinone:id/accessibility_custom_action_21 = 0x7f0a001f
com.example.allinone:attr/state_error = 0x7f040448
com.example.allinone:attr/startIconContentDescription = 0x7f04043d
com.example.allinone:id/exo_settings_listview = 0x7f0a0172
com.example.allinone:color/material_dynamic_primary60 = 0x7f060284
com.example.allinone:color/mtrl_fab_ripple_color = 0x7f06030f
com.example.allinone:style/Base.v27.Theme.SplashScreen = 0x7f140124
com.example.allinone:color/material_dynamic_primary40 = 0x7f060282
com.example.allinone:style/Widget.Material3.MaterialDivider = 0x7f1403f6
com.example.allinone:color/material_dynamic_primary20 = 0x7f060280
com.example.allinone:style/ThemeOverlay.Material3.FloatingActionButton.Primary = 0x7f1402e1
com.example.allinone:color/material_personalized_color_surface = 0x7f0602d8
com.example.allinone:attr/forceApplySystemWindowInsetTop = 0x7f040224
com.example.allinone:color/material_dynamic_primary100 = 0x7f06027f
com.example.allinone:dimen/m3_btn_icon_only_icon_padding = 0x7f0700fa
com.example.allinone:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f140348
com.example.allinone:dimen/m3_fab_border_width = 0x7f0701da
com.example.allinone:color/material_dynamic_primary0 = 0x7f06027d
com.example.allinone:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f14049b
com.example.allinone:layout/dialog_expense_investment = 0x7f0d003a
com.example.allinone:layout/fragment_wt_seminars = 0x7f0d005e
com.example.allinone:id/totalAmountText = 0x7f0a0396
com.example.allinone:dimen/mtrl_tooltip_arrowSize = 0x7f07032b
com.example.allinone:color/design_default_color_background = 0x7f060065
com.example.allinone:color/material_dynamic_neutral_variant90 = 0x7f06027a
com.example.allinone:string/end_date = 0x7f13009e
com.example.allinone:color/material_dynamic_neutral_variant80 = 0x7f060279
com.example.allinone:color/material_dynamic_neutral_variant60 = 0x7f060277
com.example.allinone:attr/showMarker = 0x7f04040b
com.example.allinone:plurals/exo_controls_fastforward_by_amount_description = 0x7f110001
com.example.allinone:color/material_dynamic_neutral_variant50 = 0x7f060276
com.example.allinone:attr/itemIconSize = 0x7f040276
com.example.allinone:dimen/m3_navigation_drawer_layout_corner_size = 0x7f0701e3
com.example.allinone:attr/actionModeFindDrawable = 0x7f040019
com.example.allinone:color/material_dynamic_neutral_variant40 = 0x7f060275
com.example.allinone:attr/firstBaselineToTopHeight = 0x7f0401f5
com.example.allinone:color/material_dynamic_neutral_variant0 = 0x7f060270
com.example.allinone:color/material_dynamic_neutral40 = 0x7f060268
com.example.allinone:attr/springBoundary = 0x7f040433
com.example.allinone:color/material_dynamic_neutral20 = 0x7f060266
com.example.allinone:color/material_dynamic_neutral10 = 0x7f060264
com.example.allinone:color/material_dynamic_color_light_on_error = 0x7f060261
com.example.allinone:style/ShapeAppearance.MaterialComponents.LargeComponent = 0x7f1401ad
com.example.allinone:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f070020
com.example.allinone:drawable/$m3_avd_show_password__0 = 0x7f08000a
com.example.allinone:attr/errorEnabled = 0x7f0401ca
com.example.allinone:string/side_sheet_behavior = 0x7f130210
com.example.allinone:color/material_dynamic_color_dark_on_error_container = 0x7f06025e
com.example.allinone:attr/telltales_velocityMode = 0x7f040488
com.example.allinone:style/ExoStyledControls.Button.Center.RewWithAmount = 0x7f140147
com.example.allinone:attr/pivotAnchor = 0x7f0403a9
com.example.allinone:color/m3_sys_color_dark_surface = 0x7f0601b2
com.example.allinone:color/material_divider_color = 0x7f06025a
com.example.allinone:id/disableIntraAutoTransition = 0x7f0a0108
com.example.allinone:dimen/m3_comp_switch_track_width = 0x7f0701bd
com.example.allinone:color/purple_500 = 0x7f060345
com.example.allinone:id/hideable = 0x7f0a01b1
com.example.allinone:color/material_deep_teal_500 = 0x7f060259
com.example.allinone:color/material_deep_teal_200 = 0x7f060258
com.example.allinone:id/transition_position = 0x7f0a03ac
com.example.allinone:attr/hintTextColor = 0x7f04024a
com.example.allinone:id/mtrl_picker_header = 0x7f0a0244
com.example.allinone:color/material_cursor_color = 0x7f060257
com.example.allinone:attr/tabGravity = 0x7f040469
com.example.allinone:attr/paddingStartSystemWindowInsets = 0x7f040397
com.example.allinone:style/Widget.AppCompat.Light.PopupMenu = 0x7f140359
com.example.allinone:dimen/m3_sys_motion_easing_legacy_decelerate_control_y2 = 0x7f07022e
com.example.allinone:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox = 0x7f1402c3
com.example.allinone:attr/checkedIconTint = 0x7f0400c8
com.example.allinone:color/material_blue_grey_900 = 0x7f060255
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant24 = 0x7f0600fe
com.example.allinone:dimen/exo_setting_width = 0x7f07009e
com.example.allinone:animator/fragment_close_enter = 0x7f020003
com.example.allinone:attr/textAppearanceListItemSmall = 0x7f0404a4
com.example.allinone:color/m3_timepicker_time_input_stroke_color = 0x7f060252
com.example.allinone:string/m3c_time_picker_period_toggle_description = 0x7f130158
com.example.allinone:macro/m3_comp_switch_unselected_hover_icon_color = 0x7f0e0136
com.example.allinone:attr/floatingActionButtonSmallPrimaryStyle = 0x7f0401fd
com.example.allinone:color/m3_timepicker_display_text_color = 0x7f06024f
com.example.allinone:attr/layout_constraintBaseline_toTopOf = 0x7f0402a8
com.example.allinone:attr/colorTertiary = 0x7f04013e
com.example.allinone:dimen/abc_alert_dialog_button_dimen = 0x7f070011
com.example.allinone:color/m3_textfield_indicator_text_color = 0x7f060245
com.example.allinone:id/attachmentsSection = 0x7f0a0075
com.example.allinone:color/m3_textfield_filled_background_color = 0x7f060244
com.example.allinone:id/ghost_view = 0x7f0a019e
com.example.allinone:color/m3_text_button_ripple_color_selector = 0x7f060243
com.example.allinone:color/m3_text_button_background_color_selector = 0x7f060241
com.example.allinone:attr/setsTag = 0x7f0403f9
com.example.allinone:attr/visibilityMode = 0x7f04052d
com.example.allinone:color/m3_tabs_text_color = 0x7f06023f
com.example.allinone:string/google_app_id = 0x7f1300ff
com.example.allinone:color/m3_tabs_ripple_color_secondary = 0x7f06023e
com.example.allinone:color/design_dark_default_color_on_error = 0x7f06005b
com.example.allinone:styleable/Snackbar = 0x7f150092
com.example.allinone:color/m3_sys_color_tertiary_fixed_dim = 0x7f06023a
com.example.allinone:integer/mtrl_btn_anim_duration_ms = 0x7f0b0034
com.example.allinone:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f1401e6
com.example.allinone:attr/floatingActionButtonLargeStyle = 0x7f0401f8
com.example.allinone:dimen/m3_ripple_selectable_pressed_alpha = 0x7f0701fe
com.example.allinone:dimen/mtrl_snackbar_margin = 0x7f070318
com.example.allinone:color/m3_sys_color_dynamic_light_surface_container_lowest = 0x7f0601fc
com.example.allinone:style/TextAppearance.AppCompat.Caption = 0x7f1401c8
com.example.allinone:color/m3_sys_color_tertiary_fixed = 0x7f060239
com.example.allinone:color/m3_sys_color_secondary_fixed_dim = 0x7f060238
com.example.allinone:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f140053
com.example.allinone:attr/maxButtonHeight = 0x7f040332
com.example.allinone:color/material_personalized_color_outline_variant = 0x7f0602ce
com.example.allinone:attr/navigationIconTint = 0x7f04037d
com.example.allinone:attr/circularProgressIndicatorStyle = 0x7f0400e2
com.example.allinone:dimen/m3_comp_time_picker_time_selector_focus_state_layer_opacity = 0x7f0701ca
com.example.allinone:attr/onPositiveCross = 0x7f04038b
com.example.allinone:color/m3_sys_color_primary_fixed_dim = 0x7f060236
com.example.allinone:attr/checkboxStyle = 0x7f0400c0
com.example.allinone:color/m3_sys_color_on_primary_fixed = 0x7f06022f
com.example.allinone:style/Base.TextAppearance.AppCompat.Title = 0x7f14002f
com.example.allinone:attr/elevation = 0x7f0401b5
com.example.allinone:color/m3_sys_color_light_tertiary_container = 0x7f06022e
com.example.allinone:color/m3_card_stroke_color = 0x7f0600ae
com.example.allinone:macro/m3_comp_radio_button_selected_focus_icon_color = 0x7f0e00d8
com.example.allinone:attr/dataPattern = 0x7f040181
com.example.allinone:color/m3_sys_color_light_surface_variant = 0x7f06022c
com.example.allinone:attr/textInputFilledExposedDropdownMenuStyle = 0x7f0404b9
com.example.allinone:styleable/BottomSheetBehavior_Layout = 0x7f15001b
com.example.allinone:color/m3_button_outline_color_selector = 0x7f0600a7
com.example.allinone:string/delete_note = 0x7f13007e
com.example.allinone:color/m3_sys_color_light_surface_container_low = 0x7f060229
com.example.allinone:attr/dropDownBackgroundTint = 0x7f0401ad
com.example.allinone:attr/carousel_touchUp_dampeningFactor = 0x7f0400b9
com.example.allinone:color/m3_sys_color_light_surface_bright = 0x7f060225
com.example.allinone:id/beginOnFirstDraw = 0x7f0a0087
com.example.allinone:drawable/exo_controls_repeat_one = 0x7f0800b7
com.example.allinone:color/m3_sys_color_light_secondary_container = 0x7f060223
com.example.allinone:dimen/m3_bottomappbar_horizontal_padding = 0x7f0700ef
com.example.allinone:color/material_personalized_color_on_tertiary_container = 0x7f0602cc
com.example.allinone:color/m3_sys_color_light_secondary = 0x7f060222
com.example.allinone:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f140351
com.example.allinone:dimen/m3_comp_time_picker_period_selector_outline_width = 0x7f0701c8
com.example.allinone:color/m3_sys_color_light_primary_container = 0x7f060221
com.example.allinone:layout/abc_popup_menu_header_item_layout = 0x7f0d0012
com.example.allinone:attr/cornerFamilyBottomRight = 0x7f040162
com.example.allinone:color/m3_sys_color_light_primary = 0x7f060220
com.example.allinone:macro/m3_comp_extended_fab_secondary_container_color = 0x7f0e0030
com.example.allinone:color/design_dark_default_color_on_secondary = 0x7f06005d
com.example.allinone:color/m3_sys_color_light_outline_variant = 0x7f06021f
com.example.allinone:attr/staggered = 0x7f04043a
com.example.allinone:color/m3_sys_color_light_outline = 0x7f06021e
com.example.allinone:bool/mtrl_btn_textappearance_all_caps = 0x7f050005
com.example.allinone:style/Base.Widget.MaterialComponents.TextView = 0x7f140121
com.example.allinone:color/m3_ref_palette_error100 = 0x7f060138
com.example.allinone:style/ThemeOverlay.Material3.DynamicColors.DayNight = 0x7f1402db
com.example.allinone:color/m3_sys_color_light_on_tertiary_container = 0x7f06021d
com.example.allinone:dimen/mtrl_alert_dialog_picker_background_inset = 0x7f07026f
com.example.allinone:color/abc_search_url_text_normal = 0x7f06000e
com.example.allinone:color/m3_ref_palette_tertiary50 = 0x7f060188
com.example.allinone:color/m3_sys_color_light_on_secondary_container = 0x7f060219
com.example.allinone:layout/item_transaction_report = 0x7f0d0079
com.example.allinone:dimen/fastscroll_margin = 0x7f0700b6
com.example.allinone:color/m3_sys_color_light_on_background = 0x7f060213
com.example.allinone:id/disablePostScroll = 0x7f0a0109
com.example.allinone:color/m3_sys_color_light_inverse_primary = 0x7f060211
com.example.allinone:dimen/abc_text_size_display_1_material = 0x7f070043
com.example.allinone:string/m3c_date_picker_scroll_to_earlier_years = 0x7f130135
com.example.allinone:id/design_navigation_view = 0x7f0a0100
com.example.allinone:attr/layout_constraintStart_toStartOf = 0x7f0402c4
com.example.allinone:attr/paddingBottomSystemWindowInsets = 0x7f040392
com.example.allinone:color/m3_sys_color_light_error = 0x7f06020e
com.example.allinone:dimen/m3_fab_translation_z_pressed = 0x7f0701dd
com.example.allinone:dimen/m3_badge_offset = 0x7f0700d9
com.example.allinone:style/Widget.Material3.TextInputEditText.OutlinedBox = 0x7f140420
com.example.allinone:string/avg_duration = 0x7f130034
com.example.allinone:color/m3_ref_palette_error50 = 0x7f06013c
com.example.allinone:dimen/m3_comp_primary_navigation_tab_active_indicator_height = 0x7f070181
com.example.allinone:color/m3_sys_color_dynamic_tertiary_fixed = 0x7f06020b
com.example.allinone:id/phoneInputLayout = 0x7f0a02b0
com.example.allinone:color/m3_sys_color_dynamic_on_secondary_fixed_variant = 0x7f060204
com.example.allinone:color/m3_sys_color_dynamic_on_primary_fixed_variant = 0x7f060202
com.example.allinone:id/exo_basic_controls = 0x7f0a014e
com.example.allinone:drawable/$avd_hide_password__0 = 0x7f080000
com.example.allinone:dimen/m3_comp_filled_button_with_icon_icon_size = 0x7f070148
com.example.allinone:color/m3_sys_color_dynamic_on_primary_fixed = 0x7f060201
com.example.allinone:dimen/m3_chip_dragged_translation_z = 0x7f07011b
com.example.allinone:color/m3_sys_color_primary_fixed = 0x7f060235
com.example.allinone:color/m3_sys_color_dynamic_light_tertiary = 0x7f0601ff
com.example.allinone:attr/transitionShapeAppearance = 0x7f040518
com.example.allinone:layout/fragment_investments = 0x7f0d0057
com.example.allinone:attr/materialCalendarHeaderCancelButton = 0x7f04030d
com.example.allinone:color/m3_ref_palette_secondary100 = 0x7f060177
com.example.allinone:string/exo_track_bitrate = 0x7f1300e3
com.example.allinone:attr/actionModeCloseDrawable = 0x7f040016
com.example.allinone:drawable/exo_styled_controls_play = 0x7f0800f0
com.example.allinone:color/m3_sys_color_dynamic_light_surface_variant = 0x7f0601fe
com.example.allinone:color/m3_sys_color_dynamic_light_surface_dim = 0x7f0601fd
com.example.allinone:string/error_saving_recording = 0x7f1300b5
com.example.allinone:macro/m3_comp_filled_text_field_container_shape = 0x7f0e004c
com.example.allinone:drawable/abc_list_pressed_holo_light = 0x7f080052
com.example.allinone:dimen/mtrl_extended_fab_min_width = 0x7f0702d4
com.example.allinone:id/eventTimeInput = 0x7f0a0140
com.example.allinone:attr/textAppearanceLineHeightEnabled = 0x7f0404a1
com.example.allinone:color/m3_sys_color_dynamic_light_surface_container = 0x7f0601f8
com.example.allinone:string/note_deleted = 0x7f1301c6
com.example.allinone:id/accessibility_custom_action_15 = 0x7f0a0018
com.example.allinone:dimen/design_fab_translation_z_pressed = 0x7f070076
com.example.allinone:color/material_dynamic_color_light_error_container = 0x7f060260
com.example.allinone:drawable/mtrl_switch_thumb_checked_unchecked = 0x7f08019d
com.example.allinone:drawable/ic_launcher_background = 0x7f080140
com.example.allinone:color/m3_sys_color_dynamic_light_secondary = 0x7f0601f4
com.example.allinone:string/color_teal = 0x7f13005b
com.example.allinone:color/m3_sys_color_dynamic_light_primary = 0x7f0601f2
com.example.allinone:attr/autoSizeTextType = 0x7f04004d
com.example.allinone:style/Theme.Material3.Light.NoActionBar = 0x7f14027e
com.example.allinone:dimen/m3_sys_motion_easing_standard_decelerate_control_y2 = 0x7f07023e
com.example.allinone:color/material_dynamic_secondary30 = 0x7f06028e
com.example.allinone:color/design_snackbar_background_color = 0x7f06007b
com.example.allinone:attr/hideMotionSpec = 0x7f040241
com.example.allinone:color/m3_sys_color_dynamic_light_on_tertiary_container = 0x7f0601ef
com.example.allinone:id/swipeRefreshLayout = 0x7f0a0350
com.example.allinone:color/m3_sys_color_dynamic_light_on_surface = 0x7f0601ec
com.example.allinone:dimen/mtrl_navigation_rail_margin = 0x7f0702f6
com.example.allinone:attr/customDimension = 0x7f040179
com.example.allinone:attr/deltaPolarRadius = 0x7f04018e
com.example.allinone:color/m3_sys_color_dynamic_light_on_secondary_container = 0x7f0601eb
com.example.allinone:color/m3_sys_color_dynamic_light_on_secondary = 0x7f0601ea
com.example.allinone:dimen/mtrl_calendar_day_vertical_padding = 0x7f07029f
com.example.allinone:string/custom_workout = 0x7f130075
com.example.allinone:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense = 0x7f140499
com.example.allinone:color/m3_sys_color_dynamic_light_on_primary_container = 0x7f0601e9
com.example.allinone:color/m3_sys_color_dynamic_light_on_primary = 0x7f0601e8
com.example.allinone:color/m3_sys_color_dynamic_light_on_error = 0x7f0601e6
com.example.allinone:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f140037
com.example.allinone:style/ExoStyledControls = 0x7f140135
com.example.allinone:attr/badgeWidth = 0x7f040067
com.example.allinone:drawable/notification_bg_normal = 0x7f0801ad
com.example.allinone:attr/motionDurationLong2 = 0x7f040351
com.example.allinone:color/m3_sys_color_dynamic_light_on_background = 0x7f0601e5
com.example.allinone:attr/materialCalendarYearNavigationButton = 0x7f040318
com.example.allinone:color/m3_sys_color_dynamic_light_inverse_on_surface = 0x7f0601e2
com.example.allinone:drawable/exo_styled_controls_check = 0x7f0800e8
com.example.allinone:attr/textColorSearchUrl = 0x7f0404b5
com.example.allinone:anim/linear_indeterminate_line1_tail_interpolator = 0x7f01001e
com.example.allinone:dimen/m3_comp_fab_primary_hover_container_elevation = 0x7f07013d
com.example.allinone:style/Base.Theme.AppCompat.Light.Dialog = 0x7f140054
com.example.allinone:color/navy_accent = 0x7f060330
com.example.allinone:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f1400ba
com.example.allinone:color/m3_sys_color_dynamic_light_error_container = 0x7f0601e1
com.example.allinone:styleable/DefaultTimeBar = 0x7f150033
com.example.allinone:drawable/mtrl_checkbox_button_icon_checked_unchecked = 0x7f080187
com.example.allinone:attr/hintTextAppearance = 0x7f040249
com.example.allinone:attr/textAppearanceHeadline1 = 0x7f040494
com.example.allinone:attr/materialCalendarHeaderLayout = 0x7f040310
com.example.allinone:attr/suffixText = 0x7f04045d
com.example.allinone:color/m3_sys_color_dynamic_light_error = 0x7f0601e0
com.example.allinone:color/m3_sys_color_dynamic_light_background = 0x7f0601df
com.example.allinone:attr/checkedIconVisible = 0x7f0400c9
com.example.allinone:color/m3_sys_color_dynamic_dark_on_primary = 0x7f0601c6
com.example.allinone:color/m3_sys_color_dynamic_dark_tertiary_container = 0x7f0601de
com.example.allinone:styleable/LinearProgressIndicator = 0x7f150052
com.example.allinone:color/m3_sys_color_dynamic_dark_tertiary = 0x7f0601dd
com.example.allinone:attr/materialAlertDialogTheme = 0x7f040303
com.example.allinone:style/Widget.Material3.CompoundButton.CheckBox = 0x7f1403c2
com.example.allinone:color/m3_sys_color_dynamic_dark_surface_variant = 0x7f0601dc
com.example.allinone:attr/actionModeCloseContentDescription = 0x7f040015
com.example.allinone:integer/mtrl_badge_max_character_count = 0x7f0b0032
com.example.allinone:dimen/m3_comp_navigation_bar_container_height = 0x7f07015e
com.example.allinone:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f08002e
com.example.allinone:color/m3_sys_color_dynamic_dark_surface_dim = 0x7f0601db
com.example.allinone:color/m3_sys_color_dynamic_dark_surface_container_lowest = 0x7f0601da
com.example.allinone:color/m3_sys_color_dynamic_dark_surface_container = 0x7f0601d6
com.example.allinone:attr/arcMode = 0x7f04003f
com.example.allinone:color/m3_sys_color_dynamic_dark_surface = 0x7f0601d4
com.example.allinone:id/exo_rew = 0x7f0a016f
com.example.allinone:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface = 0x7f1403c9
com.example.allinone:attr/imageAspectRatioAdjust = 0x7f04025c
com.example.allinone:color/mtrl_btn_text_color_selector = 0x7f0602ff
com.example.allinone:style/Theme.Material3.DynamicColors.DayNight.NoActionBar = 0x7f140275
com.example.allinone:color/m3_sys_color_dynamic_dark_secondary_container = 0x7f0601d3
com.example.allinone:color/m3_sys_color_dynamic_dark_primary = 0x7f0601d0
com.example.allinone:attr/dividerHorizontal = 0x7f040197
com.example.allinone:color/m3_sys_color_dynamic_dark_outline = 0x7f0601ce
com.example.allinone:animator/mtrl_fab_hide_motion_spec = 0x7f02001e
com.example.allinone:id/investmentDropdown = 0x7f0a01d3
com.example.allinone:styleable/ThemeEnforcement = 0x7f1500a7
com.example.allinone:drawable/abc_btn_colored_material = 0x7f080030
com.example.allinone:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f14049a
com.example.allinone:dimen/m3_comp_time_input_time_input_field_focus_outline_width = 0x7f0701c4
com.example.allinone:style/Widget.AppCompat.ActionBar.TabBar = 0x7f14032f
com.example.allinone:color/m3_sys_color_dynamic_dark_on_surface = 0x7f0601ca
com.example.allinone:attr/saturation = 0x7f0403e3
com.example.allinone:drawable/$mtrl_switch_thumb_pressed_unchecked__0 = 0x7f080025
com.example.allinone:attr/haloRadius = 0x7f040239
com.example.allinone:color/m3_sys_color_dynamic_dark_on_secondary = 0x7f0601c8
com.example.allinone:attr/textAppearanceLabelLarge = 0x7f04049d
com.example.allinone:color/m3_sys_color_dynamic_dark_on_error_container = 0x7f0601c5
com.example.allinone:string/in_progress = 0x7f13010d
com.example.allinone:color/m3_sys_color_dynamic_dark_on_background = 0x7f0601c3
com.example.allinone:id/open_search_view_scrim = 0x7f0a0296
com.example.allinone:color/m3_sys_color_dynamic_dark_inverse_surface = 0x7f0601c2
com.example.allinone:string/name = 0x7f1301b4
com.example.allinone:attr/imageZoom = 0x7f040261
com.example.allinone:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
com.example.allinone:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f14004e
com.example.allinone:dimen/exo_styled_progress_margin_bottom = 0x7f0700b3
com.example.allinone:dimen/mtrl_calendar_year_horizontal_padding = 0x7f0702bd
com.example.allinone:style/Widget.Material3.Slider = 0x7f140414
com.example.allinone:color/m3_sys_color_dynamic_dark_inverse_primary = 0x7f0601c1
com.example.allinone:color/m3_sys_color_dynamic_dark_inverse_on_surface = 0x7f0601c0
com.example.allinone:dimen/material_font_2_0_box_collapsed_padding_top = 0x7f070261
com.example.allinone:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f1401d8
com.example.allinone:color/m3_sys_color_dark_surface_container_low = 0x7f0601b7
com.example.allinone:dimen/m3_comp_navigation_rail_icon_size = 0x7f070170
com.example.allinone:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f08008c
com.example.allinone:dimen/abc_button_padding_horizontal_material = 0x7f070014
com.example.allinone:drawable/exo_notification_rewind = 0x7f0800e3
com.example.allinone:attr/materialIconButtonOutlinedStyle = 0x7f040324
com.example.allinone:color/m3_sys_color_dark_surface_bright = 0x7f0601b3
com.example.allinone:id/action_bar_root = 0x7f0a0038
com.example.allinone:color/m3_sys_color_dynamic_dark_on_tertiary = 0x7f0601cc
com.example.allinone:attr/activityAction = 0x7f040028
com.example.allinone:anim/abc_tooltip_enter = 0x7f01000a
com.example.allinone:color/m3_ref_palette_tertiary99 = 0x7f06018e
com.example.allinone:color/m3_sys_color_dark_outline_variant = 0x7f0601ad
com.example.allinone:color/material_grey_50 = 0x7f0602a6
com.example.allinone:color/m3_sys_color_dark_outline = 0x7f0601ac
com.example.allinone:style/ExoStyledControls.TimeText = 0x7f140149
com.example.allinone:string/mtrl_switch_track_path = 0x7f1301b1
com.example.allinone:string/character_counter_pattern = 0x7f13004a
com.example.allinone:style/Theme.AppCompat.Light = 0x7f140253
com.example.allinone:dimen/m3_ripple_hovered_alpha = 0x7f0701fc
com.example.allinone:color/m3_sys_color_dark_on_surface = 0x7f0601a8
com.example.allinone:color/m3_sys_color_dark_on_error_container = 0x7f0601a3
com.example.allinone:macro/m3_comp_search_bar_leading_icon_color = 0x7f0e00eb
com.example.allinone:attr/circleCrop = 0x7f0400e0
com.example.allinone:dimen/abc_action_bar_default_height_material = 0x7f070002
com.example.allinone:drawable/ic_database = 0x7f080123
com.example.allinone:layout/dialog_post_details = 0x7f0d0040
com.example.allinone:color/m3_sys_color_dark_on_background = 0x7f0601a1
com.example.allinone:dimen/m3_btn_translation_z_hovered = 0x7f070108
com.example.allinone:color/m3_card_foreground_color = 0x7f0600ac
com.example.allinone:color/m3_sys_color_dark_inverse_primary = 0x7f06019f
com.example.allinone:style/Widget.Design.TextInputEditText = 0x7f140381
com.example.allinone:drawable/ic_call_answer_video_low = 0x7f08010b
com.example.allinone:attr/expandedTitleTextAppearance = 0x7f0401db
com.example.allinone:color/design_fab_shadow_end_color = 0x7f060073
com.example.allinone:color/m3_sys_color_dark_background = 0x7f06019b
com.example.allinone:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__0 = 0x7f080015
com.example.allinone:color/m3_slider_inactive_track_color_legacy = 0x7f060196
com.example.allinone:styleable/Fragment = 0x7f15003f
com.example.allinone:color/m3_slider_halo_color_legacy = 0x7f060194
com.example.allinone:menu/search_students = 0x7f0f000a
com.example.allinone:color/switch_thumb_disabled_material_dark = 0x7f060356
com.example.allinone:color/m3_slider_active_track_color_legacy = 0x7f060193
com.example.allinone:attr/contentInsetRight = 0x7f040150
com.example.allinone:color/m3_slider_active_track_color = 0x7f060192
com.example.allinone:dimen/abc_text_size_large_material = 0x7f070048
com.example.allinone:color/abc_color_highlight_material = 0x7f060004
com.example.allinone:string/m3c_date_range_picker_scroll_to_previous_month = 0x7f130145
com.example.allinone:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f080086
com.example.allinone:drawable/$avd_hide_password__2 = 0x7f080002
com.example.allinone:color/m3_ref_palette_white = 0x7f06018f
com.example.allinone:dimen/mtrl_fab_translation_z_pressed = 0x7f0702de
com.example.allinone:color/m3_ref_palette_tertiary90 = 0x7f06018c
com.example.allinone:color/m3_ref_palette_tertiary80 = 0x7f06018b
com.example.allinone:id/compress = 0x7f0a00d1
com.example.allinone:string/weekly_summary = 0x7f13024c
com.example.allinone:id/exo_play_pause = 0x7f0a0168
com.example.allinone:color/m3_sys_color_light_on_surface = 0x7f06021a
com.example.allinone:attr/transformPivotTarget = 0x7f040513
com.example.allinone:color/m3_ref_palette_tertiary20 = 0x7f060185
com.example.allinone:color/navy_primary = 0x7f060332
com.example.allinone:dimen/mtrl_snackbar_padding_horizontal = 0x7f07031a
com.example.allinone:layout/material_clockface_textview = 0x7f0d0089
com.example.allinone:style/Animation.Material3.BottomSheetDialog = 0x7f140006
com.example.allinone:string/save = 0x7f1301f6
com.example.allinone:macro/m3_comp_switch_selected_pressed_track_color = 0x7f0e012d
com.example.allinone:dimen/abc_dialog_fixed_height_minor = 0x7f07001d
com.example.allinone:color/m3_ref_palette_secondary99 = 0x7f060181
com.example.allinone:color/exo_white_opacity_70 = 0x7f06008f
com.example.allinone:color/material_grey_800 = 0x7f0602a8
com.example.allinone:attr/behavior_overlapTop = 0x7f04007a
com.example.allinone:color/m3_ref_palette_secondary95 = 0x7f060180
com.example.allinone:color/m3_ref_palette_secondary90 = 0x7f06017f
com.example.allinone:attr/snackbarTextViewStyle = 0x7f040428
com.example.allinone:dimen/m3_slider_thumb_elevation = 0x7f070210
com.example.allinone:macro/m3_comp_time_input_time_input_field_label_text_color = 0x7f0e0149
com.example.allinone:styleable/NavigationRailView = 0x7f15007b
com.example.allinone:style/ThemeOverlay.Material3.BottomNavigationView = 0x7f1402c7
com.example.allinone:layout/select_dialog_singlechoice_material = 0x7f0d00c6
com.example.allinone:id/decor_content_parent = 0x7f0a00f1
com.example.allinone:color/m3_ref_palette_secondary60 = 0x7f06017c
com.example.allinone:color/m3_ref_palette_secondary40 = 0x7f06017a
com.example.allinone:color/material_slider_active_track_color = 0x7f0602ee
com.example.allinone:id/exo_time = 0x7f0a0179
com.example.allinone:color/material_dynamic_neutral_variant70 = 0x7f060278
com.example.allinone:color/m3_ref_palette_secondary20 = 0x7f060178
com.example.allinone:dimen/mtrl_calendar_day_height = 0x7f07029c
com.example.allinone:color/abc_tint_edittext = 0x7f060015
com.example.allinone:color/material_dynamic_neutral0 = 0x7f060263
com.example.allinone:attr/materialCardViewStyle = 0x7f04031c
com.example.allinone:color/m3_ref_palette_primary99 = 0x7f060174
com.example.allinone:dimen/m3_badge_with_text_horizontal_offset = 0x7f0700dc
com.example.allinone:id/center = 0x7f0a00b3
com.example.allinone:attr/itemShapeInsetTop = 0x7f040284
com.example.allinone:color/m3_ref_palette_tertiary95 = 0x7f06018d
com.example.allinone:color/m3_ref_palette_primary80 = 0x7f060171
com.example.allinone:color/m3_ref_palette_dynamic_tertiary60 = 0x7f060130
com.example.allinone:layout/dialog_loading = 0x7f0d003e
com.example.allinone:macro/m3_comp_switch_disabled_unselected_icon_color = 0x7f0e011d
com.example.allinone:attr/carousel_forwardTransition = 0x7f0400b4
com.example.allinone:color/m3_ref_palette_primary60 = 0x7f06016f
com.example.allinone:color/m3_primary_text_disable_only = 0x7f0600db
com.example.allinone:attr/layout_constraintBaseline_toBottomOf = 0x7f0402a7
com.example.allinone:attr/errorAccessibilityLabel = 0x7f0401c7
com.example.allinone:color/m3_ref_palette_primary30 = 0x7f06016c
com.example.allinone:dimen/abc_text_size_caption_material = 0x7f070042
com.example.allinone:color/m3_ref_palette_primary10 = 0x7f060169
com.example.allinone:color/m3_ref_palette_neutral_variant99 = 0x7f060167
com.example.allinone:attr/materialAlertDialogTitleTextStyle = 0x7f040306
com.example.allinone:id/compose_view_saveable_id_tag = 0x7f0a00d0
com.example.allinone:color/m3_ref_palette_neutral_variant90 = 0x7f060165
com.example.allinone:color/m3_ref_palette_neutral_variant60 = 0x7f060162
com.example.allinone:color/m3_ref_palette_neutral_variant40 = 0x7f060160
com.example.allinone:drawable/abc_dialog_material_background = 0x7f08003c
com.example.allinone:id/backup_name = 0x7f0a007e
com.example.allinone:id/exo_position = 0x7f0a016a
com.example.allinone:color/m3_ref_palette_neutral_variant20 = 0x7f06015e
com.example.allinone:macro/m3_comp_slider_disabled_active_track_color = 0x7f0e010c
com.example.allinone:dimen/m3_btn_text_btn_padding_right = 0x7f070106
com.example.allinone:dimen/exo_icon_horizontal_margin = 0x7f070097
com.example.allinone:color/m3_ref_palette_neutral_variant10 = 0x7f06015c
com.example.allinone:color/m3_ref_palette_neutral99 = 0x7f06015a
com.example.allinone:color/m3_ref_palette_neutral95 = 0x7f060157
com.example.allinone:attr/searchPrefixText = 0x7f0403f0
com.example.allinone:color/m3_ref_palette_neutral92 = 0x7f060155
com.example.allinone:style/Base.ThemeOverlay.Material3.TextInputEditText = 0x7f140086
com.example.allinone:color/m3_ref_palette_neutral90 = 0x7f060154
com.example.allinone:color/m3_ref_palette_neutral60 = 0x7f060150
com.example.allinone:color/m3_ref_palette_neutral6 = 0x7f06014f
com.example.allinone:id/text = 0x7f0a0369
com.example.allinone:attr/badgeRadius = 0x7f04005e
com.example.allinone:attr/labelBehavior = 0x7f040293
com.example.allinone:color/m3_ref_palette_neutral30 = 0x7f06014b
com.example.allinone:dimen/m3_comp_time_picker_time_selector_hover_state_layer_opacity = 0x7f0701cb
com.example.allinone:attr/itemVerticalPadding = 0x7f04028d
com.example.allinone:color/m3_ref_palette_neutral20 = 0x7f060148
com.example.allinone:style/Base.Widget.Material3.FloatingActionButton.Large = 0x7f14010c
com.example.allinone:dimen/m3_bottom_sheet_elevation = 0x7f0700e8
com.example.allinone:attr/motionEasingStandardDecelerateInterpolator = 0x7f040366
com.example.allinone:color/m3_ref_palette_neutral17 = 0x7f060147
com.example.allinone:dimen/mtrl_btn_text_btn_padding_left = 0x7f070292
com.example.allinone:attr/colorSecondaryFixedDim = 0x7f040131
com.example.allinone:color/m3_ref_palette_neutral0 = 0x7f060143
com.example.allinone:string/common_google_play_services_install_title = 0x7f130063
com.example.allinone:attr/showAnimationBehavior = 0x7f040407
com.example.allinone:id/mtrl_calendar_months = 0x7f0a023b
com.example.allinone:color/m3_ref_palette_error95 = 0x7f060141
com.example.allinone:string/mtrl_checkbox_button_icon_path_group_name = 0x7f130178
com.example.allinone:drawable/$mtrl_switch_thumb_unchecked_checked__0 = 0x7f080026
com.example.allinone:dimen/abc_action_bar_stacked_max_height = 0x7f070009
com.example.allinone:attr/layout_constraintBottom_toTopOf = 0x7f0402ab
com.example.allinone:color/m3_calendar_item_disabled_text = 0x7f0600aa
com.example.allinone:color/m3_ref_palette_error20 = 0x7f060139
com.example.allinone:color/m3_ref_palette_error10 = 0x7f060137
com.example.allinone:dimen/m3_navigation_item_shape_inset_end = 0x7f0701e8
com.example.allinone:attr/tabTextAppearance = 0x7f040481
com.example.allinone:attr/layout = 0x7f04029a
com.example.allinone:dimen/m3_btn_elevated_btn_elevation = 0x7f0700f4
com.example.allinone:color/m3_ref_palette_error0 = 0x7f060136
com.example.allinone:color/m3_ref_palette_dynamic_tertiary99 = 0x7f060135
com.example.allinone:color/material_on_primary_emphasis_medium = 0x7f0602b4
com.example.allinone:color/m3_ref_palette_dynamic_tertiary95 = 0x7f060134
com.example.allinone:string/please_select_end_date = 0x7f1301dc
com.example.allinone:attr/homeLayout = 0x7f04024c
com.example.allinone:attr/titlePositionInterpolator = 0x7f0404f2
com.example.allinone:id/avatarAI = 0x7f0a007a
com.example.allinone:color/registration_start = 0x7f06034c
com.example.allinone:dimen/abc_dialog_fixed_height_major = 0x7f07001c
com.example.allinone:id/rectangles = 0x7f0a02e2
com.example.allinone:color/m3_ref_palette_dynamic_neutral20 = 0x7f0600e4
com.example.allinone:dimen/mtrl_card_corner_radius = 0x7f0702c2
com.example.allinone:color/m3_ref_palette_dynamic_tertiary50 = 0x7f06012f
com.example.allinone:string/error_opening_file = 0x7f1300af
com.example.allinone:dimen/design_navigation_separator_vertical_padding = 0x7f07007f
com.example.allinone:color/m3_ref_palette_dynamic_tertiary30 = 0x7f06012d
com.example.allinone:id/typeText = 0x7f0a03b4
com.example.allinone:attr/shouldRemoveExpandedCorners = 0x7f040406
com.example.allinone:color/m3_sys_color_light_surface_container_lowest = 0x7f06022a
com.example.allinone:string/deleting = 0x7f13008e
com.example.allinone:color/m3_ref_palette_dynamic_tertiary10 = 0x7f06012a
com.example.allinone:attr/clockIcon = 0x7f0400ed
com.example.allinone:attr/colorContainer = 0x7f040106
com.example.allinone:color/material_slider_active_tick_marks_color = 0x7f0602ed
com.example.allinone:color/mtrl_navigation_bar_ripple_color = 0x7f060317
com.example.allinone:color/m3_ref_palette_dynamic_secondary80 = 0x7f060125
com.example.allinone:dimen/m3_comp_outlined_icon_button_unselected_outline_width = 0x7f070179
com.example.allinone:dimen/m3_sys_elevation_level0 = 0x7f070215
com.example.allinone:style/Widget.AppCompat.ImageButton = 0x7f140346
com.example.allinone:color/m3_ref_palette_dynamic_secondary60 = 0x7f060123
com.example.allinone:menu/workout_bottom_nav_menu = 0x7f0f000b
com.example.allinone:style/Theme.Material3.Light.Dialog = 0x7f14027a
com.example.allinone:attr/ratingBarStyle = 0x7f0403cd
com.example.allinone:attr/tabTextColor = 0x7f040482
com.example.allinone:color/m3_ref_palette_dynamic_secondary40 = 0x7f060121
com.example.allinone:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0c0000
com.example.allinone:style/Widget.MaterialComponents.TimePicker.Button = 0x7f14049e
com.example.allinone:color/m3_ref_palette_dynamic_secondary30 = 0x7f060120
com.example.allinone:style/Base.Widget.Material3.ActionBar.Solid = 0x7f140100
com.example.allinone:style/Widget.Material3.Chip.Assist.Elevated = 0x7f1403ad
com.example.allinone:color/m3_ref_palette_dynamic_secondary100 = 0x7f06011e
com.example.allinone:color/m3_ref_palette_dynamic_secondary0 = 0x7f06011c
com.example.allinone:drawable/exo_styled_controls_repeat_all = 0x7f0800f2
com.example.allinone:color/m3_ref_palette_dynamic_primary95 = 0x7f06011a
com.example.allinone:layout/abc_action_mode_close_item_material = 0x7f0d0005
com.example.allinone:attr/tabContentStart = 0x7f040468
com.example.allinone:color/m3_ref_palette_dynamic_primary80 = 0x7f060118
com.example.allinone:attr/behavior_peekHeight = 0x7f04007b
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar.DayTextView = 0x7f14046a
com.example.allinone:color/m3_ref_palette_dynamic_primary50 = 0x7f060115
com.example.allinone:attr/motionDurationExtraLong3 = 0x7f04034e
com.example.allinone:anim/abc_fade_out = 0x7f010001
com.example.allinone:macro/m3_comp_badge_color = 0x7f0e0002
com.example.allinone:id/centerCrop = 0x7f0a00b4
com.example.allinone:attr/elevationOverlayColor = 0x7f0401b7
com.example.allinone:string/selected_color = 0x7f13020b
com.example.allinone:string/color_black = 0x7f130053
com.example.allinone:color/m3_ref_palette_dynamic_primary100 = 0x7f060111
com.example.allinone:style/Widget.Design.ScrimInsetsFrameLayout = 0x7f14037e
com.example.allinone:id/material_clock_display_and_toggle = 0x7f0a0212
com.example.allinone:color/m3_ref_palette_dynamic_primary10 = 0x7f060110
com.example.allinone:color/material_on_primary_disabled = 0x7f0602b2
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant99 = 0x7f06010e
com.example.allinone:color/material_slider_inactive_tick_marks_color = 0x7f0602f0
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant98 = 0x7f06010d
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant95 = 0x7f06010b
com.example.allinone:attr/onNegativeCross = 0x7f04038a
com.example.allinone:dimen/m3_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0700eb
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant94 = 0x7f06010a
com.example.allinone:attr/imageAspectRatio = 0x7f04025b
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant92 = 0x7f060109
com.example.allinone:color/m3_textfield_input_text_color = 0x7f060246
com.example.allinone:string/mtrl_checkbox_button_path_checked = 0x7f13017b
com.example.allinone:string/common_google_play_services_update_text = 0x7f130069
com.example.allinone:menu/search_register = 0x7f0f0009
com.example.allinone:attr/artwork_display_mode = 0x7f040043
com.example.allinone:string/m3c_date_picker_title = 0x7f13013d
com.example.allinone:attr/extendStrategy = 0x7f0401de
com.example.allinone:id/dark = 0x7f0a00e4
com.example.allinone:color/material_grey_100 = 0x7f0602a4
com.example.allinone:string/program_name = 0x7f1301e3
com.example.allinone:attr/imageRotate = 0x7f040260
com.example.allinone:attr/floatingActionButtonPrimaryStyle = 0x7f0401fb
com.example.allinone:dimen/m3_navigation_item_shape_inset_bottom = 0x7f0701e7
com.example.allinone:attr/overlapAnchor = 0x7f04038f
com.example.allinone:style/Base.Widget.AppCompat.ActionMode = 0x7f1400cd
com.example.allinone:dimen/m3_comp_outlined_text_field_outline_width = 0x7f07017e
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant20 = 0x7f0600fc
com.example.allinone:dimen/m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity = 0x7f070184
com.example.allinone:dimen/exo_small_icon_width = 0x7f0700a9
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant10 = 0x7f0600f8
com.example.allinone:color/m3_ref_palette_error60 = 0x7f06013d
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant0 = 0x7f0600f7
com.example.allinone:color/orange_500 = 0x7f06033a
com.example.allinone:style/Base.Widget.MaterialComponents.AutoCompleteTextView = 0x7f140114
com.example.allinone:color/m3_ref_palette_dynamic_neutral99 = 0x7f0600f6
com.example.allinone:attr/isMaterial3DynamicColorApplied = 0x7f04026d
com.example.allinone:attr/backgroundOverlayColorAlpha = 0x7f040057
com.example.allinone:color/m3_ref_palette_dynamic_neutral96 = 0x7f0600f4
com.example.allinone:string/please_enter_valid_amount = 0x7f1301db
com.example.allinone:color/m3_ref_palette_dynamic_neutral94 = 0x7f0600f2
com.example.allinone:attr/tabInlineLabel = 0x7f040473
com.example.allinone:attr/errorTextAppearance = 0x7f0401cf
com.example.allinone:dimen/m3_comp_outlined_text_field_disabled_supporting_text_opacity = 0x7f07017c
com.example.allinone:id/accessibility_custom_action_10 = 0x7f0a0013
com.example.allinone:id/carryVelocity = 0x7f0a00a5
com.example.allinone:attr/boxCornerRadiusTopStart = 0x7f040090
com.example.allinone:color/m3_ref_palette_dynamic_neutral90 = 0x7f0600f0
com.example.allinone:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f140436
com.example.allinone:layout/dialog_edit_wt_student = 0x7f0d0039
com.example.allinone:attr/layout_constraintRight_toLeftOf = 0x7f0402c1
com.example.allinone:drawable/exo_styled_controls_subtitle_on = 0x7f0800fb
com.example.allinone:color/m3_ref_palette_dynamic_neutral87 = 0x7f0600ef
com.example.allinone:attr/shapeAppearanceMediumComponent = 0x7f040401
com.example.allinone:macro/m3_comp_outlined_text_field_hover_outline_color = 0x7f0e00be
com.example.allinone:color/m3_ref_palette_dynamic_secondary10 = 0x7f06011d
com.example.allinone:color/m3_sys_color_dynamic_light_on_tertiary = 0x7f0601ee
com.example.allinone:string/color_purple = 0x7f130059
com.example.allinone:animator/nav_default_enter_anim = 0x7f020022
com.example.allinone:integer/m3_sys_motion_duration_extra_long1 = 0x7f0b0014
com.example.allinone:color/m3_ref_palette_dynamic_neutral6 = 0x7f0600eb
com.example.allinone:color/m3_ref_palette_dynamic_neutral40 = 0x7f0600e9
com.example.allinone:dimen/mtrl_badge_size = 0x7f070272
com.example.allinone:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f14017d
com.example.allinone:id/SHOW_PROGRESS = 0x7f0a000a
com.example.allinone:attr/expandedTitleTextColor = 0x7f0401dc
com.example.allinone:color/m3_ref_palette_dynamic_neutral30 = 0x7f0600e7
com.example.allinone:color/m3_ref_palette_dynamic_neutral24 = 0x7f0600e6
com.example.allinone:macro/m3_comp_date_picker_modal_container_color = 0x7f0e000d
com.example.allinone:dimen/mtrl_fab_min_touch_target = 0x7f0702dc
com.example.allinone:id/decelerate = 0x7f0a00ef
com.example.allinone:color/m3_ref_palette_dynamic_neutral17 = 0x7f0600e3
com.example.allinone:attr/listPreferredItemPaddingLeft = 0x7f0402f6
com.example.allinone:style/FilterButtonStyle = 0x7f14014d
com.example.allinone:color/m3_ref_palette_dynamic_neutral12 = 0x7f0600e2
com.example.allinone:color/m3_sys_color_dark_on_primary_container = 0x7f0601a5
com.example.allinone:id/accessibility_custom_action_7 = 0x7f0a002e
com.example.allinone:color/m3_ref_palette_dynamic_neutral10 = 0x7f0600e0
com.example.allinone:dimen/m3_comp_progress_indicator_active_indicator_track_space = 0x7f070187
com.example.allinone:dimen/exo_styled_progress_bar_height = 0x7f0700af
com.example.allinone:color/m3_radiobutton_button_tint = 0x7f0600dc
com.example.allinone:color/m3_popupmenu_overlay_color = 0x7f0600da
com.example.allinone:id/checked = 0x7f0a00be
com.example.allinone:color/m3_navigation_rail_item_with_indicator_label_tint = 0x7f0600d8
com.example.allinone:color/m3_navigation_rail_item_with_indicator_icon_tint = 0x7f0600d7
com.example.allinone:color/m3_navigation_item_text_color = 0x7f0600d6
com.example.allinone:style/Widget.AppCompat.ActionBar.TabText = 0x7f140330
com.example.allinone:dimen/design_bottom_navigation_text_size = 0x7f07006c
com.example.allinone:dimen/m3_side_sheet_width = 0x7f07020d
com.example.allinone:layout/abc_screen_content_include = 0x7f0d0014
com.example.allinone:color/m3_navigation_item_icon_tint = 0x7f0600d4
com.example.allinone:attr/pressedTranslationZ = 0x7f0403c1
com.example.allinone:dimen/material_font_1_3_box_collapsed_padding_top = 0x7f070260
com.example.allinone:dimen/m3_comp_outlined_card_icon_size = 0x7f070177
com.example.allinone:style/FloatingDialogTheme = 0x7f14014f
com.example.allinone:color/m3_hint_foreground = 0x7f0600ce
com.example.allinone:attr/radioButtonStyle = 0x7f0403cb
com.example.allinone:color/m3_dynamic_dark_default_color_secondary_text = 0x7f0600be
com.example.allinone:color/mtrl_chip_text_color = 0x7f060308
com.example.allinone:color/m3_dynamic_dark_default_color_primary_text = 0x7f0600bd
com.example.allinone:attr/subtitleTextAppearance = 0x7f04045a
com.example.allinone:color/m3_default_color_secondary_text = 0x7f0600bc
com.example.allinone:dimen/tooltip_precise_anchor_threshold = 0x7f07034d
com.example.allinone:attr/layout_constraintWidth_default = 0x7f0402cd
com.example.allinone:color/m3_dark_highlighted_text = 0x7f0600b8
com.example.allinone:color/m3_dark_default_color_secondary_text = 0x7f0600b7
com.example.allinone:dimen/design_appbar_elevation = 0x7f070060
com.example.allinone:color/m3_chip_text_color = 0x7f0600b5
com.example.allinone:color/m3_chip_ripple_color = 0x7f0600b3
com.example.allinone:color/m3_chip_background_color = 0x7f0600b2
com.example.allinone:style/Widget.Material3.CheckedTextView = 0x7f1403ab
com.example.allinone:attr/errorIconDrawable = 0x7f0401cb
com.example.allinone:dimen/m3_comp_fab_primary_large_icon_size = 0x7f070141
com.example.allinone:id/exo_controller = 0x7f0a0154
com.example.allinone:color/material_dynamic_primary80 = 0x7f060286
com.example.allinone:color/m3_ref_palette_dynamic_neutral60 = 0x7f0600ec
com.example.allinone:color/m3_timepicker_secondary_text_button_ripple_color = 0x7f060250
com.example.allinone:id/shareButton = 0x7f0a0311
com.example.allinone:attr/itemTextAppearance = 0x7f040288
com.example.allinone:attr/layout_constraintHeight_percent = 0x7f0402b9
com.example.allinone:color/m3_calendar_item_stroke_color = 0x7f0600ab
com.example.allinone:attr/closeIconStartPadding = 0x7f0400f3
com.example.allinone:color/m3_assist_chip_stroke_color = 0x7f0600a3
com.example.allinone:layout/item_seminar = 0x7f0d0077
com.example.allinone:color/material_dynamic_neutral95 = 0x7f06026e
com.example.allinone:string/m3c_dialog = 0x7f130148
com.example.allinone:color/m3_appbar_overlay_color = 0x7f0600a1
com.example.allinone:color/image_green = 0x7f06009d
com.example.allinone:dimen/mtrl_btn_padding_top = 0x7f07028d
com.example.allinone:color/m3_ref_palette_neutral94 = 0x7f060156
com.example.allinone:color/highlighted_text_material_dark = 0x7f06009a
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant70 = 0x7f060105
com.example.allinone:color/green_500 = 0x7f060099
com.example.allinone:drawable/abc_btn_check_material = 0x7f08002c
com.example.allinone:attr/dividerInsetEnd = 0x7f040198
com.example.allinone:color/foreground_material_dark = 0x7f060092
com.example.allinone:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f14033b
com.example.allinone:style/ShapeAppearance.M3.Comp.Switch.Track.Shape = 0x7f140196
com.example.allinone:drawable/ic_graduation = 0x7f080136
com.example.allinone:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f080075
com.example.allinone:color/m3_filled_icon_button_container_color_selector = 0x7f0600cc
com.example.allinone:attr/actionBarStyle = 0x7f040008
com.example.allinone:dimen/hint_pressed_alpha_material_light = 0x7f0700be
com.example.allinone:color/abc_search_url_text_pressed = 0x7f06000f
com.example.allinone:macro/m3_comp_primary_navigation_tab_active_indicator_color = 0x7f0e00c9
com.example.allinone:attr/checkMarkCompat = 0x7f0400bd
com.example.allinone:color/feedCard = 0x7f060091
com.example.allinone:style/Theme.Material3.Dark.Dialog.Alert = 0x7f140265
com.example.allinone:id/exo_minimal_controls = 0x7f0a0160
com.example.allinone:id/right = 0x7f0a02e9
com.example.allinone:attr/lineHeight = 0x7f0402e7
com.example.allinone:id/group_title_layout = 0x7f0a01aa
com.example.allinone:dimen/fastscroll_default_thickness = 0x7f0700b5
com.example.allinone:attr/maxNumber = 0x7f040337
com.example.allinone:anim/design_snackbar_in = 0x7f01001a
com.example.allinone:color/exo_edit_mode_background_color = 0x7f06008b
com.example.allinone:id/withinBounds = 0x7f0a03d4
com.example.allinone:id/hide_ime_id = 0x7f0a01af
com.example.allinone:dimen/m3_comp_slider_active_handle_height = 0x7f0701a6
com.example.allinone:drawable/abc_textfield_activated_mtrl_alpha = 0x7f080072
com.example.allinone:color/dialog_action_button_color = 0x7f06007c
com.example.allinone:color/m3_sys_color_light_surface = 0x7f060224
com.example.allinone:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f140034
com.example.allinone:attr/colorSecondaryVariant = 0x7f040132
com.example.allinone:id/ALT = 0x7f0a0000
com.example.allinone:color/design_fab_stroke_top_inner_color = 0x7f060078
com.example.allinone:layout/mtrl_picker_dialog = 0x7f0d00a7
com.example.allinone:id/submenuarrow = 0x7f0a034a
com.example.allinone:color/design_fab_stroke_end_outer_color = 0x7f060077
com.example.allinone:color/browser_actions_text_color = 0x7f060034
com.example.allinone:dimen/mtrl_calendar_header_content_padding_fullscreen = 0x7f0702a4
com.example.allinone:id/material_timepicker_cancel_button = 0x7f0a021f
com.example.allinone:color/design_default_color_primary_variant = 0x7f06006e
com.example.allinone:style/Base.TextAppearance.AppCompat.Subhead = 0x7f14002d
com.example.allinone:drawable/abc_list_selector_background_transition_holo_light = 0x7f080054
com.example.allinone:color/material_dynamic_color_dark_on_error = 0x7f06025d
com.example.allinone:attr/collapsingToolbarLayoutMediumSize = 0x7f0400ff
com.example.allinone:style/Widget.Material3.AppBarLayout = 0x7f140385
com.example.allinone:color/design_default_color_primary_dark = 0x7f06006d
com.example.allinone:attr/listPreferredItemPaddingStart = 0x7f0402f8
com.example.allinone:id/workout_stats = 0x7f0a03db
com.example.allinone:attr/textInputFilledDenseStyle = 0x7f0404b8
com.example.allinone:color/m3_fab_ripple_color_selector = 0x7f0600cb
com.example.allinone:attr/subheaderInsetEnd = 0x7f040454
com.example.allinone:dimen/m3_searchbar_height = 0x7f070200
com.example.allinone:id/balanceValueText = 0x7f0a0084
com.example.allinone:attr/scrubber_dragged_size = 0x7f0403eb
com.example.allinone:attr/materialCalendarTheme = 0x7f040317
com.example.allinone:attr/constraint_referenced_tags = 0x7f040149
com.example.allinone:color/design_default_color_on_surface = 0x7f06006b
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar.Year = 0x7f140478
com.example.allinone:attr/waveOffset = 0x7f040531
com.example.allinone:string/m3c_date_input_no_input_description = 0x7f13012f
com.example.allinone:attr/colorOnContainer = 0x7f04010f
com.example.allinone:drawable/googleg_disabled_color_18 = 0x7f0800fe
com.example.allinone:attr/placeholderTextColor = 0x7f0403ad
com.example.allinone:attr/behavior_significantVelocityThreshold = 0x7f04007d
com.example.allinone:attr/motionDebug = 0x7f04034b
com.example.allinone:color/design_default_color_on_primary = 0x7f060069
com.example.allinone:color/m3_sys_color_dynamic_light_surface_bright = 0x7f0601f7
com.example.allinone:string/weekly_workout_count = 0x7f13024d
com.example.allinone:dimen/material_clock_size = 0x7f070254
com.example.allinone:attr/passwordToggleTint = 0x7f0403a0
com.example.allinone:style/Widget.MaterialComponents.Chip.Filter = 0x7f140454
com.example.allinone:dimen/mtrl_calendar_header_text_padding = 0x7f0702a9
com.example.allinone:macro/m3_comp_navigation_rail_active_focus_state_layer_color = 0x7f0e0093
com.example.allinone:color/design_default_color_error = 0x7f060066
com.example.allinone:attr/simpleItemSelectedRippleColor = 0x7f04041f
com.example.allinone:attr/motionEasingLinear = 0x7f040362
com.example.allinone:style/CardView = 0x7f140129
com.example.allinone:color/design_dark_default_color_primary_variant = 0x7f060061
com.example.allinone:attr/chipIconTint = 0x7f0400d3
com.example.allinone:color/design_dark_default_color_on_primary = 0x7f06005c
com.example.allinone:color/m3_tabs_icon_color_secondary = 0x7f06023c
com.example.allinone:color/design_dark_default_color_on_background = 0x7f06005a
com.example.allinone:attr/sideSheetModalStyle = 0x7f04041c
com.example.allinone:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f1401ed
com.example.allinone:color/design_dark_default_color_error = 0x7f060059
com.example.allinone:style/Base.Widget.MaterialComponents.TextInputLayout = 0x7f140120
com.example.allinone:color/common_google_signin_btn_tint = 0x7f060053
com.example.allinone:color/common_google_signin_btn_text_light_pressed = 0x7f060052
com.example.allinone:color/common_google_signin_btn_text_dark_disabled = 0x7f06004b
com.example.allinone:id/motion_base = 0x7f0a0235
com.example.allinone:color/colorSuccess = 0x7f060047
com.example.allinone:layout/mtrl_picker_header_selection_text = 0x7f0d00ab
com.example.allinone:attr/layout_scrollInterpolator = 0x7f0402e1
com.example.allinone:dimen/m3_comp_secondary_navigation_tab_active_indicator_height = 0x7f07019b
com.example.allinone:color/material_dynamic_neutral_variant20 = 0x7f060273
com.example.allinone:menu/menu_edit_note = 0x7f0f0003
com.example.allinone:attr/windowFixedWidthMinor = 0x7f04053c
com.example.allinone:attr/flow_wrapMode = 0x7f040217
com.example.allinone:attr/framePosition = 0x7f040227
com.example.allinone:color/colorError = 0x7f060043
com.example.allinone:style/Widget.MaterialComponents.Snackbar.TextView = 0x7f14048c
com.example.allinone:id/eventDate = 0x7f0a013c
com.example.allinone:string/color_brown = 0x7f130055
com.example.allinone:color/m3_ref_palette_dynamic_tertiary40 = 0x7f06012e
com.example.allinone:color/cardview_light_background = 0x7f06003c
com.example.allinone:id/tag_on_apply_window_listener = 0x7f0a035c
com.example.allinone:color/m3_ref_palette_dynamic_primary70 = 0x7f060117
com.example.allinone:color/call_notification_decline_color = 0x7f060039
com.example.allinone:color/material_dynamic_neutral99 = 0x7f06026f
com.example.allinone:color/browser_actions_divider_color = 0x7f060033
com.example.allinone:id/cardAIMessage = 0x7f0a00a3
com.example.allinone:color/excellent_green = 0x7f060087
com.example.allinone:style/Base.Widget.Material3.TabLayout.OnSurface = 0x7f140112
com.example.allinone:id/zoom = 0x7f0a03e8
com.example.allinone:color/bright_tab_unselected = 0x7f060031
com.example.allinone:attr/carousel_infinite = 0x7f0400b5
com.example.allinone:drawable/notification_bg_low_normal = 0x7f0801ab
com.example.allinone:color/bright_foreground_inverse_material_dark = 0x7f06002c
com.example.allinone:attr/circularflow_defaultAngle = 0x7f0400e4
com.example.allinone:attr/graph = 0x7f04022b
com.example.allinone:dimen/m3_comp_top_app_bar_small_on_scroll_container_elevation = 0x7f0701d1
com.example.allinone:dimen/m3_comp_fab_primary_small_icon_size = 0x7f070145
com.example.allinone:color/bright_foreground_disabled_material_light = 0x7f06002b
com.example.allinone:color/bright_foreground_disabled_material_dark = 0x7f06002a
com.example.allinone:attr/drawableSize = 0x7f0401a5
com.example.allinone:color/material_dynamic_neutral_variant30 = 0x7f060274
com.example.allinone:attr/carousel_firstView = 0x7f0400b3
com.example.allinone:dimen/clock_face_margin_start = 0x7f070057
com.example.allinone:attr/grid_orientation = 0x7f04022f
com.example.allinone:attr/thickness = 0x7f0404cb
com.example.allinone:color/exo_bottom_bar_background = 0x7f06008a
com.example.allinone:style/Widget.Material3.MaterialCalendar.Day.Selected = 0x7f1403e2
com.example.allinone:string/mtrl_picker_range_header_only_end_selected = 0x7f130198
com.example.allinone:attr/actionBarDivider = 0x7f040003
com.example.allinone:style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen = 0x7f1402ec
com.example.allinone:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox = 0x7f1402c1
com.example.allinone:color/bottom_nav_item_color_light = 0x7f060029
com.example.allinone:attr/actionModeBackground = 0x7f040013
com.example.allinone:macro/m3_comp_outlined_text_field_focus_supporting_text_color = 0x7f0e00bc
com.example.allinone:attr/tickVisible = 0x7f0404e3
com.example.allinone:string/exo_track_surround_7_point_1 = 0x7f1300f2
com.example.allinone:attr/yearTodayStyle = 0x7f040546
com.example.allinone:color/browser_actions_title_color = 0x7f060035
com.example.allinone:animator/m3_extended_fab_hide_motion_spec = 0x7f020012
com.example.allinone:drawable/ic_delete = 0x7f080124
com.example.allinone:attr/layout_constraintHorizontal_weight = 0x7f0402bc
com.example.allinone:color/bottom_nav_item_color = 0x7f060028
com.example.allinone:dimen/m3_comp_switch_disabled_unselected_icon_opacity = 0x7f0701b8
com.example.allinone:id/startDateInput = 0x7f0a0337
com.example.allinone:style/ThemeOverlay.App.MaterialAlertDialog = 0x7f1402b3
com.example.allinone:color/boldTextColor = 0x7f060027
com.example.allinone:style/Base.V21.Theme.MaterialComponents.Light.Dialog = 0x7f1400ab
com.example.allinone:attr/carousel_nextState = 0x7f0400b6
com.example.allinone:attr/state_liftable = 0x7f04044a
com.example.allinone:attr/chipEndPadding = 0x7f0400ce
com.example.allinone:style/Widget.Material3.TabLayout.OnSurface = 0x7f14041c
com.example.allinone:color/blue_500 = 0x7f060026
com.example.allinone:string/mtrl_checkbox_button_icon_path_indeterminate = 0x7f130179
com.example.allinone:id/investmentDescription = 0x7f0a01d2
com.example.allinone:attr/itemStrokeWidth = 0x7f040287
com.example.allinone:macro/m3_comp_date_picker_modal_weekdays_label_text_color = 0x7f0e001d
com.example.allinone:attr/activityChooserViewStyle = 0x7f040029
com.example.allinone:color/bg_tag_blue = 0x7f060024
com.example.allinone:dimen/mtrl_btn_padding_bottom = 0x7f07028a
com.example.allinone:dimen/m3_navigation_rail_item_active_indicator_height = 0x7f0701f1
com.example.allinone:id/workout_duration_text = 0x7f0a03d7
com.example.allinone:color/accent_material_light = 0x7f06001a
com.example.allinone:color/material_timepicker_button_stroke = 0x7f0602f4
com.example.allinone:id/investmentProfitLoss = 0x7f0a01d6
com.example.allinone:color/m3_sys_color_dark_surface_variant = 0x7f0601ba
com.example.allinone:attr/checkedIconEnabled = 0x7f0400c4
com.example.allinone:dimen/mtrl_fab_translation_z_hovered_focused = 0x7f0702dd
com.example.allinone:color/design_box_stroke_color = 0x7f060057
com.example.allinone:style/Widget.Material3.Slider.Legacy.Label = 0x7f140417
com.example.allinone:attr/checkMarkTintMode = 0x7f0400bf
com.example.allinone:style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f14008b
com.example.allinone:attr/state_lifted = 0x7f04044b
com.example.allinone:dimen/mtrl_bottomappbar_fabOffsetEndMode = 0x7f070278
com.example.allinone:attr/singleSelection = 0x7f040423
com.example.allinone:attr/titleMargin = 0x7f0404ec
com.example.allinone:color/abc_tint_switch_track = 0x7f060018
com.example.allinone:color/button_material_light = 0x7f060037
com.example.allinone:dimen/m3_sys_motion_easing_standard_control_y1 = 0x7f070239
com.example.allinone:anim/abc_popup_exit = 0x7f010004
com.example.allinone:attr/offsetAlignmentMode = 0x7f040387
com.example.allinone:color/exo_black_opacity_60 = 0x7f060088
com.example.allinone:style/TextAppearance.MaterialComponents.Badge = 0x7f14022d
com.example.allinone:drawable/ic_category_wing_tzun = 0x7f080118
com.example.allinone:id/transition_scene_layoutid_cache = 0x7f0a03ad
com.example.allinone:color/error_dark = 0x7f060085
com.example.allinone:anim/abc_tooltip_exit = 0x7f01000b
com.example.allinone:color/m3_ref_palette_neutral24 = 0x7f06014a
com.example.allinone:attr/materialButtonOutlinedStyle = 0x7f040307
com.example.allinone:color/bright_foreground_material_light = 0x7f06002f
com.example.allinone:color/m3_ref_palette_error80 = 0x7f06013f
com.example.allinone:macro/m3_comp_filled_icon_button_toggle_selected_icon_color = 0x7f0e0049
com.example.allinone:attr/extendedFloatingActionButtonStyle = 0x7f0401e1
com.example.allinone:color/abc_primary_text_material_dark = 0x7f06000b
com.example.allinone:attr/listPreferredItemPaddingRight = 0x7f0402f7
com.example.allinone:color/abc_primary_text_disable_only_material_dark = 0x7f060009
com.example.allinone:macro/m3_comp_navigation_rail_active_indicator_color = 0x7f0e0096
com.example.allinone:attr/itemPadding = 0x7f04027a
com.example.allinone:color/background_floating_material_light = 0x7f060021
com.example.allinone:color/abc_hint_foreground_material_light = 0x7f060008
com.example.allinone:attr/checkedState = 0x7f0400ca
com.example.allinone:color/dim_foreground_disabled_material_light = 0x7f06007f
com.example.allinone:attr/textAppearanceHeadlineMedium = 0x7f04049b
com.example.allinone:attr/materialSearchViewToolbarHeight = 0x7f040329
com.example.allinone:id/program_description_text = 0x7f0a02d7
com.example.allinone:id/profitLossAmountInput = 0x7f0a02d4
com.example.allinone:id/line1 = 0x7f0a01f4
com.example.allinone:drawable/$mtrl_switch_thumb_unchecked_pressed__0 = 0x7f080028
com.example.allinone:attr/splitMinSmallestWidth = 0x7f04042f
com.example.allinone:dimen/design_bottom_navigation_elevation = 0x7f070064
com.example.allinone:style/Widget.Material3.Button.TextButton.Snackbar = 0x7f1403a4
com.example.allinone:attr/arrowShaftLength = 0x7f040042
com.example.allinone:id/nav_notes = 0x7f0a0260
com.example.allinone:dimen/abc_dialog_padding_material = 0x7f070024
com.example.allinone:attr/layout_constraintLeft_toRightOf = 0x7f0402bf
com.example.allinone:attr/maxImageSize = 0x7f040335
com.example.allinone:bool/enable_system_foreground_service_default = 0x7f050003
com.example.allinone:attr/grid_skips = 0x7f040232
com.example.allinone:id/transition_image_transform = 0x7f0a03a9
com.example.allinone:dimen/abc_text_size_display_2_material = 0x7f070044
com.example.allinone:color/material_personalized__highlighted_text = 0x7f0602b9
com.example.allinone:dimen/mtrl_textinput_box_stroke_width_focused = 0x7f070325
com.example.allinone:attr/region_widthMoreThan = 0x7f0403d8
com.example.allinone:bool/abc_config_actionMenuItemAllCaps = 0x7f050001
com.example.allinone:attr/errorAccessibilityLiveRegion = 0x7f0401c8
com.example.allinone:attr/iconSize = 0x7f040254
com.example.allinone:string/exo_controls_fullscreen_enter_description = 0x7f1300c3
com.example.allinone:attr/windowFixedWidthMajor = 0x7f04053b
com.example.allinone:dimen/material_cursor_inset = 0x7f070255
com.example.allinone:string/mtrl_switch_thumb_path_name = 0x7f1301ad
com.example.allinone:attr/windowFixedHeightMinor = 0x7f04053a
com.example.allinone:id/amountLayout = 0x7f0a0063
com.example.allinone:style/Widget.Material3.MaterialCalendar = 0x7f1403df
com.example.allinone:attr/windowFixedHeightMajor = 0x7f040539
com.example.allinone:id/paidSwitch = 0x7f0a02a2
com.example.allinone:dimen/mtrl_progress_circular_track_thickness_extra_small = 0x7f070302
com.example.allinone:style/ShapeAppearance.M3.Sys.Shape.Corner.Medium = 0x7f14019c
com.example.allinone:macro/m3_comp_search_bar_container_color = 0x7f0e00e6
com.example.allinone:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
com.example.allinone:id/date_text = 0x7f0a00ec
com.example.allinone:dimen/mtrl_extended_fab_translation_z_hovered_focused = 0x7f0702d9
com.example.allinone:attr/waveVariesBy = 0x7f040535
com.example.allinone:attr/waveShape = 0x7f040534
com.example.allinone:color/mtrl_navigation_item_icon_tint = 0x7f060319
com.example.allinone:color/m3_ref_palette_primary40 = 0x7f06016d
com.example.allinone:id/right_icon = 0x7f0a02eb
com.example.allinone:drawable/mtrl_checkbox_button_icon = 0x7f080185
com.example.allinone:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f080034
com.example.allinone:attr/viewTransitionOnCross = 0x7f04052a
com.example.allinone:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0c0005
com.example.allinone:attr/motionEffect_viewTransition = 0x7f04036f
com.example.allinone:attr/yearStyle = 0x7f040545
com.example.allinone:string/sets = 0x7f13020c
com.example.allinone:attr/cornerFamilyTopLeft = 0x7f040163
com.example.allinone:color/m3_dynamic_highlighted_text = 0x7f0600c4
com.example.allinone:id/submit_area = 0x7f0a034b
com.example.allinone:attr/values = 0x7f040525
com.example.allinone:attr/showText = 0x7f04040e
com.example.allinone:string/status_paid_desc = 0x7f130219
com.example.allinone:attr/colorSecondary = 0x7f04012e
com.example.allinone:attr/motion_triggerOnCollision = 0x7f040377
com.example.allinone:attr/minHideDelay = 0x7f040341
com.example.allinone:attr/transitionPathRotate = 0x7f040517
com.example.allinone:dimen/mtrl_high_ripple_default_alpha = 0x7f0702df
com.example.allinone:color/material_dynamic_color_dark_error_container = 0x7f06025c
com.example.allinone:color/m3_sys_color_dynamic_dark_on_surface_variant = 0x7f0601cb
com.example.allinone:style/TextAppearance.Design.Prefix = 0x7f140205
com.example.allinone:color/m3_dark_hint_foreground = 0x7f0600b9
com.example.allinone:color/m3_sys_color_dynamic_dark_surface_bright = 0x7f0601d5
com.example.allinone:style/ThemeOverlay.MaterialComponents.Toolbar.Surface = 0x7f140325
com.example.allinone:attr/useMaterialThemeColors = 0x7f040522
com.example.allinone:attr/upDuration = 0x7f04051e
com.example.allinone:attr/triggerSlack = 0x7f04051b
com.example.allinone:attr/fabSize = 0x7f0401ed
com.example.allinone:attr/drawerLayoutCornerSize = 0x7f0401ab
com.example.allinone:color/abc_background_cache_hint_selector_material_dark = 0x7f060000
com.example.allinone:string/mtrl_badge_numberless_content_description = 0x7f130176
com.example.allinone:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f14024b
com.example.allinone:attr/targetPackage = 0x7f040485
com.example.allinone:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f1400c7
com.example.allinone:attr/trackTint = 0x7f040511
com.example.allinone:color/cardview_shadow_start_color = 0x7f06003e
com.example.allinone:id/detailsTextView = 0x7f0a0101
com.example.allinone:color/m3_timepicker_clock_text_color = 0x7f06024c
com.example.allinone:attr/materialSearchBarStyle = 0x7f040326
com.example.allinone:id/studentPhoto = 0x7f0a0348
com.example.allinone:attr/trackDecorationTintMode = 0x7f04050c
com.example.allinone:attr/progressBarStyle = 0x7f0403c4
com.example.allinone:attr/trackDecorationTint = 0x7f04050b
com.example.allinone:color/material_personalized_color_tertiary_container = 0x7f0602e3
com.example.allinone:attr/trackCornerRadius = 0x7f040509
com.example.allinone:macro/m3_comp_switch_selected_focus_handle_color = 0x7f0e0120
com.example.allinone:animator/m3_btn_state_list_anim = 0x7f02000b
com.example.allinone:attr/trackColorInactive = 0x7f040508
com.example.allinone:dimen/tooltip_corner_radius = 0x7f070349
com.example.allinone:dimen/m3_btn_padding_right = 0x7f070100
com.example.allinone:attr/statusBarBackground = 0x7f04044d
com.example.allinone:string/fallback_menu_item_share_link = 0x7f1300fa
com.example.allinone:string/active_status = 0x7f13001b
com.example.allinone:id/wtFragmentContainer = 0x7f0a03e1
com.example.allinone:dimen/m3_badge_size = 0x7f0700da
com.example.allinone:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f1400f1
com.example.allinone:color/m3_ref_palette_dynamic_primary90 = 0x7f060119
com.example.allinone:drawable/ic_reports = 0x7f08015a
com.example.allinone:dimen/m3_back_progress_main_container_max_translation_y = 0x7f0700d3
com.example.allinone:attr/trackColorActive = 0x7f040507
com.example.allinone:style/Base.V21.ThemeOverlay.Material3.BottomSheetDialog = 0x7f1400ad
com.example.allinone:attr/fontStyle = 0x7f040221
com.example.allinone:attr/useDrawerArrowDrawable = 0x7f040521
com.example.allinone:id/home = 0x7f0a01b2
com.example.allinone:id/transition_pause_alpha = 0x7f0a03ab
com.example.allinone:style/Widget.Material3.MaterialCalendar.HeaderToggleButton = 0x7f1403ee
com.example.allinone:color/m3_sys_color_dynamic_dark_surface_container_highest = 0x7f0601d8
com.example.allinone:attr/cursorColor = 0x7f040173
com.example.allinone:attr/tabMaxWidth = 0x7f040474
com.example.allinone:attr/boxStrokeColor = 0x7f040091
com.example.allinone:attr/touchAnchorSide = 0x7f040502
com.example.allinone:macro/m3_comp_filled_text_field_input_text_type = 0x7f0e0050
com.example.allinone:dimen/abc_text_size_title_material = 0x7f07004f
com.example.allinone:attr/chipSpacing = 0x7f0400d7
com.example.allinone:attr/touchAnchorId = 0x7f040501
com.example.allinone:attr/toolbarStyle = 0x7f0404fa
com.example.allinone:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f140058
com.example.allinone:attr/titleTextEllipsize = 0x7f0404f5
com.example.allinone:attr/popExitAnim = 0x7f0403b4
com.example.allinone:attr/titleTextAppearance = 0x7f0404f3
com.example.allinone:attr/region_heightMoreThan = 0x7f0403d6
com.example.allinone:attr/titleMarginTop = 0x7f0404f0
com.example.allinone:style/Widget.Material3.AutoCompleteTextView.FilledBox = 0x7f140386
com.example.allinone:dimen/m3_comp_assist_chip_container_height = 0x7f07011f
com.example.allinone:style/Widget.AppCompat.ListMenuView = 0x7f14035d
com.example.allinone:macro/m3_comp_search_bar_hover_supporting_text_color = 0x7f0e00e8
com.example.allinone:attr/layout_constraintLeft_toLeftOf = 0x7f0402be
com.example.allinone:attr/titleCentered = 0x7f0404e9
com.example.allinone:attr/autoSizeMaxTextSize = 0x7f040049
com.example.allinone:attr/listChoiceIndicatorSingleAnimated = 0x7f0402ec
com.example.allinone:attr/tintNavigationIcon = 0x7f0404e7
com.example.allinone:dimen/mtrl_btn_text_size = 0x7f070294
com.example.allinone:color/design_dark_default_color_on_surface = 0x7f06005e
com.example.allinone:color/material_slider_halo_color = 0x7f0602ef
com.example.allinone:attr/tickMarkTintMode = 0x7f0404e0
com.example.allinone:string/email = 0x7f13009c
com.example.allinone:color/design_dark_default_color_primary = 0x7f06005f
com.example.allinone:dimen/design_snackbar_text_size = 0x7f07008a
com.example.allinone:attr/popUpToSaveState = 0x7f0403b7
com.example.allinone:attr/floatingActionButtonStyle = 0x7f040202
com.example.allinone:attr/imagePanY = 0x7f04025f
com.example.allinone:string/add_attachment = 0x7f13001c
com.example.allinone:attr/trackTintMode = 0x7f040512
com.example.allinone:attr/materialAlertDialogTitlePanelStyle = 0x7f040305
com.example.allinone:attr/tickMarkTint = 0x7f0404df
com.example.allinone:string/mtrl_switch_thumb_group_name = 0x7f1301aa
com.example.allinone:attr/tickColorInactive = 0x7f0404dd
com.example.allinone:string/add_exercise = 0x7f13001d
com.example.allinone:attr/buffered_color = 0x7f040096
com.example.allinone:attr/thumbWidth = 0x7f0404da
com.example.allinone:attr/thumbTrackGapSize = 0x7f0404d9
com.example.allinone:id/listMode = 0x7f0a01fc
com.example.allinone:attr/tabIndicatorAnimationDuration = 0x7f04046d
com.example.allinone:string/total_workout_duration = 0x7f130243
com.example.allinone:attr/thumbTintMode = 0x7f0404d8
com.example.allinone:attr/postSplashScreenTheme = 0x7f0403bc
com.example.allinone:attr/thumbTextPadding = 0x7f0404d6
com.example.allinone:layout/abc_list_menu_item_icon = 0x7f0d000f
com.example.allinone:attr/thumbIconTintMode = 0x7f0404d2
com.example.allinone:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
com.example.allinone:dimen/abc_list_item_padding_horizontal_material = 0x7f070033
com.example.allinone:integer/m3_sys_motion_path = 0x7f0b0024
com.example.allinone:drawable/abc_tab_indicator_mtrl_alpha = 0x7f08006d
com.example.allinone:attr/spinnerDropDownItemStyle = 0x7f04042b
com.example.allinone:bool/abc_action_bar_embed_tabs = 0x7f050000
com.example.allinone:color/button_material_dark = 0x7f060036
com.example.allinone:attr/textureHeight = 0x7f0404c8
com.example.allinone:attr/textStartPadding = 0x7f0404c5
com.example.allinone:attr/textPanX = 0x7f0404c3
com.example.allinone:style/ThemeOverlay.MaterialComponents.Dark.ActionBar = 0x7f14030b
com.example.allinone:attr/textOutlineThickness = 0x7f0404c2
com.example.allinone:id/material_hour_tv = 0x7f0a021a
com.example.allinone:styleable/AppBarLayout_Layout = 0x7f15000f
com.example.allinone:color/m3_navigation_bar_item_with_indicator_icon_tint = 0x7f0600d0
com.example.allinone:dimen/material_textinput_min_width = 0x7f070268
com.example.allinone:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.example.allinone:macro/m3_comp_filled_card_container_shape = 0x7f0e0047
com.example.allinone:attr/textLocale = 0x7f0404c0
com.example.allinone:id/scroll = 0x7f0a02fa
com.example.allinone:anim/m3_bottom_sheet_slide_in = 0x7f010021
com.example.allinone:string/tab = 0x7f130226
com.example.allinone:attr/textInputOutlinedStyle = 0x7f0404be
com.example.allinone:attr/titleMarginEnd = 0x7f0404ee
com.example.allinone:attr/textInputOutlinedExposedDropdownMenuStyle = 0x7f0404bd
com.example.allinone:id/voiceNoteDuration = 0x7f0a03ca
com.example.allinone:attr/expandedTitleMarginTop = 0x7f0401da
com.example.allinone:attr/textInputLayoutFocusedRectEnabled = 0x7f0404bb
com.example.allinone:style/ExoStyledControls.Button.Bottom.FullScreen = 0x7f14013a
com.example.allinone:dimen/mtrl_calendar_dialog_background_inset = 0x7f0702a2
com.example.allinone:color/m3_ref_palette_primary95 = 0x7f060173
com.example.allinone:string/abc_menu_alt_shortcut_label = 0x7f130008
com.example.allinone:color/m3_ref_palette_neutral_variant50 = 0x7f060161
com.example.allinone:color/abc_search_url_text_selected = 0x7f060010
com.example.allinone:dimen/m3_btn_padding_bottom = 0x7f0700fe
com.example.allinone:id/marginBalanceValueText = 0x7f0a0204
com.example.allinone:attr/errorIconTint = 0x7f0401cc
com.example.allinone:attr/topInsetScrimEnabled = 0x7f040500
com.example.allinone:animator/design_fab_show_motion_spec = 0x7f020002
com.example.allinone:layout/mtrl_picker_text_input_date = 0x7f0d00ae
com.example.allinone:attr/mock_showLabel = 0x7f04034a
com.example.allinone:attr/textColorAlertDialogListItem = 0x7f0404b4
com.example.allinone:attr/drawableTopCompat = 0x7f0401a9
com.example.allinone:attr/contentInsetStartWithNavigation = 0x7f040152
com.example.allinone:attr/textBackgroundZoom = 0x7f0404b3
com.example.allinone:string/template_percent = 0x7f130232
com.example.allinone:drawable/ic_no_registrations = 0x7f08014c
com.example.allinone:attr/iconifiedByDefault = 0x7f040258
com.example.allinone:attr/titleMarginBottom = 0x7f0404ed
com.example.allinone:color/material_personalized_color_on_secondary = 0x7f0602c6
com.example.allinone:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f14017c
com.example.allinone:attr/textBackground = 0x7f0404af
com.example.allinone:attr/textInputStyle = 0x7f0404bf
com.example.allinone:attr/textAppearanceSubtitle2 = 0x7f0404ab
com.example.allinone:id/select_dialog_listview = 0x7f0a0309
com.example.allinone:attr/textAppearanceSearchResultTitle = 0x7f0404a8
com.example.allinone:attr/expandedTitleMarginBottom = 0x7f0401d7
com.example.allinone:id/cos = 0x7f0a00dc
com.example.allinone:attr/textAppearancePopupMenuHeader = 0x7f0404a6
com.example.allinone:attr/textAppearanceOverline = 0x7f0404a5
com.example.allinone:dimen/m3_comp_bottom_app_bar_container_height = 0x7f070127
com.example.allinone:attr/textAppearanceTitleLarge = 0x7f0404ac
com.example.allinone:style/Widget.MaterialComponents.AppBarLayout.Surface = 0x7f140435
com.example.allinone:dimen/m3_comp_search_bar_container_elevation = 0x7f070194
com.example.allinone:style/Widget.Material3.MaterialCalendar.Year.Selected = 0x7f1403f3
com.example.allinone:attr/flow_lastVerticalStyle = 0x7f040210
com.example.allinone:attr/deltaPolarAngle = 0x7f04018d
com.example.allinone:attr/collapsedSize = 0x7f0400f9
com.example.allinone:color/m3_navigation_item_background_color = 0x7f0600d3
com.example.allinone:attr/textAppearanceLabelSmall = 0x7f04049f
com.example.allinone:string/item_type_icon = 0x7f130112
com.example.allinone:dimen/m3_comp_secondary_navigation_tab_focus_state_layer_opacity = 0x7f07019c
com.example.allinone:attr/textAppearanceHeadlineLarge = 0x7f04049a
com.example.allinone:color/m3_sys_color_light_on_primary_container = 0x7f060217
com.example.allinone:drawable/ic_no_students = 0x7f08014d
com.example.allinone:macro/m3_comp_outlined_text_field_caret_color = 0x7f0e00b0
com.example.allinone:layout/dialog_income_investment = 0x7f0d003d
com.example.allinone:drawable/$mtrl_switch_thumb_checked_unchecked__0 = 0x7f080022
com.example.allinone:attr/gestureInsetBottomIgnored = 0x7f040229
com.example.allinone:color/m3_ref_palette_tertiary60 = 0x7f060189
com.example.allinone:attr/textAppearanceHeadline5 = 0x7f040498
com.example.allinone:attr/queryHint = 0x7f0403c9
com.example.allinone:id/accessibility_custom_action_16 = 0x7f0a0019
com.example.allinone:dimen/exo_small_icon_height = 0x7f0700a5
com.example.allinone:style/Base.V14.ThemeOverlay.Material3.SideSheetDialog = 0x7f14009e
com.example.allinone:attr/layout_constraintBaseline_toBaselineOf = 0x7f0402a6
com.example.allinone:attr/layout_marginBaseline = 0x7f0402dd
com.example.allinone:attr/textAppearanceHeadline4 = 0x7f040497
com.example.allinone:dimen/mtrl_slider_tick_radius = 0x7f070311
com.example.allinone:color/material_personalized_color_on_surface = 0x7f0602c8
com.example.allinone:macro/m3_comp_time_picker_period_selector_selected_hover_state_layer_color = 0x7f0e0157
com.example.allinone:attr/actionBarItemBackground = 0x7f040004
com.example.allinone:attr/fastScrollHorizontalTrackDrawable = 0x7f0401f0
com.example.allinone:attr/fabCradleVerticalOffset = 0x7f0401eb
com.example.allinone:string/common_google_play_services_notification_channel_name = 0x7f130064
com.example.allinone:attr/trackHeight = 0x7f04050d
com.example.allinone:color/material_timepicker_clock_text_color = 0x7f0602f5
com.example.allinone:macro/m3_comp_text_button_label_text_color = 0x7f0e0144
com.example.allinone:attr/textAppearanceDisplayMedium = 0x7f040492
com.example.allinone:attr/layout_constrainedWidth = 0x7f0402a4
com.example.allinone:style/Theme.AllinOne.SplashText = 0x7f140243
com.example.allinone:style/Base.Widget.AppCompat.Button.Colored = 0x7f1400d4
com.example.allinone:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f1401d1
com.example.allinone:attr/textAppearanceBody1 = 0x7f04048a
com.example.allinone:attr/collapsedTitleTextColor = 0x7f0400fc
com.example.allinone:string/tooltip_label = 0x7f13023e
com.example.allinone:string/select = 0x7f130202
com.example.allinone:drawable/rounded_corner_bg = 0x7f0801b7
com.example.allinone:color/tooltip_background_light = 0x7f060362
com.example.allinone:color/m3_ref_palette_neutral70 = 0x7f060151
com.example.allinone:style/TextAppearance.M3.Sys.Typescale.DisplayLarge = 0x7f14020c
com.example.allinone:id/tab_insight = 0x7f0a0356
com.example.allinone:color/material_dynamic_tertiary90 = 0x7f0602a1
com.example.allinone:attr/tabStyle = 0x7f040480
com.example.allinone:dimen/mtrl_switch_text_padding = 0x7f07031b
com.example.allinone:dimen/m3_small_fab_max_image_size = 0x7f070211
com.example.allinone:attr/motionDurationMedium3 = 0x7f040356
com.example.allinone:attr/tabSelectedTextColor = 0x7f04047f
com.example.allinone:attr/bottomSheetStyle = 0x7f040089
com.example.allinone:color/m3_sys_color_dynamic_light_outline = 0x7f0601f0
com.example.allinone:macro/m3_comp_snackbar_supporting_text_color = 0x7f0e0115
com.example.allinone:attr/motion_postLayoutCollision = 0x7f040376
com.example.allinone:attr/tabSelectedTextAppearance = 0x7f04047e
com.example.allinone:attr/tabPaddingTop = 0x7f04047b
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f140467
com.example.allinone:style/Widget.Material3.LinearProgressIndicator.Legacy = 0x7f1403dd
com.example.allinone:attr/dayStyle = 0x7f040184
com.example.allinone:layout/mtrl_alert_dialog_actions = 0x7f0d0093
com.example.allinone:string/exo_controls_time_placeholder = 0x7f1300d7
com.example.allinone:attr/perpendicularPath_percent = 0x7f0403a8
com.example.allinone:string/path_password_eye = 0x7f1301d0
com.example.allinone:attr/tabPadding = 0x7f040477
com.example.allinone:id/mondayChip = 0x7f0a022c
com.example.allinone:macro/m3_comp_search_view_header_supporting_text_color = 0x7f0e00f7
com.example.allinone:attr/thumbIconSize = 0x7f0404d0
com.example.allinone:attr/tabIndicatorAnimationMode = 0x7f04046e
com.example.allinone:dimen/mtrl_slider_track_side_padding = 0x7f070313
com.example.allinone:dimen/m3_comp_badge_large_size = 0x7f070124
com.example.allinone:attr/tabIndicator = 0x7f04046c
com.example.allinone:attr/measureWithLargestChild = 0x7f04033a
com.example.allinone:attr/tabBackground = 0x7f040467
com.example.allinone:attr/layout_goneMarginStart = 0x7f0402d9
com.example.allinone:attr/surface_type = 0x7f040461
com.example.allinone:attr/suffixTextAppearance = 0x7f04045e
com.example.allinone:string/common_google_play_services_install_text = 0x7f130062
com.example.allinone:attr/subheaderInsetStart = 0x7f040455
com.example.allinone:attr/ttcIndex = 0x7f04051c
com.example.allinone:attr/strokeWidth = 0x7f040451
com.example.allinone:color/material_blue_grey_800 = 0x7f060254
com.example.allinone:macro/m3_comp_secondary_navigation_tab_container_color = 0x7f0e00fc
com.example.allinone:attr/textAppearanceLabelMedium = 0x7f04049e
com.example.allinone:id/prevPageButton = 0x7f0a02d1
com.example.allinone:attr/drawableBottomCompat = 0x7f0401a1
com.example.allinone:attr/multiChoiceItemLayout = 0x7f040379
com.example.allinone:attr/strokeColor = 0x7f040450
com.example.allinone:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f140026
com.example.allinone:dimen/mtrl_navigation_elevation = 0x7f0702ea
com.example.allinone:style/ShapeAppearance.M3.Comp.Badge.Large.Shape = 0x7f140186
com.example.allinone:color/warning_light = 0x7f060368
com.example.allinone:attr/backHandlingEnabled = 0x7f040050
com.example.allinone:drawable/uncompleted_exercise_background = 0x7f0801bf
com.example.allinone:dimen/design_bottom_navigation_active_item_max_width = 0x7f070061
com.example.allinone:id/end = 0x7f0a012d
com.example.allinone:attr/itemShapeInsetStart = 0x7f040283
com.example.allinone:integer/default_icon_animation_duration = 0x7f0b0007
com.example.allinone:dimen/m3_back_progress_side_container_max_scale_y_distance = 0x7f0700d7
com.example.allinone:attr/navigationRailStyle = 0x7f04037f
com.example.allinone:color/textPrimary = 0x7f06035d
com.example.allinone:id/thursdayChip = 0x7f0a0386
com.example.allinone:attr/state_indeterminate = 0x7f040449
com.example.allinone:string/call_notification_answer_action = 0x7f13003d
com.example.allinone:macro/m3_comp_filled_text_field_error_trailing_icon_color = 0x7f0e004f
com.example.allinone:dimen/m3_comp_assist_chip_with_icon_icon_size = 0x7f070123
com.example.allinone:string/recording_failed = 0x7f1301ec
com.example.allinone:attr/stateLabels = 0x7f040443
com.example.allinone:animator/mtrl_btn_unelevated_state_list_anim = 0x7f020016
com.example.allinone:dimen/m3_comp_navigation_drawer_hover_state_layer_opacity = 0x7f070165
com.example.allinone:styleable/DrawerLayout = 0x7f150036
com.example.allinone:attr/startIconDrawable = 0x7f04043e
com.example.allinone:attr/startIconCheckable = 0x7f04043c
com.example.allinone:id/textSpacerNoTitle = 0x7f0a0374
com.example.allinone:id/stopLossLayout = 0x7f0a0344
com.example.allinone:string/mtrl_picker_text_input_date_hint = 0x7f13019f
com.example.allinone:attr/motionEffect_strict = 0x7f04036c
com.example.allinone:attr/secondaryActivityName = 0x7f0403f3
com.example.allinone:attr/stackFromEnd = 0x7f040439
com.example.allinone:dimen/mtrl_calendar_navigation_height = 0x7f0702b1
com.example.allinone:menu/menu_task_options = 0x7f0f0005
com.example.allinone:dimen/mtrl_btn_snackbar_margin_horizontal = 0x7f07028f
com.example.allinone:style/Base.Widget.Material3.CompoundButton.CheckBox = 0x7f140106
com.example.allinone:animator/fragment_open_exit = 0x7f020008
com.example.allinone:attr/finishPrimaryWithSecondary = 0x7f0401f3
com.example.allinone:attr/springStopThreshold = 0x7f040437
com.example.allinone:attr/textAppearanceSubtitle1 = 0x7f0404aa
com.example.allinone:color/m3_ref_palette_secondary50 = 0x7f06017b
com.example.allinone:id/exo_sub_text = 0x7f0a0175
com.example.allinone:style/ShapeAppearance.M3.Comp.SearchBar.Avatar.Shape = 0x7f140190
com.example.allinone:attr/bar_height = 0x7f04006f
com.example.allinone:attr/splitTrack = 0x7f040432
com.example.allinone:attr/triggerReceiver = 0x7f04051a
com.example.allinone:color/m3_ref_palette_dynamic_secondary99 = 0x7f060128
com.example.allinone:attr/materialCardViewFilledStyle = 0x7f04031a
com.example.allinone:style/Base.Theme.MaterialComponents = 0x7f140065
com.example.allinone:style/Widget.AppCompat.Toolbar = 0x7f140374
com.example.allinone:attr/splitLayoutDirection = 0x7f04042e
com.example.allinone:attr/behavior_halfExpandedRatio = 0x7f040078
com.example.allinone:attr/splashScreenIconSize = 0x7f04042d
com.example.allinone:style/Widget.MaterialComponents.MaterialButtonToggleGroup = 0x7f140463
com.example.allinone:attr/spinnerStyle = 0x7f04042c
com.example.allinone:dimen/design_snackbar_max_width = 0x7f070085
com.example.allinone:attr/placeholder_emptyVisibility = 0x7f0403ae
com.example.allinone:attr/sizePercent = 0x7f040424
com.example.allinone:attr/indicatorDirectionLinear = 0x7f040266
com.example.allinone:attr/simpleItems = 0x7f040420
com.example.allinone:attr/simpleItemSelectedColor = 0x7f04041e
com.example.allinone:color/m3_chip_stroke_color = 0x7f0600b4
com.example.allinone:bool/enable_system_alarm_service_default = 0x7f050002
com.example.allinone:attr/state_with_icon = 0x7f04044c
com.example.allinone:drawable/common_google_signin_btn_text_light = 0x7f08009f
com.example.allinone:attr/simpleItemLayout = 0x7f04041d
com.example.allinone:drawable/$mtrl_checkbox_button_icon_checked_indeterminate__0 = 0x7f080010
com.example.allinone:string/manage_groups = 0x7f13015c
com.example.allinone:attr/checkedIcon = 0x7f0400c3
com.example.allinone:drawable/ic_keyboard_black_24dp = 0x7f08013f
com.example.allinone:attr/closeIconTint = 0x7f0400f4
com.example.allinone:id/filled = 0x7f0a0185
com.example.allinone:attr/show_vr_button = 0x7f040418
com.example.allinone:color/common_google_signin_btn_text_dark_focused = 0x7f06004c
com.example.allinone:string/mtrl_checkbox_button_path_unchecked = 0x7f13017e
com.example.allinone:color/m3_ref_palette_primary70 = 0x7f060170
com.example.allinone:attr/coplanarSiblingViewId = 0x7f04015f
com.example.allinone:attr/nestedScrollable = 0x7f040383
com.example.allinone:attr/show_timeout = 0x7f040417
com.example.allinone:id/search_edit_frame = 0x7f0a0303
com.example.allinone:id/layout = 0x7f0a01e6
com.example.allinone:string/delete_registration = 0x7f130082
com.example.allinone:id/postDetailThumbnail = 0x7f0a02c9
com.example.allinone:drawable/ic_image = 0x7f080139
com.example.allinone:style/MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f140165
com.example.allinone:attr/textBackgroundPanX = 0x7f0404b0
com.example.allinone:attr/textAppearanceBody2 = 0x7f04048b
com.example.allinone:drawable/abc_btn_borderless_material = 0x7f08002b
com.example.allinone:color/m3_dynamic_hint_foreground = 0x7f0600c5
com.example.allinone:string/m3c_date_range_picker_title = 0x7f130147
com.example.allinone:color/m3_button_foreground_color_selector = 0x7f0600a6
com.example.allinone:color/m3_sys_color_dark_on_error = 0x7f0601a2
com.example.allinone:string/mtrl_picker_range_header_only_start_selected = 0x7f130199
com.example.allinone:style/Theme.Material3.DynamicColors.Light = 0x7f140276
com.example.allinone:attr/time_bar_min_update_interval = 0x7f0404e4
com.example.allinone:color/colorPrimaryDark = 0x7f060045
com.example.allinone:dimen/m3_navigation_rail_icon_size = 0x7f0701f0
com.example.allinone:attr/showTitle = 0x7f04040f
com.example.allinone:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date = 0x7f140314
com.example.allinone:macro/m3_comp_outlined_button_hover_outline_color = 0x7f0e00a5
com.example.allinone:attr/badgeWidePadding = 0x7f040066
com.example.allinone:attr/spanCount = 0x7f040429
com.example.allinone:id/nav_backup = 0x7f0a0254
com.example.allinone:attr/showPaths = 0x7f04040d
com.example.allinone:style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked = 0x7f140166
com.example.allinone:id/filterSection = 0x7f0a0187
com.example.allinone:attr/homeAsUpIndicator = 0x7f04024b
com.example.allinone:attr/roundPercent = 0x7f0403e1
com.example.allinone:attr/showMotionSpec = 0x7f04040c
com.example.allinone:attr/showDividers = 0x7f04040a
com.example.allinone:attr/uri = 0x7f04051f
com.example.allinone:styleable/NavDeepLink = 0x7f150074
com.example.allinone:drawable/exo_edit_mode_logo = 0x7f0800bc
com.example.allinone:drawable/ic_password = 0x7f080153
com.example.allinone:drawable/ic_category_salary = 0x7f080114
com.example.allinone:attr/showDelay = 0x7f040409
com.example.allinone:dimen/highlight_alpha_material_dark = 0x7f0700b9
com.example.allinone:macro/m3_comp_switch_unselected_hover_state_layer_color = 0x7f0e0137
com.example.allinone:string/student_has_active_registrations = 0x7f130222
com.example.allinone:id/mtrl_internal_children_alpha_tag = 0x7f0a0241
com.example.allinone:attr/chipIconVisible = 0x7f0400d4
com.example.allinone:attr/shapeAppearanceOverlay = 0x7f040402
com.example.allinone:color/design_default_color_on_background = 0x7f060067
com.example.allinone:color/exo_error_message_background_color = 0x7f06008c
com.example.allinone:attr/mimeType = 0x7f04033f
com.example.allinone:dimen/m3_datepicker_elevation = 0x7f0701d2
com.example.allinone:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen = 0x7f1401c1
com.example.allinone:dimen/mtrl_btn_icon_padding = 0x7f070286
com.example.allinone:attr/shapeAppearanceCornerSmall = 0x7f0403ff
com.example.allinone:attr/shapeAppearanceCornerLarge = 0x7f0403fd
com.example.allinone:dimen/m3_fab_corner_size = 0x7f0701db
com.example.allinone:attr/selectorSize = 0x7f0403f8
com.example.allinone:id/color_green = 0x7f0a00cc
com.example.allinone:macro/m3_comp_progress_indicator_active_indicator_color = 0x7f0e00d4
com.example.allinone:attr/seekBarStyle = 0x7f0403f4
com.example.allinone:attr/secondaryActivityAction = 0x7f0403f2
com.example.allinone:style/TextAppearance.AppCompat.Medium = 0x7f1401d5
com.example.allinone:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f14007f
com.example.allinone:color/m3_dynamic_dark_highlighted_text = 0x7f0600bf
com.example.allinone:attr/tabIconTint = 0x7f04046a
com.example.allinone:dimen/m3_sys_motion_easing_legacy_decelerate_control_y1 = 0x7f07022d
com.example.allinone:string/exo_controls_overflow_show_description = 0x7f1300c8
com.example.allinone:id/chartCard = 0x7f0a00bb
com.example.allinone:anim/abc_slide_out_top = 0x7f010009
com.example.allinone:color/m3_sys_color_dynamic_light_tertiary_container = 0x7f060200
com.example.allinone:attr/indicatorSize = 0x7f040268
com.example.allinone:style/Base.Widget.AppCompat.Button.Borderless = 0x7f1400d1
com.example.allinone:id/exo_ffwd = 0x7f0a015b
com.example.allinone:style/Widget.Material3.Button.UnelevatedButton = 0x7f1403a7
com.example.allinone:dimen/m3_comp_switch_track_height = 0x7f0701bc
com.example.allinone:attr/voiceIcon = 0x7f04052e
com.example.allinone:styleable/SideSheetBehavior_Layout = 0x7f15008f
com.example.allinone:dimen/compat_notification_large_icon_max_width = 0x7f07005e
com.example.allinone:dimen/m3_appbar_size_large = 0x7f0700cf
com.example.allinone:color/material_grey_300 = 0x7f0602a5
com.example.allinone:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f1400cb
com.example.allinone:id/off = 0x7f0a0282
com.example.allinone:color/m3_sys_color_dynamic_light_primary_container = 0x7f0601f3
com.example.allinone:attr/crossfade = 0x7f040171
com.example.allinone:attr/scrubber_color = 0x7f0403e9
com.example.allinone:dimen/m3_card_disabled_z = 0x7f070109
com.example.allinone:attr/scrimVisibleHeightTrigger = 0x7f0403e8
com.example.allinone:style/ExoMediaButton = 0x7f14012d
com.example.allinone:attr/scrimBackground = 0x7f0403e7
com.example.allinone:attr/drawableRightCompat = 0x7f0401a4
com.example.allinone:string/mtrl_picker_navigate_to_current_year_description = 0x7f130195
com.example.allinone:drawable/ic_passkey = 0x7f080152
com.example.allinone:attr/preserveIconSpacing = 0x7f0403c0
com.example.allinone:drawable/exo_icon_repeat_all = 0x7f0800d6
com.example.allinone:attr/route = 0x7f0403e2
com.example.allinone:layout/exo_styled_player_view = 0x7f0d004e
com.example.allinone:attr/reverseLayout = 0x7f0403dd
com.example.allinone:macro/m3_comp_filled_tonal_button_label_text_color = 0x7f0e0053
com.example.allinone:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f1400c9
com.example.allinone:style/Base.Widget.Material3.ActionMode = 0x7f140101
com.example.allinone:color/design_dark_default_color_background = 0x7f060058
com.example.allinone:attr/reactiveGuide_valueId = 0x7f0403d3
com.example.allinone:attr/reactiveGuide_applyToConstraintSet = 0x7f0403d2
com.example.allinone:style/Widget.Material3.Chip.Input.Icon = 0x7f1403b2
com.example.allinone:style/Theme.MaterialComponents.Light.DarkActionBar = 0x7f1402a0
com.example.allinone:attr/reactiveGuide_applyToAllConstraintSets = 0x7f0403d1
com.example.allinone:attr/isMaterial3Theme = 0x7f04026e
com.example.allinone:layout/notification_template_custom_big = 0x7f0d00bb
com.example.allinone:color/mtrl_choice_chip_background_color = 0x7f060309
com.example.allinone:layout/notification_template_big_media = 0x7f0d00b7
com.example.allinone:attr/ratingBarStyleSmall = 0x7f0403cf
com.example.allinone:drawable/common_google_signin_btn_icon_light = 0x7f080096
com.example.allinone:string/registration_updated = 0x7f1301f1
com.example.allinone:dimen/m3_sys_motion_easing_standard_control_y2 = 0x7f07023a
com.example.allinone:attr/ratingBarStyleIndicator = 0x7f0403ce
com.example.allinone:attr/tabIndicatorColor = 0x7f04046f
com.example.allinone:dimen/m3_fab_translation_z_hovered_focused = 0x7f0701dc
com.example.allinone:dimen/m3_comp_sheet_bottom_docked_standard_container_elevation = 0x7f0701a2
com.example.allinone:attr/barrierDirection = 0x7f040071
com.example.allinone:attr/enableEdgeToEdge = 0x7f0401ba
com.example.allinone:color/m3_sys_color_dynamic_light_on_error_container = 0x7f0601e7
com.example.allinone:attr/queryPatterns = 0x7f0403ca
com.example.allinone:macro/m3_comp_extended_fab_tertiary_icon_color = 0x7f0e0035
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection = 0x7f140471
com.example.allinone:color/m3_ref_palette_secondary70 = 0x7f06017d
com.example.allinone:attr/badgeTextAppearance = 0x7f040063
com.example.allinone:color/common_google_signin_btn_text_dark_default = 0x7f06004a
com.example.allinone:attr/colorOnSecondaryFixedVariant = 0x7f04011b
com.example.allinone:id/outline = 0x7f0a029b
com.example.allinone:attr/layoutDescription = 0x7f04029b
com.example.allinone:macro/m3_comp_search_view_header_leading_icon_color = 0x7f0e00f6
com.example.allinone:color/material_dynamic_color_light_error = 0x7f06025f
com.example.allinone:string/mtrl_picker_date_header_title = 0x7f13018d
com.example.allinone:animator/fragment_fade_enter = 0x7f020005
com.example.allinone:attr/quantizeMotionPhase = 0x7f0403c6
com.example.allinone:macro/m3_comp_time_picker_period_selector_label_text_type = 0x7f0e0153
com.example.allinone:id/parentPanel = 0x7f0a02a5
com.example.allinone:attr/thumbRadius = 0x7f0404d3
com.example.allinone:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f14023e
com.example.allinone:dimen/browser_actions_context_menu_min_padding = 0x7f070053
com.example.allinone:attr/thumbElevation = 0x7f0404cd
com.example.allinone:drawable/ic_format_bold = 0x7f080130
com.example.allinone:animator/mtrl_extended_fab_change_size_collapse_motion_spec = 0x7f020019
com.example.allinone:dimen/m3_carousel_small_item_default_corner_size = 0x7f070115
com.example.allinone:dimen/hint_alpha_material_light = 0x7f0700bc
com.example.allinone:attr/prefixTextColor = 0x7f0403bf
com.example.allinone:attr/prefixTextAppearance = 0x7f0403be
com.example.allinone:integer/m3_sys_shape_corner_extra_large_corner_family = 0x7f0b0025
com.example.allinone:attr/popupWindowStyle = 0x7f0403bb
com.example.allinone:attr/popupTheme = 0x7f0403ba
com.example.allinone:macro/m3_comp_filled_tonal_icon_button_container_color = 0x7f0e0054
com.example.allinone:dimen/abc_control_inset_material = 0x7f070019
com.example.allinone:color/text_primary = 0x7f060360
com.example.allinone:styleable/PopupWindowBackgroundState = 0x7f150083
com.example.allinone:attr/scrimAnimationDuration = 0x7f0403e6
com.example.allinone:style/Widget.Material3.MaterialTimePicker.Display.TextInputLayout = 0x7f1403ff
com.example.allinone:string/m3c_date_input_invalid_for_pattern = 0x7f13012b
com.example.allinone:id/averageTransactionText = 0x7f0a007b
com.example.allinone:attr/cardElevation = 0x7f0400aa
com.example.allinone:color/mtrl_text_btn_text_color_selector = 0x7f06032a
com.example.allinone:layout/fragment_wt_students = 0x7f0d005f
com.example.allinone:attr/wavePhase = 0x7f040533
com.example.allinone:color/mtrl_tabs_icon_color_selector_colored = 0x7f060327
com.example.allinone:attr/expandedHintEnabled = 0x7f0401d4
com.example.allinone:macro/m3_comp_fab_tertiary_container_color = 0x7f0e003f
com.example.allinone:attr/editTextColor = 0x7f0401b3
com.example.allinone:style/TextAppearance.AppCompat.Body1 = 0x7f1401c5
com.example.allinone:attr/behavior_fitToContents = 0x7f040077
com.example.allinone:attr/materialCalendarHeaderTitle = 0x7f040312
com.example.allinone:layout/mtrl_picker_fullscreen = 0x7f0d00a8
com.example.allinone:attr/player_layout_id = 0x7f0403b1
com.example.allinone:color/material_personalized_color_surface_inverse = 0x7f0602e0
com.example.allinone:dimen/m3_simple_item_color_hovered_alpha = 0x7f07020e
com.example.allinone:id/exercises_container = 0x7f0a0148
com.example.allinone:attr/materialIconButtonFilledStyle = 0x7f040322
com.example.allinone:animator/mtrl_extended_fab_change_size_expand_motion_spec = 0x7f02001a
com.example.allinone:dimen/mtrl_navigation_rail_elevation = 0x7f0702f3
com.example.allinone:attr/colorOnTertiaryFixed = 0x7f040121
com.example.allinone:drawable/exo_notification_play = 0x7f0800e1
com.example.allinone:attr/autoCompleteMode = 0x7f040046
com.example.allinone:dimen/m3_comp_outlined_card_container_elevation = 0x7f070175
com.example.allinone:attr/placeholderActivityName = 0x7f0403aa
com.example.allinone:id/TOP_START = 0x7f0a000d
com.example.allinone:attr/itemFillColor = 0x7f040272
com.example.allinone:style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton = 0x7f1401be
com.example.allinone:attr/collapsingToolbarLayoutLargeStyle = 0x7f0400fe
com.example.allinone:attr/percentX = 0x7f0403a6
com.example.allinone:id/videoThumbnail = 0x7f0a03be
com.example.allinone:attr/pathMotionArc = 0x7f0403a2
com.example.allinone:layout/fragment_wt_register = 0x7f0d005c
com.example.allinone:color/material_personalized__highlighted_text_inverse = 0x7f0602ba
com.example.allinone:id/offline_status_card = 0x7f0a0284
com.example.allinone:style/ThemeOverlay.Material3.Button.IconButton.Filled = 0x7f1402cc
com.example.allinone:color/material_personalized_color_control_activated = 0x7f0602bc
com.example.allinone:attr/passwordToggleTintMode = 0x7f0403a1
com.example.allinone:string/exo_track_resolution = 0x7f1300e5
com.example.allinone:attr/tabMode = 0x7f040476
com.example.allinone:style/ThemeOverlay.MaterialComponents.TimePicker = 0x7f140320
com.example.allinone:string/m3_ref_typeface_plain_medium = 0x7f130117
com.example.allinone:style/Widget.Material3.Search.ActionButton.Overflow = 0x7f140409
com.example.allinone:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x1 = 0x7f07021f
com.example.allinone:attr/boxBackgroundColor = 0x7f04008a
com.example.allinone:dimen/mtrl_calendar_action_height = 0x7f070297
com.example.allinone:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f07000b
com.example.allinone:attr/panelMenuListWidth = 0x7f04039c
com.example.allinone:id/voiceNoteCurrentTime = 0x7f0a03c9
com.example.allinone:attr/paddingStart = 0x7f040396
com.example.allinone:attr/layout_constraintGuide_end = 0x7f0402b3
com.example.allinone:id/liquidationPriceText = 0x7f0a01fb
com.example.allinone:color/m3_ref_palette_error30 = 0x7f06013a
com.example.allinone:id/antiClockwise = 0x7f0a006a
com.example.allinone:dimen/mtrl_calendar_year_width = 0x7f0702bf
com.example.allinone:attr/chipStandaloneStyle = 0x7f0400da
com.example.allinone:attr/colorOnPrimarySurface = 0x7f040117
com.example.allinone:style/Widget.Material3.ExtendedFloatingActionButton.Primary = 0x7f1403cb
com.example.allinone:color/cardview_shadow_end_color = 0x7f06003d
com.example.allinone:attr/windowActionBar = 0x7f040536
com.example.allinone:attr/springStiffness = 0x7f040436
com.example.allinone:attr/indicatorInset = 0x7f040267
com.example.allinone:attr/paddingRightSystemWindowInsets = 0x7f040395
com.example.allinone:animator/mtrl_extended_fab_state_list_animator = 0x7f02001d
com.example.allinone:attr/paddingTopSystemWindowInsets = 0x7f040399
com.example.allinone:color/dim_foreground_material_dark = 0x7f060080
com.example.allinone:macro/m3_comp_secondary_navigation_tab_focus_state_layer_color = 0x7f0e00fd
com.example.allinone:attr/tabPaddingBottom = 0x7f040478
com.example.allinone:string/error_file_not_found = 0x7f1300a8
com.example.allinone:style/Widget.Material3.FloatingActionButton.Small.Secondary = 0x7f1403d6
com.example.allinone:color/m3_checkbox_button_tint = 0x7f0600b0
com.example.allinone:style/Widget.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1403cc
com.example.allinone:color/material_personalized_color_surface_container_lowest = 0x7f0602de
com.example.allinone:drawable/tooltip_frame_dark = 0x7f0801bc
com.example.allinone:attr/onHide = 0x7f040389
com.example.allinone:macro/m3_comp_input_chip_label_text_type = 0x7f0e005c
com.example.allinone:id/backup_size = 0x7f0a007f
com.example.allinone:attr/onCross = 0x7f040388
com.example.allinone:attr/motionEasingStandardAccelerateInterpolator = 0x7f040365
com.example.allinone:attr/transitionFlags = 0x7f040516
com.example.allinone:dimen/m3_sys_motion_easing_linear_control_x2 = 0x7f070230
com.example.allinone:attr/waveDecay = 0x7f040530
com.example.allinone:attr/ifTagSet = 0x7f04025a
com.example.allinone:style/ThemeOverlay.Material3.TextInputEditText.FilledBox = 0x7f1402f8
com.example.allinone:macro/m3_comp_filled_tonal_icon_button_toggle_unselected_icon_color = 0x7f0e0056
com.example.allinone:attr/number = 0x7f040385
com.example.allinone:attr/boxCollapsedPaddingTop = 0x7f04008c
com.example.allinone:color/m3_dark_primary_text_disable_only = 0x7f0600ba
com.example.allinone:macro/m3_comp_search_bar_supporting_text_color = 0x7f0e00ee
com.example.allinone:style/TextAppearance.Design.HelperText = 0x7f140202
com.example.allinone:macro/m3_comp_badge_large_label_text_type = 0x7f0e0004
com.example.allinone:attr/navigationMode = 0x7f04037e
com.example.allinone:animator/mtrl_chip_state_list_anim = 0x7f020018
com.example.allinone:color/cardview_dark_background = 0x7f06003b
com.example.allinone:integer/mtrl_switch_track_viewport_width = 0x7f0b0042
com.example.allinone:color/m3_sys_color_dark_on_tertiary = 0x7f0601aa
com.example.allinone:attr/navigationContentDescription = 0x7f04037b
com.example.allinone:dimen/abc_seekbar_track_background_height_material = 0x7f070038
com.example.allinone:style/ShapeAppearanceOverlay.Material3.Button = 0x7f1401b1
com.example.allinone:attr/layout_constraintTag = 0x7f0402c5
com.example.allinone:attr/buttonPanelSideLayout = 0x7f0400a2
com.example.allinone:attr/navGraph = 0x7f04037a
com.example.allinone:macro/m3_comp_secondary_navigation_tab_active_indicator_color = 0x7f0e00fa
com.example.allinone:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f1400f4
com.example.allinone:attr/motionDurationMedium4 = 0x7f040357
com.example.allinone:id/invisible = 0x7f0a01db
com.example.allinone:id/fileName = 0x7f0a0181
com.example.allinone:attr/moveWhenScrollAtTop = 0x7f040378
com.example.allinone:color/common_google_signin_btn_text_light_default = 0x7f06004f
com.example.allinone:style/MaterialAlertDialog.MaterialComponents.Body.Text = 0x7f14015e
com.example.allinone:layout/item_category_summary = 0x7f0d0067
com.example.allinone:color/chip_background_selector = 0x7f060041
com.example.allinone:dimen/m3_comp_radio_button_unselected_hover_state_layer_opacity = 0x7f070190
com.example.allinone:attr/dividerColor = 0x7f040196
com.example.allinone:color/material_dynamic_neutral50 = 0x7f060269
com.example.allinone:color/bright_foreground_material_dark = 0x7f06002e
com.example.allinone:attr/motionTarget = 0x7f040375
com.example.allinone:id/layoutTyping = 0x7f0a01e9
com.example.allinone:macro/m3_comp_navigation_bar_active_hover_state_layer_color = 0x7f0e0064
com.example.allinone:drawable/ic_chevron_left = 0x7f08011a
com.example.allinone:string/mtrl_picker_announce_current_range_selection = 0x7f130187
com.example.allinone:attr/motionPathRotate = 0x7f040372
com.example.allinone:macro/m3_comp_switch_disabled_unselected_handle_color = 0x7f0e011c
com.example.allinone:attr/textAppearanceHeadline3 = 0x7f040496
com.example.allinone:macro/m3_comp_slider_label_label_text_color = 0x7f0e0112
com.example.allinone:attr/counterOverflowTextAppearance = 0x7f04016d
com.example.allinone:id/scale = 0x7f0a02f8
com.example.allinone:color/m3_ref_palette_dynamic_tertiary100 = 0x7f06012b
com.example.allinone:style/Base.TextAppearance.AppCompat.Inverse = 0x7f140020
com.example.allinone:dimen/abc_text_size_small_material = 0x7f07004c
com.example.allinone:attr/backgroundSplit = 0x7f040058
com.example.allinone:attr/listChoiceIndicatorMultipleAnimated = 0x7f0402eb
com.example.allinone:attr/colorPrimary = 0x7f040125
com.example.allinone:attr/motionEasingEmphasizedInterpolator = 0x7f040361
com.example.allinone:attr/motionDurationShort4 = 0x7f04035b
com.example.allinone:color/m3_ref_palette_dynamic_neutral92 = 0x7f0600f1
com.example.allinone:dimen/notification_right_icon_size = 0x7f070339
com.example.allinone:color/m3_sys_color_dynamic_dark_background = 0x7f0601bd
com.example.allinone:color/m3_ref_palette_neutral98 = 0x7f060159
com.example.allinone:style/Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f14009f
com.example.allinone:color/foreground_material_light = 0x7f060093
com.example.allinone:macro/m3_comp_radio_button_unselected_focus_state_layer_color = 0x7f0e00e0
com.example.allinone:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f1400c4
com.example.allinone:id/stretch = 0x7f0a0345
com.example.allinone:attr/motionDurationShort2 = 0x7f040359
com.example.allinone:dimen/mtrl_alert_dialog_background_inset_end = 0x7f07026c
com.example.allinone:attr/motionDurationMedium2 = 0x7f040355
com.example.allinone:color/design_default_color_secondary = 0x7f06006f
com.example.allinone:attr/motionDurationLong3 = 0x7f040352
com.example.allinone:color/m3_sys_color_dynamic_dark_error = 0x7f0601be
com.example.allinone:attr/splitMinWidth = 0x7f040430
com.example.allinone:attr/layout_anchor = 0x7f04029e
com.example.allinone:attr/mock_labelColor = 0x7f040348
com.example.allinone:attr/motionEasingEmphasizedAccelerateInterpolator = 0x7f04035f
com.example.allinone:bool/workmanager_test_configuration = 0x7f050006
com.example.allinone:attr/restoreState = 0x7f0403dc
com.example.allinone:attr/mock_label = 0x7f040346
com.example.allinone:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f1400fc
com.example.allinone:attr/minWidth = 0x7f040344
com.example.allinone:attr/layout_behavior = 0x7f0402a0
com.example.allinone:drawable/exo_ic_speed = 0x7f0800cb
com.example.allinone:dimen/m3_appbar_expanded_title_margin_horizontal = 0x7f0700ca
com.example.allinone:color/mtrl_textinput_hovered_box_stroke_color = 0x7f06032f
com.example.allinone:attr/minSeparation = 0x7f040342
com.example.allinone:attr/state_above_anchor = 0x7f040444
com.example.allinone:attr/closeIconSize = 0x7f0400f2
com.example.allinone:id/mtrl_calendar_days_of_week = 0x7f0a0238
com.example.allinone:color/m3_ref_palette_dynamic_tertiary90 = 0x7f060133
com.example.allinone:layout/fragment_futures_tab = 0x7f0d0054
com.example.allinone:id/password_toggle = 0x7f0a02a8
com.example.allinone:attr/motionInterpolator = 0x7f040370
com.example.allinone:attr/menuGravity = 0x7f04033d
com.example.allinone:style/Base.Widget.AppCompat.ListView = 0x7f1400ea
com.example.allinone:dimen/notification_small_icon_background_padding = 0x7f07033b
com.example.allinone:dimen/mtrl_extended_fab_translation_z_base = 0x7f0702d8
com.example.allinone:id/accessibility_custom_action_6 = 0x7f0a002d
com.example.allinone:attr/layout_constraintWidth_percent = 0x7f0402d0
com.example.allinone:style/ThemeOverlay.Material3.Dialog = 0x7f1402d7
com.example.allinone:attr/menuAlignmentMode = 0x7f04033c
com.example.allinone:string/registration_success = 0x7f1301f0
com.example.allinone:drawable/exo_controls_repeat_off = 0x7f0800b6
com.example.allinone:animator/mtrl_extended_fab_show_motion_spec = 0x7f02001c
com.example.allinone:attr/maxLines = 0x7f040336
com.example.allinone:dimen/m3_comp_radio_button_disabled_unselected_icon_opacity = 0x7f07018b
com.example.allinone:attr/flow_verticalStyle = 0x7f040216
com.example.allinone:attr/buttonBarPositiveButtonStyle = 0x7f04009a
com.example.allinone:attr/flow_padding = 0x7f040212
com.example.allinone:style/Widget.Material3.CompoundButton.MaterialSwitch = 0x7f1403c3
com.example.allinone:drawable/$mtrl_switch_thumb_checked_pressed__0 = 0x7f080021
com.example.allinone:color/m3_default_color_primary_text = 0x7f0600bb
com.example.allinone:attr/maxActionInlineWidth = 0x7f040331
com.example.allinone:attr/materialSwitchStyle = 0x7f04032b
com.example.allinone:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f14002e
com.example.allinone:drawable/$mtrl_checkbox_button_checked_unchecked__1 = 0x7f08000e
com.example.allinone:attr/dividerPadding = 0x7f04019a
com.example.allinone:id/taskNameInput = 0x7f0a0368
com.example.allinone:attr/maxAcceleration = 0x7f040330
com.example.allinone:attr/titleCollapseMode = 0x7f0404ea
com.example.allinone:string/m3c_bottom_sheet_collapse_description = 0x7f130124
com.example.allinone:attr/materialTimePickerTheme = 0x7f04032e
com.example.allinone:attr/materialTimePickerStyle = 0x7f04032d
com.example.allinone:color/material_personalized_color_primary_inverse = 0x7f0602d1
com.example.allinone:id/search_voice_btn = 0x7f0a0308
com.example.allinone:drawable/exo_icon_pause = 0x7f0800d3
com.example.allinone:attr/layout_constraintCircleRadius = 0x7f0402ae
com.example.allinone:color/exo_white = 0x7f06008e
com.example.allinone:string/delete_video_confirmation = 0x7f130089
com.example.allinone:attr/constraintSetEnd = 0x7f040146
com.example.allinone:integer/mtrl_calendar_year_selector_span = 0x7f0b0037
com.example.allinone:color/m3_ref_palette_neutral100 = 0x7f060145
com.example.allinone:drawable/abc_list_selector_background_transition_holo_dark = 0x7f080053
com.example.allinone:id/icon_only = 0x7f0a01ba
com.example.allinone:id/chains = 0x7f0a00ba
com.example.allinone:color/dialog_delete_button_color = 0x7f06007d
com.example.allinone:drawable/common_google_signin_btn_text_disabled = 0x7f08009e
com.example.allinone:attr/materialSearchViewStyle = 0x7f040328
com.example.allinone:id/decelerateAndComplete = 0x7f0a00f0
com.example.allinone:color/m3_sys_color_dynamic_on_tertiary_fixed_variant = 0x7f060206
com.example.allinone:integer/m3_card_anim_duration_ms = 0x7f0b0012
com.example.allinone:dimen/compat_button_padding_horizontal_material = 0x7f07005a
com.example.allinone:string/m3c_date_picker_headline = 0x7f130131
com.example.allinone:attr/collapsedTitleTextAppearance = 0x7f0400fb
com.example.allinone:macro/m3_comp_elevated_card_container_shape = 0x7f0e002b
com.example.allinone:id/lessonTimeLabel = 0x7f0a01ee
com.example.allinone:color/call_notification_answer_color = 0x7f060038
com.example.allinone:layout/design_bottom_sheet_dialog = 0x7f0d0023
com.example.allinone:attr/floatingActionButtonSurfaceStyle = 0x7f040203
com.example.allinone:dimen/design_bottom_navigation_icon_size = 0x7f070066
com.example.allinone:attr/materialIconButtonFilledTonalStyle = 0x7f040323
com.example.allinone:style/Widget.MaterialComponents.Snackbar = 0x7f14048a
com.example.allinone:string/pending_operations = 0x7f1301d7
com.example.allinone:macro/m3_comp_navigation_rail_inactive_label_text_color = 0x7f0e009d
com.example.allinone:drawable/ic_chevron_right = 0x7f08011b
com.example.allinone:styleable/include = 0x7f1500b2
com.example.allinone:attr/trackDecoration = 0x7f04050a
com.example.allinone:id/largestExpenseText = 0x7f0a01e5
com.example.allinone:attr/use_controller = 0x7f040524
com.example.allinone:styleable/SearchView = 0x7f15008c
com.example.allinone:drawable/ic_add_photo = 0x7f080101
com.example.allinone:color/m3_sys_color_light_on_surface_variant = 0x7f06021b
com.example.allinone:attr/backgroundTint = 0x7f04005a
com.example.allinone:attr/materialCardViewElevatedStyle = 0x7f040319
com.example.allinone:color/material_personalized_color_surface_dim = 0x7f0602df
com.example.allinone:color/m3_sys_color_dark_error_container = 0x7f06019d
com.example.allinone:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f140306
com.example.allinone:integer/m3_card_anim_delay_ms = 0x7f0b0011
com.example.allinone:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f1401e2
com.example.allinone:attr/chipStrokeColor = 0x7f0400dc
com.example.allinone:attr/tabIndicatorHeight = 0x7f040472
com.example.allinone:attr/materialCalendarMonth = 0x7f040314
com.example.allinone:attr/materialCalendarHeaderSelection = 0x7f040311
com.example.allinone:dimen/m3_comp_search_view_container_elevation = 0x7f070198
com.example.allinone:attr/itemShapeFillColor = 0x7f040280
com.example.allinone:style/ShapeAppearance.M3.Comp.NavigationRail.ActiveIndicator.Shape = 0x7f14018e
com.example.allinone:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__0 = 0x7f08001b
com.example.allinone:attr/quantizeMotionSteps = 0x7f0403c7
com.example.allinone:attr/dividerThickness = 0x7f04019b
com.example.allinone:string/take_photo = 0x7f130227
com.example.allinone:attr/materialCalendarFullscreenTheme = 0x7f04030c
com.example.allinone:dimen/material_filled_edittext_font_1_3_padding_bottom = 0x7f07025c
com.example.allinone:attr/maxWidth = 0x7f040339
com.example.allinone:attr/grid_validateInputs = 0x7f040235
com.example.allinone:dimen/design_navigation_item_vertical_padding = 0x7f07007c
com.example.allinone:string/abc_menu_sym_shortcut_label = 0x7f130010
com.example.allinone:attr/materialCalendarDayOfWeekLabel = 0x7f04030b
com.example.allinone:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day = 0x7f140317
com.example.allinone:id/cancel_action = 0x7f0a00a1
com.example.allinone:attr/contentInsetEnd = 0x7f04014d
com.example.allinone:attr/materialCalendarDay = 0x7f04030a
com.example.allinone:attr/materialButtonToggleGroupStyle = 0x7f040309
com.example.allinone:attr/buttonCompat = 0x7f04009c
com.example.allinone:drawable/m3_tabs_rounded_line_indicator = 0x7f080176
com.example.allinone:attr/deriveConstraintsFrom = 0x7f04018f
com.example.allinone:style/Widget.Material3.Chip.Assist = 0x7f1403ac
com.example.allinone:string/mark_incomplete = 0x7f13015e
com.example.allinone:color/m3_sys_color_dynamic_dark_secondary = 0x7f0601d2
com.example.allinone:attr/thumbIcon = 0x7f0404cf
com.example.allinone:string/remove_exercise = 0x7f1301f2
com.example.allinone:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f140036
com.example.allinone:attr/shapeAppearanceCornerMedium = 0x7f0403fe
com.example.allinone:attr/motionDurationExtraLong1 = 0x7f04034c
com.example.allinone:attr/materialAlertDialogButtonSpacerVisibility = 0x7f040302
com.example.allinone:layout/material_radial_view_group = 0x7f0d008b
com.example.allinone:attr/customBoolean = 0x7f040176
com.example.allinone:integer/m3_sys_motion_duration_short2 = 0x7f0b0021
com.example.allinone:color/ripple_material_light = 0x7f06034e
com.example.allinone:color/m3_textfield_label_color = 0x7f060247
com.example.allinone:attr/colorScheme = 0x7f04012d
com.example.allinone:color/cardStroke = 0x7f06003a
com.example.allinone:drawable/abc_seekbar_track_material = 0x7f080065
com.example.allinone:attr/materialAlertDialogBodyTextStyle = 0x7f040301
com.example.allinone:dimen/m3_comp_sheet_bottom_docked_drag_handle_height = 0x7f07019f
com.example.allinone:color/m3_ref_palette_primary20 = 0x7f06016b
com.example.allinone:attr/thumbStrokeColor = 0x7f0404d4
com.example.allinone:color/m3_sys_color_dynamic_light_secondary_container = 0x7f0601f5
com.example.allinone:style/Theme.MaterialComponents.Light = 0x7f14029d
com.example.allinone:string/character_counter_content_description = 0x7f130048
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout = 0x7f14046f
com.example.allinone:attr/chipSpacingHorizontal = 0x7f0400d8
com.example.allinone:layout/item_wt_event = 0x7f0d007b
com.example.allinone:attr/marginRightSystemWindowInsets = 0x7f0402ff
com.example.allinone:attr/startIconTintMode = 0x7f040442
com.example.allinone:attr/marginHorizontal = 0x7f0402fd
com.example.allinone:attr/circularflow_radiusInDP = 0x7f0400e6
com.example.allinone:macro/m3_comp_fab_surface_container_color = 0x7f0e003d
com.example.allinone:layout/item_chat_source = 0x7f0d0069
com.example.allinone:dimen/m3_comp_switch_selected_pressed_state_layer_opacity = 0x7f0701bb
com.example.allinone:attr/splitRatio = 0x7f040431
com.example.allinone:attr/customFloatValue = 0x7f04017a
com.example.allinone:attr/use_artwork = 0x7f040523
com.example.allinone:attr/listPopupWindowStyle = 0x7f0402f1
com.example.allinone:attr/hideOnScroll = 0x7f040244
com.example.allinone:attr/clearTop = 0x7f0400e8
com.example.allinone:attr/hoveredFocusedTranslationZ = 0x7f04024f
com.example.allinone:attr/flow_verticalAlign = 0x7f040213
com.example.allinone:color/m3_checkbox_button_icon_tint = 0x7f0600af
com.example.allinone:color/m3_sys_color_dynamic_light_surface_container_high = 0x7f0601f9
com.example.allinone:color/material_blue_grey_950 = 0x7f060256
com.example.allinone:string/edit_coming_soon = 0x7f130096
com.example.allinone:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f080046
com.example.allinone:attr/lineSpacing = 0x7f0402e8
com.example.allinone:color/material_personalized_primary_text_disable_only = 0x7f0602ec
com.example.allinone:color/design_dark_default_color_secondary_variant = 0x7f060063
com.example.allinone:dimen/m3_appbar_size_compact = 0x7f0700ce
com.example.allinone:color/m3_timepicker_display_ripple_color = 0x7f06024e
com.example.allinone:string/m3_sys_motion_easing_emphasized_accelerate = 0x7f13011a
com.example.allinone:attr/materialClockStyle = 0x7f04031e
com.example.allinone:attr/layout_scrollEffect = 0x7f0402df
com.example.allinone:drawable/mtrl_ic_check_mark = 0x7f080192
com.example.allinone:attr/layout_optimizationLevel = 0x7f0402de
com.example.allinone:attr/counterMaxLength = 0x7f04016c
com.example.allinone:style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f140071
com.example.allinone:attr/layout_insetEdge = 0x7f0402db
com.example.allinone:dimen/mtrl_navigation_item_horizontal_padding = 0x7f0702eb
com.example.allinone:id/action_bar_spinner = 0x7f0a0039
com.example.allinone:dimen/m3_extended_fab_icon_padding = 0x7f0701d6
com.example.allinone:attr/layout_goneMarginLeft = 0x7f0402d7
com.example.allinone:string/task_name = 0x7f13022f
com.example.allinone:id/search_close_btn = 0x7f0a0302
com.example.allinone:dimen/abc_dropdownitem_text_padding_left = 0x7f07002a
com.example.allinone:attr/layout_editor_absoluteY = 0x7f0402d3
com.example.allinone:id/liquidationPriceLabelText = 0x7f0a01fa
com.example.allinone:attr/colorTertiaryFixed = 0x7f040140
com.example.allinone:attr/destination = 0x7f040190
com.example.allinone:attr/constraint_referenced_ids = 0x7f040148
com.example.allinone:style/TextAppearance.AppCompat.Button = 0x7f1401c7
com.example.allinone:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f14023f
com.example.allinone:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
com.example.allinone:attr/toolbarNavigationButtonStyle = 0x7f0404f9
com.example.allinone:attr/layout_dodgeInsetEdges = 0x7f0402d1
com.example.allinone:attr/behavior_saveFlags = 0x7f04007c
com.example.allinone:style/Widget.MaterialComponents.Button.TextButton.Dialog.Icon = 0x7f14044a
com.example.allinone:attr/layout_constraintWidth_min = 0x7f0402cf
com.example.allinone:style/ShapeAppearance.Material3.Corner.ExtraLarge = 0x7f14019f
com.example.allinone:string/history = 0x7f13010a
com.example.allinone:attr/subtitleCentered = 0x7f040459
com.example.allinone:attr/layout_constraintWidth = 0x7f0402cc
com.example.allinone:color/m3_slider_thumb_color_legacy = 0x7f060198
com.example.allinone:attr/layout_constraintVertical_weight = 0x7f0402cb
com.example.allinone:id/buttonPanel = 0x7f0a009c
com.example.allinone:attr/layout_constraintVertical_chainStyle = 0x7f0402ca
com.example.allinone:dimen/mtrl_slider_label_square_side = 0x7f07030d
com.example.allinone:style/Base.TextAppearance.AppCompat.Menu = 0x7f140027
com.example.allinone:attr/layout_constraintTop_toTopOf = 0x7f0402c8
com.example.allinone:color/m3_ref_palette_neutral22 = 0x7f060149
com.example.allinone:attr/fontProviderCerts = 0x7f04021b
com.example.allinone:attr/colorSecondaryContainer = 0x7f04012f
com.example.allinone:attr/layout_constraintTop_toBottomOf = 0x7f0402c7
com.example.allinone:styleable/ClockHandView = 0x7f150025
com.example.allinone:attr/layout_constraintTop_creator = 0x7f0402c6
com.example.allinone:attr/tabMinWidth = 0x7f040475
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant30 = 0x7f0600ff
com.example.allinone:attr/action = 0x7f040002
com.example.allinone:layout/support_simple_spinner_dropdown_item = 0x7f0d00c9
com.example.allinone:style/Theme.AllinOne = 0x7f140241
com.example.allinone:drawable/exo_rounded_rectangle = 0x7f0800e6
com.example.allinone:attr/popEnterAnim = 0x7f0403b3
com.example.allinone:attr/colorSwitchThumbNormal = 0x7f04013d
com.example.allinone:style/Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge = 0x7f14028b
com.example.allinone:attr/badgeStyle = 0x7f040061
com.example.allinone:string/delete_video = 0x7f130088
com.example.allinone:attr/layout_constraintHorizontal_bias = 0x7f0402ba
com.example.allinone:color/m3_ref_palette_error90 = 0x7f060140
com.example.allinone:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f140256
com.example.allinone:attr/lastItemDecorated = 0x7f040298
com.example.allinone:string/exo_controls_hide = 0x7f1300c5
com.example.allinone:macro/m3_comp_outlined_text_field_focus_input_text_color = 0x7f0e00b9
com.example.allinone:id/image = 0x7f0a01be
com.example.allinone:attr/layout_constraintBaseline_creator = 0x7f0402a5
com.example.allinone:attr/layout_constraintHeight_max = 0x7f0402b7
com.example.allinone:color/material_dynamic_neutral80 = 0x7f06026c
com.example.allinone:attr/colorSurfaceContainerHighest = 0x7f040137
com.example.allinone:id/continuousVelocity = 0x7f0a00da
com.example.allinone:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
com.example.allinone:attr/windowSplashScreenAnimationDuration = 0x7f040541
com.example.allinone:anim/abc_slide_out_bottom = 0x7f010008
com.example.allinone:attr/layout_collapseMode = 0x7f0402a1
com.example.allinone:attr/colorSurfaceContainerLow = 0x7f040138
com.example.allinone:color/m3_sys_color_light_background = 0x7f06020d
com.example.allinone:attr/buttonIconDimen = 0x7f04009f
com.example.allinone:attr/launchSingleTop = 0x7f040299
com.example.allinone:color/material_dynamic_neutral100 = 0x7f060265
com.example.allinone:attr/enforceMaterialTheme = 0x7f0401c3
com.example.allinone:attr/layout_constraintStart_toEndOf = 0x7f0402c3
com.example.allinone:style/Base.TextAppearance.AppCompat.Small = 0x7f14002b
com.example.allinone:attr/drawableEndCompat = 0x7f0401a2
com.example.allinone:attr/lastBaselineToBottomHeight = 0x7f040297
com.example.allinone:attr/lStar = 0x7f040292
com.example.allinone:dimen/abc_dialog_corner_radius_material = 0x7f07001b
com.example.allinone:dimen/m3_carousel_small_item_size_max = 0x7f070116
com.example.allinone:color/error_light = 0x7f060086
com.example.allinone:layout/abc_list_menu_item_layout = 0x7f0d0010
com.example.allinone:attr/compatShadowEnabled = 0x7f040143
com.example.allinone:layout/m3_alert_dialog = 0x7f0d007f
com.example.allinone:attr/dialogPreferredPadding = 0x7f040192
com.example.allinone:attr/dialogTheme = 0x7f040193
com.example.allinone:attr/cardBackgroundColor = 0x7f0400a8
com.example.allinone:attr/buttonStyle = 0x7f0400a4
com.example.allinone:dimen/mtrl_snackbar_background_corner_radius = 0x7f070316
com.example.allinone:attr/textInputOutlinedDenseStyle = 0x7f0404bc
com.example.allinone:id/nameLayout = 0x7f0a0252
com.example.allinone:dimen/m3_comp_progress_indicator_track_thickness = 0x7f070189
com.example.allinone:macro/m3_comp_sheet_bottom_docked_drag_handle_color = 0x7f0e0106
com.example.allinone:attr/sliderStyle = 0x7f040425
com.example.allinone:attr/keep_content_on_player_reset = 0x7f04028e
com.example.allinone:attr/barrierAllowsGoneWidgets = 0x7f040070
com.example.allinone:id/fullscreenImageItem = 0x7f0a0198
com.example.allinone:attr/itemTextColor = 0x7f04028c
com.example.allinone:styleable/FloatingActionButton_Behavior_Layout = 0x7f15003a
com.example.allinone:attr/fabCustomSize = 0x7f0401ec
com.example.allinone:style/ExoStyledControls.Button.Bottom.Shuffle = 0x7f140140
com.example.allinone:id/exo_shutter = 0x7f0a0174
com.example.allinone:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x1 = 0x7f07021b
com.example.allinone:id/spline = 0x7f0a032b
com.example.allinone:color/m3_ref_palette_dynamic_secondary95 = 0x7f060127
com.example.allinone:macro/m3_comp_radio_button_selected_focus_state_layer_color = 0x7f0e00d9
com.example.allinone:id/pieChart = 0x7f0a02b3
com.example.allinone:attr/itemSpacing = 0x7f040285
com.example.allinone:color/m3_sys_color_dynamic_light_on_surface_variant = 0x7f0601ed
com.example.allinone:drawable/mtrl_tabs_default_indicator = 0x7f0801a6
com.example.allinone:attr/drawableStartCompat = 0x7f0401a6
com.example.allinone:attr/popupMenuBackground = 0x7f0403b8
com.example.allinone:string/grant_permission = 0x7f130102
com.example.allinone:attr/flow_lastHorizontalStyle = 0x7f04020e
com.example.allinone:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f140088
com.example.allinone:attr/colorOnSecondaryContainer = 0x7f040119
com.example.allinone:attr/itemPaddingTop = 0x7f04027c
com.example.allinone:attr/percentWidth = 0x7f0403a5
com.example.allinone:attr/itemPaddingBottom = 0x7f04027b
com.example.allinone:attr/materialCalendarStyle = 0x7f040316
com.example.allinone:layout/item_investment_dropdown = 0x7f0d0070
com.example.allinone:string/drawing_saved_to_gallery = 0x7f130093
com.example.allinone:color/design_default_color_on_error = 0x7f060068
com.example.allinone:anim/nav_default_enter_anim = 0x7f01002c
com.example.allinone:style/Widget.Material3.DrawerLayout = 0x7f1403c6
com.example.allinone:string/mtrl_picker_a11y_prev_month = 0x7f130186
com.example.allinone:attr/state_collapsible = 0x7f040446
com.example.allinone:drawable/exo_icon_fullscreen_exit = 0x7f0800d1
com.example.allinone:color/material_dynamic_neutral_variant100 = 0x7f060272
com.example.allinone:string/common_google_play_services_update_button = 0x7f130068
com.example.allinone:attr/insetForeground = 0x7f04026b
com.example.allinone:string/no_group = 0x7f1301bb
com.example.allinone:dimen/mtrl_slider_widget_height = 0x7f070314
com.example.allinone:dimen/material_clock_face_margin_top = 0x7f07024b
com.example.allinone:id/pnlText = 0x7f0a02b8
com.example.allinone:attr/indeterminateProgressStyle = 0x7f040263
com.example.allinone:string/task_added = 0x7f130229
com.example.allinone:drawable/$m3_avd_hide_password__0 = 0x7f080007
com.example.allinone:attr/boxStrokeWidth = 0x7f040093
com.example.allinone:color/m3_sys_color_dynamic_dark_on_tertiary_container = 0x7f0601cd
com.example.allinone:string/com.google.firebase.crashlytics.mapping_file_id = 0x7f13005d
com.example.allinone:id/exo_center_controls = 0x7f0a0151
com.example.allinone:attr/chipCornerRadius = 0x7f0400cd
com.example.allinone:attr/closeItemLayout = 0x7f0400f6
com.example.allinone:id/workout_program = 0x7f0a03da
com.example.allinone:attr/chainUseRtl = 0x7f0400bc
com.example.allinone:attr/allowStacking = 0x7f040032
com.example.allinone:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f140012
com.example.allinone:drawable/abc_btn_radio_material = 0x7f080032
com.example.allinone:attr/imageButtonStyle = 0x7f04025d
com.example.allinone:string/exo_controls_overflow_hide_description = 0x7f1300c7
com.example.allinone:attr/chipIconEnabled = 0x7f0400d1
com.example.allinone:attr/checkedChip = 0x7f0400c2
com.example.allinone:color/m3_sys_color_light_surface_container = 0x7f060226
com.example.allinone:string/call_notification_screening_text = 0x7f130043
com.example.allinone:attr/alpha = 0x7f040033
com.example.allinone:attr/iconTintMode = 0x7f040257
com.example.allinone:style/Widget.MaterialComponents.ChipGroup = 0x7f140455
com.example.allinone:dimen/m3_comp_filter_chip_with_icon_icon_size = 0x7f070154
com.example.allinone:style/Base.Widget.AppCompat.ButtonBar = 0x7f1400d6
com.example.allinone:attr/drawableTint = 0x7f0401a7
com.example.allinone:attr/elevationOverlayEnabled = 0x7f0401b8
com.example.allinone:dimen/mtrl_tooltip_cornerSize = 0x7f07032c
com.example.allinone:attr/collapsingToolbarLayoutStyle = 0x7f040101
com.example.allinone:attr/queryBackground = 0x7f0403c8
com.example.allinone:macro/m3_comp_secondary_navigation_tab_active_label_text_color = 0x7f0e00fb
com.example.allinone:dimen/m3_sys_motion_easing_standard_decelerate_control_y1 = 0x7f07023d
com.example.allinone:style/TextAppearance.AppCompat.Large.Inverse = 0x7f1401d0
com.example.allinone:style/MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f140161
com.example.allinone:attr/materialCalendarHeaderToggleButton = 0x7f040313
com.example.allinone:macro/m3_comp_time_picker_time_selector_label_text_type = 0x7f0e015f
com.example.allinone:attr/itemTextAppearanceActive = 0x7f040289
com.example.allinone:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f1401eb
com.example.allinone:macro/m3_comp_outlined_text_field_label_text_color = 0x7f0e00c2
com.example.allinone:dimen/mtrl_alert_dialog_background_inset_top = 0x7f07026e
com.example.allinone:dimen/mtrl_high_ripple_focused_alpha = 0x7f0702e0
com.example.allinone:attr/iconGravity = 0x7f040252
com.example.allinone:attr/icon = 0x7f040250
com.example.allinone:styleable/ConstraintLayout_ReactiveGuide = 0x7f15002c
com.example.allinone:drawable/ic_call_decline_low = 0x7f08010d
com.example.allinone:id/tag_transition_group = 0x7f0a0361
com.example.allinone:dimen/design_fab_image_size = 0x7f070072
com.example.allinone:id/exercise_name_input = 0x7f0a0143
com.example.allinone:drawable/ic_stats = 0x7f08015f
com.example.allinone:style/TextAppearance.Compat.Notification.Info = 0x7f1401f5
com.example.allinone:id/action_text = 0x7f0a004b
com.example.allinone:attr/argType = 0x7f040040
com.example.allinone:attr/targetId = 0x7f040484
com.example.allinone:id/left = 0x7f0a01ea
com.example.allinone:drawable/exo_controls_fullscreen_exit = 0x7f0800b0
com.example.allinone:string/brightness = 0x7f13003b
com.example.allinone:macro/m3_comp_navigation_rail_active_label_text_color = 0x7f0e0097
com.example.allinone:attr/switchStyle = 0x7f040465
com.example.allinone:dimen/abc_edit_text_inset_bottom_material = 0x7f07002c
com.example.allinone:attr/layout_constraintCircle = 0x7f0402ac
com.example.allinone:dimen/m3_appbar_expanded_title_margin_bottom = 0x7f0700c9
com.example.allinone:attr/actionBarTabBarStyle = 0x7f040009
com.example.allinone:color/exo_styled_error_message_background = 0x7f06008d
com.example.allinone:attr/checkedButton = 0x7f0400c1
com.example.allinone:id/title = 0x7f0a038a
com.example.allinone:id/ignore = 0x7f0a01bc
com.example.allinone:attr/helperTextTextAppearance = 0x7f04023e
com.example.allinone:dimen/highlight_alpha_material_colored = 0x7f0700b8
com.example.allinone:attr/materialAlertDialogTitleIconStyle = 0x7f040304
com.example.allinone:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f1400f6
com.example.allinone:attr/colorSurfaceBright = 0x7f040134
com.example.allinone:style/Platform.V25.AppCompat = 0x7f140172
com.example.allinone:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f1400d3
com.example.allinone:attr/grid_rowWeights = 0x7f040230
com.example.allinone:id/bulletListButton = 0x7f0a009b
com.example.allinone:attr/layout_constraintVertical_bias = 0x7f0402c9
com.example.allinone:style/Base.Widget.Material3.ExtendedFloatingActionButton = 0x7f140109
com.example.allinone:color/dim_foreground_disabled_material_dark = 0x7f06007e
com.example.allinone:attr/grid_columns = 0x7f04022d
com.example.allinone:attr/textAllCaps = 0x7f040489
com.example.allinone:style/Widget.MaterialComponents.Button.Icon = 0x7f140444
com.example.allinone:attr/trackStopIndicatorSize = 0x7f04050f
com.example.allinone:attr/drawerLayoutStyle = 0x7f0401ac
com.example.allinone:dimen/notification_media_narrow_margin = 0x7f070338
com.example.allinone:color/material_dynamic_secondary80 = 0x7f060293
com.example.allinone:dimen/m3_sys_motion_easing_legacy_decelerate_control_x2 = 0x7f07022c
com.example.allinone:attr/thumbHeight = 0x7f0404ce
com.example.allinone:style/Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f1402aa
com.example.allinone:attr/layout_constraintHeight_default = 0x7f0402b6
com.example.allinone:color/m3_sys_color_dark_primary_container = 0x7f0601af
com.example.allinone:attr/attributeName = 0x7f040044
com.example.allinone:attr/horizontalOffsetWithText = 0x7f04024e
com.example.allinone:color/abc_hint_foreground_material_dark = 0x7f060007
com.example.allinone:attr/fontWeight = 0x7f040223
com.example.allinone:style/Widget.AppCompat.ListView.Menu = 0x7f140361
com.example.allinone:macro/m3_comp_fab_primary_large_container_shape = 0x7f0e0039
com.example.allinone:attr/fontProviderPackage = 0x7f04021e
com.example.allinone:attr/autoSizeStepGranularity = 0x7f04004c
com.example.allinone:attr/contentPaddingEnd = 0x7f040155
com.example.allinone:attr/colorControlHighlight = 0x7f040108
com.example.allinone:string/m3c_bottom_sheet_expand_description = 0x7f130127
com.example.allinone:animator/m3_appbar_state_list_animator = 0x7f020009
com.example.allinone:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f140051
com.example.allinone:attr/switchTextAppearance = 0x7f040466
com.example.allinone:id/exo_extra_controls_scroll_view = 0x7f0a015a
com.example.allinone:attr/flow_verticalBias = 0x7f040214
com.example.allinone:id/exercises_recycler_view = 0x7f0a0149
com.example.allinone:attr/flow_lastHorizontalBias = 0x7f04020d
com.example.allinone:integer/m3_sys_shape_corner_medium_corner_family = 0x7f0b0029
com.example.allinone:macro/m3_comp_radio_button_disabled_selected_icon_color = 0x7f0e00d6
com.example.allinone:id/callButton = 0x7f0a009e
com.example.allinone:attr/boxStrokeErrorColor = 0x7f040092
com.example.allinone:dimen/mtrl_calendar_header_divider_thickness = 0x7f0702a5
com.example.allinone:attr/flow_horizontalGap = 0x7f04020b
com.example.allinone:id/open_search_view_background = 0x7f0a028e
com.example.allinone:attr/expanded = 0x7f0401d3
com.example.allinone:string/select_color = 0x7f130203
com.example.allinone:attr/textAppearanceCaption = 0x7f040490
com.example.allinone:attr/flow_firstVerticalStyle = 0x7f040208
com.example.allinone:attr/floatingActionButtonSecondaryStyle = 0x7f0401fc
com.example.allinone:color/material_dynamic_secondary60 = 0x7f060291
com.example.allinone:drawable/mtrl_dialog_background = 0x7f08018d
com.example.allinone:attr/textAppearanceTitleSmall = 0x7f0404ae
com.example.allinone:dimen/m3_btn_text_btn_icon_padding_right = 0x7f070104
com.example.allinone:id/snackbar_text = 0x7f0a0323
com.example.allinone:dimen/mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f0702b6
com.example.allinone:attr/floatingActionButtonTertiaryStyle = 0x7f040204
com.example.allinone:color/m3_dynamic_primary_text_disable_only = 0x7f0600c6
com.example.allinone:attr/show_subtitle_button = 0x7f040416
com.example.allinone:layout/design_text_input_end_icon = 0x7f0d002f
com.example.allinone:anim/linear_indeterminate_line1_head_interpolator = 0x7f01001d
com.example.allinone:animator/m3_card_elevated_state_list_anim = 0x7f02000c
com.example.allinone:layout/notification_template_lines_media = 0x7f0d00bd
com.example.allinone:attr/defaultMarginsEnabled = 0x7f040187
com.example.allinone:attr/circularflow_defaultRadius = 0x7f0400e5
com.example.allinone:dimen/m3_comp_assist_chip_elevated_container_elevation = 0x7f070120
com.example.allinone:attr/motionEffect_translationX = 0x7f04036d
com.example.allinone:attr/SharedValue = 0x7f040000
com.example.allinone:attr/customStringValue = 0x7f04017f
com.example.allinone:dimen/m3_comp_switch_disabled_selected_handle_opacity = 0x7f0701b4
com.example.allinone:dimen/exo_small_icon_padding_vertical = 0x7f0700a8
com.example.allinone:color/m3_ref_palette_neutral80 = 0x7f060152
com.example.allinone:attr/displayOptions = 0x7f040194
com.example.allinone:style/Base.V14.Theme.MaterialComponents.Light = 0x7f140098
com.example.allinone:id/message = 0x7f0a0229
com.example.allinone:attr/arrowHeadLength = 0x7f040041
com.example.allinone:dimen/design_navigation_elevation = 0x7f070077
com.example.allinone:attr/layout_constraintRight_creator = 0x7f0402c0
com.example.allinone:drawable/ic_error = 0x7f080128
com.example.allinone:attr/floatingActionButtonSmallStyle = 0x7f0401ff
com.example.allinone:attr/constraintSet = 0x7f040145
com.example.allinone:string/m3c_dropdown_menu_collapsed = 0x7f130149
com.example.allinone:attr/layout_constraintHorizontal_chainStyle = 0x7f0402bb
com.example.allinone:attr/floatingActionButtonSmallSecondaryStyle = 0x7f0401fe
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant87 = 0x7f060107
com.example.allinone:id/tooltipText = 0x7f0a0392
com.example.allinone:attr/layout_constraintHeight = 0x7f0402b5
com.example.allinone:attr/floatingActionButtonLargeSurfaceStyle = 0x7f0401f9
com.example.allinone:id/mostSpentCategoryText = 0x7f0a0234
com.example.allinone:style/TextAppearance.Material3.SearchBar = 0x7f140227
com.example.allinone:drawable/exo_ic_subtitle_on = 0x7f0800cd
com.example.allinone:attr/actionModeShareDrawable = 0x7f04001d
com.example.allinone:string/attachment_click_to_open = 0x7f130032
com.example.allinone:attr/floatingActionButtonLargePrimaryStyle = 0x7f0401f6
com.example.allinone:layout/dialog_student_details = 0x7f0d0044
com.example.allinone:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f1402b7
com.example.allinone:attr/chipStrokeWidth = 0x7f0400dd
com.example.allinone:attr/subtitleTextColor = 0x7f04045b
com.example.allinone:id/right_side = 0x7f0a02ec
com.example.allinone:color/highlighted_text_material_light = 0x7f06009b
com.example.allinone:id/closest = 0x7f0a00c8
com.example.allinone:dimen/mtrl_slider_label_radius = 0x7f07030c
com.example.allinone:attr/windowActionBarOverlay = 0x7f040537
com.example.allinone:style/Base.Theme.MaterialComponents.Dialog.Alert = 0x7f140069
com.example.allinone:id/bounceStart = 0x7f0a0092
com.example.allinone:attr/iconEndPadding = 0x7f040251
com.example.allinone:macro/m3_comp_filled_icon_button_toggle_unselected_icon_color = 0x7f0e004a
com.example.allinone:id/saveFab = 0x7f0a02f4
com.example.allinone:color/mtrl_textinput_focused_box_stroke_color = 0x7f06032e
com.example.allinone:attr/actionBarTabStyle = 0x7f04000a
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar.MonthTextView = 0x7f140477
com.example.allinone:attr/fabAnchorMode = 0x7f0401e7
com.example.allinone:attr/cardCornerRadius = 0x7f0400a9
com.example.allinone:attr/snackbarStyle = 0x7f040427
com.example.allinone:attr/extraMultilineHeightEnabled = 0x7f0401e4
com.example.allinone:string/m3c_bottom_sheet_dismiss_description = 0x7f130125
com.example.allinone:style/Base.V14.Theme.MaterialComponents.Dialog = 0x7f140096
com.example.allinone:id/dragDown = 0x7f0a010f
com.example.allinone:attr/textAppearanceBodyLarge = 0x7f04048c
com.example.allinone:drawable/ic_income = 0x7f08013a
com.example.allinone:attr/emojiCompatEnabled = 0x7f0401b9
com.example.allinone:style/Theme.Material3.Dark.Dialog = 0x7f140264
com.example.allinone:attr/textBackgroundRotate = 0x7f0404b2
com.example.allinone:attr/extendedFloatingActionButtonPrimaryStyle = 0x7f0401df
com.example.allinone:attr/extendMotionSpec = 0x7f0401dd
com.example.allinone:attr/transitionDisable = 0x7f040514
com.example.allinone:attr/textAppearanceListItemSecondary = 0x7f0404a3
com.example.allinone:style/TextAppearance.MaterialComponents.Caption = 0x7f140231
com.example.allinone:color/m3_ref_palette_neutral96 = 0x7f060158
com.example.allinone:attr/collapsingToolbarLayoutLargeSize = 0x7f0400fd
com.example.allinone:id/descriptionText = 0x7f0a00fa
com.example.allinone:style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f14011b
com.example.allinone:styleable/ExtendedFloatingActionButton_Behavior_Layout = 0x7f150038
com.example.allinone:attr/expandActivityOverflowButtonDrawable = 0x7f0401d2
com.example.allinone:style/Widget.MaterialComponents.Chip.Entry = 0x7f140453
com.example.allinone:attr/layout_constraintRight_toRightOf = 0x7f0402c2
com.example.allinone:color/abc_btn_colored_text_material = 0x7f060003
com.example.allinone:animator/m3_extended_fab_change_size_expand_motion_spec = 0x7f020011
com.example.allinone:drawable/abc_list_divider_mtrl_alpha = 0x7f08004e
com.example.allinone:attr/suffixTextColor = 0x7f04045f
com.example.allinone:attr/layout_goneMarginBaseline = 0x7f0402d4
com.example.allinone:attr/elevationOverlayAccentColor = 0x7f0401b6
com.example.allinone:style/Base.Theme.Material3.Dark = 0x7f140059
com.example.allinone:id/split_action_bar = 0x7f0a032c
com.example.allinone:dimen/design_fab_size_normal = 0x7f070074
com.example.allinone:color/m3_ref_palette_neutral12 = 0x7f060146
com.example.allinone:integer/m3_sys_motion_duration_long2 = 0x7f0b0019
com.example.allinone:attr/endIconMinSize = 0x7f0401be
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant60 = 0x7f060104
com.example.allinone:color/m3_elevated_chip_background_color = 0x7f0600c8
com.example.allinone:color/m3_tabs_ripple_color = 0x7f06023d
com.example.allinone:xml/file_paths = 0x7f160002
com.example.allinone:attr/itemStrokeColor = 0x7f040286
com.example.allinone:string/material_timepicker_pm = 0x7f130172
com.example.allinone:style/Widget.MaterialComponents.LinearProgressIndicator = 0x7f140462
com.example.allinone:dimen/design_bottom_sheet_modal_elevation = 0x7f07006e
com.example.allinone:id/exo_subtitle = 0x7f0a0176
com.example.allinone:drawable/mtrl_checkbox_button_icon_unchecked_checked = 0x7f08018a
com.example.allinone:color/design_fab_stroke_top_outer_color = 0x7f060079
com.example.allinone:style/Theme.Material3.Light = 0x7f140278
com.example.allinone:color/abc_primary_text_material_light = 0x7f06000c
com.example.allinone:macro/m3_comp_navigation_bar_active_indicator_color = 0x7f0e0066
com.example.allinone:attr/buttonBarNeutralButtonStyle = 0x7f040099
com.example.allinone:dimen/m3_comp_navigation_drawer_icon_size = 0x7f070166
com.example.allinone:color/m3_tabs_text_color_secondary = 0x7f060240
com.example.allinone:attr/ifTagNotSet = 0x7f040259
com.example.allinone:attr/endIconTintMode = 0x7f0401c2
com.example.allinone:string/material_slider_value = 0x7f13016d
com.example.allinone:dimen/m3_searchbar_elevation = 0x7f0701ff
com.example.allinone:layout/ime_base_split_test_activity = 0x7f0d0060
com.example.allinone:styleable/Tooltip = 0x7f1500a9
com.example.allinone:attr/endIconScaleType = 0x7f0401c0
com.example.allinone:layout/mtrl_layout_snackbar = 0x7f0d00a3
com.example.allinone:dimen/design_tab_text_size = 0x7f07008d
com.example.allinone:color/m3_sys_color_dark_primary = 0x7f0601ae
com.example.allinone:attr/endIconMode = 0x7f0401bf
com.example.allinone:color/mtrl_chip_surface_color = 0x7f060307
com.example.allinone:style/Theme.Material3.DayNight.NoActionBar = 0x7f140270
com.example.allinone:color/m3_timepicker_button_text_color = 0x7f06024b
com.example.allinone:attr/endIconCheckable = 0x7f0401bb
com.example.allinone:id/exo_check = 0x7f0a0152
com.example.allinone:dimen/notification_top_pad_large_text = 0x7f07033f
com.example.allinone:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f140175
com.example.allinone:attr/autoSizeMinTextSize = 0x7f04004a
com.example.allinone:dimen/design_bottom_navigation_shadow_height = 0x7f07006b
com.example.allinone:attr/singleChoiceItemLayout = 0x7f040421
com.example.allinone:attr/drawPath = 0x7f0401a0
com.example.allinone:color/m3_tabs_icon_color = 0x7f06023b
com.example.allinone:integer/design_tab_indicator_anim_duration_ms = 0x7f0b0009
com.example.allinone:color/m3_sys_color_dark_secondary_container = 0x7f0601b1
com.example.allinone:attr/show_buffering = 0x7f040410
com.example.allinone:attr/indicatorColor = 0x7f040264
com.example.allinone:layout/dialog_futures_tp_sl = 0x7f0d003c
com.example.allinone:attr/shapeAppearanceCornerExtraLarge = 0x7f0403fb
com.example.allinone:anim/abc_popup_enter = 0x7f010003
com.example.allinone:attr/textAppearanceTitleMedium = 0x7f0404ad
com.example.allinone:color/m3_navigation_bar_ripple_color_selector = 0x7f0600d2
com.example.allinone:drawable/notification_bg_low = 0x7f0801aa
com.example.allinone:attr/buttonSize = 0x7f0400a3
com.example.allinone:style/Animation.AppCompat.Tooltip = 0x7f140004
com.example.allinone:layout/mtrl_navigation_rail_item = 0x7f0d00a5
com.example.allinone:attr/carousel_emptyViewsBehavior = 0x7f0400b2
com.example.allinone:attr/liftOnScroll = 0x7f0402e3
com.example.allinone:color/m3_ref_palette_dynamic_tertiary70 = 0x7f060131
com.example.allinone:attr/buttonBarButtonStyle = 0x7f040097
com.example.allinone:id/edit_text_id = 0x7f0a0123
com.example.allinone:attr/editTextBackground = 0x7f0401b2
com.example.allinone:attr/indeterminateAnimationType = 0x7f040262
com.example.allinone:id/groupSpinner = 0x7f0a01a4
com.example.allinone:attr/colorOnSecondaryFixed = 0x7f04011a
com.example.allinone:attr/toolbarId = 0x7f0404f8
com.example.allinone:attr/duration = 0x7f0401b0
com.example.allinone:dimen/mtrl_badge_toolbar_action_menu_item_vertical_offset = 0x7f070276
com.example.allinone:attr/statusBarForeground = 0x7f04044e
com.example.allinone:style/Base.V28.Theme.AppCompat.Light = 0x7f1400bc
com.example.allinone:macro/m3_comp_outlined_card_pressed_outline_color = 0x7f0e00af
com.example.allinone:dimen/mtrl_switch_thumb_size = 0x7f07031e
com.example.allinone:styleable/Badge = 0x7f150017
com.example.allinone:id/nav_controller_view_tag = 0x7f0a0258
com.example.allinone:attr/nullable = 0x7f040384
com.example.allinone:dimen/m3_searchbar_margin_horizontal = 0x7f070201
com.example.allinone:layout/design_layout_tab_text = 0x7f0d0027
com.example.allinone:attr/navigationViewStyle = 0x7f040380
com.example.allinone:attr/onShow = 0x7f04038c
com.example.allinone:dimen/m3_alert_dialog_title_bottom_margin = 0x7f0700c8
com.example.allinone:styleable/ConstraintLayout_Layout = 0x7f15002b
com.example.allinone:color/m3_sys_color_light_surface_container_high = 0x7f060227
com.example.allinone:style/Widget.Material3.MaterialDivider.Heavy = 0x7f1403f7
com.example.allinone:attr/boxBackgroundMode = 0x7f04008b
com.example.allinone:attr/panelBackground = 0x7f04039a
com.example.allinone:color/m3_ref_palette_dynamic_neutral_variant6 = 0x7f060103
com.example.allinone:attr/drawableLeftCompat = 0x7f0401a3
com.example.allinone:attr/tooltipStyle = 0x7f0404fe
com.example.allinone:attr/floatingActionButtonLargeSecondaryStyle = 0x7f0401f7
com.example.allinone:attr/windowSplashScreenAnimatedIcon = 0x7f040540
com.example.allinone:attr/contentPaddingRight = 0x7f040157
com.example.allinone:dimen/task_indent_margin = 0x7f070348
com.example.allinone:attr/dragDirection = 0x7f04019d
com.example.allinone:dimen/mtrl_btn_max_width = 0x7f070289
com.example.allinone:id/search_plate = 0x7f0a0306
com.example.allinone:attr/tooltipFrameBackground = 0x7f0404fd
com.example.allinone:string/abc_searchview_description_clear = 0x7f130013
com.example.allinone:attr/dividerVertical = 0x7f04019c
com.example.allinone:attr/currentState = 0x7f040172
com.example.allinone:attr/layout_editor_absoluteX = 0x7f0402d2
com.example.allinone:style/MaterialAlertDialog.Material3.Animation = 0x7f140154
com.example.allinone:string/m3c_date_input_invalid_year_range = 0x7f13012d
com.example.allinone:dimen/m3_comp_fab_primary_pressed_state_layer_opacity = 0x7f070143
com.example.allinone:color/carousel_orange = 0x7f060040
com.example.allinone:id/item_touch_helper_previous_elevation = 0x7f0a01e1
com.example.allinone:id/glide_custom_view_target_tag = 0x7f0a01a0
com.example.allinone:drawable/abc_cab_background_internal_bg = 0x7f080038
com.example.allinone:color/m3_ref_palette_neutral50 = 0x7f06014e
com.example.allinone:dimen/mtrl_snackbar_action_text_color_alpha = 0x7f070315
com.example.allinone:anim/nav_default_pop_exit_anim = 0x7f01002f
com.example.allinone:attr/defaultDuration = 0x7f040186
com.example.allinone:string/amount = 0x7f130029
com.example.allinone:attr/layoutDuringTransition = 0x7f04029c
com.example.allinone:id/sliding_pane_detail_container = 0x7f0a0320
com.example.allinone:dimen/mtrl_calendar_days_of_week_height = 0x7f0702a1
com.example.allinone:dimen/m3_comp_navigation_bar_pressed_state_layer_opacity = 0x7f070162
com.example.allinone:layout/m3_alert_dialog_title = 0x7f0d0081
com.example.allinone:id/emptyState = 0x7f0a0129
com.example.allinone:dimen/m3_comp_radio_button_unselected_pressed_state_layer_opacity = 0x7f070191
com.example.allinone:attr/defaultState = 0x7f04018b
com.example.allinone:menu/search_notes = 0x7f0f0008
com.example.allinone:color/m3_sys_color_secondary_fixed = 0x7f060237
com.example.allinone:attr/grid_columnWeights = 0x7f04022c
com.example.allinone:attr/dayTodayStyle = 0x7f040185
com.example.allinone:string/mtrl_picker_date_header_unselected = 0x7f13018e
com.example.allinone:color/design_dark_default_color_surface = 0x7f060064
com.example.allinone:color/mtrl_choice_chip_ripple_color = 0x7f06030a
com.example.allinone:attr/colorPrimaryVariant = 0x7f04012c
com.example.allinone:attr/switchPadding = 0x7f040464
com.example.allinone:attr/textureEffect = 0x7f0404c7
com.example.allinone:attr/customPixelDimension = 0x7f04017d
com.example.allinone:attr/customNavigationLayout = 0x7f04017c
com.example.allinone:attr/backgroundInsetBottom = 0x7f040053
com.example.allinone:attr/floatingActionButtonLargeTertiaryStyle = 0x7f0401fa
com.example.allinone:style/Base.Theme.MaterialComponents.Light.DarkActionBar = 0x7f140070
com.example.allinone:styleable/LoadingImageView = 0x7f150054
com.example.allinone:color/material_dynamic_primary30 = 0x7f060281
com.example.allinone:color/material_dynamic_tertiary100 = 0x7f060299
com.example.allinone:attr/fontProviderFetchStrategy = 0x7f04021c
com.example.allinone:id/investmentCurrentValue = 0x7f0a01d0
com.example.allinone:id/auto = 0x7f0a0076
com.example.allinone:attr/badgeTextColor = 0x7f040064
com.example.allinone:color/background_color = 0x7f06001f
com.example.allinone:drawable/mtrl_ic_arrow_drop_up = 0x7f080190
com.example.allinone:attr/counterEnabled = 0x7f04016b
com.example.allinone:color/common_google_signin_btn_text_light = 0x7f06004e
com.example.allinone:attr/alertDialogButtonGroupStyle = 0x7f04002e
com.example.allinone:string/m3c_date_range_picker_start_headline = 0x7f130146
com.example.allinone:id/checkbox = 0x7f0a00bc
com.example.allinone:dimen/m3_comp_navigation_drawer_focus_state_layer_opacity = 0x7f070164
com.example.allinone:macro/m3_comp_filled_text_field_error_supporting_text_color = 0x7f0e004e
com.example.allinone:color/mtrl_error = 0x7f06030c
com.example.allinone:style/ShapeAppearance.M3.Comp.NavigationBar.ActiveIndicator.Shape = 0x7f14018b
com.example.allinone:attr/colorOnSurfaceVariant = 0x7f04011e
com.example.allinone:attr/itemMaxLines = 0x7f040278
com.example.allinone:drawable/abc_scrubber_track_mtrl_alpha = 0x7f080062
com.example.allinone:attr/fontProviderFetchTimeout = 0x7f04021d
com.example.allinone:color/navy_variant = 0x7f060335
com.example.allinone:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary = 0x7f1402dd
com.example.allinone:drawable/notification_oversize_large_icon_bg = 0x7f0801b0
com.example.allinone:attr/cornerRadius = 0x7f040165
com.example.allinone:id/exercise_weight_input = 0x7f0a0147
com.example.allinone:attr/snackbarButtonStyle = 0x7f040426
com.example.allinone:attr/coordinatorLayoutStyle = 0x7f04015e
com.example.allinone:style/Base.Widget.AppCompat.Button.Small = 0x7f1400d5
com.example.allinone:drawable/ic_file = 0x7f08012d
com.example.allinone:attr/windowNoTitle = 0x7f04053f
com.example.allinone:style/Widget.Material3.Snackbar.FullWidth = 0x7f140419
com.example.allinone:attr/checkedIconGravity = 0x7f0400c5
com.example.allinone:dimen/abc_list_item_height_large_material = 0x7f070030
com.example.allinone:attr/controlBackground = 0x7f04015c
com.example.allinone:drawable/mtrl_popupmenu_background_overlay = 0x7f080199
com.example.allinone:animator/mtrl_card_state_list_anim = 0x7f020017
com.example.allinone:attr/actionModeCloseButtonStyle = 0x7f040014
com.example.allinone:attr/springMass = 0x7f040435
com.example.allinone:id/themeSwitch = 0x7f0a0384
com.example.allinone:attr/materialThemeOverlay = 0x7f04032c
com.example.allinone:attr/spinBars = 0x7f04042a
com.example.allinone:animator/design_fab_hide_motion_spec = 0x7f020001
com.example.allinone:attr/contentPaddingStart = 0x7f040158
com.example.allinone:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
com.example.allinone:id/wrap = 0x7f0a03dc
com.example.allinone:id/mtrl_view_tag_bottom_padding = 0x7f0a024c
com.example.allinone:attr/layout_constraintEnd_toStartOf = 0x7f0402b1
com.example.allinone:color/mtrl_scrim_color = 0x7f060320
com.example.allinone:attr/logoDescription = 0x7f0402fb
com.example.allinone:attr/iconStartPadding = 0x7f040255
com.example.allinone:attr/textAppearanceDisplayLarge = 0x7f040491
com.example.allinone:attr/divider = 0x7f040195
com.example.allinone:id/endToStart = 0x7f0a0133
com.example.allinone:attr/cornerSizeBottomRight = 0x7f040168
com.example.allinone:style/TextAppearance.Material3.DisplaySmall = 0x7f14021f
com.example.allinone:attr/repeat_toggle_modes = 0x7f0403da
com.example.allinone:attr/editTextStyle = 0x7f0401b4
com.example.allinone:layout/dialog_add_student = 0x7f0d0033
com.example.allinone:drawable/$avd_show_password__0 = 0x7f080003
com.example.allinone:id/media_actions = 0x7f0a0226
com.example.allinone:attr/itemIconPadding = 0x7f040275
com.example.allinone:string/mtrl_picker_invalid_format_example = 0x7f130192
com.example.allinone:attr/actionOverflowMenuStyle = 0x7f040023
com.example.allinone:attr/layout_constraintDimensionRatio = 0x7f0402af
com.example.allinone:dimen/mtrl_progress_circular_inset_extra_small = 0x7f0702fa
com.example.allinone:animator/nav_default_pop_exit_anim = 0x7f020025
com.example.allinone:string/m3c_search_bar_search = 0x7f13014b
com.example.allinone:dimen/m3_alert_dialog_corner_size = 0x7f0700c4
com.example.allinone:attr/contentInsetEndWithActions = 0x7f04014e
com.example.allinone:attr/itemTextAppearanceInactive = 0x7f04028b
com.example.allinone:string/m3c_time_picker_hour_suffix = 0x7f130152
com.example.allinone:color/material_dynamic_secondary50 = 0x7f060290
com.example.allinone:macro/m3_comp_primary_navigation_tab_active_focus_state_layer_color = 0x7f0e00c7
com.example.allinone:id/escape = 0x7f0a013b
com.example.allinone:dimen/m3_comp_radio_button_selected_hover_state_layer_opacity = 0x7f07018d
com.example.allinone:attr/colorSecondaryFixed = 0x7f040130
com.example.allinone:attr/actionViewClass = 0x7f040026
com.example.allinone:style/Base.V21.Theme.MaterialComponents.Dialog = 0x7f1400a9
com.example.allinone:attr/colorTertiaryContainer = 0x7f04013f
com.example.allinone:string/app_id = 0x7f13002e
com.example.allinone:array/exo_controls_playback_speeds = 0x7f030000
com.example.allinone:styleable/KeyAttribute = 0x7f150047
com.example.allinone:style/Base.TextAppearance.AppCompat.Body2 = 0x7f140018
com.example.allinone:style/TextAppearance.Material3.LabelLarge = 0x7f140223
com.example.allinone:color/m3_sys_color_dynamic_light_inverse_surface = 0x7f0601e4
com.example.allinone:attr/actionModeStyle = 0x7f04001f
com.example.allinone:attr/collapsingToolbarLayoutMediumStyle = 0x7f040100
com.example.allinone:string/add_registration = 0x7f130022
com.example.allinone:style/Theme.Material3.DayNight.Dialog.MinWidth = 0x7f14026e
com.example.allinone:id/action_menu_presenter = 0x7f0a0046
com.example.allinone:attr/colorError = 0x7f04010a
com.example.allinone:macro/m3_comp_outlined_button_outline_color = 0x7f0e00a6
com.example.allinone:color/m3_sys_color_dark_surface_container_highest = 0x7f0601b6
com.example.allinone:style/Theme.SplashScreen.IconBackground = 0x7f1402b2
com.example.allinone:id/scrollView = 0x7f0a02fd
com.example.allinone:attr/behavior_autoHide = 0x7f040073
com.example.allinone:attr/chipStyle = 0x7f0400de
com.example.allinone:attr/itemHorizontalPadding = 0x7f040273
com.example.allinone:string/mtrl_checkbox_state_description_unchecked = 0x7f130181
com.example.allinone:layout/exo_styled_settings_list = 0x7f0d004f
com.example.allinone:id/report_drawn = 0x7f0a02e6
com.example.allinone:drawable/ic_format_text = 0x7f080134
com.example.allinone:color/material_personalized_color_secondary_container = 0x7f0602d5
com.example.allinone:drawable/exo_icon_play = 0x7f0800d4
com.example.allinone:attr/layout_goneMarginBottom = 0x7f0402d5
com.example.allinone:attr/bottomSheetDialogTheme = 0x7f040087
com.example.allinone:drawable/mtrl_ic_error = 0x7f080195
com.example.allinone:drawable/exo_styled_controls_next = 0x7f0800ec
com.example.allinone:attr/bottomSheetDragHandleStyle = 0x7f040088
com.example.allinone:attr/chipMinHeight = 0x7f0400d5
com.example.allinone:attr/colorPrimaryFixedDim = 0x7f040129
com.example.allinone:dimen/mtrl_calendar_day_corner = 0x7f07029b
com.example.allinone:attr/colorSurfaceContainerLowest = 0x7f040139
com.example.allinone:attr/colorPrimaryFixed = 0x7f040128
com.example.allinone:style/ThemeOverlay.Material3.Toolbar.Surface = 0x7f1402fc
com.example.allinone:attr/touch_target_height = 0x7f040504
com.example.allinone:id/voiceNoteCountText = 0x7f0a03c8
com.example.allinone:attr/toggleCheckedStateOnClick = 0x7f0404f7
com.example.allinone:attr/passwordToggleContentDescription = 0x7f04039d
com.example.allinone:string/m3c_time_picker_hour_text_field = 0x7f130153
com.example.allinone:attr/colorPrimaryContainer = 0x7f040126
com.example.allinone:animator/m3_card_state_list_anim = 0x7f02000d
com.example.allinone:attr/colorOutlineVariant = 0x7f040124
com.example.allinone:id/mtrl_picker_header_selection_text = 0x7f0a0245
com.example.allinone:id/customPanel = 0x7f0a00e2
com.example.allinone:attr/helperText = 0x7f04023c
com.example.allinone:color/design_bottom_navigation_shadow_color = 0x7f060056
com.example.allinone:color/material_on_primary_emphasis_high_type = 0x7f0602b3
com.example.allinone:attr/tintMode = 0x7f0404e6
com.example.allinone:attr/activityName = 0x7f04002a
com.example.allinone:style/Widget.Material3.Button.IconButton.Filled = 0x7f14039a
com.example.allinone:attr/round = 0x7f0403e0
com.example.allinone:id/parent_matrix = 0x7f0a02a7
com.example.allinone:attr/colorOutline = 0x7f040123
com.example.allinone:id/titleText = 0x7f0a038c
com.example.allinone:style/Widget.MaterialComponents.Chip.Action = 0x7f140451
com.example.allinone:attr/startIconTint = 0x7f040441
com.example.allinone:dimen/design_bottom_navigation_item_min_width = 0x7f070068
com.example.allinone:string/workout_timer = 0x7f13025b
com.example.allinone:macro/m3_comp_slider_label_container_color = 0x7f0e0111
com.example.allinone:styleable/KeyFrame = 0x7f150049
com.example.allinone:attr/actionDropDownStyle = 0x7f04000f
com.example.allinone:style/Widget.MaterialComponents.CheckedTextView = 0x7f140450
com.example.allinone:interpolator/mtrl_linear = 0x7f0c0010
com.example.allinone:color/material_personalized_color_secondary_text_inverse = 0x7f0602d7
com.example.allinone:style/Widget.MaterialComponents.CircularProgressIndicator.Small = 0x7f140459
com.example.allinone:attr/colorOnTertiary = 0x7f04011f
com.example.allinone:attr/colorOnError = 0x7f040111
com.example.allinone:style/Widget.Material3.Chip.Filter.Elevated = 0x7f1403af
com.example.allinone:color/m3_sys_color_dynamic_light_surface = 0x7f0601f6
com.example.allinone:color/material_personalized_hint_foreground = 0x7f0602e9
com.example.allinone:attr/colorSurfaceContainer = 0x7f040135
com.example.allinone:interpolator/m3_sys_motion_easing_emphasized = 0x7f0c0007
com.example.allinone:id/aligned = 0x7f0a005d
com.example.allinone:attr/barLength = 0x7f04006d
com.example.allinone:color/material_dynamic_secondary20 = 0x7f06028d
com.example.allinone:attr/colorOnSurfaceInverse = 0x7f04011d
com.example.allinone:attr/passwordToggleDrawable = 0x7f04039e
com.example.allinone:style/Base.TextAppearance.AppCompat.Body1 = 0x7f140017
com.example.allinone:color/m3_ref_palette_primary90 = 0x7f060172
com.example.allinone:attr/errorTextColor = 0x7f0401d0
com.example.allinone:style/Platform.V21.AppCompat.Light = 0x7f140171
com.example.allinone:style/Theme.MaterialComponents.Dialog.FixedSize = 0x7f140298
com.example.allinone:attr/finishSecondaryWithPrimary = 0x7f0401f4
com.example.allinone:dimen/m3_back_progress_bottom_container_max_scale_x_distance = 0x7f0700d1
com.example.allinone:color/good_orange = 0x7f060094
com.example.allinone:attr/actionBarPopupTheme = 0x7f040005
com.example.allinone:macro/m3_comp_search_bar_pressed_state_layer_color = 0x7f0e00ec
com.example.allinone:anim/m3_side_sheet_enter_from_left = 0x7f010025
com.example.allinone:id/exo_overlay = 0x7f0a0165
com.example.allinone:attr/onStateTransition = 0x7f04038d
com.example.allinone:color/accent_material_dark = 0x7f060019
com.example.allinone:style/Widget.MaterialComponents.MaterialCalendar.Year.Today = 0x7f14047a
com.example.allinone:id/tag_on_receive_content_listener = 0x7f0a035d
com.example.allinone:attr/data = 0x7f040180
com.example.allinone:color/bright_tab_selected = 0x7f060030
com.example.allinone:macro/m3_comp_outlined_text_field_focus_label_text_color = 0x7f0e00ba
com.example.allinone:layout/fragment_wt_lessons = 0x7f0d005b
com.example.allinone:attr/chipMinTouchTargetSize = 0x7f0400d6
com.example.allinone:string/switch_to_list_view = 0x7f130225
com.example.allinone:dimen/m3_comp_slider_inactive_track_height = 0x7f0701ac
com.example.allinone:dimen/mtrl_extended_fab_disabled_elevation = 0x7f0702cc
com.example.allinone:string/tasks = 0x7f130231
com.example.allinone:attr/guidelineUseRtl = 0x7f040237
com.example.allinone:attr/color = 0x7f040102
com.example.allinone:id/restore_button = 0x7f0a02e7
com.example.allinone:color/m3_sys_color_dynamic_dark_surface_container_high = 0x7f0601d7
com.example.allinone:attr/recyclerViewStyle = 0x7f0403d4
com.example.allinone:dimen/m3_btn_padding_left = 0x7f0700ff
com.example.allinone:string/enter_task_description = 0x7f13009f
com.example.allinone:attr/alertDialogCenterButtons = 0x7f04002f
com.example.allinone:id/postDate = 0x7f0a02c7
com.example.allinone:attr/checkedIconMargin = 0x7f0400c6
com.example.allinone:attr/itemMinHeight = 0x7f040279
com.example.allinone:style/ShapeAppearanceOverlay.Material3.Corner.Top = 0x7f1401b6
com.example.allinone:attr/searchIcon = 0x7f0403ef
com.example.allinone:attr/badgeWithTextShapeAppearance = 0x7f04006a
com.example.allinone:anim/linear_indeterminate_line2_head_interpolator = 0x7f01001f
com.example.allinone:attr/colorAccent = 0x7f040103
com.example.allinone:dimen/mtrl_progress_circular_size_medium = 0x7f070300
com.example.allinone:dimen/m3_side_sheet_standard_elevation = 0x7f07020c
com.example.allinone:string/error_deleting_registration = 0x7f1300a5
com.example.allinone:color/m3_ref_palette_tertiary40 = 0x7f060187
com.example.allinone:macro/m3_comp_dialog_headline_color = 0x7f0e0024
com.example.allinone:dimen/m3_searchbar_padding_start = 0x7f070204
com.example.allinone:style/Widget.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1403e7
com.example.allinone:attr/badgeHeight = 0x7f04005d
com.example.allinone:color/abc_secondary_text_material_dark = 0x7f060011
com.example.allinone:integer/abc_config_activityDefaultDur = 0x7f0b0000
com.example.allinone:id/axisRelative = 0x7f0a007c
com.example.allinone:attr/textAppearanceHeadline2 = 0x7f040495
com.example.allinone:macro/m3_comp_time_picker_period_selector_selected_container_color = 0x7f0e0155
com.example.allinone:attr/carousel_previousState = 0x7f0400b7
com.example.allinone:attr/placeholderTextAppearance = 0x7f0403ac
com.example.allinone:attr/closeIconEndPadding = 0x7f0400f1
com.example.allinone:attr/closeIconEnabled = 0x7f0400f0
com.example.allinone:dimen/material_clock_hand_center_dot_radius = 0x7f07024c
com.example.allinone:attr/clickAction = 0x7f0400ea
com.example.allinone:attr/badgeWithTextWidth = 0x7f04006c
com.example.allinone:attr/dialogCornerRadius = 0x7f040191
com.example.allinone:attr/colorOnErrorContainer = 0x7f040112
com.example.allinone:color/mtrl_calendar_item_stroke_color = 0x7f060301
com.example.allinone:attr/chipSurfaceColor = 0x7f0400df
com.example.allinone:attr/motionEffect_alpha = 0x7f040368
com.example.allinone:attr/addElevationShadow = 0x7f04002d
com.example.allinone:attr/floatingActionButtonSmallSurfaceStyle = 0x7f040200
com.example.allinone:attr/motionEasingLinearInterpolator = 0x7f040363
com.example.allinone:attr/tabSecondaryStyle = 0x7f04047d
com.example.allinone:attr/badgeShapeAppearanceOverlay = 0x7f040060
com.example.allinone:attr/behavior_skipCollapsed = 0x7f04007e
com.example.allinone:string/workout = 0x7f130250
com.example.allinone:attr/mock_showDiagonals = 0x7f040349
com.example.allinone:dimen/mtrl_textinput_box_label_cutout_padding = 0x7f070323
com.example.allinone:id/counterclockwise = 0x7f0a00dd
com.example.allinone:style/Widget.MaterialComponents.TimePicker.Clock = 0x7f14049f
com.example.allinone:drawable/ic_other_sign_in = 0x7f080151
com.example.allinone:attr/quantizeMotionInterpolator = 0x7f0403c5
com.example.allinone:drawable/bg_day_with_event = 0x7f08007c
com.example.allinone:animator/m3_btn_elevated_btn_state_list_anim = 0x7f02000a
com.example.allinone:attr/chipStartPadding = 0x7f0400db
com.example.allinone:color/green = 0x7f060098
com.example.allinone:id/leftToRight = 0x7f0a01eb
com.example.allinone:attr/fontProviderSystemFontFamily = 0x7f040220
com.example.allinone:string/tap_to_view_attachment = 0x7f130228
com.example.allinone:id/backup_card = 0x7f0a007d
com.example.allinone:style/ShapeAppearance.M3.Sys.Shape.Corner.Large = 0x7f14019b
com.example.allinone:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f14035a
com.example.allinone:dimen/mtrl_textinput_start_icon_margin_end = 0x7f070329
com.example.allinone:color/design_default_color_surface = 0x7f060071
com.example.allinone:attr/alwaysExpand = 0x7f040036
com.example.allinone:integer/m3_sys_motion_duration_extra_long4 = 0x7f0b0017
com.example.allinone:id/chain = 0x7f0a00b8
com.example.allinone:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f140028
com.example.allinone:attr/colorSurfaceDim = 0x7f04013a
com.example.allinone:color/switch_thumb_normal_material_dark = 0x7f06035a
com.example.allinone:color/design_fab_shadow_mid_color = 0x7f060074
com.example.allinone:dimen/mtrl_badge_horizontal_edge_offset = 0x7f070270
com.example.allinone:id/blocking = 0x7f0a008b
com.example.allinone:attr/layout_constraintEnd_toEndOf = 0x7f0402b0
com.example.allinone:attr/layoutManager = 0x7f04029d
com.example.allinone:macro/m3_comp_time_picker_clock_dial_selector_handle_container_color = 0x7f0e014d
com.example.allinone:attr/chipBackgroundColor = 0x7f0400cc
com.example.allinone:dimen/m3_comp_radio_button_selected_focus_state_layer_opacity = 0x7f07018c
com.example.allinone:style/Widget.Compat.NotificationActionText = 0x7f140377
com.example.allinone:color/primary_dark_material_light = 0x7f06033d
com.example.allinone:id/mtrl_calendar_year_selector_frame = 0x7f0a023e
com.example.allinone:macro/m3_comp_outlined_text_field_disabled_supporting_text_color = 0x7f0e00b5
com.example.allinone:attr/centerIfNoTextEnabled = 0x7f0400bb
com.example.allinone:id/notification_main_column_container = 0x7f0a0281
com.example.allinone:attr/colorButtonNormal = 0x7f040105
com.example.allinone:color/m3_ref_palette_error70 = 0x7f06013e
com.example.allinone:dimen/m3_comp_navigation_drawer_pressed_state_layer_opacity = 0x7f070168
com.example.allinone:dimen/m3_alert_dialog_elevation = 0x7f0700c5
com.example.allinone:color/m3_sys_color_dark_secondary = 0x7f0601b0
com.example.allinone:attr/colorOnTertiaryContainer = 0x7f040120
com.example.allinone:dimen/m3_comp_filled_card_focus_state_layer_opacity = 0x7f07014b
com.example.allinone:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f1400ce
com.example.allinone:attr/backgroundInsetStart = 0x7f040055
com.example.allinone:attr/contentPaddingLeft = 0x7f040156
com.example.allinone:id/design_menu_item_text = 0x7f0a00ff
com.example.allinone:style/Widget.Material3.TextInputLayout.OutlinedBox = 0x7f140426
com.example.allinone:dimen/m3_comp_navigation_rail_pressed_state_layer_opacity = 0x7f070171
com.example.allinone:dimen/mtrl_badge_long_text_horizontal_padding = 0x7f070271
com.example.allinone:attr/defaultScrollFlagsEnabled = 0x7f04018a
com.example.allinone:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f08005e
com.example.allinone:color/m3_icon_button_icon_color_selector = 0x7f0600cf
com.example.allinone:id/startTimeLayout = 0x7f0a033b
com.example.allinone:attr/checkedTextViewStyle = 0x7f0400cb
com.example.allinone:attr/borderlessButtonStyle = 0x7f040083
com.example.allinone:integer/mtrl_switch_thumb_motion_duration = 0x7f0b003b
com.example.allinone:color/reels_background = 0x7f060349
com.example.allinone:attr/blendSrc = 0x7f04007f
com.example.allinone:dimen/exo_styled_bottom_bar_height = 0x7f0700aa
com.example.allinone:drawable/$mtrl_checkbox_button_icon_unchecked_checked__2 = 0x7f08001a
com.example.allinone:color/m3_sys_color_dark_tertiary_container = 0x7f0601bc
com.example.allinone:attr/flow_lastVerticalBias = 0x7f04020f
com.example.allinone:attr/swipeRefreshLayoutProgressSpinnerBackgroundColor = 0x7f040462
com.example.allinone:attr/animateMenuItems = 0x7f040038
com.example.allinone:dimen/m3_comp_outlined_card_disabled_outline_opacity = 0x7f070176
com.example.allinone:styleable/MenuItem = 0x7f150068
com.example.allinone:style/TextAppearance.Material3.DisplayLarge = 0x7f14021d
com.example.allinone:color/m3_switch_thumb_tint = 0x7f060199
com.example.allinone:attr/textAppearanceBodyMedium = 0x7f04048d
com.example.allinone:attr/carousel_alignment = 0x7f0400b0
com.example.allinone:id/balanceText = 0x7f0a0082
com.example.allinone:color/material_personalized_color_on_tertiary = 0x7f0602cb
com.example.allinone:attr/buttonTintMode = 0x7f0400a7
com.example.allinone:attr/motionStagger = 0x7f040374
com.example.allinone:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__2 = 0x7f080017
com.example.allinone:layout/item_chat_user = 0x7f0d006a
com.example.allinone:color/material_dynamic_secondary99 = 0x7f060296
com.example.allinone:string/exo_track_selection_auto = 0x7f1300ea
com.example.allinone:attr/buttonIconTint = 0x7f0400a0
com.example.allinone:anim/abc_fade_in = 0x7f010000
com.example.allinone:attr/autoSizePresetSizes = 0x7f04004b
com.example.allinone:attr/circularflow_viewCenter = 0x7f0400e7
com.example.allinone:attr/colorNavigationItem = 0x7f04010c
com.example.allinone:drawable/exo_styled_controls_audiotrack = 0x7f0800e7
com.example.allinone:attr/boxStrokeWidthFocused = 0x7f040094
com.example.allinone:attr/cursorErrorColor = 0x7f040174
com.example.allinone:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f140176
com.example.allinone:attr/boxCornerRadiusTopEnd = 0x7f04008f
com.example.allinone:id/all = 0x7f0a005e
com.example.allinone:color/video_background = 0x7f060366
com.example.allinone:style/Platform.Widget.AppCompat.Spinner = 0x7f140174
com.example.allinone:id/visible_removing_fragment_view_tag = 0x7f0a03c7
com.example.allinone:attr/tickMark = 0x7f0404de
com.example.allinone:style/ThemeOverlay.Material3.DynamicColors.Light = 0x7f1402dc
com.example.allinone:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f070006
com.example.allinone:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f140180
com.example.allinone:id/addStudentFab = 0x7f0a0058
com.example.allinone:attr/buttonStyleSmall = 0x7f0400a5
com.example.allinone:string/switch_role = 0x7f130223
com.example.allinone:animator/mtrl_btn_state_list_anim = 0x7f020015
com.example.allinone:attr/cornerFamily = 0x7f040160
com.example.allinone:attr/contrast = 0x7f04015b
com.example.allinone:attr/labelStyle = 0x7f040294
com.example.allinone:attr/buttonIcon = 0x7f04009e
com.example.allinone:drawable/$mtrl_checkbox_button_unchecked_checked__1 = 0x7f08001f
com.example.allinone:animator/mtrl_fab_transformation_sheet_collapse_spec = 0x7f020020
com.example.allinone:styleable/MaterialTimePicker = 0x7f150065
com.example.allinone:color/material_dynamic_primary50 = 0x7f060283
com.example.allinone:attr/colorPrimaryDark = 0x7f040127
com.example.allinone:interpolator/m3_sys_motion_easing_emphasized_decelerate = 0x7f0c0009
com.example.allinone:attr/srcCompat = 0x7f040438
com.example.allinone:attr/colorOnTertiaryFixedVariant = 0x7f040122
com.example.allinone:dimen/m3_comp_menu_container_elevation = 0x7f07015a
com.example.allinone:style/Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall = 0x7f140457
com.example.allinone:attr/bottomAppBarStyle = 0x7f040084
com.example.allinone:id/homeAsUp = 0x7f0a01b3
com.example.allinone:attr/ad_marker_width = 0x7f04002c
com.example.allinone:attr/tabUnboundedRipple = 0x7f040483
com.example.allinone:style/Base.V24.Theme.Material3.Light = 0x7f1400b6
com.example.allinone:attr/subheaderTextAppearance = 0x7f040456
com.example.allinone:styleable/MaterialButtonToggleGroup = 0x7f150059
com.example.allinone:attr/chipIcon = 0x7f0400d0
com.example.allinone:drawable/mtrl_ic_indeterminate = 0x7f080196
com.example.allinone:attr/borderRoundPercent = 0x7f040081
com.example.allinone:string/project_id = 0x7f1301e5
com.example.allinone:string/camera_permission_title = 0x7f130045
com.example.allinone:attr/triggerId = 0x7f040519
com.example.allinone:color/m3_timepicker_button_background_color = 0x7f060249
com.example.allinone:layout/abc_screen_simple = 0x7f0d0015
com.example.allinone:attr/badgeWithTextHeight = 0x7f040068
com.example.allinone:attr/actionModePopupWindowStyle = 0x7f04001b
com.example.allinone:id/exo_prev = 0x7f0a016b
com.example.allinone:attr/brightness = 0x7f040095
com.example.allinone:attr/behavior_expandedOffset = 0x7f040076
com.example.allinone:id/pnlValueText = 0x7f0a02bb
com.example.allinone:attr/menu = 0x7f04033b
com.example.allinone:dimen/m3_comp_navigation_bar_icon_size = 0x7f070161
com.example.allinone:id/groups = 0x7f0a01ac
com.example.allinone:color/m3_ref_palette_tertiary30 = 0x7f060186
com.example.allinone:attr/thumbTint = 0x7f0404d7
com.example.allinone:attr/autoAdjustToWithinGrandparentBounds = 0x7f040045
com.example.allinone:attr/goIcon = 0x7f04022a
com.example.allinone:color/m3_sys_color_dynamic_secondary_fixed_dim = 0x7f06020a
com.example.allinone:id/exo_text = 0x7f0a0178
com.example.allinone:drawable/ic_lessons = 0x7f080142
com.example.allinone:color/m3_dark_default_color_primary_text = 0x7f0600b6
com.example.allinone:id/ltr = 0x7f0a0201
com.example.allinone:attr/titleTextStyle = 0x7f0404f6
com.example.allinone:attr/bottomInsetScrimEnabled = 0x7f040085
com.example.allinone:layout/m3_alert_dialog_actions = 0x7f0d0080
com.example.allinone:color/m3_ref_palette_error99 = 0x7f060142
com.example.allinone:attr/thumbStrokeWidth = 0x7f0404d5
com.example.allinone:attr/animateNavigationIcon = 0x7f040039
com.example.allinone:attr/motionDurationShort1 = 0x7f040358
com.example.allinone:macro/m3_comp_switch_selected_track_color = 0x7f0e012e
com.example.allinone:color/material_dynamic_neutral30 = 0x7f060267
com.example.allinone:drawable/notify_panel_notification_icon_bg = 0x7f0801b4
com.example.allinone:attr/titleMargins = 0x7f0404f1
com.example.allinone:macro/m3_comp_filled_autocomplete_text_field_input_text_type = 0x7f0e0042
com.example.allinone:attr/colorControlActivated = 0x7f040107
com.example.allinone:style/ShapeAppearance.M3.Sys.Shape.Corner.Full = 0x7f14019a
com.example.allinone:attr/errorIconTintMode = 0x7f0401cd
com.example.allinone:dimen/m3_sys_motion_easing_standard_accelerate_control_x1 = 0x7f070233
com.example.allinone:attr/ad_marker_color = 0x7f04002b
com.example.allinone:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f08003f
com.example.allinone:color/m3_ref_palette_tertiary10 = 0x7f060183
com.example.allinone:color/m3_sys_color_dark_on_surface_variant = 0x7f0601a9
com.example.allinone:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f140048
com.example.allinone:attr/backgroundInsetEnd = 0x7f040054
com.example.allinone:attr/background = 0x7f040051
com.example.allinone:anim/m3_motion_fade_enter = 0x7f010023
com.example.allinone:color/m3_ref_palette_dynamic_neutral98 = 0x7f0600f5
com.example.allinone:attr/activeIndicatorLabelPadding = 0x7f040027
com.example.allinone:color/abc_background_cache_hint_selector_material_light = 0x7f060001
com.example.allinone:color/material_personalized_color_primary = 0x7f0602cf
com.example.allinone:layout/item_history = 0x7f0d006e
com.example.allinone:attr/warmth = 0x7f04052f
com.example.allinone:string/google_storage_bucket = 0x7f130101
com.example.allinone:color/m3_sys_color_dynamic_on_tertiary_fixed = 0x7f060205
com.example.allinone:macro/m3_comp_navigation_bar_inactive_hover_state_layer_color = 0x7f0e0071
com.example.allinone:attr/labelVisibilityMode = 0x7f040295
com.example.allinone:attr/badgeGravity = 0x7f04005c
com.example.allinone:layout/abc_alert_dialog_material = 0x7f0d0009
com.example.allinone:id/dragUp = 0x7f0a0114
com.example.allinone:attr/cardPreventCornerOverlap = 0x7f0400ad
com.example.allinone:attr/state_collapsed = 0x7f040445
com.example.allinone:attr/panelMenuListTheme = 0x7f04039b
com.example.allinone:layout/item_note_video = 0x7f0d0076
com.example.allinone:attr/buttonTint = 0x7f0400a6
com.example.allinone:dimen/m3_card_hovered_z = 0x7f070110
com.example.allinone:drawable/ic_category_bills = 0x7f08010f
com.example.allinone:string/save_to_note = 0x7f1301f8
com.example.allinone:attr/autoTransition = 0x7f04004e
com.example.allinone:color/common_google_signin_btn_text_dark = 0x7f060049
com.example.allinone:id/accessibility_custom_action_27 = 0x7f0a0025
com.example.allinone:attr/startDestination = 0x7f04043b
com.example.allinone:macro/m3_comp_checkbox_selected_disabled_icon_color = 0x7f0e0008
com.example.allinone:attr/alertDialogTheme = 0x7f040031
com.example.allinone:dimen/abc_text_size_button_material = 0x7f070041
com.example.allinone:style/Widget.Material3.SearchBar = 0x7f14040b
com.example.allinone:dimen/exo_error_message_height = 0x7f070092
com.example.allinone:drawable/mtrl_switch_thumb_unchecked = 0x7f0801a1
com.example.allinone:drawable/error_image = 0x7f0800ad
com.example.allinone:attr/paddingLeftSystemWindowInsets = 0x7f040394
com.example.allinone:dimen/m3_sys_state_focus_state_layer_opacity = 0x7f070240
com.example.allinone:anim/mtrl_bottom_sheet_slide_out = 0x7f01002a
com.example.allinone:drawable/ic_investments = 0x7f08013e
com.example.allinone:attr/yearSelectedStyle = 0x7f040544
com.example.allinone:string/material_clock_toggle_content_description = 0x7f130160
com.example.allinone:animator/fragment_close_exit = 0x7f020004
com.example.allinone:animator/mtrl_fab_transformation_sheet_expand_spec = 0x7f020021
com.example.allinone:dimen/m3_bottom_nav_item_active_indicator_margin_horizontal = 0x7f0700e2
com.example.allinone:color/m3_sys_color_on_secondary_fixed_variant = 0x7f060232
com.example.allinone:dimen/material_clock_hand_padding = 0x7f07024d
com.example.allinone:attr/navigationIcon = 0x7f04037c
com.example.allinone:layout/item_note = 0x7f0d0074
com.example.allinone:attr/telltales_tailColor = 0x7f040486
com.example.allinone:string/default_error_message = 0x7f130076
com.example.allinone:dimen/mtrl_calendar_year_corner = 0x7f0702bb
com.example.allinone:id/disableHome = 0x7f0a0107
com.example.allinone:dimen/m3_comp_date_picker_modal_range_selection_header_container_height = 0x7f07012b
com.example.allinone:attr/actionMenuTextAppearance = 0x7f040011
com.example.allinone:attr/textPanY = 0x7f0404c4
com.example.allinone:color/material_dynamic_tertiary95 = 0x7f0602a2
com.example.allinone:color/m3_switch_track_tint = 0x7f06019a
com.example.allinone:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f1401d2
com.example.allinone:style/Base.Theme.AppCompat = 0x7f14004b
com.example.allinone:attr/colorOnPrimaryContainer = 0x7f040114
com.example.allinone:attr/imagePanX = 0x7f04025e
com.example.allinone:dimen/material_clock_period_toggle_height = 0x7f070250
com.example.allinone:attr/checkedIconSize = 0x7f0400c7
com.example.allinone:dimen/material_clock_display_height = 0x7f070247
com.example.allinone:id/program_name_text = 0x7f0a02d9
com.example.allinone:color/m3_chip_assist_text_color = 0x7f0600b1
com.example.allinone:macro/m3_comp_assist_chip_container_shape = 0x7f0e0000
com.example.allinone:dimen/mtrl_low_ripple_hovered_alpha = 0x7f0702e5
com.example.allinone:attr/fontVariationSettings = 0x7f040222
com.example.allinone:dimen/material_clock_period_toggle_width = 0x7f070253
com.example.allinone:attr/badgeShapeAppearance = 0x7f04005f
com.example.allinone:id/light = 0x7f0a01f3
com.example.allinone:animator/m3_elevated_chip_state_list_anim = 0x7f02000f
com.example.allinone:string/exercise_name = 0x7f1300be
com.example.allinone:color/m3_sys_color_dynamic_dark_on_error = 0x7f0601c4
com.example.allinone:anim/fragment_fast_out_extra_slow_in = 0x7f01001c
com.example.allinone:id/group_title_edit = 0x7f0a01a9
com.example.allinone:dimen/m3_card_elevation = 0x7f07010f
com.example.allinone:dimen/m3_sys_motion_easing_legacy_control_x1 = 0x7f070227
com.example.allinone:attr/textureWidth = 0x7f0404c9
com.example.allinone:color/m3_ref_palette_neutral40 = 0x7f06014d
com.example.allinone:attr/maxVelocity = 0x7f040338
com.example.allinone:color/light_blue = 0x7f06009f
com.example.allinone:styleable/RadialViewGroup = 0x7f150085
com.example.allinone:animator/design_appbar_state_list_animator = 0x7f020000
com.example.allinone:dimen/design_snackbar_padding_vertical_2lines = 0x7f070089
com.example.allinone:string/m3c_time_picker_hour_24h_suffix = 0x7f130150
com.example.allinone:attr/actionBarWidgetTheme = 0x7f04000d
com.example.allinone:attr/expandedTitleMarginEnd = 0x7f0401d8
com.example.allinone:string/material_timepicker_minute = 0x7f130171
com.example.allinone:style/Widget.Material3.CircularProgressIndicator.Medium = 0x7f1403bd
com.example.allinone:dimen/mtrl_btn_text_btn_padding_right = 0x7f070293
com.example.allinone:attr/actionBarTheme = 0x7f04000c
com.example.allinone:macro/m3_comp_date_picker_modal_range_selection_month_subhead_color = 0x7f0e001b
com.example.allinone:dimen/mtrl_extended_fab_start_padding_icon = 0x7f0702d6
com.example.allinone:attr/thumbIconTint = 0x7f0404d1
com.example.allinone:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
com.example.allinone:attr/materialCalendarHeaderDivider = 0x7f04030f
com.example.allinone:attr/badgeWithTextShapeAppearanceOverlay = 0x7f04006b
com.example.allinone:drawable/notification_bg_normal_pressed = 0x7f0801ae
com.example.allinone:drawable/ic_cleardata = 0x7f08011e
com.example.allinone:attr/badgeText = 0x7f040062
com.example.allinone:attr/animationMode = 0x7f04003b
com.example.allinone:attr/shutter_background_color = 0x7f04041a
com.example.allinone:anim/m3_side_sheet_exit_to_left = 0x7f010027
com.example.allinone:attr/contentScrim = 0x7f04015a
com.example.allinone:color/material_personalized_color_text_secondary_and_tertiary_inverse = 0x7f0602e7
com.example.allinone:layout/design_layout_tab_icon = 0x7f0d0026
com.example.allinone:attr/colorTertiaryFixedDim = 0x7f040141
com.example.allinone:id/noLessonsText = 0x7f0a0274
com.example.allinone:drawable/ic_call = 0x7f080107
com.example.allinone:attr/grid_verticalGaps = 0x7f040236
com.example.allinone:id/amount = 0x7f0a0061
com.example.allinone:drawable/bg_tag_red = 0x7f080083
com.example.allinone:style/Widget.Material3.MaterialCalendar.Day.Today = 0x7f1403e3
com.example.allinone:style/ThemeOverlay.Material3.FloatingActionButton.Surface = 0x7f1402e3
com.example.allinone:attr/percentHeight = 0x7f0403a4
com.example.allinone:attr/grid_rows = 0x7f040231
com.example.allinone:attr/listPreferredItemHeightLarge = 0x7f0402f3
com.example.allinone:color/m3_ref_palette_dynamic_neutral22 = 0x7f0600e5
com.example.allinone:attr/startIconMinSize = 0x7f04043f
com.example.allinone:color/design_fab_shadow_start_color = 0x7f060075
com.example.allinone:attr/backgroundInsetTop = 0x7f040056
com.example.allinone:style/Base.Widget.Material3.FloatingActionButton = 0x7f14010b
com.example.allinone:color/m3_sys_color_on_secondary_fixed = 0x7f060231
com.example.allinone:attr/exitAnim = 0x7f0401d1
com.example.allinone:attr/chipIconSize = 0x7f0400d2
com.example.allinone:id/nav_database_management = 0x7f0a0259
com.example.allinone:attr/prefixText = 0x7f0403bd
com.example.allinone:style/Widget.Material3.CardView.Elevated = 0x7f1403a8
com.example.allinone:attr/autoShowKeyboard = 0x7f040048
com.example.allinone:style/Widget.Material3.BottomAppBar.Legacy = 0x7f14038e
com.example.allinone:attr/tickColorActive = 0x7f0404dc
com.example.allinone:style/TextAppearance.Compat.Notification.Title.Media = 0x7f1401fd
com.example.allinone:attr/collapsedTitleGravity = 0x7f0400fa
com.example.allinone:id/investmentAmount = 0x7f0a01ce
com.example.allinone:dimen/m3_comp_slider_active_handle_width = 0x7f0701a8
com.example.allinone:dimen/design_snackbar_padding_horizontal = 0x7f070087
com.example.allinone:string/m3_sys_motion_easing_linear = 0x7f130120
com.example.allinone:id/navigation_bar_item_labels_group = 0x7f0a026a
com.example.allinone:color/m3_sys_color_light_error_container = 0x7f06020f
com.example.allinone:style/Platform.AppCompat.Light = 0x7f140168
com.example.allinone:attr/applyMotionScene = 0x7f04003e
com.example.allinone:dimen/m3_comp_navigation_rail_active_indicator_width = 0x7f07016b
com.example.allinone:dimen/abc_dropdownitem_text_padding_right = 0x7f07002b
com.example.allinone:anim/m3_side_sheet_enter_from_right = 0x7f010026
com.example.allinone:string/exo_download_paused_for_wifi = 0x7f1300e0
com.example.allinone:attr/tickRadiusInactive = 0x7f0404e2
com.example.allinone:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
com.example.allinone:color/m3_sys_color_light_on_secondary = 0x7f060218
com.example.allinone:anim/mtrl_card_lowers_interpolator = 0x7f01002b
com.example.allinone:anim/nav_default_exit_anim = 0x7f01002d
com.example.allinone:dimen/abc_star_small = 0x7f07003d
com.example.allinone:attr/motionDurationLong4 = 0x7f040353
com.example.allinone:color/m3_ref_palette_dynamic_neutral100 = 0x7f0600e1
com.example.allinone:attr/mock_labelBackgroundColor = 0x7f040347
com.example.allinone:dimen/m3_comp_navigation_rail_hover_state_layer_opacity = 0x7f07016f
com.example.allinone:macro/m3_comp_top_app_bar_medium_headline_color = 0x7f0e016d
com.example.allinone:color/switch_thumb_material_light = 0x7f060359
com.example.allinone:attr/itemBackground = 0x7f040271
com.example.allinone:macro/m3_comp_progress_indicator_track_color = 0x7f0e00d5
com.example.allinone:drawable/material_ic_keyboard_arrow_left_black_24dp = 0x7f08017c
com.example.allinone:color/material_harmonized_color_error = 0x7f0602ab
com.example.allinone:attr/counterTextColor = 0x7f040170
com.example.allinone:string/default_popup_window_title = 0x7f130077
com.example.allinone:drawable/abc_btn_radio_material_anim = 0x7f080033
com.example.allinone:color/material_dynamic_neutral_variant10 = 0x7f060271
com.example.allinone:color/mtrl_tabs_colored_ripple_color = 0x7f060325
com.example.allinone:color/m3_ref_palette_dynamic_secondary90 = 0x7f060126
com.example.allinone:dimen/m3_comp_navigation_rail_container_elevation = 0x7f07016c
com.example.allinone:dimen/design_bottom_navigation_height = 0x7f070065
com.example.allinone:style/ShapeAppearanceOverlay.MaterialComponents.Chip = 0x7f1401bd
com.example.allinone:attr/alertDialogStyle = 0x7f040030
com.example.allinone:style/Widget.MaterialComponents.NavigationRailView.Colored.Compact = 0x7f14047f
com.example.allinone:attr/indicatorTrackGapSize = 0x7f040269
com.example.allinone:animator/mtrl_fab_show_motion_spec = 0x7f02001f
com.example.allinone:attr/materialSearchViewToolbarStyle = 0x7f04032a
com.example.allinone:animator/fragment_fade_exit = 0x7f020006
com.example.allinone:color/m3_sys_color_light_surface_dim = 0x7f06022b
com.example.allinone:dimen/m3_comp_switch_selected_focus_state_layer_opacity = 0x7f0701b9
com.example.allinone:id/exo_overflow_show = 0x7f0a0164
com.example.allinone:attr/default_artwork = 0x7f04018c
com.example.allinone:id/categoryIcon = 0x7f0a00ab
com.example.allinone:drawable/navigation_empty_icon = 0x7f0801a7
com.example.allinone:style/Base.Theme.AppCompat.Dialog = 0x7f14004d
com.example.allinone:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
com.example.allinone:attr/unplayed_color = 0x7f04051d
com.example.allinone:id/west = 0x7f0a03ce
com.example.allinone:attr/fabAlignmentModeEndMargin = 0x7f0401e6
com.example.allinone:string/m3c_date_picker_year_picker_pane_title = 0x7f13013f
com.example.allinone:dimen/exo_icon_size = 0x7f07009a
com.example.allinone:attr/viewTransitionMode = 0x7f040529
com.example.allinone:attr/tooltipForegroundColor = 0x7f0404fc
