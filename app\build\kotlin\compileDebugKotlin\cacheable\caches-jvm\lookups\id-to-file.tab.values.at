/ Header Record For PersistentHashMapValueStorage> =app/src/main/java/com/example/allinone/AllinOneApplication.kt7 6app/src/main/java/com/example/allinone/MainActivity.ktI Happ/src/main/java/com/example/allinone/adapters/BinanceFuturesAdapter.ktJ Iapp/src/main/java/com/example/allinone/adapters/BinancePositionAdapter.ktK Japp/src/main/java/com/example/allinone/adapters/CategoryDropdownAdapter.ktK Japp/src/main/java/com/example/allinone/adapters/CategorySpendingAdapter.ktJ Iapp/src/main/java/com/example/allinone/adapters/CategorySummaryAdapter.kt@ ?app/src/main/java/com/example/allinone/adapters/EventAdapter.ktJ Iapp/src/main/java/com/example/allinone/adapters/FullscreenImageAdapter.ktG Fapp/src/main/java/com/example/allinone/adapters/GroupedTasksAdapter.ktB Aapp/src/main/java/com/example/allinone/adapters/HistoryAdapter.ktE Dapp/src/main/java/com/example/allinone/adapters/InvestmentAdapter.ktM Lapp/src/main/java/com/example/allinone/adapters/InvestmentDropdownAdapter.ktJ Iapp/src/main/java/com/example/allinone/adapters/InvestmentImageAdapter.ktJ Iapp/src/main/java/com/example/allinone/adapters/InvestmentPagerAdapter.ktN Mapp/src/main/java/com/example/allinone/adapters/InvestmentSelectionAdapter.ktC Bapp/src/main/java/com/example/allinone/adapters/LogEntryAdapter.ktD Capp/src/main/java/com/example/allinone/adapters/NoteImageAdapter.ktD Capp/src/main/java/com/example/allinone/adapters/NoteVideoAdapter.kt@ ?app/src/main/java/com/example/allinone/adapters/NotesAdapter.ktB Aapp/src/main/java/com/example/allinone/adapters/SeminarAdapter.kt@ ?app/src/main/java/com/example/allinone/adapters/TasksAdapter.ktF Eapp/src/main/java/com/example/allinone/adapters/TransactionAdapter.ktL Kapp/src/main/java/com/example/allinone/adapters/TransactionReportAdapter.ktD Capp/src/main/java/com/example/allinone/adapters/VoiceNoteAdapter.ktB Aapp/src/main/java/com/example/allinone/adapters/WTEventAdapter.ktI Happ/src/main/java/com/example/allinone/adapters/WTRegistrationAdapter.ktD Capp/src/main/java/com/example/allinone/adapters/WTStudentAdapter.ktE Dapp/src/main/java/com/example/allinone/api/BinanceExternalService.ktE Dapp/src/main/java/com/example/allinone/api/BinanceWebSocketClient.kt@ ?app/src/main/java/com/example/allinone/api/ExternalApiModels.ktG Fapp/src/main/java/com/example/allinone/api/ExternalBinanceApiClient.ktH Gapp/src/main/java/com/example/allinone/api/ExternalBinanceRepository.kt@ ?app/src/main/java/com/example/allinone/backup/BackupActivity.kt? >app/src/main/java/com/example/allinone/backup/BackupAdapter.kt= <app/src/main/java/com/example/allinone/cache/CacheManager.kt> =app/src/main/java/com/example/allinone/config/MuscleGroups.ktG Fapp/src/main/java/com/example/allinone/config/TransactionCategories.ktJ Iapp/src/main/java/com/example/allinone/core/data/datasource/DataSource.ktN Mapp/src/main/java/com/example/allinone/core/data/repository/BaseRepository.kt> =app/src/main/java/com/example/allinone/data/BinanceBalance.kt= <app/src/main/java/com/example/allinone/data/BinanceFuture.kt> =app/src/main/java/com/example/allinone/data/BinanceFutures.kt< ;app/src/main/java/com/example/allinone/data/BinanceOrder.kt? >app/src/main/java/com/example/allinone/data/BinancePosition.kt? >app/src/main/java/com/example/allinone/data/CategorySummary.kt5 4app/src/main/java/com/example/allinone/data/Event.kt8 7app/src/main/java/com/example/allinone/data/Exercise.kt; :app/src/main/java/com/example/allinone/data/HistoryItem.kt: 9app/src/main/java/com/example/allinone/data/Investment.kt4 3app/src/main/java/com/example/allinone/data/Note.kt7 6app/src/main/java/com/example/allinone/data/Program.kt4 3app/src/main/java/com/example/allinone/data/Task.kt9 8app/src/main/java/com/example/allinone/data/TaskGroup.kt; :app/src/main/java/com/example/allinone/data/Transaction.kt9 8app/src/main/java/com/example/allinone/data/VoiceNote.kt8 7app/src/main/java/com/example/allinone/data/WTLesson.kt> =app/src/main/java/com/example/allinone/data/WTRegistration.kt9 8app/src/main/java/com/example/allinone/data/WTSeminar.kt9 8app/src/main/java/com/example/allinone/data/WTStudent.kt7 6app/src/main/java/com/example/allinone/data/Workout.ktD Capp/src/main/java/com/example/allinone/data/common/BaseViewModel.kt> =app/src/main/java/com/example/allinone/data/common/UiState.ktA @app/src/main/java/com/example/allinone/data/local/AppDatabase.ktF Eapp/src/main/java/com/example/allinone/data/local/RoomCacheManager.ktG Fapp/src/main/java/com/example/allinone/data/local/dao/CachedNoteDao.ktJ Iapp/src/main/java/com/example/allinone/data/local/dao/CachedProgramDao.ktN Mapp/src/main/java/com/example/allinone/data/local/dao/CachedTransactionDao.ktL Kapp/src/main/java/com/example/allinone/data/local/dao/CachedWTStudentDao.ktJ Iapp/src/main/java/com/example/allinone/data/local/dao/CachedWorkoutDao.ktU Tapp/src/main/java/com/example/allinone/data/local/entities/CachedInvestmentEntity.ktO Napp/src/main/java/com/example/allinone/data/local/entities/CachedNoteEntity.ktR Qapp/src/main/java/com/example/allinone/data/local/entities/CachedProgramEntity.ktV Uapp/src/main/java/com/example/allinone/data/local/entities/CachedTransactionEntity.ktT Sapp/src/main/java/com/example/allinone/data/local/entities/CachedWTStudentEntity.ktR Qapp/src/main/java/com/example/allinone/data/local/entities/CachedWorkoutEntity.kt7 6app/src/main/java/com/example/allinone/di/AppModule.ktX Wapp/src/main/java/com/example/allinone/feature/instagram/data/api/InstagramApiClient.ktY Xapp/src/main/java/com/example/allinone/feature/instagram/data/api/InstagramApiService.ktW Vapp/src/main/java/com/example/allinone/feature/instagram/data/model/InstagramModels.ktd capp/src/main/java/com/example/allinone/feature/instagram/data/repository/InstagramRepositoryImpl.ktO Napp/src/main/java/com/example/allinone/feature/instagram/di/InstagramModule.ktb aapp/src/main/java/com/example/allinone/feature/instagram/domain/repository/InstagramRepository.kt] \app/src/main/java/com/example/allinone/feature/instagram/domain/usecase/InstagramUseCases.ktS Rapp/src/main/java/com/example/allinone/feature/instagram/ui/adapter/ChatAdapter.ktT Sapp/src/main/java/com/example/allinone/feature/instagram/ui/adapter/PostsAdapter.kt^ ]app/src/main/java/com/example/allinone/feature/instagram/ui/viewmodel/InstagramAIViewModel.kt\ [app/src/main/java/com/example/allinone/feature/instagram/ui/viewmodel/InstagramViewModel.ktW Vapp/src/main/java/com/example/allinone/feature/notes/data/datasource/NoteDataSource.kt` _app/src/main/java/com/example/allinone/feature/notes/data/datasource/NoteLocalDataSourceImpl.kta `app/src/main/java/com/example/allinone/feature/notes/data/datasource/NoteRemoteDataSourceImpl.kt[ Zapp/src/main/java/com/example/allinone/feature/notes/data/repository/NoteRepositoryImpl.ktY Xapp/src/main/java/com/example/allinone/feature/notes/domain/repository/NoteRepository.kt\ [app/src/main/java/com/example/allinone/feature/program/data/datasource/ProgramDataSource.kti happ/src/main/java/com/example/allinone/feature/transactions/data/repository/TransactionRepositoryImpl.ktg fapp/src/main/java/com/example/allinone/feature/transactions/domain/repository/TransactionRepository.kt_ ^app/src/main/java/com/example/allinone/feature/wingtzun/data/datasource/WTStudentDataSource.kt\ [app/src/main/java/com/example/allinone/feature/workout/data/datasource/WorkoutDataSource.kte dapp/src/main/java/com/example/allinone/feature/workout/data/datasource/WorkoutLocalDataSourceImpl.ktf eapp/src/main/java/com/example/allinone/feature/workout/data/datasource/WorkoutRemoteDataSourceImpl.kt` _app/src/main/java/com/example/allinone/feature/workout/data/repository/WorkoutRepositoryImpl.kt^ ]app/src/main/java/com/example/allinone/feature/workout/domain/repository/WorkoutRepository.ktF Eapp/src/main/java/com/example/allinone/firebase/DataChangeNotifier.ktE Dapp/src/main/java/com/example/allinone/firebase/FirebaseIdManager.ktC Bapp/src/main/java/com/example/allinone/firebase/FirebaseManager.ktF Eapp/src/main/java/com/example/allinone/firebase/FirebaseRepository.ktG Fapp/src/main/java/com/example/allinone/firebase/FirebaseStorageUtil.ktM Lapp/src/main/java/com/example/allinone/firebase/GenericDataChangeNotifier.ktP Oapp/src/main/java/com/example/allinone/firebase/GenericOfflineQueueProcessor.kt@ ?app/src/main/java/com/example/allinone/firebase/OfflineQueue.kt: 9app/src/main/java/com/example/allinone/ui/BaseFragment.kt= <app/src/main/java/com/example/allinone/ui/CalendarDialogs.kt> =app/src/main/java/com/example/allinone/ui/CalendarFragment.kt< ;app/src/main/java/com/example/allinone/ui/CalendarScreen.ktB Aapp/src/main/java/com/example/allinone/ui/CoinMFuturesFragment.ktH Gapp/src/main/java/com/example/allinone/ui/DatabaseManagementFragment.kt< ;app/src/main/java/com/example/allinone/ui/EditNoteScreen.ktE Dapp/src/main/java/com/example/allinone/ui/ExternalFuturesFragment.kt= <app/src/main/java/com/example/allinone/ui/FuturesFragment.kt= <app/src/main/java/com/example/allinone/ui/HistoryFragment.ktG Fapp/src/main/java/com/example/allinone/ui/InstagramBusinessFragment.ktA @app/src/main/java/com/example/allinone/ui/InvestmentsFragment.ktD Capp/src/main/java/com/example/allinone/ui/InvestmentsTabFragment.kt? >app/src/main/java/com/example/allinone/ui/LogErrorsFragment.kt9 8app/src/main/java/com/example/allinone/ui/NotesScreen.kt9 8app/src/main/java/com/example/allinone/ui/TaskDialogs.kt; :app/src/main/java/com/example/allinone/ui/TasksFragment.kt9 8app/src/main/java/com/example/allinone/ui/TasksScreen.ktG Fapp/src/main/java/com/example/allinone/ui/TransactionReportFragment.ktA @app/src/main/java/com/example/allinone/ui/UsdMFuturesFragment.kt; :app/src/main/java/com/example/allinone/ui/WorkoutScreen.ktF Eapp/src/main/java/com/example/allinone/ui/components/DrawingCanvas.ktL Kapp/src/main/java/com/example/allinone/ui/components/InteractiveHtmlText.ktI Happ/src/main/java/com/example/allinone/ui/components/MediaAttachments.ktD Capp/src/main/java/com/example/allinone/ui/components/MediaViewer.ktG Fapp/src/main/java/com/example/allinone/ui/components/RichTextEditor.ktF Eapp/src/main/java/com/example/allinone/ui/components/VoiceRecorder.ktE Dapp/src/main/java/com/example/allinone/ui/compose/wt/WTComponents.ktB Aapp/src/main/java/com/example/allinone/ui/compose/wt/WTDialogs.ktI Happ/src/main/java/com/example/allinone/ui/compose/wt/WTRegistryScreen.ktL Kapp/src/main/java/com/example/allinone/ui/dialogs/TaskGroupDialogManager.ktE Dapp/src/main/java/com/example/allinone/ui/drawing/DrawingActivity.ktC Bapp/src/main/java/com/example/allinone/ui/futures/FuturesScreen.ktN Mapp/src/main/java/com/example/allinone/ui/instagram/InstagramAskAIFragment.ktQ Papp/src/main/java/com/example/allinone/ui/instagram/InstagramInsightsFragment.ktN Mapp/src/main/java/com/example/allinone/ui/instagram/InstagramPostsFragment.ktJ Iapp/src/main/java/com/example/allinone/ui/investments/InvestmentScreen.ktH Gapp/src/main/java/com/example/allinone/ui/navigation/NavigationItems.kt9 8app/src/main/java/com/example/allinone/ui/theme/Color.kt9 8app/src/main/java/com/example/allinone/ui/theme/Theme.kt8 7app/src/main/java/com/example/allinone/ui/theme/Type.ktT Sapp/src/main/java/com/example/allinone/ui/transactions/TransactionOverviewScreen.ktR Qapp/src/main/java/com/example/allinone/ui/transactions/TransactionReportScreen.ktV Uapp/src/main/java/com/example/allinone/ui/transactions/TransactionsDashboardScreen.ktK Japp/src/main/java/com/example/allinone/ui/workout/ActiveWorkoutFragment.ktN Mapp/src/main/java/com/example/allinone/ui/workout/WorkoutDashboardFragment.ktM Lapp/src/main/java/com/example/allinone/ui/workout/WorkoutExerciseFragment.ktE Dapp/src/main/java/com/example/allinone/ui/workout/WorkoutFragment.ktL Kapp/src/main/java/com/example/allinone/ui/workout/WorkoutProgramFragment.ktJ Iapp/src/main/java/com/example/allinone/ui/workout/WorkoutStatsFragment.ktF Eapp/src/main/java/com/example/allinone/ui/workout/WorkoutViewModel.ktM Lapp/src/main/java/com/example/allinone/ui/workout/adapters/ProgramAdapter.ktU Tapp/src/main/java/com/example/allinone/ui/workout/adapters/ProgramExerciseAdapter.ktU Tapp/src/main/java/com/example/allinone/ui/workout/adapters/WorkoutExerciseAdapter.ktP Oapp/src/main/java/com/example/allinone/ui/workout/adapters/WorkoutLogAdapter.ktB Aapp/src/main/java/com/example/allinone/ui/wt/WTLessonsFragment.kt? >app/src/main/java/com/example/allinone/ui/wt/WTPagerAdapter.ktJ Iapp/src/main/java/com/example/allinone/ui/wt/WTRegisterContentFragment.ktC Bapp/src/main/java/com/example/allinone/ui/wt/WTRegisterFragment.ktC Bapp/src/main/java/com/example/allinone/ui/wt/WTRegistryFragment.ktC Bapp/src/main/java/com/example/allinone/ui/wt/WTSeminarsFragment.ktC Bapp/src/main/java/com/example/allinone/ui/wt/WTStudentsFragment.kt> =app/src/main/java/com/example/allinone/utils/ApiKeyManager.kt= <app/src/main/java/com/example/allinone/utils/BackupHelper.ktD Capp/src/main/java/com/example/allinone/utils/ConnectivityMonitor.kt= <app/src/main/java/com/example/allinone/utils/ErrorHandler.ktI Happ/src/main/java/com/example/allinone/utils/GooglePlayServicesHelper.kt= <app/src/main/java/com/example/allinone/utils/LogcatHelper.kt= <app/src/main/java/com/example/allinone/utils/NetworkUtils.ktB Aapp/src/main/java/com/example/allinone/utils/NumberFormatUtils.ktD Capp/src/main/java/com/example/allinone/utils/OfflineStatusHelper.kt? >app/src/main/java/com/example/allinone/utils/TextStyleUtils.kt= <app/src/main/java/com/example/allinone/utils/TradingUtils.ktN Mapp/src/main/java/com/example/allinone/utils/security/SecureStorageManager.ktG Fapp/src/main/java/com/example/allinone/viewmodels/CalendarViewModel.ktF Eapp/src/main/java/com/example/allinone/viewmodels/FuturesViewModel.ktF Eapp/src/main/java/com/example/allinone/viewmodels/HistoryViewModel.ktC Bapp/src/main/java/com/example/allinone/viewmodels/HomeViewModel.ktJ Iapp/src/main/java/com/example/allinone/viewmodels/InvestmentsViewModel.ktG Fapp/src/main/java/com/example/allinone/viewmodels/LogErrorViewModel.ktD Capp/src/main/java/com/example/allinone/viewmodels/NotesViewModel.ktD Capp/src/main/java/com/example/allinone/viewmodels/TasksViewModel.ktH Gapp/src/main/java/com/example/allinone/viewmodels/WTLessonsViewModel.ktI Happ/src/main/java/com/example/allinone/viewmodels/WTRegisterViewModel.ktI Happ/src/main/java/com/example/allinone/viewmodels/WTSeminarsViewModel.kt? >app/src/main/java/com/example/allinone/workers/BackupWorker.ktO Napp/src/main/java/com/example/allinone/workers/ExpirationNotificationWorker.ktF Eapp/src/main/java/com/example/allinone/workers/LogcatCaptureWorker.kt7 6app/src/main/java/com/example/allinone/MainActivity.kt= <app/src/main/java/com/example/allinone/ui/CalendarDialogs.kt< ;app/src/main/java/com/example/allinone/ui/CalendarScreen.kt; :app/src/main/java/com/example/allinone/ui/HistoryScreen.kt= <app/src/main/java/com/example/allinone/ui/InstagramScreen.kt9 8app/src/main/java/com/example/allinone/ui/TaskDialogs.kt9 8app/src/main/java/com/example/allinone/ui/TasksScreen.kt; :app/src/main/java/com/example/allinone/ui/WorkoutScreen.kt