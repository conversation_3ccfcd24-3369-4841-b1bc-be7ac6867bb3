// Generated by view binder compiler. Do not edit!
package com.example.allinone.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.allinone.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class OfflineStatusViewBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageView offlineIcon;

  @NonNull
  public final CardView offlineStatusCard;

  @NonNull
  public final TextView offlineStatusMessage;

  @NonNull
  public final TextView offlineStatusTitle;

  @NonNull
  public final TextView pendingOperationsCount;

  private OfflineStatusViewBinding(@NonNull CardView rootView, @NonNull ImageView offlineIcon,
      @NonNull CardView offlineStatusCard, @NonNull TextView offlineStatusMessage,
      @NonNull TextView offlineStatusTitle, @NonNull TextView pendingOperationsCount) {
    this.rootView = rootView;
    this.offlineIcon = offlineIcon;
    this.offlineStatusCard = offlineStatusCard;
    this.offlineStatusMessage = offlineStatusMessage;
    this.offlineStatusTitle = offlineStatusTitle;
    this.pendingOperationsCount = pendingOperationsCount;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static OfflineStatusViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static OfflineStatusViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.offline_status_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static OfflineStatusViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.offline_icon;
      ImageView offlineIcon = ViewBindings.findChildViewById(rootView, id);
      if (offlineIcon == null) {
        break missingId;
      }

      CardView offlineStatusCard = (CardView) rootView;

      id = R.id.offline_status_message;
      TextView offlineStatusMessage = ViewBindings.findChildViewById(rootView, id);
      if (offlineStatusMessage == null) {
        break missingId;
      }

      id = R.id.offline_status_title;
      TextView offlineStatusTitle = ViewBindings.findChildViewById(rootView, id);
      if (offlineStatusTitle == null) {
        break missingId;
      }

      id = R.id.pending_operations_count;
      TextView pendingOperationsCount = ViewBindings.findChildViewById(rootView, id);
      if (pendingOperationsCount == null) {
        break missingId;
      }

      return new OfflineStatusViewBinding((CardView) rootView, offlineIcon, offlineStatusCard,
          offlineStatusMessage, offlineStatusTitle, pendingOperationsCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
