http://schemas.android.com/apk/res-auto;;${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/attrs.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/wt_bottom_nav_item_color.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/ic_launcher_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/text_input_box_stroke.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color-night/text_input_box_stroke.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values-night/themes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/drawer_item_color.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/text_input_text_color.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color-night/text_input_text_color.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/bottom_nav_item_color_light.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/bottom_nav_item_color.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/dialog_action_button_color.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color-night/dialog_action_button_color.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/chip_background_selector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/dialog_delete_button_color.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color-night/dialog_delete_button_color.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_expand_less.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_game.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_history.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_no_registrations.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_expense.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_format_underline.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_back.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_all.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/selected_circle_shape.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_transport.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_home.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notification.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_instagram_posts.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_chevron_left.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_add.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_corner_bg.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_stats.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_student.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_code.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_chevron_right.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_bills.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_clear_data.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_tasks.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_calendar.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_note.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_lessons.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_database.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_cleardata.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_file.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_pause.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_expand_more.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_save.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/transparent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_investment.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_no_students.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_shape.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_log.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_investment.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_registration.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_wing_tzun.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_format_bold.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_format_italic.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_fitness.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_day_with_registration.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_sports.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_format_list_numbered.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_students.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_day_with_event.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_tag_blue.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_exercise.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_graduation.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/placeholder_image.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_checkbox.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_share.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_investments.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_format_list_bulleted.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/uncompleted_exercise_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_transactions.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_instagram.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_delete.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_image.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_draw.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_income.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/simple_text_splash.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_tag_red.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/fully_uncompleted_exercise_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_tag_green.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_call.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_general.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_remove.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_format_text.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_food.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_close.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_dashboard.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_current_day.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_background_red.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_folder.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/border_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_play_circle.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/splash_layout_drawable.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_edit.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_stop.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/dialog_rounded_bg.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-night/dialog_rounded_bg.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/error_image.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_backup.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_wt_registers.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_wt.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_shopping.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_attach_image.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_day_with_events.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_view_list.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_day_with_lesson.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_category_chip.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-night/bg_category_chip.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_error.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_video_placeholder.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_program.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-v24/ic_launcher_foreground.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_play.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/completed_exercise_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_add_photo.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/default_profile.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_search_white.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_empty_state.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_salary.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_whatsapp.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_selected_day.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_reports.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_video_error.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/opensans_bold.ttf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/opensans.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/opensans_regular.ttf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_futures.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_note.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_fullscreen_image.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_file_structure.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/offline_status_view.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_futures_tab.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/bottom_nav_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/drawer_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_transaction_report.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_transactions_overview.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/instagram_bottom_nav_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_futures_position.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_task.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_edit_seminar.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_post_details.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_futures_tp_sl.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_program_details.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/menu_group_options.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_task_group.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_binance_position.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/pie_chart_tooltip.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/ids.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_edit_investment.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_category_summary.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/menu_task_options.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/wt_registration_context_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/wt_student_context_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_expense_investment.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_income_investment.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_investment.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_transaction.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_backup.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout-night/activity_backup.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_wt_registration.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_edit_lesson.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/wt_bottom_nav_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_voice_note.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_chat_source.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_backup.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout-night/item_backup.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_student_details.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout-night/dialog_student_details.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_workout_details.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_wt_student.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_category_spending.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_transaction_report.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_chat_user.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_investments.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_investments_tab.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_student.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_event.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/workout_bottom_nav_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_home.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_add_exercise.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_edit_wt_student.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_note_video.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/menu_edit_note.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_seminar.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_wt_event.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_chat_ai.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_lesson.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/theme_switch_layout.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_investment_dropdown.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_investment_selection.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_investment_image.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_note_image.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_program.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_edit_program.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/search_history.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/search_notes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/search_register.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/search_students.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_profit_loss.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_progress.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/layout_page_header.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/menu_tasks.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_category_dropdown.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/splash_text_layout.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_pin_input.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_fullscreen_image.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dropdown_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/nav_header.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_loading.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v33/ic_launcher.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*buildDir}/generated/res/processReleaseGoogleServices/values/values.xml,${\:app*buildDir}/generated/res/injectCrashlyticsMappingFileIdRelease/values/com_google_firebase_crashlytics_mappingfileid.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/styles.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values-night/styles.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/file_paths.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+attr:colorNavigationItem,0,V400020039,360002006b,;color:;colorNavigationItemSelected,0,V400030071,3e000300ab,;color:;+color:lesson_event_color,1,V4000e0278,34000e02a8,;"#E3F2FD";lesson_event_color,2,V4000e026d,34000e029d,;"#2C3440";colorPrimaryDark,1,V400080146,**********,;"#1976D2";reels_background,1,V400420b8d,3b00420bc4,;"@color/reelsCard";wt_bottom_nav_item_color,3,F;navy_primary,1,V4001e0494,2e001e04be,;"#003566";navy_primary,2,V4001e05cb,2e001e05f5,;"#4DA8DA";text_primary,1,V400530e6b,2e00530e95,;"#212121";start_color,1,V4000c0212,2d000c023b,;"#E8F5E9";start_color,2,V4000d021a,2d000d0243,;"#2E3B2E";colorError,1,V4001903c5,2c001903ed,;"#F44336";colorError,2,V400190469,2c00190491,;"#CF6679";task_checkbox_color,1,V400631079,35006310aa,;"#4CAF50";green_500,1,V40067114d,2b00671174,;"#4CAF50";red,1,V40014031d,250014033e,;"#F44336";red,2,V40014034b,250014036c,;"#CF6679";surface_color,1,V4005b0f70,2f005b0f9b,;"#FFFFFF";white,1,V400030064,2900030089,;"#FFFFFFFF";white,2,V400030064,2900030089,;"#FFFFFFFF";video_blue,1,V4004d0d5d,2c004d0d85,;"#2196F3";colorSuccess,1,V4001a03f3,2e001a041d,;"#4CAF50";colorSuccess,2,V4001a04c6,2e001a04f0,;"#03DAC6";dark_gray,1,V400150344,2b0015036b,;"#555555";dark_gray,2,V4001503a8,2b001503cf,;"#CCCCCC";purple_500,1,V4006a11d3,2c006a11fb,;"#9C27B0";colorPrimary,1,V400070116,2e00070140,;"#2196F3";colorPrimary,2,V400080119,3a0008014f,;"@color/navy_primary";default_event_color,1,V4000d0241,35000d0272,;"#FFFFFF";default_event_color,2,V400090155,4100090192,;"@color/navy_surface";navy_variant,1,V4002105ab,2e002105d5,;"#004E92";navy_variant,2,V4002106dd,2e00210707,;"#004E92";registration_start,1,V4003108d1,3400310901,;"#4CAF50";blue_500,1,V400661121,2a00661147,;"#2196F3";reels_purple,1,V4004c0d2d,2e004c0d57,;"#8E24AA";reelsCard,1,V400390a25,2b00390a4c,;"#F0F8FF";cardStroke,1,V4003c0aac,2c003c0ad4,;"#E0E0E0";colorWarning,1,V4001b0423,2e001b044d,;"#FFC107";colorWarning,2,V4001b0525,2e001b054f,;"#FFB74D";error_dark,1,V400540e9b,2c00540ec3,;"#D32F2F";ic_launcher_background,4,V400020039,380002006d,;"#000000";textPrimary,1,V4001102ca,2d001102f3,;"#212121";textPrimary,2,V4001102ef,320011031d,;"@color/white";text_input_box_stroke,5,F;text_input_box_stroke,6,F;good_orange,1,V400480ca6,2d00480ccf,;"#FF9800";mtrl_textinput_default_box_stroke_color,7,V4000300a6,4d000300ef,;"@color/gray";mtrl_textinput_default_box_stroke_color,8,V400460fea,4e00461034,;"@color/white";gray_dark,1,V40004008f,2d000400b8,;"#FF444444";gray_dark,2,V40004008f,2d000400b8,;"#FF444444";bright_tab_unselected,1,V400270767,370027079a,;"#7FB5DE";bright_tab_unselected,2,V40027086f,37002708a2,;"#4F5B62";drawer_item_color,9,F;user_message_bg,1,V4005f0ff5,31005f1022,;"#007AFF";text_input_text_color,10,F;text_input_text_color,11,F;poor_red,1,V400490cd5,2a00490cfb,;"#F44336";bottom_nav_item_color_light,12,F;expiration_color,1,V4000b01de,32000b020c,;"#FFEBEE";expiration_color,2,V4000c01c8,32000c01f6,;"#3E2828";feedCard,1,V4003809f9,2a00380a1f,;"#FFFFFF";albumCard,1,V4003b0a7f,2b003b0aa6,;"#F5F5F5";bottom_nav_item_color,13,F;red_500,1,V40068117a,290068119f,;"#F44336";dialog_action_button_color,14,F;dialog_action_button_color,15,F;storyCard,1,V4003a0a52,2b003a0a79,;"#F0FFF0";navy_text_secondary,1,V400230664,3500230695,;"#A5C5E1";navy_text_secondary,2,V400230789,35002307ba,;"#B0BEC5";error_light,1,V4002907ca,2d002907f3,;"#FFCDD2";colorAccent,1,V4000a01af,2d000a01d8,;"#FF4081";gray_light,1,V4000500be,2e000500e8,;"#FFD3D3D3";gray_light,2,V4000500be,2e000500e8,;"#FF666666";gray,1,V4000600ee,2600060110,;"#999999";bright_tab_selected,1,V400260708,3500260739,;"#0466C8";bright_tab_selected,2,V40026080f,3500260840,;"#64B5F6";boldTextColor,1,V4003f0b21,34003f0b51,;"@color/black";boldTextColor,2,V4002a08fb,34002a092b,;"@color/white";navy_accent,1,V400220601,2d0022062a,;"#4DA8DA";navy_accent,2,V400220727,2d00220750,;"#64B5F6";warning_light,1,V4002a07f9,2f002a0824,;"#FFF9C4";colorPrimaryLight,1,V40009017a,33000901a9,;"#BBDEFB";green,1,V4002d0854,27002d0877,;"#4CAF50";on_surface_color,1,V4005c0fa1,32005c0fcf,;"#212121";light_gray,1,V400570ee8,2c00570f10,;"#F0F0F0";black,1,V400020039,290002005e,;"#FF000000";black,2,V400020039,290002005e,;"#FF000000";light_blue,1,V400500ded,2c00500e15,;"#E3F2FD";excellent_green,1,V400470c73,3100470ca0,;"#4CAF50";orange_500,1,V4006911a5,2c006911cd,;"#FF9800";navy_surface,1,V40020054a,2e00200574,;"#00264D";navy_surface,2,V40020067d,2e002006a7,;"#002B59";ai_message_bg,1,V400601028,2f00601053,;"#E8E8E8";image_green,1,V4004f0dbe,2d004f0de7,;"#4CAF50";navy_background,1,V4001f04ee,31001f051b,;"#001D3D";navy_background,2,V4001f062b,31001f0658,;"#001D3D";background_color,1,V4005a0f3c,32005a0f6a,;"#F5F5F5";chip_background_selector,16,F;bg_tag_blue,1,V400350977,2d003509a0,;"#2196F3";medium_gray,1,V400160371,2d0016039a,;"#888888";medium_gray,2,V400160401,2d0016042a,;"#999999";purple,1,V4002e087d,28002e08a1,;"#9C27B0";dialog_delete_button_color,17,F;dialog_delete_button_color,18,F;carousel_background,1,V400440c07,3e00440c41,;"@color/albumCard";registration_end,1,V400320917,3500320948,;"@color/red";video_background,1,V400430bca,3b00430c01,;"@color/storyCard";carousel_orange,1,V4004e0d8b,31004e0db8,;"#FF9800";+dimen:task_indent_margin,19,V400030071,310003009e,;"32dp";+drawable:ic_expand_less,20,F;ic_category_game,21,F;ic_history,22,F;ic_no_registrations,23,F;ic_expense,24,F;ic_format_underline,25,F;ic_back,26,F;ic_category_all,27,F;selected_circle_shape,28,F;ic_category_transport,29,F;ic_home,30,F;ic_notification,31,F;ic_instagram_posts,32,F;circle_background,33,F;ic_chevron_left,34,F;ic_add,35,F;rounded_corner_bg,36,F;ic_stats,37,F;ic_student,38,F;ic_code,39,F;ic_launcher_background,40,F;ic_chevron_right,41,F;ic_category_bills,42,F;ic_clear_data,43,F;ic_tasks,44,F;ic_calendar,45,F;ic_note,46,F;ic_lessons,47,F;ic_database,48,F;ic_cleardata,49,F;ic_file,50,F;ic_pause,51,F;ic_expand_more,52,F;ic_save,53,F;transparent,54,F;ic_investment,55,F;ic_no_students,56,F;circle_shape,57,F;ic_log,58,F;ic_category_investment,59,F;ic_registration,60,F;ic_category_wing_tzun,61,F;ic_format_bold,62,F;ic_format_italic,63,F;ic_fitness,64,F;bg_day_with_registration,65,F;ic_category_sports,66,F;ic_format_list_numbered,67,F;ic_students,68,F;bg_day_with_event,69,F;bg_tag_blue,70,F;ic_exercise,71,F;ic_graduation,72,F;placeholder_image,73,F;ic_checkbox,74,F;ic_share,75,F;ic_investments,76,F;ic_format_list_bulleted,77,F;uncompleted_exercise_background,78,F;ic_transactions,79,F;ic_instagram,80,F;ic_delete,81,F;ic_image,82,F;ic_draw,83,F;ic_income,84,F;simple_text_splash,85,F;bg_tag_red,86,F;fully_uncompleted_exercise_background,87,F;bg_tag_green,88,F;ic_notes,89,F;ic_call,90,F;ic_category_general,91,F;ic_remove,92,F;ic_format_text,93,F;ic_menu,94,F;ic_category_food,95,F;ic_close,96,F;ic_dashboard,97,F;bg_current_day,98,F;rounded_background,99,F;circle_background_red,100,F;ic_folder,101,F;border_background,102,F;ic_play_circle,103,F;splash_layout_drawable,104,F;ic_edit,105,F;ic_stop,106,F;dialog_rounded_bg,107,F;dialog_rounded_bg,108,F;error_image,109,F;ic_backup,110,F;ic_wt_registers,111,F;ic_wt,112,F;ic_category_shopping,113,F;ic_attach_image,114,F;bg_day_with_events,115,F;ic_view_list,116,F;bg_day_with_lesson,117,F;bg_category_chip,118,F;bg_category_chip,119,F;ic_error,120,F;ic_video_placeholder,121,F;ic_program,122,F;ic_launcher_foreground,123,F;ic_launcher_foreground,124,F;ic_play,125,F;completed_exercise_background,126,F;ic_add_photo,127,F;default_profile,128,F;ic_search_white,129,F;ic_empty_state,130,F;ic_category_salary,131,F;ic_whatsapp,132,F;bg_selected_day,133,F;ic_reports,134,F;ic_video_error,135,F;+font:opensans_bold,136,F;opensans,137,F;opensans_regular,138,F;+id:futuresViewPager,139,F;noteTitle,140,F;imageCounterText,141,F;fileName,142,F;offline_icon,143,F;pnlTitleText,144,F;nav_investments,145,F;nav_history,146,F;balanceText,147,F;balanceText,148,F;tab_insight,149,F;marginTypeText,150,F;clearDueDateButton,151,F;endTimeInput,152,F;closeButton,153,F;nav_clear_db,146,F;positionDetailsText,154,F;program_name_text,155,F;action_delete_group,156,F;group_title_edit,157,F;marginLabel,158,F;tooltipText,159,F;pending_operations_count,143,F;pending_operations_count,160,V4000500c8,36000500fa,;"";btn_save,157,F;descriptionInput,161,F;descriptionInput,152,F;descriptionInput,148,F;categoryText,162,F;action_edit,163,F;action_edit,164,F;action_edit,165,F;newInvestmentButton,166,F;newInvestmentButton,167,F;marginValue,158,F;leverageLabelText,150,F;investmentDescription,168,F;chartCard,147,F;fileIcon,142,F;typeText,169,F;nav_wt_registry,146,F;backups_recycler_view,170,F;backups_recycler_view,171,F;dueDateLabel,151,F;startDate,172,F;nav_transaction_report,145,F;startTimeField,173,F;wtLessonsFragment,174,F;liqPriceLabel,158,F;voiceNoteCurrentTime,175,F;offline_status_message,143,F;offline_status_message,160,V400040092,34000400c2,;"";textSourceScore,176,F;positionAmtLabelText,150,F;loadingProgress,144,F;backup_name,177,F;backup_name,178,F;nameTextView,179,F;nameTextView,180,F;workout_date_text,181,F;categoryAutoComplete,147,F;voiceNoteCountText,140,F;restore_button,177,F;restore_button,178,F;email,182,F;categoryColorIndicator,183,F;categoryColorIndicator,184,F;totalIncomeText,147,F;textUserMessage,185,F;positionsRecyclerView,144,F;addInvestmentButton,186,F;positionsTitle,144,F;swipeRefreshLayout,187,F;backups_card,170,F;backups_card,171,F;nameInputLayout,188,F;eventDescriptionInput,189,F;instagramButton,179,F;instagramButton,180,F;eventTimeInput,189,F;workout_stats,190,F;btn_cancel,157,F;liqPriceValue,158,F;fragment_container,191,F;exercise_reps_input,192,F;cardUserMessage,185,F;balanceValueText,144,F;amountLayout,193,F;amountLayout,148,F;videoThumbnail,194,F;investmentTabLayout,186,F;statusIndicator,182,F;instagramEditText,188,F;fullscreenViewPager,141,F;action_delete,195,F;action_delete,163,F;action_delete,164,F;action_delete,165,F;investmentsRecyclerView,187,F;categoryNameText,183,F;transactionDescription,184,F;callButton,179,F;callButton,180,F;profileImage,182,F;markPriceValue,158,F;liquidationPriceLabelText,150,F;nav_notes,146,F;dueDateText,151,F;seminarDescriptionText,196,F;eventDescription,197,F;paidSwitch,193,F;nav_workout,146,F;nameEditText,188,F;pieChart,148,F;nav_instagram_business,146,F;eventEndTimeInput,189,F;detailsTextView,179,F;detailsTextView,180,F;averageTransactionText,147,F;positionAmtText,150,F;layoutSources,198,F;program_description_text,155,F;takeProfitInput,154,F;categorySpendingCard,147,F;markPriceText,150,F;startDateInput,193,F;dayText,173,F;dayText,199,F;addImageButton,161,F;postLinkLabel,153,F;postLinkLabel,153,F;categoryPercentText,183,F;wtSeminarsFragment,174,F;phoneNumber,182,F;amountText,162,F;amountText,162,F;amountText,169,F;typeDropdown,148,F;tpslValue,158,F;emailInputLayout,188,F;dateRangeAutoComplete,147,F;tpSlText,150,F;share_button,177,F;share_button,178,F;eventDate,197,F;studentPhoto,172,F;studentPhoto,172,F;studentPhoto,172,F;activeSwitch,188,F;textConfidence,198,F;noteContent,140,F;dateLayout,152,F;dateLayout,152,F;dateLayout,152,F;closePositionButton,154,F;amountInput,161,F;amountInput,193,F;amountInput,148,F;entryPriceLabel,158,F;themeSwitch,200,F;cancelButton,188,F;cancelButton,166,F;cancelButton,154,F;cancelButton,167,F;mostSpentCategoryText,147,F;divider,153,F;divider,153,F;divider,144,F;nav_transactions,146,F;positionSymbolText,154,F;taskDescriptionInput,151,F;textSourceMetrics,176,F;backup_size,177,F;backup_size,178,F;sizeValue,158,F;postType,153,F;postType,153,F;investmentAmount,168,F;investmentAmount,201,F;investmentAmount,202,F;color_purple,157,F;attachmentPreview,193,F;postDetailThumbnail,153,F;postDetailThumbnail,153,F;isPastInvestmentCheckbox,161,F;transactionDate,184,F;imagesRecyclerView,161,F;exercise_notes_input,192,F;phoneInputLayout,188,F;pnlText,150,F;seminarDateText,196,F;seminarDateText,196,F;expenseText,148,F;noteDate,140,F;offline_status_title,143,F;exercises_recycler_view,155,F;exercises_recycler_view,181,F;investmentType,168,F;investmentType,201,F;investmentType,202,F;leverageText,158,F;leverageText,150,F;deleteButton,203,F;deleteButton,199,F;deleteButton,204,F;deleteButton,194,F;applyFiltersButton,147,F;endDate,172,F;endDate,172,F;nav_clear_data,146,F;insightsLabel,153,F;insightsLabel,153,F;add_exercise_button,205,F;add_exercise_button,206,F;summaryCard,144,F;summaryCard,187,F;summaryCard,147,F;imageView,203,F;imageView,204,F;categoryChip,184,F;exercises_container,205,F;exercises_container,206,F;typeInput,161,F;program_description_input,205,F;program_description_input,206,F;postCaptionLabel,153,F;postCaptionLabel,153,F;postCaptionLabel,153,F;shareButton,140,F;shareButton,140,F;shareButton,196,F;shareButton,196,F;shareButton,196,F;shareButton,196,F;shareButton,172,F;shareButton,172,F;textAITimestamp,198,F;titleText,166,F;nav_error_logs,146,F;taskNameInput,151,F;offline_status_card,143,F;offline_status_card,160,V40003005f,310003008c,;"";paginationControls,148,F;dateText,169,F;deleteVoiceNoteButton,175,F;action_search,207,F;action_search,208,F;action_search,209,F;action_search,210,F;groupSpinner,151,F;lineChart,147,F;nav_backup,146,F;group_description_edit,157,F;roiLabel,158,F;layoutTyping,198,F;investmentDropdown,166,F;investmentDropdown,167,F;profitRadio,211,F;cashFlowTitle,147,F;investmentViewPager,186,F;timeSlash,175,F;insightsCard,147,F;workout_name_text,181,F;roiValue,158,F;layoutConfidence,198,F;studentName,172,F;studentName,172,F;studentName,172,F;studentName,182,F;topCategoriesRecyclerView,147,F;voiceNoteTitle,175,F;symbolText,158,F;symbolText,150,F;recyclerSources,198,F;dialogTitle,167,F;group_description_layout,157,F;notesEditText,193,F;balanceTitleText,144,F;typeLayout,148,F;entryPriceValue,158,F;action_edit_group,156,F;emailEditText,188,F;transactionCountText,147,F;nav_calendar,146,F;lossRadio,211,F;pnlValue,158,F;nav_database_management,146,F;voiceNoteDuration,175,F;addAttachmentButton,193,F;marginBalanceValueText,144,F;prevPageButton,148,F;dateInput,152,F;endDateInput,193,F;incomeText,148,F;studentDropdown,193,F;tab_posts,149,F;btn_delete,157,F;homeFragment,145,F;workout_duration_text,181,F;create_backup_button,170,F;create_backup_button,171,F;entryPriceText,150,F;exercise_name_input,192,F;transactionsCard,148,F;color_blue,157,F;remove_exercise_button,192,F;instagramInputLayout,188,F;imageContainer,168,F;imageContainer,140,F;dateRangeLayout,147,F;phoneEditText,188,F;color_green,157,F;markPriceLabelText,150,F;startTimeInput,152,F;addExpenseButton,148,F;investmentDate,168,F;toolbar,170,F;toolbar,171,F;progress_text,212,F;pickDueDateButton,151,F;no_backups_text,170,F;no_backups_text,171,F;editButton,199,F;pnlValueText,144,F;dialog_title,157,F;workout_exercise,190,F;postDate,153,F;postDate,153,F;workout_dashboard,190,F;wtRegisterFragment,174,F;categoryLayout,147,F;tab_ask_ai,149,F;pnlLabel,158,F;workout_program,190,F;totalInvestmentsText,187,F;program_name_input,205,F;program_name_input,206,F;tpslLabel,158,F;page_title,213,F;futuresSwipeRefreshLayout,144,F;textUserTimestamp,185,F;voiceNoteIndicator,140,F;descriptionText,169,F;addIncomeButton,148,F;nextPageButton,148,F;color_orange,157,F;largestExpenseText,147,F;cardAIMessage,198,F;cardAIMessage,198,F;markPriceLabel,158,F;textAIMessage,198,F;postLink,153,F;postLink,153,F;postInsights,153,F;postInsights,153,F;color_red,157,F;delete_button,177,F;delete_button,178,F;investmentCountText,187,F;categoryPieChart,147,F;profileImageView,188,F;profileImageView,179,F;profileImageView,180,F;group_title_layout,157,F;liquidationPriceText,150,F;transactionsRecyclerView,148,F;futuresTabLayout,139,F;futuresTabLayout,139,F;nameInput,161,F;nameInput,152,F;stopLossLayout,154,F;investmentName,168,F;investmentName,201,F;investmentName,202,F;attachmentsSection,140,F;transactionTitle,184,F;seminarNameText,196,F;seminarNameText,196,F;marginTypeLabelText,150,F;takeProfitLayout,154,F;progress_indicator,212,F;postTypeLabel,153,F;postTypeLabel,153,F;action_manage_groups,214,F;timeText,199,F;entryPriceLabelText,150,F;textSourceEmoji,176,F;exercise_weight_input,192,F;textSourcePost,176,F;postDateLabel,153,F;postDateLabel,153,F;marginBalanceTitleText,144,F;avatarAI,198,F;avatarAI,198,F;avatarAI,198,F;positionSideText,150,F;group_settings,146,F;muscle_group_dropdown,192,F;progress_bar,170,F;progress_bar,171,F;startTimeLayout,152,F;startTimeLayout,152,F;startTimeLayout,152,F;confirmButton,154,F;totalExpenseText,147,F;categoryName,215,F;filterCard,147,F;whatsappButton,179,F;whatsappButton,180,F;appNameText,216,F;eventTitle,197,F;eventTitleInput,189,F;endTimeField,173,F;categoryAmountText,183,F;progressBar,194,F;transactionAmount,184,F;emptyTransactionsText,148,F;backup_card,170,F;backup_card,171,F;investmentProfitLoss,168,F;saveButton,188,F;action_toggle_view,214,F;pinEditText,217,F;pageIndicator,148,F;amount,172,F;attachmentNameText,193,F;exercise_sets_input,192,F;investmentCurrentValue,168,F;nameLayout,152,F;nameLayout,152,F;emptyStateText,166,F;emptyStateText,167,F;emptyStateText,144,F;emptyStateText,187,F;drawer_button,213,F;wtStudentsFragment,174,F;endTimeLayout,152,F;endTimeLayout,152,F;investmentDropdownLayout,166,F;investmentDropdownLayout,167,F;descriptionLayout,152,F;descriptionLayout,148,F;paymentStatusChip,172,F;paymentStatusChip,172,F;postCaption,153,F;postCaption,153,F;sizeLabel,158,F;categoryIcon,215,F;fullscreenImageItem,218,F;profitLossAmountInput,211,F;playPauseButton,175,F;nav_tasks,146,F;seminarTimeText,196,F;seminarTimeText,196,F;stopLossInput,154,F;+layout:dialog_edit_investment,161,F;item_category_dropdown,215,F;item_chat_source,176,F;dialog_task_group,157,F;item_category_spending,183,F;dialog_profit_loss,211,F;item_note_video,194,F;dialog_edit_seminar,152,F;dialog_add_event,189,F;dialog_pin_input,217,F;dialog_add_program,205,F;item_lesson,199,F;dialog_edit_program,206,F;item_wt_registration,172,F;dropdown_item,219,F;item_note_image,204,F;fragment_investments,186,F;fragment_investments_tab,187,F;item_futures_position,150,F;layout_page_header,213,F;item_category_summary,162,F;item_transaction,169,F;fragment_home,191,F;theme_switch_layout,200,F;dialog_post_details,153,F;fragment_futures,139,F;dialog_futures_tp_sl,154,F;item_file_structure,142,F;dialog_student_details,179,F;dialog_student_details,180,F;item_chat_user,185,F;dialog_program_details,155,F;activity_backup,170,F;activity_backup,171,F;item_note,140,F;nav_header,220,F;dialog_edit_lesson,173,F;dialog_add_student,188,F;item_investment_dropdown,201,F;item_backup,177,F;item_backup,178,F;dialog_add_task,151,F;fragment_futures_tab,144,F;item_investment_image,203,F;item_add_exercise,192,F;item_investment,168,F;dialog_edit_wt_student,193,F;fragment_transaction_report,147,F;item_wt_student,182,F;item_seminar,196,F;dialog_workout_details,181,F;item_wt_event,197,F;item_voice_note,175,F;dialog_loading,221,F;item_investment_selection,202,F;fragment_transactions_overview,148,F;dialog_income_investment,167,F;item_binance_position,158,F;dialog_progress,212,F;dialog_fullscreen_image,141,F;offline_status_view,143,F;splash_text_layout,216,F;dialog_expense_investment,166,F;item_transaction_report,184,F;item_chat_ai,198,F;pie_chart_tooltip,159,F;item_fullscreen_image,218,F;+menu:search_history,207,F;search_register,209,F;wt_bottom_nav_menu,174,F;drawer_menu,146,F;menu_tasks,214,F;wt_registration_context_menu,164,F;menu_group_options,156,F;menu_edit_note,195,F;instagram_bottom_nav_menu,149,F;bottom_nav_menu,145,F;wt_student_context_menu,165,F;workout_bottom_nav_menu,190,F;search_notes,208,F;menu_task_options,163,F;search_students,210,F;+mipmap:ic_launcher_round,222,F;ic_launcher_round,223,F;ic_launcher_round,224,F;ic_launcher_round,225,F;ic_launcher_round,226,F;ic_launcher_round,227,F;ic_launcher,228,F;ic_launcher,229,F;ic_launcher,230,F;ic_launcher,231,F;ic_launcher,232,F;ic_launcher,233,F;ic_launcher,234,F;+plurals:exercise_count,235,V401394833,e013c48c6,;one:1 exercise,other:%d exercises,;+string:cancel,235,V4001203fe,2900120423,;"Cancel";instagram,235,V40051101d,2f00511048,;"Instagram";color_pink,235,V4003009eb,3100300a18,;"Pink Color";workout_deleted,235,V401304614,3b0130464b,;"Workout deleted";not_set,235,V4014e4b70,2b014e4b97,;"Not set";registration_updated,235,V4006113c4,5200611412,;"Registration updated successfully";no_group,235,V400bd2a9b,2d00bd2ac4,;"No Group";delete_program,235,V401314651,3901314686,;"Delete Program";record,235,V400f338ad,2900f338d2,;"Record";please_enter_title,235,V400901f83,4300901fc2,;"Please enter a title";select_group,235,V400be2aca,3500be2afb,;"Select Group";app_id,235,V400330a43,2f00330a6e,;"954911141967";status_registered_desc,235,V4009b2258,5b009b22af,;"Active student with current registration";save_workout,235,V401113e1f,3501113e50,;"Save Workout";weekly_workout_count,235,V40120414f,4701204192,;"Workouts this week\: %d";camera_permission_title,235,V40078197d,45007819be,;"Camera Permission";student_has_active_registrations,235,V400e635e0,7900e63655,;"This student has active registrations and cannot be deleted.";image_counter,235,V401484aac,3301484adb,;"%1$d/%2$d";edit,235,V400861d0e,2500861d2f,;"Edit";color_black,235,V400270810,330027083f,;"Black Color";edit_coming_soon,235,V40134475b,4b013447a2,;"Edit functionality coming soon";delete_image_confirmation,235,V40011039b,61001103f8,;"Are you sure you want to delete this image?";share_registration,235,V4006514cf,4600651511,;"Share Registration Info";mark_completed,235,V400a62522,3c00a6255a,;"Mark as completed";investment_type,235,V4000a01f0,3b000a0227,;"Investment Type";error_database,235,V400d33070,5200d330be,;"Database error. Please try again later.";stop,235,V400f5390a,2500f5392b,;"Stop";add_task,235,V400a023bc,2d00a023e5,;"Add Task";delete_item,235,V400410d0c,3300410d3b,;"Delete item";task_completed,235,V400ad26d7,3900ad270c,;"Task completed";add_student,235,V400531087,33005310b6,;"Add Student";cannot_delete,235,V400e535a7,3700e535da,;"Cannot Delete";no_history_items,235,V4003e0c4a,43003e0c89,;"No history items found";end_date,235,V4005e12f9,2d005e1322,;"End Date";create_task_group,235,V400b12775,3f00b127b0,;"Create Task Group";error_camera,235,V4007e1b9a,45007e1bdb,;"Error preparing camera\: %1$s";delete_note_confirmation,235,V400eb36d1,5f00eb372c,;"Are you sure you want to delete this note?";workout_details,235,V4012d450d,3b012d4544,;"Workout Details";custom_workout,235,V4011e40d9,39011e410e,;"Custom Workout";weekly_summary,235,V4011f4114,39011f4149,;"Weekly Summary";select_type,235,V400080172,3f000801ad,;"Select transaction type";search,235,V401023b7c,3e01023bb6,;"Search";please_enter_valid_amount,235,V4006915fc,4f00691647,;"Please enter valid amount";error_playing_audio,235,V400fb3a87,4300fb3ac6,;"Error playing audio";save_to_note_and_gallery,235,V400190521,4d0019056a,;"Save to Note and Gallery";edit_task,235,V400a424b2,2f00a424dd,;"Edit Task";error_document_not_found,235,V400d731f6,5800d7324a,;"The requested record was not found.";close,235,V4013547a8,27013547cb,;"Close";email,235,V400500ff3,2800501017,;"E-mail";create_drawing,235,V40016046d,39001604a2,;"Create Drawing";sets,235,V401163f36,2501163f57,;"Sets";expand_collapse,235,V400bf2b01,3b00bf2b38,;"Expand/Collapse";empty_history,235,V4003d0c11,37003d0c44,;"Empty history";app_name,235,V400020039,2d00020062,;"AllInOne";error_storage,235,V400db32d1,5000db331d,;"Storage error. Please try again later.";note_deleted,235,V400932032,3500932063,;"Note deleted";workout,235,V401093c87,2b01093cae,;"Workout";workout_stats,235,V4013f48fc,2f013f4927,;"Stats";edit_task_group,235,V400b227b6,3b00b227ed,;"Edit Task Group";save_to_note,235,V4001804e5,3a0018051b,;"Save to Note Only";voice_note_plural,235,V400f037c1,3c00f037f9,;"%d voice notes";exercise_name,235,V401153efd,3701153f30,;"Exercise Name";error_quota_exceeded,235,V400d83250,6000d832ac,;"Service limit exceeded. Please try again later.";pending_operations,235,V400380b43,4700380b86,;"Pending operations\: %1$d";notes,235,V4009e236a,27009e238d,;"Notes";gcm_defaultSenderId,236,V400020037,5100020084,;"954911141967";program_name,235,V401143ec6,3501143ef7,;"Program Name";program_deleted,235,V401334711,4801334755,;"Program deleted successfully";select_student,235,V4005c128b,39005c12c0,;"Select Student";delete_workout_confirmation,235,V4012f4585,8d012f460e,;"Are you sure you want to delete this workout from %1$s? This action cannot be undone.";program_created,235,V4012b4493,3b012b44ca,;"Program created";project_id,236,V400070240,4a00070286,;"allinone-bd6f3";receipt_unavailable,235,V40064148a,43006414c9,;"Receipt unavailable";delete_group,235,V400b82917,3500b82948,;"Delete Group";payment_received,235,V4005f1328,3d005f1361,;"Payment Received";attachment_click_to_open,235,V40095209c,58009520f0,;"📄 Attachment\: %1$s (click to open)";error_service_unavailable,235,V400d5312c,7100d53199,;"Service is temporarily unavailable. Please try again later.";error_file_not_found,235,V400dc3323,5600dc3375,;"The file you requested was not found.";weight,235,V401183f84,2e01183fae,;"Weight (kg)";error_saving_drawing,235,V4001f06a4,4b001f06eb,;"Error saving drawing\: %1$s";title_wing_tzun_registry,235,V400460dcd,4700460e10,;"Wing Tzun Registry";delete_voice_note_confirmation,235,V400f23840,6b00f238a7,;"Are you sure you want to delete this voice note?";delete_registration,235,V400881d60,4300881d9f,;"Delete Registration";no_workout_history,235,V40128438c,69012843f1,;"No workout history yet. Complete a workout to see it here.";stop_workout_confirmation,235,V4012943f7,6101294454,;"Are you sure you want to stop this workout?";name,235,V4004e0f95,25004e0fb6,;"Name";storage_permission_title,235,V4007a1a3c,47007a1a7f,;"Storage Permission";phone_number,235,V4004f0fbc,35004f0fed,;"Phone Number";delete_task,235,V400a825a0,3300a825cf,;"Delete Task";favorite_muscle,235,V401444a15,3b01444a4c,;"Favorite Muscle";create_workout,235,V401123e56,3901123e8b,;"Create Workout";camera_permission_message,235,V4007919c4,7600791a36,;"Camera permission is needed to take photos for student profiles.";color,235,V4002407c2,28002407e6,;"Color\:";select_month,235,V4006d1773,35006d17a4,;"Select Month";network_unavailable,235,V4009d2311,57009d2364,;"Network unavailable. Using cached data.";description,235,V4000500ba,33000500e9,;"Description";recording,235,V400f438d8,3000f43904,;"Recording…";pause_workout,235,V4010f3dbf,2f010f3dea,;"Pause";selected_color,235,V400220761,**********,;"Selected\:";stop_workout,235,V401103df0,2d01103e19,;"Stop";manage_groups,235,V400b728de,3700b72911,;"Manage Groups";error_user_not_found,235,V400cc2e5b,6700cc2ebe,;"User account not found. Please check your credentials.";add_program,235,V401133e91,3301133ec0,;"Add Program";offline_message,235,V400370aca,7700370b3d,;"You are currently offline. Changes will be synchronized when you reconnect.";title_register,235,V400480e4b,3300480e7a,;"Register";new_task,235,V400a22437,2d00a22460,;"New Task";no_recent_workouts,235,V401234225,**********,;"No recent workouts";error_deleting_registration,235,V4008b1e6d,53008b1ebc,;"Error deleting registration";retry,235,V4008d1ef4,27008d1f17,;"Retry";apply,235,V4006e17aa,27006e17cd,;"Apply";color_purple,235,V4002c0915,35002c0946,;"Purple Color";delete_registration_confirmation,235,V400891da5,7200891e13,;"Are you sure you want to delete %1$s's registration?";task_deleted,235,V400ac26a0,3500ac26d1,;"Task deleted";total_workouts,235,V401414970,39014149a5,;"Total Workouts";save_changes,235,V4000600ef,**********,;"Save Changes";title_notes,235,V400450d9e,2d00450dc7,;"Notes";start_workout,235,V4010e3d86,37010e3db9,;"Start Workout";add_attachment,235,V4000d028f,39000d02c4,;"Add Attachment";brightness,235,V401063c33,3201063c61,;"Brightness\:";error_wrong_password,235,V400cd2ec4,5600cd2f16,;"Incorrect password. Please try again.";title_students,235,V400470e16,3300470e45,;"Students";pull_to_refresh,235,V4003f0c8f,40003f0ccb,;"Pull down to refresh";recording_failed,235,V400f839af,4600f839f1,;"Failed to start recording";most_recent_workout,235,V4012241e0,430122421f,;"Most Recent Workout";status_active_desc,235,V4009c22b5,5a009c230b,;"Active student without current registration";no_programs,235,V40127432e,5c01274386,;"No programs yet. Create one by tapping the \+ button.";task_added,235,V400aa2636,3100aa2663,;"Task added";type_input_description,235,V400070126,4a0007016c,;"Select transaction type";select,235,V400230797,29002307bc,;"Select";title_lesson_schedule,235,V4004a0eb3,41004a0ef0,;"Lesson Schedule";no_students,235,V40056114b,330056117a,;"No Students";enter_task_description,235,V400a32466,4a00a324ac,;"Enter task description\:";error_saving_recording,235,V400f939f7,4b00f93a3e,;"Failed to save recording";title_history,235,V400490e80,3100490ead,;"History";type,235,V400040093,25000400b4,;"Type";delete_voice_note,235,V400f137ff,3f00f1383a,;"Delete Voice Note";student_deleted,235,V400e4355d,4800e435a1,;"Student deleted successfully";field_required,235,V400831c9b,4100831cd8,;"This field is required";com.google.firebase.crashlytics.mapping_file_id,237,V8011e,ad000801cb,UnusedResources,TypographyDashes;"176bed5747e2458db4f134c167834b2b";error_storage_quota_exceeded,235,V400dd337b,5000dd33c7,;"Storage quota exceeded.";delete_note,235,V400ea369c,3300ea36cb,;"Delete Note";registration_deleted,235,V4008a1e19,52008a1e67,;"Registration deleted successfully";total_time,235,V4014349e2,3101434a0f,;"Total Time";edit_student,235,V4005410bc,35005410ed,;"Edit Student";tasks,235,V4009f2393,27009f23b6,;"Tasks";all_months,235,V4006f17d3,31006f1800,;"All Months";group_title,235,V400b327f3,3300b32822,;"Group Title";error_not_authorized,235,V400df342a,5f00df3485,;"You are not authorized to perform this action.";no_tasks_yet,235,V400a123eb,4a00a12431,;"No tasks yet. Add your first one!";total_amount_label,235,V40071182f,3c00711867,;"Total Amount\:";offline_mode,235,V400360a93,3500360ac4,;"Offline Mode";title_seminars,235,V4009921e0,330099220f,;"Seminars";title_investments,235,V400440d63,3900440d98,;"Investments";storage_permission_message,235,V4007b1a85,7a007b1afb,;"Storage permission is needed to select photos for student profiles.";group_deleted,235,V400bc2a5d,3c00bc2a95,;"Task group deleted";task_groups,235,V400b628a9,3300b628d8,;"Task Groups";saving,235,V400fe3aee,2900fe3b13,;"Saving";note_updated,235,V400921ffb,350092202c,;"Note updated";workout_timer,235,V4011c4065,37011c4098,;"Workout Timer";brush_size,235,V4001c05f8,32001c0626,;"Brush Size\:";drawing_saved_to_gallery,235,V4001e0655,4d001e069e,;"Drawing saved to gallery";error_firebase_network,235,V400c82d68,7100c82dd5,;"Unable to connect to the server. Please check your connection.";delete_workout,235,V4012e454a,39012e457f,;"Delete Workout";toggle_view,235,V400c02b3e,3300c02b6d,;"Toggle View";delete_group_confirmation,235,V400b9294e,9100b929db,;"Are you sure you want to delete this group? Tasks in this group will be moved to ungrouped.";share_note,235,V400942069,**********,;"Share Note";error_user_disabled,235,V400ce2f1c,5400ce2f6c,;"This user account has been disabled.";recording_saved,235,V400f73972,3b00f739a9,;"Recording saved";error_opening_file,235,V400982197,47009821da,;"Error opening file\: %1$s";reps,235,V401173f5d,2501173f7e,;"Reps";add_student_prompt,235,V400571180,61005711dd,;"Add students to start registering them for courses";google_api_key,236,V400030089,67000300ec,;"AIzaSyCXF4JpOl3_FXEzODDslf9VeTs9BGOiO1s";select_program,235,V4011d409e,39011d40d3,;"Select Program";delete_image,235,V4000e02ca,35000e02fb,;"Delete Image";save,235,V400130429,250013044a,;"Save";status_inactive_desc,235,V4009a2215,41009a2252,;"Inactive student";voice_note_singular,235,V400ef3783,3c00ef37bb,;"1 voice note";note_saved,235,V400911fc8,3100911ff5,;"Note saved";select_color,235,V40021072a,350021075b,;"Select Color";remove_exercise,235,V4011b4028,3b011b405f,;"Remove Exercise";task_updated,235,V400ab2669,3500ab269a,;"Task updated";weekly_workout_duration,235,V401214198,46012141da,;"Total duration\: %s";add_registration,235,V4005a1207,41005a1244,;"Add New Registration";error_adding_drawing,235,V4001b05b1,45001b05f2,;"Error adding drawing";color_brown,235,V4002e0983,33002e09b2,;"Brown Color";add_photo,235,V4004d0f64,2f004d0f8f,;"Add Photo";please_select_end_date,235,V4006815b1,49006815f6,;"Please select end date";start_date,235,V4005d12c6,31005d12f3,;"Start Date";status_paid_desc,235,V4007f1be1,4a007f1c27,;"Paid. Click to mark as unpaid";color_blue,235,V4002a08ab,31002a08d8,;"Blue Color";delete_video_confirmation,235,V400100338,6100100395,;"Are you sure you want to delete this video?";recording_started,235,V400f63931,3f00f6396c,;"Recording started";task_incomplete,235,V400ae2712,4500ae2753,;"Task marked as incomplete";investment_name,235,V4000901b3,3b000901ea,;"Investment Name";color_teal,235,V4002f09b8,31002f09e5,;"Teal Color";group_color,235,V400b52874,3300b528a3,;"Group Color";search_hint,235,V401033bbc,4601033bfe,;"Search...";color_green,235,V400290876,33002908a5,;"Green Color";google_crash_reporting_api_key,236,V40005015e,77000501d1,;"AIzaSyCXF4JpOl3_FXEzODDslf9VeTs9BGOiO1s";workout_log,235,V4010d3d59,2b010d3d80,;"Log";no_app_for_file_type,235,V400972141,5400972191,;"No app found to open this file type";edit_registration,235,V4005b124a,3f005b1285,;"Edit Registration";program_updated,235,V4013647d1,4801364815,;"Program updated successfully";deleting,235,V4008c1ec2,30008c1eee,;"Deleting...";group_created,235,V400ba29e1,3c00ba2a19,;"Task group created";workout_exercise,235,V4010b3ced,35010b3d1e,;"Exercise";error_requires_recent_login,235,V400d02fd2,7b00d03049,;"This operation requires recent authentication. Please log in again.";workout_dashboard,235,V4010a3cb4,37010a3ce7,;"Dashboard";switch_to_grouped_view,235,V400c12b73,4900c12bb8,;"Switch to Grouped View";error_network_unavailable,235,V400c62c91,7400c62d01,;"Network is unavailable. Please check your internet connection.";group_description,235,V400b42828,4a00b4286e,;"Group Description (Optional)";workout_program,235,V4010c3d24,33010c3d53,;"Program";task_description,235,V400a524e3,3d00a5251c,;"Task description";required_fields_missing,235,V4005510f3,5600551145,;"Name and Phone are required fields";please_select_student,235,V400661517,490066155c,;"Please select a student";error_permission_denied,235,V400d430c4,6600d43126,;"You don't have permission to perform this action.";total_workout_count,235,V4012542a3,42012542e1,;"Total workouts\: %d";payment_receipt,235,V400621418,3b0062144f,;"Payment Receipt";error_network,235,V400c52c22,6d00c52c8b,;"Network error occurred. Please check your connection and try again.";avg_duration,235,V4014249ab,35014249dc,;"Avg Duration";color_orange,235,V4002d094c,35002d097d,;"Orange Color";profile_image,235,V4004c0f2b,37004c0f5e,;"Profile Image";error_unknown,235,V400c72d07,5f00c72d62,;"An unexpected error occurred. Please try again later.";workout_saved,235,V4012a445a,37012a448d,;"Workout saved";notes_optional,235,V401193fb4,3b01193feb,;"Notes (Optional)";error_too_many_requests,235,V400cf2f72,5e00cf2fcc,;"Too many attempts. Please try again later.";uploading_attachments,235,V400ff3b19,4a00ff3b5f,;"Uploading attachments...";color_red,235,V400280845,2f00280870,;"Red Color";delete_video,235,V4000f0301,35000f0332,;"Delete Video";color_yellow,235,V4002b08de,35002b090f,;"Yellow Color";permission_denied,235,V4007d1b40,58007d1b94,;"%1$s permission denied. Unable to proceed.";registration_success,235,V400601367,5b006013be,;"Student registered for course successfully";delete_student_confirmation,235,V400e334e7,7400e33557,;"Are you sure you want to delete %1$s from your student list?";mark_incomplete,235,V400a72560,3e00a7259a,;"Mark as incomplete";history,235,V4003c0be4,2b003c0c0b,;"History";switch_to_list_view,235,V400c22bbe,4300c22bfd,;"Switch to List View";title_calendar,235,V4004b0ef6,33004b0f25,;"Calendar";drawing_added,235,V4001a0570,3f001a05ab,;"Drawing added to note";google_storage_bucket,236,V4000601d6,690006023b,;"allinone-bd6f3.firebasestorage.app";add_receipt,235,V400631455,3300631484,;"Add Receipt";workout_history,235,V401454a52,3b01454a89,;"Workout History";task_name,235,V4014b4b10,2f014b4b3b,;"Task name";google_app_id,236,V4000400f1,6c00040159,;"1\:954911141967\:android\:8369e5e490f1dab9ce7a3a";add_video,235,V4000c025e,2f000c0289,;"Add Video";edit_note,235,V4008f1f52,2f008f1f7d,;"Edit Note";error_not_authenticated,235,V400de33cd,5b00de3424,;"Authentication required. Please log in.";error_document_already_exists,235,V400d6319f,5500d631f0,;"This record already exists.";program_details,235,V4012c44d0,3b012c4507,;"Program Details";create_note,235,V4008e1f1d,33008e1f4c,;"Create Note";total_workout_duration,235,V4012642e7,4501264328,;"Total duration\: %s";no_registrations,235,V4006b16c7,44006b1707,;"No Course Registrations";voice_note_deleted,235,V400fa3a44,4100fa3a81,;"Voice note deleted";delete,235,V400871d35,2900871d5a,;"Delete";select_profile_photo,235,V4007518be,45007518ff,;"Select Profile Photo";item_type_icon,235,V400400cd1,3900400d06,;"Item type icon";drawing_options,235,V4001704a8,3b001704df,;"Drawing Options";choose_from_gallery,235,V400771938,4300771977,;"Choose from Gallery";total,235,V400701806,2700701829,;"Total";add_image,235,V4000b022d,2f000b0258,;"Add Image";delete_program_confirmation,235,V40132468c,830132470b,;"Are you sure you want to delete program %1$s? This action cannot be undone.";attachments_only_for_paid_registrations,235,V4006a164d,78006a16c1,;"Attachments can only be added for paid registrations";take_photo,235,V400761905,3100761932,;"Take Photo";ok,235,V400e7365b,2100e73678,;"OK";all_time_stats,235,V401244268,390124429d,;"All-Time Stats";amount,235,V400030068,290003008d,;"Amount";workout_statistics,235,V40140492d,410140496a,;"Workout Statistics";choose_color,235,V4002006f1,3700200724,;"Choose a Color";clear,235,V4001d062c,27001d064f,;"Clear";no_registrations_prompt,235,V4006c170d,64006c176d,;"Add students to courses by clicking the \+ button";voice_notes,235,V400ee374e,3300ee377d,;"Voice Notes";grant_permission,235,V4007c1b01,3d007c1b3a,;"Grant Permission";group_updated,235,V400bb2a1f,3c00bb2a57,;"Task group updated";error_authentication,235,V400cb2e00,5900cb2e55,;"Authentication failed. Please try again.";please_select_start_date,235,V400671562,4d006715ab,;"Please select start date";delete_student,235,V400e234ac,3900e234e1,;"Delete Student";offline_status,235,V400390b8c,3900390bc1,;"Offline status";active_status,235,V40052104e,3700521081,;"Active Status";status_unpaid_desc,235,V400801c2d,4c00801c75,;"Unpaid. Click to mark as paid";delete_task_confirmation,235,V400a925d5,5f00a92630,;"Are you sure you want to delete this task?";tap_to_view_attachment,235,V4009620f6,490096213b,;"Tap to view attachment";amount_format,235,V40072186d,2f00721898,;"₺%.2f";add_exercise,235,V4011a3ff1,35011a4022,;"Add Exercise";+style:FilterButtonStyle,238,V4001f04e8,c002305df,;DWidget.MaterialComponents.Button,android\:minHeight:36dp,android\:layout_height:36dp,android\:textSize:14sp,;Widget.App.Button,8,V40049106d,c004b1101,;DWidget.MaterialComponents.Button,android\:textColor:@color/white,;Theme.AllinOne.NoActionBar,7,V4001d0603,c00220749,;DTheme.AllinOne,windowActionBar:false,windowNoTitle:true,android\:statusBarColor:@android\:color/black,android\:windowBackground:@android\:color/white,;BoldText,238,V4001903ff,c001c049c,;Nandroid\:textStyle:bold,android\:textColor:@android\:color/black,;BoldText,239,V40009013a,c000c01d7,;Nandroid\:textStyle:bold,android\:textColor:@android\:color/white,;CalendarDayStyle,238,V40003005a,c000a01d6,;Nandroid\:gravity:center,android\:padding:8dp,android\:textSize:14sp,android\:clickable:true,android\:focusable:true,android\:background:?attr/selectableItemBackground,;BottomNavigationStyle,238,V4000d0224,c0011032d,;NitemIconTint:@color/bottom_nav_item_color,itemTextColor:@color/bottom_nav_item_color,itemRippleColor:@color/bright_tab_selected,;Widget.App.TextInputLayout,8,V4002e0a9c,c003c0e3f,;DWidget.MaterialComponents.TextInputLayout.OutlinedBox,android\:textColorHint:@color/white,android\:textColor:@color/text_input_text_color,android\:editTextColor:@color/text_input_text_color,hintTextColor:@color/white,boxStrokeColor:@color/text_input_box_stroke,boxStrokeWidth:1dp,boxStrokeWidthFocused:2dp,endIconTint:@color/white,startIconTint:@color/white,helperTextTextColor:@color/white,errorTextColor:@color/red,counterTextColor:@color/white,boxBackgroundColor:@android\:color/transparent,;Widget.App.NavigationView,8,V40066168a,c006b17f6,;DWidget.MaterialComponents.NavigationView,itemIconTint:@color/drawer_item_color,itemTextColor:@color/drawer_item_color,itemBackground:?attr/selectableItemBackground,itemIconPadding:16dp,;Widget.App.Button.TextButton.Dialog.Action,8,V400761a12,c007c1bf6,;DWidget.MaterialComponents.Button.TextButton,android\:textColor:@color/dialog_action_button_color,android\:textAllCaps:true,android\:textAppearance:@style/TextAppearance.MaterialComponents.Button,rippleColor:@color/white,backgroundTint:@android\:color/transparent,;FilterDropdownStyle,238,V40026061f,c002a06ee,;Nandroid\:minHeight:36dp,android\:layout_height:36dp,android\:textSize:14sp,;Widget.App.BottomNavigationView,8,V4005e14ce,c00631655,;DWidget.MaterialComponents.BottomNavigationView,itemIconTint:@color/bottom_nav_item_color,itemTextColor:@color/bottom_nav_item_color,itemRippleColor:@color/bright_tab_selected,itemActiveIndicatorStyle:@null,;Widget.App.Button.TextButton.Dialog.Delete,8,V4006e1828,c00741a0a,;DWidget.MaterialComponents.Button.TextButton,android\:textColor:@color/dialog_delete_button_color,android\:textAllCaps:true,android\:textAppearance:@style/TextAppearance.MaterialComponents.Button,rippleColor:@color/red,backgroundTint:@android\:color/transparent,;Theme.AllinOne,7,V40006011d,c001a05ab,;DTheme.MaterialComponents.DayNight.NoActionBar,android\:forceDarkAllowed:false,android\:statusBarColor:@color/black,android\:windowBackground:@color/white,colorPrimary:@color/black,colorPrimaryVariant:@color/gray_dark,colorOnPrimary:@color/white,colorSecondary:@color/black,colorSecondaryVariant:@color/gray_dark,colorOnSecondary:@color/white,windowActionBar:false,windowNoTitle:true,colorNavigationItem:@color/black,colorNavigationItemSelected:@color/gray_dark,;Theme.AllinOne,8,V40003008e,c002b0a56,;DTheme.MaterialComponents.DayNight.NoActionBar,colorPrimary:@color/navy_surface,colorPrimaryVariant:@color/navy_surface,colorOnPrimary:@color/white,colorSecondary:@color/bright_tab_selected,colorSecondaryVariant:@color/navy_variant,colorOnSecondary:@color/white,windowActionBar:false,windowNoTitle:true,colorSurface:@color/navy_surface,textInputStyle:@style/Widget.App.TextInputLayout,editTextStyle:@style/Widget.App.EditText,materialButtonStyle:@style/Widget.App.Button,materialAlertDialogTheme:@style/ThemeOverlay.App.MaterialAlertDialog,colorNavigationItem:@color/drawer_item_color,colorNavigationItemSelected:@color/bright_tab_selected,colorControlHighlight:@color/bright_tab_selected,android\:statusBarColor:@color/navy_surface,android\:windowBackground:@color/navy_background,android\:colorBackground:@color/navy_background,android\:textColor:@color/white,android\:textColorPrimary:@color/white,android\:textColorSecondary:@color/white,android\:textColorHint:@color/white,android\:editTextColor:@color/white,android\:navigationBarColor:@color/navy_surface,android\:itemBackground:@color/navy_surface,android\:listChoiceBackgroundIndicator:@color/bright_tab_selected,;Theme.AllinOne.Starting,7,V4002b08d8,c00370b98,;DTheme.SplashScreen,windowSplashScreenBackground:@color/black,windowSplashScreenAnimatedIcon:@drawable/transparent,postSplashScreenTheme:@style/Theme.AllinOne,android\:windowDisablePreview:true,;Theme.AllinOne.SplashText,7,V40025078d,c00280886,;DTheme.AllinOne,android\:windowBackground:@color/black,android\:windowLayoutInDisplayCutoutMode:shortEdges,;ThemeOverlay.App.MaterialAlertDialog,8,V4004e113a,c00531310,;DThemeOverlay.MaterialComponents.MaterialAlertDialog,android\:textColorPrimary:@color/white,android\:textColorSecondary:@color/white,materialAlertDialogTitleTextStyle:@style/MaterialAlertDialog.App.Title.Text,materialAlertDialogBodyTextStyle:@style/MaterialAlertDialog.App.Body.Text,;MaterialAlertDialog.App.Title.Text,8,V400551318,c005713ce,;DMaterialAlertDialog.MaterialComponents.Title.Text,android\:textColor:@color/white,;circleImageView,238,V400130335,c001603bf,;NcornerFamily:rounded,cornerSize:50%,;circleImageView,239,V40003006d,c000600f7,;NcornerFamily:rounded,cornerSize:50%,;Widget.App.EditText,8,V4003f0e7a,c00430fb8,;DWidget.MaterialComponents.TextInputEditText.OutlinedBox,android\:textColor:@color/text_input_text_color,android\:textColorHint:@color/white,android\:backgroundTint:@color/white,;MaterialAlertDialog.App.Body.Text,8,V4005913d6,c005b148a,;DMaterialAlertDialog.MaterialComponents.Body.Text,android\:textColor:@color/white,;+xml:file_paths,240,F;network_security_config,241,F;data_extraction_rules,242,F;backup_rules,243,F;