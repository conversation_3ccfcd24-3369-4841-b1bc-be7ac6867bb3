<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_workout_program" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\fragment_workout_program.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_workout_program_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="50" endOffset="51"/></Target><Target id="@+id/swipe_refresh_layout" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="8" startOffset="4" endLine="25" endOffset="59"/></Target><Target id="@+id/programs_recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="15" startOffset="8" endLine="23" endOffset="51"/></Target><Target id="@+id/empty_programs_text" view="TextView"><Expressions/><location startLine="27" startOffset="4" endLine="38" endOffset="36"/></Target><Target id="@+id/add_program_fab" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="40" startOffset="4" endLine="48" endOffset="51"/></Target></Targets></Layout>