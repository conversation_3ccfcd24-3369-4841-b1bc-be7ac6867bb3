<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_wt_seminars" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\fragment_wt_seminars.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_wt_seminars_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="55" endOffset="53"/></Target><Target id="@+id/seminarsRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="11" startOffset="8" endLine="18" endOffset="55"/></Target><Target id="@+id/emptyStateText" view="TextView"><Expressions/><location startLine="20" startOffset="8" endLine="32" endOffset="55"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="34" startOffset="8" endLine="42" endOffset="55"/></Target><Target id="@+id/addSeminarFab" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="46" startOffset="4" endLine="53" endOffset="56"/></Target></Targets></Layout>