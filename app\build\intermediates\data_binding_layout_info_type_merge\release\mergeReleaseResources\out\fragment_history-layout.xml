<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_history" modulePackage="com.example.allinone" filePath="app\src\main\res\layout\fragment_history.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_history_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="135" endOffset="51"/></Target><Target id="@+id/filter_scroll" view="HorizontalScrollView"><Expressions/><location startLine="9" startOffset="4" endLine="78" endOffset="26"/></Target><Target id="@+id/filter_chip_group" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="23" startOffset="8" endLine="77" endOffset="52"/></Target><Target id="@+id/filter_all" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="29" startOffset="12" endLine="36" endOffset="75"/></Target><Target id="@+id/filter_income" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="38" startOffset="12" endLine="46" endOffset="75"/></Target><Target id="@+id/filter_expense" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="48" startOffset="12" endLine="56" endOffset="75"/></Target><Target id="@+id/filter_registrations" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="58" startOffset="12" endLine="66" endOffset="75"/></Target><Target id="@+id/filter_notes" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="68" startOffset="12" endLine="75" endOffset="75"/></Target><Target id="@+id/swipe_refresh" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="80" startOffset="4" endLine="97" endOffset="59"/></Target><Target id="@+id/recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="89" startOffset="8" endLine="95" endOffset="51"/></Target><Target id="@+id/empty_state" view="LinearLayout"><Expressions/><location startLine="99" startOffset="4" endLine="133" endOffset="18"/></Target></Targets></Layout>