R_DEF: Internal format may change without notice
local
attr colorNavigationItem
attr colorNavigationItemSelected
color ai_message_bg
color albumCard
color background_color
color bg_tag_blue
color black
color blue_500
color boldTextColor
color bottom_nav_item_color
color bottom_nav_item_color_light
color bright_tab_selected
color bright_tab_unselected
color cardStroke
color carousel_background
color carousel_orange
color chip_background_selector
color colorAccent
color colorError
color colorPrimary
color colorPrimaryDark
color colorPrimaryLight
color colorSuccess
color colorWarning
color dark_gray
color default_event_color
color dialog_action_button_color
color dialog_delete_button_color
color drawer_item_color
color error_dark
color error_light
color excellent_green
color expiration_color
color feedCard
color good_orange
color gray
color gray_dark
color gray_light
color green
color green_500
color ic_launcher_background
color image_green
color lesson_event_color
color light_blue
color light_gray
color medium_gray
color mtrl_textinput_default_box_stroke_color
color navy_accent
color navy_background
color navy_primary
color navy_surface
color navy_text_secondary
color navy_variant
color on_surface_color
color orange_500
color poor_red
color purple
color purple_500
color red
color red_500
color reelsCard
color reels_background
color reels_purple
color registration_end
color registration_start
color start_color
color storyCard
color surface_color
color task_checkbox_color
color textPrimary
color text_input_box_stroke
color text_input_text_color
color text_primary
color user_message_bg
color video_background
color video_blue
color warning_light
color white
color wt_bottom_nav_item_color
dimen task_indent_margin
drawable bg_category_chip
drawable bg_current_day
drawable bg_day_with_event
drawable bg_day_with_events
drawable bg_day_with_lesson
drawable bg_day_with_registration
drawable bg_selected_day
drawable bg_tag_blue
drawable bg_tag_green
drawable bg_tag_red
drawable border_background
drawable circle_background
drawable circle_background_red
drawable circle_shape
drawable completed_exercise_background
drawable default_profile
drawable dialog_rounded_bg
drawable error_image
drawable fully_uncompleted_exercise_background
drawable ic_add
drawable ic_add_photo
drawable ic_attach_image
drawable ic_back
drawable ic_backup
drawable ic_calendar
drawable ic_call
drawable ic_category_all
drawable ic_category_bills
drawable ic_category_food
drawable ic_category_game
drawable ic_category_general
drawable ic_category_investment
drawable ic_category_salary
drawable ic_category_shopping
drawable ic_category_sports
drawable ic_category_transport
drawable ic_category_wing_tzun
drawable ic_checkbox
drawable ic_chevron_left
drawable ic_chevron_right
drawable ic_clear_data
drawable ic_cleardata
drawable ic_close
drawable ic_code
drawable ic_dashboard
drawable ic_database
drawable ic_delete
drawable ic_draw
drawable ic_edit
drawable ic_empty_state
drawable ic_error
drawable ic_exercise
drawable ic_expand_less
drawable ic_expand_more
drawable ic_expense
drawable ic_file
drawable ic_fitness
drawable ic_folder
drawable ic_format_bold
drawable ic_format_italic
drawable ic_format_list_bulleted
drawable ic_format_list_numbered
drawable ic_format_text
drawable ic_format_underline
drawable ic_graduation
drawable ic_history
drawable ic_home
drawable ic_image
drawable ic_income
drawable ic_instagram
drawable ic_instagram_posts
drawable ic_investment
drawable ic_investments
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_lessons
drawable ic_log
drawable ic_menu
drawable ic_no_registrations
drawable ic_no_students
drawable ic_note
drawable ic_notes
drawable ic_notification
drawable ic_pause
drawable ic_play
drawable ic_play_circle
drawable ic_program
drawable ic_registration
drawable ic_remove
drawable ic_reports
drawable ic_save
drawable ic_search_white
drawable ic_share
drawable ic_stats
drawable ic_stop
drawable ic_student
drawable ic_students
drawable ic_tasks
drawable ic_transactions
drawable ic_video_error
drawable ic_video_placeholder
drawable ic_view_list
drawable ic_whatsapp
drawable ic_wt
drawable ic_wt_registers
drawable placeholder_image
drawable rounded_background
drawable rounded_corner_bg
drawable selected_circle_shape
drawable simple_text_splash
drawable splash_layout_drawable
drawable transparent
drawable uncompleted_exercise_background
font opensans
font opensans_bold
font opensans_regular
id action_delete
id action_delete_group
id action_edit
id action_edit_group
id action_manage_groups
id action_search
id action_toggle_view
id activeSwitch
id addAttachmentButton
id addExpenseButton
id addImageButton
id addIncomeButton
id addInvestmentButton
id add_exercise_button
id amount
id amountInput
id amountLayout
id amountText
id appNameText
id applyFiltersButton
id attachmentNameText
id attachmentPreview
id attachmentsSection
id avatarAI
id averageTransactionText
id backup_card
id backup_name
id backup_size
id backups_card
id backups_recycler_view
id balanceText
id balanceTitleText
id balanceValueText
id btn_cancel
id btn_delete
id btn_save
id callButton
id cancelButton
id cardAIMessage
id cardUserMessage
id cashFlowTitle
id categoryAmountText
id categoryAutoComplete
id categoryChip
id categoryColorIndicator
id categoryIcon
id categoryLayout
id categoryName
id categoryNameText
id categoryPercentText
id categoryPieChart
id categorySpendingCard
id categoryText
id chartCard
id clearDueDateButton
id closeButton
id closePositionButton
id color_blue
id color_green
id color_orange
id color_purple
id color_red
id confirmButton
id create_backup_button
id dateInput
id dateLayout
id dateRangeAutoComplete
id dateRangeLayout
id dateText
id dayText
id deleteButton
id deleteVoiceNoteButton
id delete_button
id descriptionInput
id descriptionLayout
id descriptionText
id detailsTextView
id dialogTitle
id dialog_title
id divider
id drawer_button
id dueDateLabel
id dueDateText
id editButton
id email
id emailEditText
id emailInputLayout
id emptyStateText
id emptyTransactionsText
id endDate
id endDateInput
id endTimeField
id endTimeInput
id endTimeLayout
id entryPriceLabel
id entryPriceLabelText
id entryPriceText
id entryPriceValue
id eventDate
id eventDescription
id eventDescriptionInput
id eventEndTimeInput
id eventTimeInput
id eventTitle
id eventTitleInput
id exercise_name_input
id exercise_notes_input
id exercise_reps_input
id exercise_sets_input
id exercise_weight_input
id exercises_container
id exercises_recycler_view
id expenseText
id fileIcon
id fileName
id filterCard
id fragment_container
id fullscreenImageItem
id fullscreenViewPager
id futuresSwipeRefreshLayout
id futuresTabLayout
id futuresViewPager
id groupSpinner
id group_description_edit
id group_description_layout
id group_settings
id group_title_edit
id group_title_layout
id homeFragment
id imageContainer
id imageCounterText
id imageView
id imagesRecyclerView
id incomeText
id insightsCard
id insightsLabel
id instagramButton
id instagramEditText
id instagramInputLayout
id investmentAmount
id investmentCountText
id investmentCurrentValue
id investmentDate
id investmentDescription
id investmentDropdown
id investmentDropdownLayout
id investmentName
id investmentProfitLoss
id investmentTabLayout
id investmentType
id investmentViewPager
id investmentsRecyclerView
id isPastInvestmentCheckbox
id largestExpenseText
id layoutConfidence
id layoutSources
id layoutTyping
id leverageLabelText
id leverageText
id lineChart
id liqPriceLabel
id liqPriceValue
id liquidationPriceLabelText
id liquidationPriceText
id loadingProgress
id lossRadio
id marginBalanceTitleText
id marginBalanceValueText
id marginLabel
id marginTypeLabelText
id marginTypeText
id marginValue
id markPriceLabel
id markPriceLabelText
id markPriceText
id markPriceValue
id mostSpentCategoryText
id muscle_group_dropdown
id nameEditText
id nameInput
id nameInputLayout
id nameLayout
id nameTextView
id nav_backup
id nav_calendar
id nav_clear_data
id nav_clear_db
id nav_database_management
id nav_error_logs
id nav_history
id nav_instagram_business
id nav_investments
id nav_notes
id nav_tasks
id nav_transaction_report
id nav_transactions
id nav_workout
id nav_wt_registry
id newInvestmentButton
id nextPageButton
id no_backups_text
id noteContent
id noteDate
id noteTitle
id notesEditText
id offline_icon
id offline_status_card
id offline_status_message
id offline_status_title
id pageIndicator
id page_title
id paginationControls
id paidSwitch
id paymentStatusChip
id pending_operations_count
id phoneEditText
id phoneInputLayout
id phoneNumber
id pickDueDateButton
id pieChart
id pinEditText
id playPauseButton
id pnlLabel
id pnlText
id pnlTitleText
id pnlValue
id pnlValueText
id positionAmtLabelText
id positionAmtText
id positionDetailsText
id positionSideText
id positionSymbolText
id positionsRecyclerView
id positionsTitle
id postCaption
id postCaptionLabel
id postDate
id postDateLabel
id postDetailThumbnail
id postInsights
id postLink
id postLinkLabel
id postType
id postTypeLabel
id prevPageButton
id profileImage
id profileImageView
id profitLossAmountInput
id profitRadio
id program_description_input
id program_description_text
id program_name_input
id program_name_text
id progressBar
id progress_bar
id progress_indicator
id progress_text
id recyclerSources
id remove_exercise_button
id restore_button
id roiLabel
id roiValue
id saveButton
id seminarDateText
id seminarDescriptionText
id seminarNameText
id seminarTimeText
id shareButton
id share_button
id sizeLabel
id sizeValue
id startDate
id startDateInput
id startTimeField
id startTimeInput
id startTimeLayout
id statusIndicator
id stopLossInput
id stopLossLayout
id studentDropdown
id studentName
id studentPhoto
id summaryCard
id swipeRefreshLayout
id symbolText
id tab_ask_ai
id tab_insight
id tab_posts
id takeProfitInput
id takeProfitLayout
id taskDescriptionInput
id taskNameInput
id textAIMessage
id textAITimestamp
id textConfidence
id textSourceEmoji
id textSourceMetrics
id textSourcePost
id textSourceScore
id textUserMessage
id textUserTimestamp
id themeSwitch
id timeSlash
id timeText
id titleText
id toolbar
id tooltipText
id topCategoriesRecyclerView
id totalExpenseText
id totalIncomeText
id totalInvestmentsText
id tpSlText
id tpslLabel
id tpslValue
id transactionAmount
id transactionCountText
id transactionDate
id transactionDescription
id transactionTitle
id transactionsCard
id transactionsRecyclerView
id typeDropdown
id typeInput
id typeLayout
id typeText
id videoThumbnail
id voiceNoteCountText
id voiceNoteCurrentTime
id voiceNoteDuration
id voiceNoteIndicator
id voiceNoteTitle
id whatsappButton
id workout_dashboard
id workout_date_text
id workout_duration_text
id workout_exercise
id workout_name_text
id workout_program
id workout_stats
id wtLessonsFragment
id wtRegisterFragment
id wtSeminarsFragment
id wtStudentsFragment
layout activity_backup
layout dialog_add_event
layout dialog_add_program
layout dialog_add_student
layout dialog_add_task
layout dialog_edit_investment
layout dialog_edit_lesson
layout dialog_edit_program
layout dialog_edit_seminar
layout dialog_edit_wt_student
layout dialog_expense_investment
layout dialog_fullscreen_image
layout dialog_futures_tp_sl
layout dialog_income_investment
layout dialog_loading
layout dialog_pin_input
layout dialog_post_details
layout dialog_profit_loss
layout dialog_program_details
layout dialog_progress
layout dialog_student_details
layout dialog_task_group
layout dialog_workout_details
layout dropdown_item
layout fragment_futures
layout fragment_futures_tab
layout fragment_home
layout fragment_investments
layout fragment_investments_tab
layout fragment_transaction_report
layout fragment_transactions_overview
layout item_add_exercise
layout item_backup
layout item_binance_position
layout item_category_dropdown
layout item_category_spending
layout item_category_summary
layout item_chat_ai
layout item_chat_source
layout item_chat_user
layout item_file_structure
layout item_fullscreen_image
layout item_futures_position
layout item_investment
layout item_investment_dropdown
layout item_investment_image
layout item_investment_selection
layout item_lesson
layout item_note
layout item_note_image
layout item_note_video
layout item_seminar
layout item_transaction
layout item_transaction_report
layout item_voice_note
layout item_wt_event
layout item_wt_registration
layout item_wt_student
layout layout_page_header
layout nav_header
layout offline_status_view
layout pie_chart_tooltip
layout splash_text_layout
layout theme_switch_layout
menu bottom_nav_menu
menu drawer_menu
menu instagram_bottom_nav_menu
menu menu_edit_note
menu menu_group_options
menu menu_task_options
menu menu_tasks
menu search_history
menu search_notes
menu search_register
menu search_students
menu workout_bottom_nav_menu
menu wt_bottom_nav_menu
menu wt_registration_context_menu
menu wt_student_context_menu
mipmap ic_launcher
mipmap ic_launcher_round
plurals exercise_count
string active_status
string add_attachment
string add_exercise
string add_image
string add_photo
string add_program
string add_receipt
string add_registration
string add_student
string add_student_prompt
string add_task
string add_video
string all_months
string all_time_stats
string amount
string amount_format
string app_id
string app_name
string apply
string attachment_click_to_open
string attachments_only_for_paid_registrations
string avg_duration
string brightness
string brush_size
string camera_permission_message
string camera_permission_title
string cancel
string cannot_delete
string choose_color
string choose_from_gallery
string clear
string close
string color
string color_black
string color_blue
string color_brown
string color_green
string color_orange
string color_pink
string color_purple
string color_red
string color_teal
string color_yellow
string com.google.firebase.crashlytics.mapping_file_id
string create_drawing
string create_note
string create_task_group
string create_workout
string custom_workout
string delete
string delete_group
string delete_group_confirmation
string delete_image
string delete_image_confirmation
string delete_item
string delete_note
string delete_note_confirmation
string delete_program
string delete_program_confirmation
string delete_registration
string delete_registration_confirmation
string delete_student
string delete_student_confirmation
string delete_task
string delete_task_confirmation
string delete_video
string delete_video_confirmation
string delete_voice_note
string delete_voice_note_confirmation
string delete_workout
string delete_workout_confirmation
string deleting
string description
string drawing_added
string drawing_options
string drawing_saved_to_gallery
string edit
string edit_coming_soon
string edit_note
string edit_registration
string edit_student
string edit_task
string edit_task_group
string email
string empty_history
string end_date
string enter_task_description
string error_adding_drawing
string error_authentication
string error_camera
string error_database
string error_deleting_registration
string error_document_already_exists
string error_document_not_found
string error_file_not_found
string error_firebase_network
string error_network
string error_network_unavailable
string error_not_authenticated
string error_not_authorized
string error_opening_file
string error_permission_denied
string error_playing_audio
string error_quota_exceeded
string error_requires_recent_login
string error_saving_drawing
string error_saving_recording
string error_service_unavailable
string error_storage
string error_storage_quota_exceeded
string error_too_many_requests
string error_unknown
string error_user_disabled
string error_user_not_found
string error_wrong_password
string exercise_name
string expand_collapse
string favorite_muscle
string field_required
string gcm_defaultSenderId
string google_api_key
string google_app_id
string google_crash_reporting_api_key
string google_storage_bucket
string grant_permission
string group_color
string group_created
string group_deleted
string group_description
string group_title
string group_updated
string history
string image_counter
string instagram
string investment_name
string investment_type
string item_type_icon
string manage_groups
string mark_completed
string mark_incomplete
string most_recent_workout
string name
string network_unavailable
string new_task
string no_app_for_file_type
string no_group
string no_history_items
string no_programs
string no_recent_workouts
string no_registrations
string no_registrations_prompt
string no_students
string no_tasks_yet
string no_workout_history
string not_set
string note_deleted
string note_saved
string note_updated
string notes
string notes_optional
string offline_message
string offline_mode
string offline_status
string ok
string pause_workout
string payment_receipt
string payment_received
string pending_operations
string permission_denied
string phone_number
string please_enter_title
string please_enter_valid_amount
string please_select_end_date
string please_select_start_date
string please_select_student
string profile_image
string program_created
string program_deleted
string program_details
string program_name
string program_updated
string project_id
string pull_to_refresh
string receipt_unavailable
string record
string recording
string recording_failed
string recording_saved
string recording_started
string registration_deleted
string registration_success
string registration_updated
string remove_exercise
string reps
string required_fields_missing
string retry
string save
string save_changes
string save_to_note
string save_to_note_and_gallery
string save_workout
string saving
string search
string search_hint
string select
string select_color
string select_group
string select_month
string select_profile_photo
string select_program
string select_student
string select_type
string selected_color
string sets
string share_note
string share_registration
string start_date
string start_workout
string status_active_desc
string status_inactive_desc
string status_paid_desc
string status_registered_desc
string status_unpaid_desc
string stop
string stop_workout
string stop_workout_confirmation
string storage_permission_message
string storage_permission_title
string student_deleted
string student_has_active_registrations
string switch_to_grouped_view
string switch_to_list_view
string take_photo
string tap_to_view_attachment
string task_added
string task_completed
string task_deleted
string task_description
string task_groups
string task_incomplete
string task_name
string task_updated
string tasks
string title_calendar
string title_history
string title_investments
string title_lesson_schedule
string title_notes
string title_register
string title_seminars
string title_students
string title_wing_tzun_registry
string toggle_view
string total
string total_amount_label
string total_time
string total_workout_count
string total_workout_duration
string total_workouts
string type
string type_input_description
string uploading_attachments
string voice_note_deleted
string voice_note_plural
string voice_note_singular
string voice_notes
string weekly_summary
string weekly_workout_count
string weekly_workout_duration
string weight
string workout
string workout_dashboard
string workout_deleted
string workout_details
string workout_exercise
string workout_history
string workout_log
string workout_program
string workout_saved
string workout_statistics
string workout_stats
string workout_timer
style BoldText
style BottomNavigationStyle
style CalendarDayStyle
style FilterButtonStyle
style FilterDropdownStyle
style MaterialAlertDialog.App.Body.Text
style MaterialAlertDialog.App.Title.Text
style ThemeOverlay.App.MaterialAlertDialog
style Theme.AllinOne
style Theme.AllinOne.NoActionBar
style Theme.AllinOne.SplashText
style Theme.AllinOne.Starting
style Widget.App.BottomNavigationView
style Widget.App.Button
style Widget.App.Button.TextButton.Dialog.Action
style Widget.App.Button.TextButton.Dialog.Delete
style Widget.App.EditText
style Widget.App.NavigationView
style Widget.App.TextInputLayout
style circleImageView
xml backup_rules
xml data_extraction_rules
xml file_paths
xml network_security_config
